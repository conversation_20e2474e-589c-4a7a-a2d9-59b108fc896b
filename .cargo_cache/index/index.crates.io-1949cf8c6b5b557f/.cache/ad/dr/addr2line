   etag: W/"33a65167c70425f34ff739e5d9406cef" 0.1.0 {"name":"addr2line","vers":"0.1.0","deps":[{"name":"fallible-iterator","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2.14","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"gimli","req":"^0.9.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.5.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"de267b02d37156829690719e20cd78abe92d166410602dee98796bacf8be04f2","features":{"nightly":[]},"yanked":false} 0.2.0 {"name":"addr2line","vers":"0.2.0","deps":[{"name":"clap","req":"^2.19.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"error-chain","req":"^0.7.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"gimli","req":"^0.11.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"glob","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"itertools","req":"^0.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap","req":"^0.5.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"owning_ref","req":"^0.2.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"a13dfe51e0b4262552895841de509eabe97594ee9b2a6f2523ff5cee73c9e318","features":{"nightly":[]},"yanked":false} 0.2.1 {"name":"addr2line","vers":"0.2.1","deps":[{"name":"clap","req":"^2.19.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"error-chain","req":"^0.7.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"gimli","req":"^0.11.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"glob","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"itertools","req":"^0.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap","req":"^0.5.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"owning_ref","req":"^0.2.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"8e16a42e0bd036a4e64b7380d8fa6eacba4383f1340203663de19ee922d9f01a","features":{"nightly":[]},"yanked":false} 0.3.0 {"name":"addr2line","vers":"0.3.0","deps":[{"name":"clap","req":"^2.19.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"cpp_demangle","req":"^0.2.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"error-chain","req":"^0.7.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"gimli","req":"^0.11.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"glob","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"itertools","req":"^0.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap","req":"^0.5.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"owning_ref","req":"^0.2.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1.3","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"1c5075b2d78fb77bdda44aa6e797d75aae0365be88306fff8ec7bc857e27c06b","features":{"default":["demangle"],"demangle":["cpp_demangle","rustc-demangle"],"nightly":[]},"yanked":false} 0.4.0 {"name":"addr2line","vers":"0.4.0","deps":[{"name":"clap","req":"^2.19.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"cpp_demangle","req":"^0.2.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"error-chain","req":"^0.7.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"gimli","req":"^0.13.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"glob","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"itertools","req":"^0.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap","req":"^0.5.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.4.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"owning_ref","req":"^0.2.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1.3","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"161c1a8e59caabfa81f0319688aeb07e9baa66812b10214524f467764968fc1e","features":{"default":["demangle"],"demangle":["cpp_demangle","rustc-demangle"],"nightly":[]},"yanked":false} 0.5.0 {"name":"addr2line","vers":"0.5.0","deps":[{"name":"clap","req":"^2.19.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"cpp_demangle","req":"^0.2.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"error-chain","req":"^0.7.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"gimli","req":"^0.14.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"glob","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"itertools","req":"^0.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap","req":"^0.5.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.4.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"owning_ref","req":"^0.2.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1.3","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"6cd178cc199d1f1ffe63206f18291f9656fb7578244ca378dc1dd558de8ac4b6","features":{"default":["demangle"],"demangle":["cpp_demangle","rustc-demangle"],"nightly":[]},"yanked":false} 0.6.0 {"name":"addr2line","vers":"0.6.0","deps":[{"name":"backtrace","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cpp_demangle","req":"^0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.15","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"intervaltree","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"lazy-init","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"cb0edd8d04635ee7996ad7daaf173d392f5b737ef9cf315ee79beffa05689cfb","features":{"default":["rustc-demangle","cpp_demangle"]},"yanked":false} 0.7.0 {"name":"addr2line","vers":"0.7.0","deps":[{"name":"backtrace","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cpp_demangle","req":"^0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.16","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"intervaltree","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"lazycell","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.9","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"c866703d4f9130dec71afffafd4a6cda7b1a5cd6db63d2cc05c2499194cc700e","features":{"default":["rustc-demangle","cpp_demangle"]},"yanked":false} 0.8.0 {"name":"addr2line","vers":"0.8.0","deps":[{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cpp_demangle","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.16.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"intervaltree","req":"^0.2","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"lazycell","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.11","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^0.6","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"93942d6503c238271b9023f28a97681d74fef65b6fb74a358717bf955a6b1d21","features":{"alloc":["gimli/alloc"],"default":["rustc-demangle","cpp_demangle","object","std"],"std":["gimli/std","intervaltree/std","object/std","smallvec/std"]},"yanked":false} 0.9.0 {"name":"addr2line","vers":"0.9.0","deps":[{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cpp_demangle","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.18.0","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"intervaltree","req":"^0.2","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"lazycell","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.12","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^0.6","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"dd760163046252747559e501642cde66de6af5246207633ccbe38d6e0970349f","features":{"alloc":["gimli/alloc"],"default":["rustc-demangle","cpp_demangle","std-object"],"std":["gimli/std","intervaltree/std","smallvec/std"],"std-object":["std","object","object/std"]},"yanked":false} 0.10.0 {"name":"addr2line","vers":"0.10.0","deps":[{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cpp_demangle","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.19","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"intervaltree","req":"^0.2","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"lazycell","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.12","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^0.6","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"95b06ae5a8a3bae54910c9029a52f83203ce2001c71b10b1faae3a337fee4ab5","features":{"alloc":["gimli/alloc"],"default":["rustc-demangle","cpp_demangle","std-object"],"std":["gimli/std","intervaltree/std","smallvec/std"],"std-object":["std","object","object/std"]},"yanked":false} 0.11.0 {"name":"addr2line","vers":"0.11.0","deps":[{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cpp_demangle","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.20","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"lazycell","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.17","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"1c4e698660ed2d0f625c39bb877332b4269668720e330e2aa3d67bb1187a656a","features":{"default":["rustc-demangle","cpp_demangle","std-object"],"std":["gimli/std"],"std-object":["std","object","object/std"]},"yanked":false} 0.12.0 {"name":"addr2line","vers":"0.12.0","deps":[{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cpp_demangle","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.21","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.19","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"456d75cbb82da1ad150c8a9d97285ffcd21c9931dcb11e995903e7d75141b38b","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.12.1 {"name":"addr2line","vers":"0.12.1","deps":[{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cpp_demangle","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.21","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.19","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"a49806b9dadc843c61e7c97e72490ad7f7220ae249012fbda9ad0609457c0543","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.12.2 {"name":"addr2line","vers":"0.12.2","deps":[{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cpp_demangle","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.21","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.19","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"602d785912f476e480434627e8732e6766b760c045bbf897d9dfaa9f4fbd399c","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.13.0 {"name":"addr2line","vers":"0.13.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.22","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.20","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"1b6a2d3371669ab3ca9797670853d61402b03d0b4b9ebf33d677dfa720203072","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.14.0 {"name":"addr2line","vers":"0.14.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.23","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.22","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"7c0929d69e78dd9bf5408269919fcbcaeb2e35e5d43e5815517cdc6a8e11a423","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.14.1 {"name":"addr2line","vers":"0.14.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.23","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.22","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"a55f82cfe485775d02112886f4169bde0c5894d75e79ead7eafe7e40a25e45f7","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.15.0 {"name":"addr2line","vers":"0.15.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.24","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.24","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"99d5f3b7df8db1994169bc3506ea1128469206e94f8a501e4cd559fa9d161c6d","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.15.1 {"name":"addr2line","vers":"0.15.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.24","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.24","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"03345e98af8f3d786b6d9f656ccfa6ac316d954e92bc4841f0bba20789d5fb5a","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.15.2 {"name":"addr2line","vers":"0.15.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.24","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.24","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"e7a2e47a1fbe209ee101dd6d61285226744c6c8d3c21c8dc878ba6cb9f467f3a","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.16.0 {"name":"addr2line","vers":"0.16.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.25","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.26","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"3e61f2b7f93d2c7d2b08263acaa4a363b3e276806c68af6134c44f523bf1aacd","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.17.0 {"name":"addr2line","vers":"0.17.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.26","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.27.1","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"b9ecd88a8c8378ca913a680cd98f0f13ac67383d35993f86c90a70e3f137816b","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.18.0 {"name":"addr2line","vers":"0.18.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^3.1.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.26","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.5.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.29","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"6ca9b76e919fd83ccfb509f51b28c333c0e03f2221616e347a129215cec4e4a9","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.19.0 {"name":"addr2line","vers":"0.19.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^3.1.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.4","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.27.0","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.5.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.30.0","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rustc-test","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"a76fd60b23679b7d19bd066031410fb7e458ccc5e958eb5c325888ce4baedc97","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.20.0 {"name":"addr2line","vers":"0.20.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^3.1.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.4","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.27.2","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"libtest-mimic","req":"^0.5.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap2","req":"^0.5.5","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.31.0","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"f4fa78e18c64fce05e902adecd7a5eed15a5e0a3439f7b0e169f0252214865e3","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec","memmap2"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false} 0.21.0 {"name":"addr2line","vers":"0.21.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^4.3.21","features":["wrap_help"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.4","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.28.0","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"libtest-mimic","req":"^0.6.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap2","req":"^0.5.5","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.32.0","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"8a30b2e23b9e17a9f90641c7ab1549cd9b44f296d3ccbf309d2863cfe398a0cb","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec","memmap2"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false,"rust_version":"1.65"} 0.22.0 {"name":"addr2line","vers":"0.22.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^4.3.21","features":["wrap_help"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.4","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.29.0","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"libtest-mimic","req":"^0.7.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap2","req":"^0.9.4","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.35.0","features":["read"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"6e4503c46a5c0c7844e948c9a4d6acd9f50cccb4de1c48eb9e291ea17470c678","features":{"default":["rustc-demangle","cpp_demangle","std-object","fallible-iterator","smallvec","memmap2"],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"],"std-object":["std","object","object/std","object/compression","gimli/endian-reader"]},"yanked":false,"rust_version":"1.65"} 0.23.0 {"name":"addr2line","vers":"0.23.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^4.3.21","features":["wrap_help"],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.4","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.30.0","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"libtest-mimic","req":"^0.7.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap2","req":"^0.9.4","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.36.0","features":["read","compression"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"7b9d03130b08257bc8110b0df827d8b137fdf67a95e2459eaace2e13fecf1d72","features":{"cargo-all":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"]},"features2":{"all":["bin"],"bin":["loader","rustc-demangle","cpp_demangle","smallvec","dep:clap"],"default":["rustc-demangle","cpp_demangle","loader","fallible-iterator","smallvec"],"loader":["std","dep:object","dep:memmap2","dep:typed-arena"]},"yanked":false,"rust_version":"1.65","v":2} 0.24.0 {"name":"addr2line","vers":"0.24.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^4.3.21","features":["wrap_help"],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.4","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.31.0","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"libtest-mimic","req":"^0.7.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap2","req":"^0.9.4","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.36.0","features":["read","compression"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"e60698898f23be659cb86289e5805b1e059a5fe1cd95c9a1d4def50369e74b31","features":{"cargo-all":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"]},"features2":{"all":["bin"],"bin":["loader","rustc-demangle","cpp_demangle","smallvec","dep:clap"],"default":["rustc-demangle","cpp_demangle","loader","fallible-iterator","smallvec"],"loader":["std","dep:object","dep:memmap2","dep:typed-arena"]},"yanked":false,"rust_version":"1.65","v":2} 0.24.1 {"name":"addr2line","vers":"0.24.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^4.3.21","features":["wrap_help"],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.4","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.31.0","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"libtest-mimic","req":"^0.7.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap2","req":"^0.9.4","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.36.0","features":["read","compression"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"f5fb1d8e4442bd405fdfd1dacb42792696b0cf9cb15882e5d097b742a676d375","features":{"cargo-all":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"]},"features2":{"all":["bin"],"bin":["loader","rustc-demangle","cpp_demangle","smallvec","dep:clap"],"default":["rustc-demangle","cpp_demangle","loader","fallible-iterator","smallvec"],"loader":["std","dep:object","dep:memmap2","dep:typed-arena"]},"yanked":false,"rust_version":"1.65","v":2} 0.24.2 {"name":"addr2line","vers":"0.24.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^4.3.21","features":["wrap_help"],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.4","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.31.1","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"libtest-mimic","req":"^0.7.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap2","req":"^0.9.4","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.36.0","features":["read","compression"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"dfbe277e56a376000877090da837660b4427aad530e3028d44e0bffe4f89a1c1","features":{"cargo-all":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","gimli/rustc-dep-of-std"],"std":["gimli/std"]},"features2":{"all":["bin"],"bin":["loader","rustc-demangle","cpp_demangle","fallible-iterator","smallvec","dep:clap"],"default":["rustc-demangle","cpp_demangle","loader","fallible-iterator","smallvec"],"loader":["std","dep:object","dep:memmap2","dep:typed-arena"]},"yanked":false,"rust_version":"1.65","v":2} 0.25.0 {"name":"addr2line","vers":"0.25.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"backtrace","req":"^0.3.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"clap","req":"^4.3.21","features":["wrap_help"],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"cpp_demangle","req":"^0.4","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"criterion","req":"^0.6.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"findshlibs","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"gimli","req":"^0.32.0","features":["read"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"libtest-mimic","req":"^0.8.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap2","req":"^0.9.4","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"object","req":"^0.37.0","features":["read","compression"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-demangle","req":"^0.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"smallvec","req":"^1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"typed-arena","req":"^2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"9acbfca36652500c911ddb767ed433e3ed99b032b5d935be73c6923662db1d43","features":{"cargo-all":[],"rustc-dep-of-std":["core","alloc","gimli/rustc-dep-of-std"],"std":["gimli/std"],"wasm":["object/wasm"]},"features2":{"all":["bin","wasm"],"bin":["loader","rustc-demangle","cpp_demangle","fallible-iterator","smallvec","dep:clap"],"default":["rustc-demangle","cpp_demangle","loader","fallible-iterator","smallvec"],"loader":["std","dep:object","dep:memmap2","dep:typed-arena"]},"yanked":false,"rust_version":"1.81","v":2} 