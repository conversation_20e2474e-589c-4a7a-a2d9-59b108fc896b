   etag: W/"cd2092a28192de83297a79cea1dd42d0" 0.1.0 {"name":"dlmalloc","vers":"0.1.0","deps":[{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"64eac39fc51067b3e1a43c3d228f2cc690be48342fecb54bf15752ab50dbd386","features":{"allocator-api":[],"debug":[],"global":[]},"yanked":false} 0.1.1 {"name":"dlmalloc","vers":"0.1.1","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"4c46c65de42b063004b31c67a98abe071089b289ff0919c660ed7ff4f59317f8","features":{"allocator-api":[],"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.1.2 {"name":"dlmalloc","vers":"0.1.2","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"d56ad71b31043818d0ee10a7fb9664882f8e45849c81647585e6a3124f185517","features":{"allocator-api":[],"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.1.3 {"name":"dlmalloc","vers":"0.1.3","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"f283302e035e61c23f2b86b3093e8c6273a4c3125742d6087e96ade001ca5e63","features":{"allocator-api":[],"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.1.4 {"name":"dlmalloc","vers":"0.1.4","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"35055b1021724f4eb5262eb49130eebff23fc59fc5a14160e05faad8eeb36673","features":{"allocator-api":[],"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.2.0 {"name":"dlmalloc","vers":"0.2.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"a8e5891365fc09635604a13e4694fb18fdd8c79cfb386d2f7a2d3142dc1ca91a","features":{"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.2.1 {"name":"dlmalloc","vers":"0.2.1","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"332570860c2edf2d57914987bf9e24835425f75825086b6ba7d1e6a3e4f1f254","features":{"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.2.2 {"name":"dlmalloc","vers":"0.2.2","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"96d075454d918d7358fcd2290dcb4c3ae7bc735388dd1b31ccee1426c1106d8a","features":{"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.2.3 {"name":"dlmalloc","vers":"0.2.3","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"a6fe28e0bf9357092740362502f5cc7955d8dc125ebda71dec72336c2e15c62e","features":{"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.2.4 {"name":"dlmalloc","vers":"0.2.4","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"203540e710bfadb90e5e29930baf5d10270cec1f43ab34f46f78b147b2de715a","features":{"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.2.5 {"name":"dlmalloc","vers":"0.2.5","deps":[{"name":"arbitrary","req":"^1.3.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cfg-if","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.8","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"windows-sys","req":"^0.52.0","features":["Win32_Foundation","Win32_System_Memory","Win32_System_Threading","Win32_System_SystemInformation"],"optional":false,"default_features":true,"target":"cfg(target_os = \"windows\")","kind":"normal"}],"cksum":"960a02b0caee913a3df97cd43fcc7370d0695c1dfcd9aca2af3bb9e96c0acd80","features":{"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.2.6 {"name":"dlmalloc","vers":"0.2.6","deps":[{"name":"arbitrary","req":"^1.3.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cfg-if","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.8","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"windows-sys","req":"^0.52.0","features":["Win32_Foundation","Win32_System_Memory","Win32_System_Threading","Win32_System_SystemInformation"],"optional":false,"default_features":true,"target":"cfg(target_os = \"windows\")","kind":"normal"}],"cksum":"3264b043b8e977326c1ee9e723da2c1f8d09a99df52cacf00b4dbce5ac54414d","features":{"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.2.7 {"name":"dlmalloc","vers":"0.2.7","deps":[{"name":"arbitrary","req":"^1.3.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cfg-if","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.8","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"windows-sys","req":">=0.52.0, <=0.59","features":["Win32_Foundation","Win32_System_Memory","Win32_System_Threading","Win32_System_SystemInformation"],"optional":false,"default_features":true,"target":"cfg(target_os = \"windows\")","kind":"normal"}],"cksum":"d9b5e0d321d61de16390ed273b647ce51605b575916d3c25e6ddf27a1e140035","features":{"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.2.8 {"name":"dlmalloc","vers":"0.2.8","deps":[{"name":"arbitrary","req":"^1.3.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cfg-if","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.8","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"windows-sys","req":">=0.52.0, <=0.59","features":["Win32_Foundation","Win32_System_Memory","Win32_System_Threading","Win32_System_SystemInformation"],"optional":false,"default_features":true,"target":"cfg(target_os = \"windows\")","kind":"normal"}],"cksum":"8cff88b751e7a276c4ab0e222c3f355190adc6dde9ce39c851db39da34990df7","features":{"debug":[],"global":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.2.9 {"name":"dlmalloc","vers":"0.2.9","deps":[{"name":"arbitrary","req":"^1.3.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cfg-if","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.8","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"windows-sys","req":">=0.52.0, <=0.59","features":["Win32_Foundation","Win32_System_Memory","Win32_System_Threading","Win32_System_SystemInformation"],"optional":false,"default_features":true,"target":"cfg(target_os = \"windows\")","kind":"normal"}],"cksum":"d01597dde41c0b9da50d5f8c219023d63d8f27f39a27095070fd191fddc83891","features":{"debug":[],"global":[],"rustc-dep-of-std":["core"]},"yanked":false} 0.2.10 {"name":"dlmalloc","vers":"0.2.10","deps":[{"name":"arbitrary","req":"^1.3.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cfg-if","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.8","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"windows-sys","req":">=0.52.0, <=0.59","features":["Win32_Foundation","Win32_System_Memory","Win32_System_Threading","Win32_System_SystemInformation"],"optional":false,"default_features":true,"target":"cfg(target_os = \"windows\")","kind":"normal"}],"cksum":"fa3a2dbee57b69fbb5dbe852fa9c0925697fb0c7fbcb1593e90e5ffaedf13d51","features":{"debug":[],"global":[],"rustc-dep-of-std":["core"]},"yanked":false} 0.2.11 {"name":"dlmalloc","vers":"0.2.11","deps":[{"name":"arbitrary","req":"^1.3.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"cfg-if","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":false,"target":"cfg(all(unix, not(target_arch = \"wasm32\")))","kind":"normal"},{"name":"rand","req":"^0.8","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"windows-sys","req":">=0.52.0, <=0.60","features":["Win32_Foundation","Win32_System_Memory","Win32_System_Threading","Win32_System_SystemInformation"],"optional":false,"default_features":true,"target":"cfg(target_os = \"windows\")","kind":"normal"}],"cksum":"06cdfe340b16dd990c54cce79743613fa09fbb16774f33a77c9fd196f8f3fa30","features":{"debug":[],"global":[],"rustc-dep-of-std":["core"]},"yanked":false} 