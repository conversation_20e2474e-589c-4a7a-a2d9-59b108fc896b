   etag: W/"024c584deb504e552d3e7aec7ab0c16f" 0.1.0-rc1 {"name":"fortanix-sgx-abi","vers":"0.1.0-rc1","deps":[],"cksum":"f83f252f23a5088273a3f611c3cb1d377ab171b2b851f7ae2a6022a4e5caf49a","features":{"docs":[]},"yanked":false} 0.3.0 {"name":"fortanix-sgx-abi","vers":"0.3.0","deps":[],"cksum":"9a9c25ab4b6af906ae8bafcf4aa604cc8b5ddc0f5bd10648f1d41b9dfb72e717","features":{"docs":[]},"yanked":false} 0.3.1 {"name":"fortanix-sgx-abi","vers":"0.3.1","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"26105e20b4c3f7a319db1376b54ac9a46e5761e949405553375095d05a0cee4d","features":{"docs":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.3.2 {"name":"fortanix-sgx-abi","vers":"0.3.2","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"3f8cbee5e872cf7db61a999a041f9bc4706ca7bf7df4cb914f53fabb1c1bc550","features":{"docs":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.3.3 {"name":"fortanix-sgx-abi","vers":"0.3.3","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"c56c422ef86062869b2d57ae87270608dc5929969dd130a6e248979cf4fb6ca6","features":{"docs":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.4.0 {"name":"fortanix-sgx-abi","vers":"0.4.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"5a4dc78078fbba2327b22e5a8141ca5efa172f3a0f31678e94875323de3e7523","features":{"docs":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.4.1 {"name":"fortanix-sgx-abi","vers":"0.4.1","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"816a38bd53bd5c87dd7edf4f15a2ee6b989ad7a5b5e616b75d70de64ad2a1329","features":{"docs":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.5.0 {"name":"fortanix-sgx-abi","vers":"0.5.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"57cafc2274c10fab234f176b25903ce17e690fca7597090d50880e047a0389c5","features":{"docs":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.6.0 {"name":"fortanix-sgx-abi","vers":"0.6.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"a23384eec6bf7f021d76ebac28f716647c1ae92138219c4fe93b44f9b82eb393","features":{"docs":[],"rustc-dep-of-std":["core","compiler_builtins/rustc-dep-of-std"]},"yanked":false} 0.6.1 {"name":"fortanix-sgx-abi","vers":"0.6.1","deps":[{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"5efc85edd5b83e8394f4371dd0da6859dff63dd387dab8568fece6af4cde6f84","features":{"docs":[],"rustc-dep-of-std":["core"]},"yanked":false} 