   etag: W/"959bc4f742ab0a1e2ad2525da5365fa9" 0.1.0 {"name":"gimli","vers":"0.1.0","deps":[{"name":"clippy","req":"^0.0.76","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"leb128","req":"^0.1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"nom","req":"^1.2.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"0972ba60617140a9fda7ef946c1099d9ced6bc78601e0f19a60afc7713de223a","features":{"nightly":["clippy"]},"yanked":false} 0.2.0 {"name":"gimli","vers":"0.2.0","deps":[{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"leb128","req":"^0.1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"56523a084722feb72ad81b47ab4f8f2d81f57908b18f99e6b67adab37f062029","features":{"nightly":[]},"yanked":false} 0.3.0 {"name":"gimli","vers":"0.3.0","deps":[{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"elf","req":"^0.0.9","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"linux\")","kind":"dev"},{"name":"leb128","req":"^0.2.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"mach_o","req":"^0.1.1","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"macos\")","kind":"dev"}],"cksum":"f3782612a426cca86fb1f79fcbdb1fa0826caf94fc5d354b48e9beb5708de656","features":{"nightly":[]},"yanked":false} 0.4.0 {"name":"gimli","vers":"0.4.0","deps":[{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"elf","req":"^0.0.9","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"linux\")","kind":"dev"},{"name":"leb128","req":"^0.2.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"mach_o","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"macos\")","kind":"dev"}],"cksum":"5e4f0e32e7d5c157e86df238ea4da6309f489b32a0270a0e87c8e2e2a15a3adf","features":{"nightly":[]},"yanked":false} 0.5.0 {"name":"gimli","vers":"0.5.0","deps":[{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"elf","req":"^0.0.9","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"linux\")","kind":"dev"},{"name":"leb128","req":"^0.2.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"mach_o","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"macos\")","kind":"dev"}],"cksum":"71bd0196a22adb392609b29067326a9e69b84f9b90a90043196a2fcc37300e0f","features":{"nightly":[]},"yanked":false} 0.6.0 {"name":"gimli","vers":"0.6.0","deps":[{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"elf","req":"^0.0.9","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"linux\")","kind":"dev"},{"name":"leb128","req":"^0.2.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"mach_o","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"macos\")","kind":"dev"}],"cksum":"c05ec658feedc951f4d2a42cfc4dad6155cffe2ee14897f5c776678250053ff5","features":{"nightly":[]},"yanked":false} 0.7.0 {"name":"gimli","vers":"0.7.0","deps":[{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"leb128","req":"^0.2.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.4.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"723822d2f571e383d9f4f4793e1b5d4ee74cd7de7eea68f9ca7bd6bb0f8673e3","features":{"nightly":[]},"yanked":false} 0.8.0 {"name":"gimli","vers":"0.8.0","deps":[{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"leb128","req":"^0.2.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.4.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"fa2f875d7b865779850c4c01775b6de656d31671cbbf79f474a3afb36ed4b916","features":{"nightly":[]},"yanked":false} 0.9.0 {"name":"gimli","vers":"0.9.0","deps":[{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"leb128","req":"^0.2.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.4.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"95201337ddc830213a857c0ba40f6322805f2765a05ef4a4dc0d808a21583dc5","features":{"nightly":[]},"yanked":false} 0.10.0 {"name":"gimli","vers":"0.10.0","deps":[{"name":"arrayvec","req":"^0.3.20","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"leb128","req":"^0.2.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.4.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"8598a708af7a9762e5dce577a84ef422c01159bc9951824b4c076c77b4bfb24b","features":{"nightly":[]},"yanked":false} 0.11.0 {"name":"gimli","vers":"0.11.0","deps":[{"name":"arrayvec","req":"^0.3.20","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"leb128","req":"^0.2.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.4.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.3.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"e81c5bcd255bec7240830a34b1e07496772c60d1e9454759bdb015d689ed21a2","features":{"nightly":[]},"yanked":false} 0.12.0 {"name":"gimli","vers":"0.12.0","deps":[{"name":"arrayvec","req":"^0.3.20","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"leb128","req":"^0.2.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.4.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.3.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"9bc5159c98d40940602837d90767eee5478025ca075a1c15dddb81f25bc8d09f","features":{"nightly":[]},"yanked":false} 0.13.0 {"name":"gimli","vers":"0.13.0","deps":[{"name":"arrayvec","req":"^0.3.20","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"byteorder","req":"^0.5.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"leb128","req":"^0.2.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.4.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.3.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"98d0e29f01e945ea2aee75060d4e3012683ddfdccc690784927121b687f15db8","features":{"nightly":[]},"yanked":false} 0.14.0 {"name":"gimli","vers":"0.14.0","deps":[{"name":"arrayvec","req":"^0.3.20","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"byteorder","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap","req":"^0.5.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.4.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"3ab35543ca43579966ba65c75396a7f4ddf8e99910e3ab7827c7e53a44885042","features":{"nightly":[]},"yanked":false} 0.15.0 {"name":"gimli","vers":"0.15.0","deps":[{"name":"arrayvec","req":"^0.4.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"byteorder","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"fallible-iterator","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.5.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"ffa1f5db1b7b50875bd8fc111f64025b05c01c7a6f8f36de0da9d53d02ba5fac","features":{"nightly":[]},"yanked":false} 0.16.0 {"name":"gimli","vers":"0.16.0","deps":[{"name":"arrayvec","req":"^0.4.6","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"crossbeam","req":"^0.3.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.1.4","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^0.2.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.0.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"3d080d7b981be6932486671c6cb0d8a82ed61d88b81419d39b33ea8966717f4d","features":{"alloc":["fallible-iterator/alloc"],"default":["std"],"std":["fallible-iterator/std"]},"yanked":false} 0.16.1 {"name":"gimli","vers":"0.16.1","deps":[{"name":"arrayvec","req":"^0.4.6","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"crossbeam","req":"^0.4.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.1.4","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"memmap","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"9a7f6ee5390883802431e4abe323390f52f10ff16e8f8d2d6ce598251f900ede","features":{"alloc":["fallible-iterator/alloc","stable_deref_trait/alloc"],"default":["std"],"std":["fallible-iterator/std","stable_deref_trait/std"]},"yanked":false} 0.17.0 {"name":"gimli","vers":"0.17.0","deps":[{"name":"arrayvec","req":"^0.4.6","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"crossbeam","req":"^0.7.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.1.4","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.11","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"eb3243218ca3773e9aa00d27602f35bd1daca3be1b7112ea5fc23b2899f1a4f3","features":{"alloc":["fallible-iterator/alloc","stable_deref_trait/alloc"],"default":["read","write","std"],"read":[],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["std","indexmap"]},"yanked":false} 0.18.0 {"name":"gimli","vers":"0.18.0","deps":[{"name":"arrayvec","req":"^0.4.6","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"crossbeam","req":"^0.7.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.11","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"93787807811884ae7fd9cb4d8e949037c8d0fe1f569eaa553e24633ac5fb4155","features":{"alloc":["fallible-iterator/alloc","stable_deref_trait/alloc"],"default":["read","write","std"],"read":[],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["std","indexmap"]},"yanked":false} 0.19.0 {"name":"gimli","vers":"0.19.0","deps":[{"name":"arrayvec","req":"^0.4.6","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"crossbeam","req":"^0.7.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.12","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"162d18ae5f2e3b90a993d202f1ba17a5633c2484426f8bcae201f86194bacd00","features":{"alloc":["fallible-iterator/alloc","stable_deref_trait/alloc"],"default":["read","write","std"],"read":[],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["std","indexmap"]},"yanked":false} 0.20.0 {"name":"gimli","vers":"0.20.0","deps":[{"name":"arrayvec","req":"^0.5.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"crossbeam","req":"^0.7.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.17","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"smallvec","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"81dd6190aad0f05ddbbf3245c54ed14ca4aa6dd32f22312b70d8f168c3e3e633","features":{"default":["read","write","std"],"read":["arrayvec","fallible-iterator","smallvec","stable_deref_trait"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.21.0 {"name":"gimli","vers":"0.21.0","deps":[{"name":"crossbeam","req":"^0.7.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.19","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"bcc8e0c9bce37868955864dbecd2b1ab2bdf967e6f28066d65aaac620444b65c","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["stable_deref_trait"],"read":[],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.22.0 {"name":"gimli","vers":"0.22.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crossbeam","req":"^0.7.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.20","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"aaf91faf136cb47367fa430cd46e37a788775e7fa104f8b4bcb3861dc389b724","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["stable_deref_trait"],"read":[],"rustc-dep-of-std":["core","alloc","compiler_builtins"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.23.0 {"name":"gimli","vers":"0.23.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crossbeam","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.22","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"f6503fe142514ca4799d4c26297c4248239fe8838d827db6bd6065c6ed29a6ce","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["stable_deref_trait"],"read":[],"rustc-dep-of-std":["core","alloc","compiler_builtins"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.24.0 {"name":"gimli","vers":"0.24.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crossbeam","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.24","features":["wasm"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"0e4075386626662786ddb0ec9081e7c7eeb1ba31951f447ca780ef9f5d568189","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["stable_deref_trait"],"read":[],"rustc-dep-of-std":["core","alloc","compiler_builtins"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.25.0 {"name":"gimli","vers":"0.25.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crossbeam","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.26","features":["wasm"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"f0a01e0497841a3b2db4f8afa483cce65f7e96a3498bd6c541734792aeac8fe7","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["stable_deref_trait"],"read":[],"rustc-dep-of-std":["core","alloc","compiler_builtins"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.26.0 {"name":"gimli","vers":"0.26.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crossbeam","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.27.1","features":["wasm"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"81a03ce013ffccead76c11a15751231f777d9295b845cc1266ed4d34fcbd7977","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["read","stable_deref_trait"],"read":["read-core"],"read-core":[],"rustc-dep-of-std":["core","alloc","compiler_builtins"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.26.1 {"name":"gimli","vers":"0.26.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crossbeam","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.27.1","features":["wasm"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"78cc372d058dcf6d5ecd98510e7fbc9e5aec4d21de70f65fea8fecebcd881bd4","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["read","stable_deref_trait"],"read":["read-core"],"read-core":[],"rustc-dep-of-std":["core","alloc","compiler_builtins"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.26.2 {"name":"gimli","vers":"0.26.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crossbeam","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.5.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.29.0","features":["wasm"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"22030e2c5a68ec659fde1e949a745124b48e6fa8b045b7ed5bd1fe4ccc5c4e5d","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["read","stable_deref_trait"],"read":["read-core"],"read-core":[],"rustc-dep-of-std":["core","alloc","compiler_builtins"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.27.0 {"name":"gimli","vers":"0.27.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crossbeam","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.5.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.30.0","features":["wasm"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"dec7af912d60cdbd3677c1af9352ebae6fb8394d165568a2234df0fa00f87793","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["read","stable_deref_trait"],"read":["read-core"],"read-core":[],"rustc-dep-of-std":["core","alloc","compiler_builtins"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.27.1 {"name":"gimli","vers":"0.27.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crossbeam","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.5.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.30.0","features":["wasm"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"221996f774192f0f718773def8201c4ae31f02616a54ccfc2d358bb0e5cefdec","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["read","stable_deref_trait"],"read":["read-core"],"read-core":[],"rustc-dep-of-std":["core","alloc","compiler_builtins"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.27.2 {"name":"gimli","vers":"0.27.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crossbeam","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.5.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.30.0","features":["wasm"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"ad0a93d233ebf96623465aad4046a8d3aa4da22d4f4beba5388838c8a434bbb4","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["read","stable_deref_trait"],"read":["read-core"],"read-core":[],"rustc-dep-of-std":["core","alloc","compiler_builtins"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.27.3 {"name":"gimli","vers":"0.27.3","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crossbeam","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fallible-iterator","req":"^0.2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"getopts","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"indexmap","req":"^1.0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.6.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"num_cpus","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"object","req":"^0.30.0","features":["wasm"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"regex","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"typed-arena","req":"^2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"b6c80984affa11d98d1b88b66ac8853f143217b399d3c74116778ff8fdb4ed2e","features":{"default":["read","write","std","fallible-iterator","endian-reader"],"endian-reader":["read","stable_deref_trait"],"read":["read-core"],"read-core":[],"rustc-dep-of-std":["core","alloc","compiler_builtins"],"std":["fallible-iterator/std","stable_deref_trait/std"],"write":["indexmap"]},"yanked":false} 0.28.0 {"name":"gimli","vers":"0.28.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"6fb8d784f27acf97159b40fc4db5ecd8aa23b9ad5ef69cdd136d3bc80665f0c0","features":{"read":["read-core"],"read-core":[]},"features2":{"default":["read-all","write"],"endian-reader":["read","dep:stable_deref_trait"],"fallible-iterator":["dep:fallible-iterator"],"read-all":["read","std","fallible-iterator","endian-reader"],"rustc-dep-of-std":["dep:core","dep:alloc","dep:compiler_builtins"],"std":["fallible-iterator?/std","stable_deref_trait?/std"],"write":["dep:indexmap"]},"yanked":false,"rust_version":"1.60","v":2} 0.28.1 {"name":"gimli","vers":"0.28.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"4271d37baee1b8c7e4b708028c57d816cf9d2434acb33a549475f78c181f6253","features":{"read":["read-core"],"read-core":[]},"features2":{"default":["read-all","write"],"endian-reader":["read","dep:stable_deref_trait"],"fallible-iterator":["dep:fallible-iterator"],"read-all":["read","std","fallible-iterator","endian-reader"],"rustc-dep-of-std":["dep:core","dep:alloc","dep:compiler_builtins"],"std":["fallible-iterator?/std","stable_deref_trait?/std"],"write":["dep:indexmap"]},"yanked":false,"rust_version":"1.60","v":2} 0.29.0 {"name":"gimli","vers":"0.29.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"40ecd4077b5ae9fd2e9e169b102c6c330d0605168eb0e8bf79952b256dbefffd","features":{"read":["read-core"],"read-core":[]},"features2":{"default":["read-all","write"],"endian-reader":["read","dep:stable_deref_trait"],"fallible-iterator":["dep:fallible-iterator"],"read-all":["read","std","fallible-iterator","endian-reader"],"rustc-dep-of-std":["dep:core","dep:alloc","dep:compiler_builtins"],"std":["fallible-iterator?/std","stable_deref_trait?/std"],"write":["dep:indexmap"]},"yanked":false,"rust_version":"1.60","v":2} 0.30.0 {"name":"gimli","vers":"0.30.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"e2e1d97fbe9722ba9bbd0c97051c2956e726562b61f86a25a4360398a40edfc9","features":{"read":["read-core"],"read-core":[]},"features2":{"default":["read-all","write"],"endian-reader":["read","dep:stable_deref_trait"],"fallible-iterator":["dep:fallible-iterator"],"read-all":["read","std","fallible-iterator","endian-reader"],"rustc-dep-of-std":["dep:core","dep:alloc","dep:compiler_builtins"],"std":["fallible-iterator?/std","stable_deref_trait?/std"],"write":["dep:indexmap"]},"yanked":false,"rust_version":"1.60","v":2} 0.31.0 {"name":"gimli","vers":"0.31.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"32085ea23f3234fc7846555e85283ba4de91e21016dc0455a16286d87a292d64","features":{"read":["read-core"],"read-core":[]},"features2":{"default":["read-all","write"],"endian-reader":["read","dep:stable_deref_trait"],"fallible-iterator":["dep:fallible-iterator"],"read-all":["read","std","fallible-iterator","endian-reader"],"rustc-dep-of-std":["dep:core","dep:alloc","dep:compiler_builtins"],"std":["fallible-iterator?/std","stable_deref_trait?/std"],"write":["dep:indexmap"]},"yanked":false,"rust_version":"1.60","v":2} 0.31.1 {"name":"gimli","vers":"0.31.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"07e28edb80900c19c28f1072f2e8aeca7fa06b23cd4169cefe1af5aa3260783f","features":{"read":["read-core"],"read-core":[]},"features2":{"default":["read-all","write"],"endian-reader":["read","dep:stable_deref_trait"],"fallible-iterator":["dep:fallible-iterator"],"read-all":["read","std","fallible-iterator","endian-reader"],"rustc-dep-of-std":["dep:core","dep:alloc","dep:compiler_builtins"],"std":["fallible-iterator?/std","stable_deref_trait?/std"],"write":["dep:indexmap"]},"yanked":false,"rust_version":"1.60","v":2} 0.32.0 {"name":"gimli","vers":"0.32.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"93563d740bc9ef04104f9ed6f86f1e3275c2cdafb95664e26584b9ca807a8ffe","features":{"read":["read-core"],"read-core":[]},"features2":{"default":["read-all","write"],"endian-reader":["read","dep:stable_deref_trait"],"fallible-iterator":["dep:fallible-iterator"],"read-all":["read","std","fallible-iterator","endian-reader"],"rustc-dep-of-std":["dep:core","dep:alloc"],"std":["fallible-iterator?/std","stable_deref_trait?/std"],"write":["dep:indexmap"]},"yanked":false,"rust_version":"1.60","v":2} 0.32.1 {"name":"gimli","vers":"0.32.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"fallible-iterator","req":"^0.3.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"stable_deref_trait","req":"^1.1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"test-assembler","req":"^0.1.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"cd54e8795d97c9f5561c63529d98fcc0c2eed9e982deecc396797f2c04cff298","features":{"read":["read-core"],"read-core":[]},"features2":{"default":["read-all","write"],"endian-reader":["read","dep:stable_deref_trait"],"fallible-iterator":["dep:fallible-iterator"],"read-all":["read","std","fallible-iterator","endian-reader"],"rustc-dep-of-std":["dep:core","dep:alloc"],"std":["fallible-iterator?/std","stable_deref_trait?/std"],"write":["dep:indexmap"]},"yanked":false,"rust_version":"1.60","v":2} 