   etag: W/"656b9cb08041dd3c4554ccf71b0b8ac0" 0.1.0 {"name":"hashbrown","vers":"0.1.0","deps":[{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"scopeguard","req":"^0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"38799f5c6a3fbd412bdc8d8647108a3e5b8031823210db000968556be358bf64","features":{"nightly":[]},"yanked":false} 0.1.1 {"name":"hashbrown","vers":"0.1.1","deps":[{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"scopeguard","req":"^0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"2dfb69c301cead891d010536c7d1b3affe792f5a2be5d6fbd0fcaf7fa0976962","features":{"nightly":[]},"yanked":false} 0.1.2 {"name":"hashbrown","vers":"0.1.2","deps":[{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"scopeguard","req":"^0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"74c957b0092b8afcac86fd64b83c983b5d2251fd7ad3bac6584e3f76b93825c8","features":{"nightly":[]},"yanked":false} 0.1.3 {"name":"hashbrown","vers":"0.1.3","deps":[{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"scopeguard","req":"^0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"serde","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"f7d5e18b0d6a43feab3d89a388fa96473820a3f44030dd4035cdd19ac5f85a8b","features":{"nightly":[]},"yanked":false} 0.1.4 {"name":"hashbrown","vers":"0.1.4","deps":[{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"scopeguard","req":"^0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"serde","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"6aedf060d0e2d1770598d500d5662dac8e48914f65a1a160557f52c08039e164","features":{"nightly":[]},"yanked":false} 0.1.5 {"name":"hashbrown","vers":"0.1.5","deps":[{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"scopeguard","req":"^0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"serde","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"18cb46d7cc72edf1580c1e75c1988715c52d5efdc9dad51539aa88f381f634af","features":{"nightly":[]},"yanked":false} 0.1.6 {"name":"hashbrown","vers":"0.1.6","deps":[{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"scopeguard","req":"^0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"serde","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"72d9de4ebc5b184289fc3db3a3c023b072e56c649e32fcfcd434636e7ba912d4","features":{"nightly":[]},"yanked":false} 0.1.7 {"name":"hashbrown","vers":"0.1.7","deps":[{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"scopeguard","req":"^0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"serde","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"64b7d419d0622ae02fe5da6b9a5e1964b610a65bb37923b976aeebb6dbb8f86e","features":{"nightly":[]},"yanked":false} 0.1.8 {"name":"hashbrown","vers":"0.1.8","deps":[{"name":"byteorder","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"lazy_static","req":"~1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"scopeguard","req":"^0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"serde","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"3bae29b6653b3412c2e71e9d486db9f9df5d701941d86683005efb9f2d28e3da","features":{"nightly":[]},"yanked":false} 0.2.0 {"name":"hashbrown","vers":"0.2.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"lazy_static","req":"~1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"58623735fa622916205f9e0a52a031b25b0e251ddaef47f7cb288444c4410beb","features":{"default":[],"nightly":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc"]},"yanked":false} 0.2.1 {"name":"hashbrown","vers":"0.2.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"lazy_static","req":"~1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"d22f2163f3350b00b15b96da81d4ec3a8616983c010e0b69f6e4d060a2db9cd4","features":{"default":[],"nightly":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc"]},"yanked":false} 0.2.2 {"name":"hashbrown","vers":"0.2.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"lazy_static","req":"~1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"61e4900fa4e80b3d15c78a08ec8a08433246063fa7577e7b2c6426b3b21b1f79","features":{"default":[],"nightly":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc"]},"yanked":false} 0.3.0 {"name":"hashbrown","vers":"0.3.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"lazy_static","req":"~1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"570178d5e4952010d138b0f1d581271ff3a02406d990f887d1e87e3d6e43b0ac","features":{"default":[],"nightly":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc"]},"yanked":false} 0.3.1 {"name":"hashbrown","vers":"0.3.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"lazy_static","req":"~1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"29fba9abe4742d586dfd0c06ae4f7e73a1c2d86b856933509b269d82cdf06e18","features":{"default":[],"nightly":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc"]},"yanked":false} 0.4.0 {"name":"hashbrown","vers":"0.4.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"lazy_static","req":"~1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"9529213c67695ca2d146e6f263b7b72df8fa973368beadf767e8ed80c03f2f36","features":{"default":[],"nightly":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc"]},"yanked":false} 0.5.0 {"name":"hashbrown","vers":"0.5.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"lazy_static","req":"~1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"e1de41fb8dba9714efd92241565cdff73f78508c95697dd56787d3cba27e2353","features":{"default":[],"nightly":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc"]},"yanked":false} 0.5.1 {"name":"hashbrown","vers":"0.5.1","deps":[{"name":"ahash","req":"^0.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^0.1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"b072077810586b86f17e055b13721b7585266575d552146a0e9b74b7c6da0a5e","features":{"default":["ahash"],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":true} 0.6.0 {"name":"hashbrown","vers":"0.6.0","deps":[{"name":"ahash","req":"^0.2.11","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^0.1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"2bcea5b597dd98e6d1f1ec171744cc5dee1a30d1c23c5b98e3cf9d4fbdf8a526","features":{"default":["ahash"],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.6.1 {"name":"hashbrown","vers":"0.6.1","deps":[{"name":"ahash","req":"^0.2.11","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^0.1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"6587d09be37fb98a11cb08b9000a3f592451c1b1b613ca69d949160e313a430a","features":{"default":["ahash"],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.6.2 {"name":"hashbrown","vers":"0.6.2","deps":[{"name":"ahash","req":"^0.2.11","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^0.1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"3cd9867f119b19fecb08cd5c326ad4488d7a1da4bf75b4d95d71db742525aaab","features":{"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.6.3 {"name":"hashbrown","vers":"0.6.3","deps":[{"name":"ahash","req":"^0.2.11","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^0.1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"8e6073d0ca812575946eb5f35ff68dbe519907b25c42530389ff946dc84c6ead","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","ahash-compile-time-rng","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.7.0 {"name":"hashbrown","vers":"0.7.0","deps":[{"name":"ahash","req":"^0.3.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"728e7d31e63d53c436094370f1e6fa249f60a4bb318cc5dfbbbe0aa2bc5a29d7","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","ahash-compile-time-rng","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.7.1 {"name":"hashbrown","vers":"0.7.1","deps":[{"name":"ahash","req":"^0.3.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"= 1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"479e9d9a1a3f8c489868a935b557ab5710e3e223836da2ecd52901d88935cb56","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","ahash-compile-time-rng","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.7.2 {"name":"hashbrown","vers":"0.7.2","deps":[{"name":"ahash","req":"^0.3.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"= 1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"96282e96bfcd3da0d3aa9938bedf1e50df3269b6db08b4876d2da0bb1a0841cf","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.8.0 {"name":"hashbrown","vers":"0.8.0","deps":[{"name":"ahash","req":"^0.3.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"=1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"ab9b7860757ce258c89fd48d28b68c41713e597a7b09e793f6c6a6e2ea37c827","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.8.1 {"name":"hashbrown","vers":"0.8.1","deps":[{"name":"ahash","req":"^0.3.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"=1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"34f595585f103464d8d2f6e9864682d74c1601fed5e07d62b1c9058dba8246fb","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.8.2 {"name":"hashbrown","vers":"0.8.2","deps":[{"name":"ahash","req":"^0.3.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^1","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"=1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"e91b62f79061a0bc2e046024cb7ba44b08419ed238ecbd9adbd787434b9e8c25","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.9.0 {"name":"hashbrown","vers":"0.9.0","deps":[{"name":"ahash","req":"^0.4.4","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"=1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"00d63df3d41950fb462ed38308eea019113ad1508da725bbedcd0fa5a85ef5f7","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.9.1 {"name":"hashbrown","vers":"0.9.1","deps":[{"name":"ahash","req":"^0.4.4","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rustc-hash","req":"=1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"d7afe4a420e3fe79967a00898cc1f4db7c8a49a9333a29f8a4bd76a253d5cd04","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.10.0 {"name":"hashbrown","vers":"0.10.0","deps":[{"name":"ahash","req":"^0.6.1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"2140e9c963869f01789fa4fef4805211081ec794af5fc77c0d5b377906118853","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":true} 0.11.0 {"name":"hashbrown","vers":"0.11.0","deps":[{"name":"ahash","req":"^0.7.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"bumpalo","req":"^3.5.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"362385356d610bd1e5a408ddf8d022041774b683f345a1d2cfcb4f60f8ae2db5","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.11.1 {"name":"hashbrown","vers":"0.11.1","deps":[{"name":"ahash","req":"^0.7.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"bumpalo","req":"^3.5.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"a3de7a9a685bb301f5cb29587f13833270c59e7d2c6f457a66372996afad4504","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.11.2 {"name":"hashbrown","vers":"0.11.2","deps":[{"name":"ahash","req":"^0.7.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"bumpalo","req":"^3.5.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.7.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"ab5ef0d4909ef3724cc8cce6ccc8572c5c817592e9285f5464f8e86f8bd3726e","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.12.0 {"name":"hashbrown","vers":"0.12.0","deps":[{"name":"ahash","req":"^0.7.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"bumpalo","req":"^3.5.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"8c21d40587b92fa6a6c6e3c1bdbf87d75511db5672f9c93175574b3a00df1758","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false} 0.12.1 {"name":"hashbrown","vers":"0.12.1","deps":[{"name":"ahash","req":"^0.7.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"bumpalo","req":"^3.5.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"db0d4cf898abf0081f964436dc980e96670a0f36863e4b83aaacdb65c9d7ccc3","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false,"rust_version":"1.56.1"} 0.12.2 {"name":"hashbrown","vers":"0.12.2","deps":[{"name":"ahash","req":"^0.7.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"bumpalo","req":"^3.5.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"607c8a29735385251a339424dd462993c0fed8fa09d378f259377df08c126022","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false,"rust_version":"1.56.0"} 0.12.3 {"name":"hashbrown","vers":"0.12.3","deps":[{"name":"ahash","req":"^0.7.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"bumpalo","req":"^3.5.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"8a9ee70c43aaf417c914396645a0fa852624801b24ebb7ae78fe8272889ac888","features":{"ahash-compile-time-rng":["ahash/compile-time-rng"],"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false,"rust_version":"1.56.0"} 0.13.0 {"name":"hashbrown","vers":"0.13.0","deps":[{"name":"ahash","req":"^0.8.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"bumpalo","req":"^3.5.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"f3d4daf4a9ff5efccf5b7a97176ea20560bcbdba8ae53d46d0dc302900b851dc","features":{"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":true,"rust_version":"1.61.0"} 0.13.1 {"name":"hashbrown","vers":"0.13.1","deps":[{"name":"ahash","req":"^0.8.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"bumpalo","req":"^3.5.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"33ff8ae62cd3a9102e5637afc8452c55acf3844001bd5374e0b0bd7b6616c038","features":{"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false,"rust_version":"1.61.0"} 0.13.2 {"name":"hashbrown","vers":"0.13.2","deps":[{"name":"ahash","req":"^0.8.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"bumpalo","req":"^3.5.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"43a3c133739dddd0d2990f9a4bdf8eb4b21ef50e4851ca85ab661199821d510e","features":{"default":["ahash","inline-more"],"inline-more":[],"nightly":[],"raw":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"yanked":false,"rust_version":"1.61.0"} 0.14.0 {"name":"hashbrown","vers":"0.14.0","deps":[{"name":"ahash","req":"^0.8.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rkyv","req":"^0.7.42","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rkyv","req":"^0.7.42","features":["validation"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"2c6201b9ff9fd90a5a3bac2e56a830d0caa509576f0e503818ee82c181b3437a","features":{"default":["ahash","inline-more","allocator-api2"],"inline-more":[],"raw":[],"rustc-internal-api":[]},"features2":{"nightly":["allocator-api2?/nightly","bumpalo/allocator_api"],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"]},"yanked":false,"rust_version":"1.64.0","v":2} 0.14.1 {"name":"hashbrown","vers":"0.14.1","deps":[{"name":"ahash","req":"^0.8.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"equivalent","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rkyv","req":"^0.7.42","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rkyv","req":"^0.7.42","features":["validation"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"7dfda62a12f55daeae5015f81b0baea145391cb4520f86c248fc615d72640d12","features":{"default":["ahash","inline-more","allocator-api2"],"inline-more":[],"raw":[],"rustc-internal-api":[]},"features2":{"nightly":["allocator-api2?/nightly","bumpalo/allocator_api"],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"]},"yanked":false,"rust_version":"1.63.0","v":2} 0.14.2 {"name":"hashbrown","vers":"0.14.2","deps":[{"name":"ahash","req":"^0.8.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"equivalent","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rkyv","req":"^0.7.42","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rkyv","req":"^0.7.42","features":["validation"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"f93e7192158dbcda357bdec5fb5788eebf8bbac027f3f33e719d29135ae84156","features":{"default":["ahash","inline-more","allocator-api2"],"inline-more":[],"raw":[],"rustc-internal-api":[]},"features2":{"nightly":["allocator-api2?/nightly","bumpalo/allocator_api"],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"]},"yanked":false,"rust_version":"1.63.0","v":2} 0.14.3 {"name":"hashbrown","vers":"0.14.3","deps":[{"name":"ahash","req":"^0.8.6","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"equivalent","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rkyv","req":"^0.7.42","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rkyv","req":"^0.7.42","features":["validation"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"290f1a1d9242c78d09ce40a5e87e7554ee637af1351968159f4952f028f75604","features":{"default":["ahash","inline-more","allocator-api2"],"inline-more":[],"raw":[],"rustc-internal-api":[]},"features2":{"nightly":["allocator-api2?/nightly","bumpalo/allocator_api"],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"]},"yanked":false,"rust_version":"1.63.0","v":2} 0.14.4 {"name":"hashbrown","vers":"0.14.4","deps":[{"name":"ahash","req":"^0.8.7","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"equivalent","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rkyv","req":"^0.7.42","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rkyv","req":"^0.7.42","features":["validation"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"e57fa0ae458eb99874f54c09f4f9174f8b45fb87e854536a4e608696247f0c23","features":{"default":["ahash","inline-more","allocator-api2"],"inline-more":[],"raw":[],"rustc-internal-api":[]},"features2":{"nightly":["allocator-api2?/nightly","bumpalo/allocator_api"],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"]},"yanked":true,"rust_version":"1.63.0","v":2} 0.14.5 {"name":"hashbrown","vers":"0.14.5","deps":[{"name":"ahash","req":"^0.8.7","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"equivalent","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rkyv","req":"^0.7.42","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"rkyv","req":"^0.7.42","features":["validation"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"e5274423e17b7c9fc20b6e7e208532f9b19825d82dfd615708b70edd83df41f1","features":{"default":["ahash","inline-more","allocator-api2"],"inline-more":[],"raw":[],"rustc-internal-api":[]},"features2":{"nightly":["allocator-api2?/nightly","bumpalo/allocator_api"],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"]},"yanked":false,"rust_version":"1.63.0","v":2} 0.15.0 {"name":"hashbrown","vers":"0.15.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"borsh","req":"^1.5.0","features":["derive"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"equivalent","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"foldhash","req":"^0.1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"1e087f84d4f86bf4b218b927129862374b72199ae7d8657835f1e89000eea4fb","features":{"inline-more":[],"raw-entry":[],"rustc-internal-api":[]},"features2":{"default":["default-hasher","inline-more","allocator-api2","equivalent","raw-entry"],"default-hasher":["dep:foldhash"],"nightly":["allocator-api2?/nightly","bumpalo/allocator_api"],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api","raw-entry"]},"yanked":false,"rust_version":"1.63.0","v":2} 0.15.1 {"name":"hashbrown","vers":"0.15.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"equivalent","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"foldhash","req":"^0.1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"3a9bfc1af68b1726ea47d3d5109de126281def866b33970e10fbab11b5dafab3","features":{"inline-more":[],"raw-entry":[],"rustc-internal-api":[]},"features2":{"default":["default-hasher","inline-more","allocator-api2","equivalent","raw-entry"],"default-hasher":["dep:foldhash"],"nightly":["allocator-api2?/nightly","bumpalo/allocator_api"],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api","raw-entry"]},"yanked":false,"rust_version":"1.65.0","v":2} 0.15.2 {"name":"hashbrown","vers":"0.15.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"equivalent","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"foldhash","req":"^0.1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.8.3","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"bf151400ff0baff5465007dd2f3e717f3fe502074ca563069ce3a6629d07b289","features":{"inline-more":[],"raw-entry":[],"rustc-internal-api":[]},"features2":{"default":["default-hasher","inline-more","allocator-api2","equivalent","raw-entry"],"default-hasher":["dep:foldhash"],"nightly":["allocator-api2?/nightly","bumpalo/allocator_api"],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api","raw-entry"]},"yanked":false,"rust_version":"1.65.0","v":2} 0.15.3 {"name":"hashbrown","vers":"0.15.3","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"equivalent","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"foldhash","req":"^0.1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.9.0","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"84b26c544d002229e640969970a2e74021aadf6e2f96372b9c58eff97de08eb3","features":{"inline-more":[],"nightly":["bumpalo/allocator_api"],"raw-entry":[],"rustc-dep-of-std":["nightly","core","compiler_builtins","alloc","rustc-internal-api"],"rustc-internal-api":[]},"features2":{"default":["default-hasher","inline-more","allocator-api2","equivalent","raw-entry"],"default-hasher":["dep:foldhash"]},"yanked":false,"rust_version":"1.65.0","v":2} 0.15.4 {"name":"hashbrown","vers":"0.15.4","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"equivalent","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"foldhash","req":"^0.1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.9.0","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"5971ac85611da7067dbfcabef3c70ebb5606018acd9e2a3903a0da507521e0d5","features":{"inline-more":[],"nightly":["bumpalo/allocator_api"],"raw-entry":[],"rustc-dep-of-std":["nightly","core","alloc","rustc-internal-api"],"rustc-internal-api":[]},"features2":{"default":["default-hasher","inline-more","allocator-api2","equivalent","raw-entry"],"default-hasher":["dep:foldhash"]},"yanked":false,"rust_version":"1.65.0","v":2} 0.15.5 {"name":"hashbrown","vers":"0.15.5","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"allocator-api2","req":"^0.2.9","features":["alloc"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"bumpalo","req":"^3.13.0","features":["allocator-api2"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"doc-comment","req":"^0.3.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"equivalent","req":"^1.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"fnv","req":"^1.0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"foldhash","req":"^0.1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"lazy_static","req":"^1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rand","req":"^0.9.0","features":["small_rng"],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"rayon","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"rayon","req":"^1.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"serde","req":"^1.0.25","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"serde_test","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"9229cfe53dfd69f0609a49f65461bd93001ea1ef889cd5529dd176593f5338a1","features":{"inline-more":[],"nightly":["bumpalo/allocator_api"],"raw-entry":[],"rustc-dep-of-std":["nightly","core","alloc","rustc-internal-api"],"rustc-internal-api":[]},"features2":{"default":["default-hasher","inline-more","allocator-api2","equivalent","raw-entry"],"default-hasher":["dep:foldhash"]},"yanked":false,"rust_version":"1.65.0","v":2} 