   etag: W/"98385fdc916e8d6e4f0275d8cb53b9c1" 0.1.1 {"name":"memchr","vers":"0.1.1","deps":[{"name":"libc","req":"0.1.*","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"0.2.*","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"b9c9343b3332ca7c9a0b9037244031bd601862a2d8d92460abe7614c36083001","features":{},"yanked":false} 0.1.2 {"name":"memchr","vers":"0.1.2","deps":[{"name":"libc","req":"0.1.*","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"0.2.*","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"fd599015244ae33e71dad76dfa57dcd2a53f6c2e26262df0d5971eb8de8b6190","features":{},"yanked":false} 0.1.3 {"name":"memchr","vers":"0.1.3","deps":[{"name":"libc","req":"0.1.*","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"0.2.*","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"361009278619eef5dd90d17e502835b555061d24923eae783e3b4b6766857000","features":{},"yanked":false} 0.1.4 {"name":"memchr","vers":"0.1.4","deps":[{"name":"libc","req":"0.1.*","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"0.2.*","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"872706d9adc99bb64d4b85a60e16c6f5f6326cc7e453dd2157262e489fb03abb","features":{},"yanked":false} 0.1.5 {"name":"memchr","vers":"0.1.5","deps":[{"name":"libc","req":"0.1.*","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"0.2.*","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"3b10dbf33ba2f2dadce3718f0e41ac47c19d641add0d34ac8f254407430542bd","features":{},"yanked":false} 0.1.6 {"name":"memchr","vers":"0.1.6","deps":[{"name":"libc","req":"^0.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"5e6ccc9689c3436c502392ce72163d192ed82beea051f88f570bff312ffc9c5e","features":{},"yanked":false} 0.1.7 {"name":"memchr","vers":"0.1.7","deps":[{"name":"libc","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"dc66b0957bf6ae6590681ceac49b0df16823d43037d49aaf2ee658d483af30ab","features":{},"yanked":false} 0.1.8 {"name":"memchr","vers":"0.1.8","deps":[{"name":"libc","req":"^0.2.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"1e949f54d28d4a2edf745973721d33a39448466d448f8fae9f22515488a36b60","features":{},"yanked":false} 0.1.9 {"name":"memchr","vers":"0.1.9","deps":[{"name":"libc","req":"^0.2.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"3d6a28668369c3a29b9993a59a8f65947b74e19a7914cb925273dbd97077c69f","features":{},"yanked":false} 0.1.10 {"name":"memchr","vers":"0.1.10","deps":[{"name":"libc","req":"^0.2.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"c98adb597263e245c6ffe48dc50d338b51acb8cc53e8e7b3e9c21f53c0a411cb","features":{},"yanked":false} 0.1.11 {"name":"memchr","vers":"0.1.11","deps":[{"name":"libc","req":"^0.2.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"d8b629fb514376c675b98c1421e80b151d3817ac42d7c667717d282761418d20","features":{},"yanked":false} 1.0.0 {"name":"memchr","vers":"1.0.0","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.4.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"7492849298f0731c393b1f34ce03a7c84c00bead2e7057db9342907c8fdcae28","features":{},"yanked":false} 1.0.1 {"name":"memchr","vers":"1.0.1","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.4.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"1dbccc0e46f1ea47b9f17e6d67c5a96bd27030519c519c9c91327e31275a47b4","features":{"default":["use_std"],"use_std":["libc/use_std"]},"yanked":false} 1.0.2 {"name":"memchr","vers":"1.0.2","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.4.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"148fab2e51b4f1cfc66da2a7c32981d1d3c083a803978268bb11fe4b86925e7a","features":{"default":["use_std","libc"],"use_std":["libc","libc/use_std"]},"yanked":false} 2.0.0 {"name":"memchr","vers":"2.0.0","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"e01e64d9017d18e7fc09d8e4fe0e28ff6931019e979fb8019319db7ca827f8a6","features":{"default":["use_std","libc"],"use_std":["libc","libc/use_std"]},"yanked":false} 2.0.1 {"name":"memchr","vers":"2.0.1","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.5","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"796fba70e76612589ed2ce7f45282f5af869e0fdd7cc6199fa1aa1f1d591ba9d","features":{"default":["use_std","libc"],"use_std":["libc","libc/use_std"]},"yanked":false} 2.0.2 {"name":"memchr","vers":"2.0.2","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.7","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"a3b4142ab8738a78c51896f704f83c11df047ff1bda9a92a661aa6361552d93d","features":{"default":["use_std","libc"],"use_std":["libc","libc/use_std"]},"yanked":false} 2.1.0 {"name":"memchr","vers":"2.1.0","deps":[{"name":"cfg-if","req":"^0.1.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.7","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"},{"name":"version_check","req":"^0.1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"}],"cksum":"4b3629fe9fdbff6daa6c33b90f7c08355c1aca05a3d01fa8063b822fcf185f3b","features":{"default":["use_std","libc"],"use_std":["libc","libc/use_std"]},"yanked":false} 2.1.1 {"name":"memchr","vers":"2.1.1","deps":[{"name":"cfg-if","req":"^0.1.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.7","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"},{"name":"version_check","req":"^0.1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"}],"cksum":"0a3eb002f0535929f1199681417029ebea04aadc0c7a4224b46be99c7f5d6a16","features":{"default":["use_std","libc"],"use_std":["libc","libc/use_std"]},"yanked":false} 2.1.2 {"name":"memchr","vers":"2.1.2","deps":[{"name":"cfg-if","req":"^0.1.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.7","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"},{"name":"version_check","req":"^0.1.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"}],"cksum":"db4c41318937f6e76648f42826b1d9ade5c09cafb5aef7e351240a70f39206e9","features":{"default":["use_std","libc"],"use_std":["libc","libc/use_std"]},"yanked":false} 2.1.3 {"name":"memchr","vers":"2.1.3","deps":[{"name":"cfg-if","req":"^0.1.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.8","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"e1dd4eaac298c32ce07eb6ed9242eda7d82955b9170b7d6db59b2e02cc63fcb8","features":{"default":["use_std","libc"],"use_std":["libc","libc/use_std"]},"yanked":false} 2.2.0 {"name":"memchr","vers":"2.2.0","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.8","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"2efc7bc57c883d4a4d6e3246905283d8dae951bb3bd32f49d6ef297f546e1c39","features":{"default":["use_std"],"use_std":[]},"yanked":false} 2.2.1 {"name":"memchr","vers":"2.2.1","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.8","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"88579771288728879b57485cc7d6b07d648c9f0141eb955f8ab7f9d45394468e","features":{"default":["use_std"],"use_std":[]},"yanked":false} 2.3.0 {"name":"memchr","vers":"2.3.0","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"3197e20c7edb283f87c071ddfc7a2cca8f8e0b888c242959846a6fce03c72223","features":{"default":["std"],"std":[],"use_std":["std"]},"yanked":false} 2.3.1 {"name":"memchr","vers":"2.3.1","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"1406817d4e80bb8acc9f0219187396db83252515b51314771f26975e515b9e30","features":{"default":["std"],"std":[],"use_std":["std"]},"yanked":true} 2.3.2 {"name":"memchr","vers":"2.3.2","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"53445de381a1f436797497c61d851644d0e8e88e6140f22872ad33a704933978","features":{"default":["std"],"std":[],"use_std":["std"]},"yanked":false} 2.3.3 {"name":"memchr","vers":"2.3.3","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"3728d817d99e5ac407411fa471ff9800a778d88a24685968b36824eaf4bee400","features":{"default":["std"],"std":[],"use_std":["std"]},"yanked":false} 2.3.4 {"name":"memchr","vers":"2.3.4","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"0ee1c47aaa256ecabcaea351eae4a9b01ef39ed810004e298d2511ed284b1525","features":{"default":["std"],"std":[],"use_std":["std"]},"yanked":false} 2.4.0 {"name":"memchr","vers":"2.4.0","deps":[{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"b16bd47d9e329435e309c58469fe0791c2d0d1ba96ec0954152a5ae2b04387dc","features":{"default":["std"],"std":[],"use_std":["std"]},"yanked":false} 2.4.1 {"name":"memchr","vers":"2.4.1","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"308cc39be01b73d0d18f82a0e7b2a3df85245f84af96fdddc5d202d27e47b86a","features":{"default":["std"],"rustc-dep-of-std":["core","compiler_builtins"],"std":[],"use_std":["std"]},"yanked":false} 2.5.0 {"name":"memchr","vers":"2.5.0","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"libc","req":"^0.2.18","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"2dffe52ecf27772e601905b7522cb4ef790d2cc203488bbd0e2fe85fcb74566d","features":{"default":["std"],"rustc-dep-of-std":["core","compiler_builtins"],"std":[],"use_std":["std"]},"yanked":false} 2.6.0 {"name":"memchr","vers":"2.6.0","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"log","req":"^0.4.20","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"76fc44e2588d5b436dbc3c6cf62aef290f90dab6235744a93dfe1cc18f451e2c","features":{"alloc":[],"default":["std"],"libc":[],"rustc-dep-of-std":["core","compiler_builtins"],"std":["alloc"],"use_std":["std"]},"features2":{"logging":["dep:log"]},"yanked":false,"v":2} 2.6.1 {"name":"memchr","vers":"2.6.1","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"log","req":"^0.4.20","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"f478948fd84d9f8e86967bf432640e46adfb5a4bd4f14ef7e864ab38220534ae","features":{"alloc":[],"default":["std"],"libc":[],"rustc-dep-of-std":["core","compiler_builtins"],"std":["alloc"],"use_std":["std"]},"features2":{"logging":["dep:log"]},"yanked":false,"rust_version":"1.60","v":2} 2.6.2 {"name":"memchr","vers":"2.6.2","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"log","req":"^0.4.20","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"5486aed0026218e61b8a01d5fbd5a0a134649abb71a0e53b7bc088529dced86e","features":{"alloc":[],"default":["std"],"libc":[],"rustc-dep-of-std":["core","compiler_builtins"],"std":["alloc"],"use_std":["std"]},"features2":{"logging":["dep:log"]},"yanked":false,"rust_version":"1.60","v":2} 2.6.3 {"name":"memchr","vers":"2.6.3","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"log","req":"^0.4.20","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"8f232d6ef707e1956a43342693d2a31e72989554d58299d7a88738cc95b0d35c","features":{"alloc":[],"default":["std"],"libc":[],"rustc-dep-of-std":["core","compiler_builtins"],"std":["alloc"],"use_std":["std"]},"features2":{"logging":["dep:log"]},"yanked":false,"rust_version":"1.61","v":2} 2.6.4 {"name":"memchr","vers":"2.6.4","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"log","req":"^0.4.20","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"f665ee40bc4a3c5590afb1e9677db74a508659dfd71e126420da8274909a0167","features":{"alloc":[],"default":["std"],"libc":[],"rustc-dep-of-std":["core","compiler_builtins"],"std":["alloc"],"use_std":["std"]},"features2":{"logging":["dep:log"]},"yanked":false,"rust_version":"1.61","v":2} 2.7.0 {"name":"memchr","vers":"2.7.0","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"log","req":"^0.4.20","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"cbd85c6502c853919a7ffb75f80e67ecca712dda8f07c698f9df6e020a4f3e44","features":{"alloc":[],"default":["std"],"libc":[],"rustc-dep-of-std":["core","compiler_builtins"],"std":["alloc"],"use_std":["std"]},"features2":{"logging":["dep:log"]},"yanked":false,"rust_version":"1.61","v":2} 2.7.1 {"name":"memchr","vers":"2.7.1","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"log","req":"^0.4.20","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"523dc4f511e55ab87b694dc30d0f820d60906ef06413f93d4d7a1385599cc149","features":{"alloc":[],"default":["std"],"libc":[],"rustc-dep-of-std":["core","compiler_builtins"],"std":["alloc"],"use_std":["std"]},"features2":{"logging":["dep:log"]},"yanked":false,"rust_version":"1.61","v":2} 2.7.2 {"name":"memchr","vers":"2.7.2","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"log","req":"^0.4.20","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"6c8640c5d730cb13ebd907d8d04b52f55ac9a2eec55b440c8892f40d56c76c1d","features":{"alloc":[],"default":["std"],"libc":[],"rustc-dep-of-std":["core","compiler_builtins"],"std":["alloc"],"use_std":["std"]},"features2":{"logging":["dep:log"]},"yanked":false,"rust_version":"1.61","v":2} 2.7.3 {"name":"memchr","vers":"2.7.3","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"log","req":"^0.4.20","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"6d0d8b92cd8358e8d229c11df9358decae64d137c5be540952c5ca7b25aea768","features":{"alloc":[],"default":["std"],"libc":[],"rustc-dep-of-std":["core","compiler_builtins"],"std":["alloc"],"use_std":["std"]},"features2":{"logging":["dep:log"]},"yanked":false,"rust_version":"1.61","v":2} 2.7.4 {"name":"memchr","vers":"2.7.4","deps":[{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"log","req":"^0.4.20","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"78ca9ab1a0babb1e7d5695e3530886289c18cf2f87ec19a575a0abdce112e3a3","features":{"alloc":[],"default":["std"],"libc":[],"rustc-dep-of-std":["core","compiler_builtins"],"std":["alloc"],"use_std":["std"]},"features2":{"logging":["dep:log"]},"yanked":false,"rust_version":"1.61","v":2} 2.7.5 {"name":"memchr","vers":"2.7.5","deps":[{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"log","req":"^0.4.20","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"quickcheck","req":"^1.0.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"dev"}],"cksum":"32a282da65faaf38286cf3be983213fcf1d2e2a58700e808f83f4ea9a4804bc0","features":{"alloc":[],"default":["std"],"libc":[],"rustc-dep-of-std":["core"],"std":["alloc"],"use_std":["std"]},"features2":{"logging":["dep:log"]},"yanked":false,"rust_version":"1.61","v":2} 