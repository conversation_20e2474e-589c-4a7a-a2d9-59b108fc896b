   etag: W/"64a1a78852d444972d438333dde9a1f2" 0.1.0 {"name":"miniz_oxide","vers":"0.1.0","deps":[{"name":"adler32","req":"^1.0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"libc","req":"^0.2.22","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"ded3e450075c28b8a98c87135dacee953667b6eeeec8a29425b30ebe1dcd64f8","features":{},"yanked":false} 0.1.1 {"name":"miniz_oxide","vers":"0.1.1","deps":[{"name":"adler32","req":"^1.0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"libc","req":"^0.2.22","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"19b78d8133f8d31a8ec87d53385a265332c6606cfe2691194f4a3e4c81afb6d1","features":{},"yanked":false} 0.1.2 {"name":"miniz_oxide","vers":"0.1.2","deps":[{"name":"adler32","req":"^1.0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"libc","req":"^0.2.22","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"aaa2d3ad070f428fffbd7d3ca2ea20bb0d8cffe9024405c44e1840bc1418b398","features":{},"yanked":false} 0.1.3 {"name":"miniz_oxide","vers":"0.1.3","deps":[{"name":"adler32","req":"^1.0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"libc","req":"^0.2.22","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"9ba430291c9d6cedae28bcd2d49d1c32fc57d60cd49086646c5dd5673a870eb5","features":{},"yanked":false} 0.2.0 {"name":"miniz_oxide","vers":"0.2.0","deps":[{"name":"adler32","req":"^1.0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"5ad30a47319c16cde58d0314f5d98202a80c9083b5f61178457403dfb14e509c","features":{},"yanked":false} 0.2.1 {"name":"miniz_oxide","vers":"0.2.1","deps":[{"name":"adler32","req":"^1.0.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"c468f2369f07d651a5d0bb2c9079f8488a66d5466efe42d0c5c6466edcb7f71e","features":{},"yanked":false} 0.2.2 {"name":"miniz_oxide","vers":"0.2.2","deps":[{"name":"adler32","req":"^1.0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"b6c3756d66cf286314d5f7ebe74886188a9a92f5eee68b06f31ac2b4f314c99d","features":{},"yanked":false} 0.2.3 {"name":"miniz_oxide","vers":"0.2.3","deps":[{"name":"adler32","req":"^1.0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"5f6d7b3dd914b70db7cef7ab9dc74339ffcadf4d033464a987237bb0b9418cd4","features":{},"yanked":false} 0.3.0 {"name":"miniz_oxide","vers":"0.3.0","deps":[{"name":"adler32","req":"^1.0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"c061edee74a88eb35d876ce88b94d77a0448a201de111c244b70d047f5820516","features":{},"yanked":false} 0.3.1 {"name":"miniz_oxide","vers":"0.3.1","deps":[{"name":"adler32","req":"^1.0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"fe2959c5a0747a8d7a56b4444c252ffd2dda5d452cfd147cdfdda73b1c3ece5b","features":{},"yanked":false} 0.3.2 {"name":"miniz_oxide","vers":"0.3.2","deps":[{"name":"adler32","req":"^1.0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"7108aff85b876d06f22503dcce091e29f76733b2bfdd91eebce81f5e68203a10","features":{},"yanked":false} 0.3.3 {"name":"miniz_oxide","vers":"0.3.3","deps":[{"name":"adler32","req":"^1.0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"304f66c19be2afa56530fa7c39796192eef38618da8d19df725ad7c6d6b2aaae","features":{},"yanked":false} 0.3.4 {"name":"miniz_oxide","vers":"0.3.4","deps":[{"name":"adler32","req":"^1.0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"8ab4b6e85c0d81267c29a95d600278b1e2272d8551e74e439982a34b2751a82b","features":{},"yanked":true} 0.3.5 {"name":"miniz_oxide","vers":"0.3.5","deps":[{"name":"adler32","req":"^1.0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"6f3f74f726ae935c3f514300cc6773a0c9492abc5e972d42ba0c0ebb88757625","features":{},"yanked":false} 0.3.6 {"name":"miniz_oxide","vers":"0.3.6","deps":[{"name":"adler32","req":"^1.0.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"aa679ff6578b1cddee93d7e82e263b94a575e0bfced07284eb0c037c1d2416a5","features":{},"yanked":false} 0.3.7 {"name":"miniz_oxide","vers":"0.3.7","deps":[{"name":"adler32","req":"^1.0.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"791daaae1ed6889560f8c4359194f56648355540573244a5448a83ba1ecc7435","features":{},"yanked":false} 0.4.0 {"name":"miniz_oxide","vers":"0.4.0","deps":[{"name":"adler","req":"^0.2.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"be0f75932c1f6cfae3c04000e40114adf955636e19040f9c0a2c380702aa1c7f","features":{"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"]},"yanked":false} 0.4.1 {"name":"miniz_oxide","vers":"0.4.1","deps":[{"name":"adler","req":"^0.2.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"4d7559a8a40d0f97e1edea3220f698f78b1c5ab67532e49f68fde3910323b722","features":{"no_extern_crate_alloc":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"]},"yanked":false} 0.4.2 {"name":"miniz_oxide","vers":"0.4.2","deps":[{"name":"adler","req":"^0.2.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"c60c0dfe32c10b43a144bad8fc83538c52f58302c92300ea7ec7bf7b38d5a7b9","features":{"no_extern_crate_alloc":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"]},"yanked":false} 0.4.3 {"name":"miniz_oxide","vers":"0.4.3","deps":[{"name":"adler","req":"^0.2.3","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"0f2d26ec3309788e423cfbf68ad1800f061638098d76a83681af979dc4eda19d","features":{"no_extern_crate_alloc":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"]},"yanked":false} 0.4.4 {"name":"miniz_oxide","vers":"0.4.4","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"autocfg","req":"^1.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"build"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"a92518e98c078586bc6c934028adcca4c92a53d6a958196de835170a01d84e4b","features":{"no_extern_crate_alloc":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"]},"yanked":false} 0.5.0 {"name":"miniz_oxide","vers":"0.5.0","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"972e674995946bbd2a8324ad41211cb65b731177c06b5d1aee83ac114a06ebff","features":{"default":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std","simd-adler32/std"],"simd":["simd-adler32"]},"yanked":false} 0.5.1 {"name":"miniz_oxide","vers":"0.5.1","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"d2b29bd4bc3f33391105ebee3589c19197c4271e3e5a9ec9bfe8127eeff8f082","features":{"default":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std","simd-adler32/std"],"simd":["simd-adler32"]},"yanked":false} 0.5.3 {"name":"miniz_oxide","vers":"0.5.3","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"6f5c75688da582b8ffc1f1799e9db273f32133c49e048f614d22ec3256773ccc","features":{"default":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"]},"yanked":false} 0.6.0 {"name":"miniz_oxide","vers":"0.6.0","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"11dbbb6231ce58357b3015f2039c7989358397ac3afb4abb8e48fa8c500b5157","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"],"with-alloc":[]},"yanked":false} 0.6.1 {"name":"miniz_oxide","vers":"0.6.1","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"128d8ff01d10c86c446b4514e228ff58237ccc609573dad9997ec288f9f1c4d0","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"],"with-alloc":[]},"yanked":false} 0.5.4 {"name":"miniz_oxide","vers":"0.5.4","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"96590ba8f175222643a85693f33d26e9c8a015f599c216509b1a6894af675d34","features":{"default":[],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"]},"yanked":false} 0.6.2 {"name":"miniz_oxide","vers":"0.6.2","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"b275950c28b37e794e8c55d88aeb5e139d0ce23fdbbeda68f8d7174abdf9e8fa","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.7.0 {"name":"miniz_oxide","vers":"0.7.0","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"1c0a278c1442e9480db49d296fd27518e08e6180c3b9e2c104e0579bb6d06570","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":true} 0.6.3 {"name":"miniz_oxide","vers":"0.6.3","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"913e8a62b0f7df0bdeca8b7273d2c616d26350a1f9b64d3627fc518ae9fd94e8","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":true} 0.6.4 {"name":"miniz_oxide","vers":"0.6.4","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"f2e212582ede878b109755efd0773a4f0f4ec851584cf0aefbeb4d9ecc114822","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":true} 0.7.1 {"name":"miniz_oxide","vers":"0.7.1","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"e7810e0be55b428ada41041c41f32c9f1a42817901b4ccf45fa3d4b6561e74c7","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.7.2 {"name":"miniz_oxide","vers":"0.7.2","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"9d811f3e15f28568be3407c8e7fdb6514c1cda3cb30683f15b6a1a1dc4ea14a7","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.7.3 {"name":"miniz_oxide","vers":"0.7.3","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"87dfd01fe195c66b572b37921ad8803d010623c0aca821bea2302239d155cdae","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.7.4 {"name":"miniz_oxide","vers":"0.7.4","deps":[{"name":"adler","req":"^1.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"b8a240ddb74feaf34a79a7add65a741f3167852fba007066dcac1ca548d89c08","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.8.0 {"name":"miniz_oxide","vers":"0.8.0","deps":[{"name":"adler2","req":"^2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"e2d80299ef12ff69b16a84bb182e3b9df68b5a91574d3d4fa6e41b65deec4df1","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler2/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.8.1 {"name":"miniz_oxide","vers":"0.8.1","deps":[{"name":"adler2","req":"^2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"a2ef2593ffb6958c941575cee70c8e257438749971869c4ae5acf6f91a168a61","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler2/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":true} 0.8.2 {"name":"miniz_oxide","vers":"0.8.2","deps":[{"name":"adler2","req":"^2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"4ffbe83022cedc1d264172192511ae958937694cd57ce297164951b8b3568394","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler2/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.8.3 {"name":"miniz_oxide","vers":"0.8.3","deps":[{"name":"adler2","req":"^2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"b8402cab7aefae129c6977bb0ff1b8fd9a04eb5b51efc50a70bea51cda0c7924","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler2/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.8.4 {"name":"miniz_oxide","vers":"0.8.4","deps":[{"name":"adler2","req":"^2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"criterion","req":"^0.5","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"b3b1c9bd4fe1f0f8b387f6eb9eb3b4a1aa26185e5750efb9140301703f62cd1b","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler2/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.8.5 {"name":"miniz_oxide","vers":"0.8.5","deps":[{"name":"adler2","req":"^2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"8e3e04debbb59698c15bacbb6d93584a8c0ca9cc3213cb423d31f760d8843ce5","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler2/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.8.6 {"name":"miniz_oxide","vers":"0.8.6","deps":[{"name":"adler2","req":"^2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"serde","req":"^1.0","features":["derive"],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"29c72f6929239626840b28f919ce8981a317fc5dc63ce25c30d2ab372f94886f","features":{"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler2/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":true} 0.8.7 {"name":"miniz_oxide","vers":"0.8.7","deps":[{"name":"adler2","req":"^2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"serde","req":"^1.0","features":["derive"],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"ff70ce3e48ae43fa075863cef62e8b43b71a4f2382229920e0df362592919430","features":{"block-boundary":[],"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler2/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.8.8 {"name":"miniz_oxide","vers":"0.8.8","deps":[{"name":"adler2","req":"^2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"serde","req":"^1.0","features":["derive"],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"3be647b768db090acb35d5ec5db2b0e1f1de11133ca123b9eacf5137868f892a","features":{"block-boundary":[],"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","compiler_builtins","adler2/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 0.8.9 {"name":"miniz_oxide","vers":"0.8.9","deps":[{"name":"adler2","req":"^2.0","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"serde","req":"^1.0","features":["derive"],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"simd-adler32","req":"^0.3.3","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"1fa76a2c86f704bdb222d66965fb3d63269ce38518b83cb0575fca855ebb6316","features":{"block-boundary":[],"default":["with-alloc"],"rustc-dep-of-std":["core","alloc","adler2/rustc-dep-of-std"],"simd":["simd-adler32"],"std":[],"with-alloc":[]},"yanked":false} 