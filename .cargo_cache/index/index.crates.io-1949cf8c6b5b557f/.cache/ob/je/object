   etag: W/"011e9422f558dea69fee1034d33b4b6a" 0.0.1 {"name":"object","vers":"0.0.1","deps":[{"name":"elf","req":"^0.0.9","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"linux\")","kind":"normal"},{"name":"mach_o","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"macos\")","kind":"normal"}],"cksum":"fef1db405204db1ba6bbabaa84d693058eb1e37ddfeb35cdae0d05b1b63956a9","features":{},"yanked":false} 0.0.2 {"name":"object","vers":"0.0.2","deps":[{"name":"elf","req":"^0.0.9","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"mach_o","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"macos\")","kind":"normal"}],"cksum":"dd1ba8ce94220de87fddb791b9c4283c12b85f66290473031f1a71601d6e6100","features":{},"yanked":false} 0.1.0 {"name":"object","vers":"0.1.0","deps":[{"name":"mach_o","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"macos\")","kind":"normal"},{"name":"xmas-elf","req":"^0.3.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"7ebe1b2d137eb894b8b9df5fbcbf5541b0f4c4f8cc60f7141204dd943b743ccb","features":{},"yanked":false} 0.3.0 {"name":"object","vers":"0.3.0","deps":[{"name":"mach_o","req":"^0.1.2","features":[],"optional":false,"default_features":true,"target":"cfg(target_os = \"macos\")","kind":"normal"},{"name":"xmas-elf","req":"^0.4.0","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"c1358583d355ac6bd4edacacd65b3b5c6c31e611f930435cbb396d1a6a55e23c","features":{"nightly":[]},"yanked":false} 0.4.0 {"name":"object","vers":"0.4.0","deps":[{"name":"goblin","req":"^0.0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"c97b2804f2c9a6b1b413f0acd922f05cf97ef2e01281344fcf1da4abbc5c9b73","features":{"nightly":[]},"yanked":false} 0.4.1 {"name":"object","vers":"0.4.1","deps":[{"name":"goblin","req":"^0.0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"d6a84bfd6de5cdd57bb60867552f008433c5371e21b9a39c58aa26cc9a3d6025","features":{"nightly":[]},"yanked":false} 0.5.0 {"name":"object","vers":"0.5.0","deps":[{"name":"goblin","req":"^0.0.11","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.5.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"e58ed5e7b22523fe7d62521e425a7b945a0ec264c04c48ef394f4897a55e7af8","features":{"nightly":[]},"yanked":false} 0.6.0 {"name":"object","vers":"0.6.0","deps":[{"name":"goblin","req":"^0.0.12","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"cd1212bd80b5ce3628be791b450c0d1605b4710ab1dbaf741a1e8df88e4ace70","features":{"nightly":[]},"yanked":false} 0.7.0 {"name":"object","vers":"0.7.0","deps":[{"name":"goblin","req":"^0.0.13","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"}],"cksum":"87689d98441d54f2404b4e9f7b214663a9dc996fd3e4416a475e2a28048f81da","features":{"nightly":[]},"yanked":false} 0.8.0 {"name":"object","vers":"0.8.0","deps":[{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"goblin","req":"^0.0.15","features":["endian_fd","elf32","elf64","mach32","mach64","pe32","pe64","archive"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"parity-wasm","req":"^0.28.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"scroll","req":"^0.9","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"uuid","req":"^0.5.1","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"}],"cksum":"439febe5c240fb61d25e027d0eda6c9a9381969995532c33a84206ab638e485a","features":{"compression":["flate2"],"default":["std","compression","wasm"],"std":["goblin/std"],"wasm":["std","parity-wasm"]},"yanked":false} 0.9.0 {"name":"object","vers":"0.9.0","deps":[{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"goblin","req":"^0.0.15","features":["endian_fd","elf32","elf64","mach32","mach64","pe32","pe64","archive"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"parity-wasm","req":"^0.31.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"scroll","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"uuid","req":"^0.6","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"9d516d9e7232a46e709ab9027f51e2d3ce97608bce167df6badf12b258a6a61b","features":{"compression":["flate2"],"default":["std","compression","wasm"],"std":["goblin/std"],"wasm":["std","parity-wasm"]},"yanked":false} 0.10.0 {"name":"object","vers":"0.10.0","deps":[{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"goblin","req":"^0.0.17","features":["endian_fd","elf32","elf64","mach32","mach64","pe32","pe64","archive"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.6","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"parity-wasm","req":"^0.31.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"scroll","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"uuid","req":"^0.6","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"6cca6ad89d0801138cb4ef606908ae12d83edc4c790ef5178fc7b4c72d959e90","features":{"compression":["flate2"],"default":["std","compression","wasm"],"std":["goblin/std"],"wasm":["std","parity-wasm"]},"yanked":false} 0.11.0 {"name":"object","vers":"0.11.0","deps":[{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"goblin","req":"^0.0.19","features":["endian_fd","elf32","elf64","mach32","mach64","pe32","pe64","archive"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"parity-wasm","req":"^0.35.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"scroll","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"uuid","req":"^0.7","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"2e7934267669d9b8637926ed243df1b917b40e43dd070aea2a178296e6ed1b72","features":{"compression":["flate2"],"default":["std","compression","wasm"],"std":["goblin/std"],"wasm":["std","parity-wasm"]},"yanked":false} 0.12.0 {"name":"object","vers":"0.12.0","deps":[{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"goblin","req":"^0.0.22","features":["endian_fd","elf32","elf64","mach32","mach64","pe32","pe64","archive"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"parity-wasm","req":"^0.38.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"scroll","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"uuid","req":"^0.7","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"df4af347f5ac3d0e83e78c26be33cd10e8e874dcb68517a909ad802ba50a90b5","features":{"compression":["flate2"],"default":["std","compression","wasm"],"std":["goblin/std"],"wasm":["std","parity-wasm"]},"yanked":false} 0.13.0 {"name":"object","vers":"0.13.0","deps":[{"name":"crc32fast","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"goblin","req":"^0.0.24","features":["endian_fd","elf32","elf64","mach32","mach64","pe32","pe64","archive"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"parity-wasm","req":"^0.40.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"scroll","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"target-lexicon","req":"^0.4","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"uuid","req":"^0.7","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"d89ec45bc6b810c6ee998e22953fbf387a40fcbf5014dcbb9e5ba9a09a81ee15","features":{"compression":["flate2"],"default":["read","std","compression","wasm"],"read":[],"std":["goblin/std"],"wasm":["std","parity-wasm"],"write":["crc32fast","indexmap","std"]},"yanked":false} 0.14.0 {"name":"object","vers":"0.14.0","deps":[{"name":"crc32fast","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"goblin","req":"^0.0.24","features":["endian_fd","elf32","elf64","mach32","mach64","pe32","pe64","archive"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"parity-wasm","req":"^0.40.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"scroll","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"target-lexicon","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"uuid","req":"^0.7","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"81afbc5773e99efe9533d8a539dfac37e531dcd0f4eeb41584bae03ccf76d4c2","features":{"compression":["flate2"],"default":["read","std","compression","wasm"],"read":[],"std":["goblin/std"],"wasm":["std","parity-wasm"],"write":["crc32fast","indexmap","std"]},"yanked":false} 0.14.1 {"name":"object","vers":"0.14.1","deps":[{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"goblin","req":"^0.0.24","features":["endian_fd","elf32","elf64","mach32","mach64","pe32","pe64","archive"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"parity-wasm","req":"^0.40.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"scroll","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"target-lexicon","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"uuid","req":"^0.7","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"a411a7fd46b7ebc9849c80513c84280f41cbc3159f489cd77fb30ecefdd1218a","features":{"compression":["flate2"],"default":["read","std","compression","wasm"],"read":[],"std":["goblin/std"],"wasm":["std","parity-wasm"],"write":["crc32fast","indexmap","std"]},"yanked":false} 0.15.0 {"name":"object","vers":"0.15.0","deps":[{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"goblin","req":"^0.0.24","features":["endian_fd","elf32","elf64","mach32","mach64","pe32","pe64","archive"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"parity-wasm","req":"^0.40.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"scroll","req":"^0.9","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"target-lexicon","req":"^0.8","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"uuid","req":"^0.7","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"973befa444477065996ee5692b920ebe38299f115d191a4df03f0e872bbb6aef","features":{"compression":["flate2"],"default":["read","std","compression","wasm"],"read":[],"std":["goblin/std"],"wasm":["std","parity-wasm"],"write":["crc32fast","indexmap","std"]},"yanked":false} 0.16.0 {"name":"object","vers":"0.16.0","deps":[{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"goblin","req":"^0.1","features":["endian_fd","elf32","elf64","mach32","mach64","pe32","pe64","archive"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"parity-wasm","req":"^0.41.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"scroll","req":"^0.10","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"target-lexicon","req":"^0.9","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"uuid","req":"^0.8","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"a3c61759aa254402e53c79a68dc519cda1ceee2ff2b6d70b3e58bf64ac2f03e3","features":{"compression":["flate2"],"default":["read","std","compression","wasm"],"read":[],"std":["goblin/std"],"wasm":["std","parity-wasm"],"write":["crc32fast","indexmap","std"]},"yanked":false} 0.17.0 {"name":"object","vers":"0.17.0","deps":[{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"goblin","req":"^0.1","features":["endian_fd","elf32","elf64","mach32","mach64","pe32","pe64","archive"],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"parity-wasm","req":"^0.41.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"scroll","req":"^0.10","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"target-lexicon","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"uuid","req":"^0.8","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"}],"cksum":"ea44a4fd660ab0f38434934ca0212e90fbeaaee54126ef20a3451c30c95bafae","features":{"compression":["flate2"],"default":["read","std","compression","wasm"],"read":[],"std":["goblin/std"],"wasm":["std","parity-wasm"],"write":["crc32fast","indexmap","std"]},"yanked":false} 0.18.0 {"name":"object","vers":"0.18.0","deps":[{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"target-lexicon","req":"^0.10","features":[],"optional":false,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.51.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"e5666bbb90bc4d1e5bdcb26c0afda1822d25928341e9384ab187a9b37ab69e36","features":{"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","coff","elf","macho","pe","wasm"],"read_core":[],"std":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.19.0 {"name":"object","vers":"0.19.0","deps":[{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.54","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"9cbca9424c482ee628fa549d9c812e2cd22f1180b9222c9200fdfa6eb31aecb2","features":{"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","coff","elf","macho","pe","wasm"],"read_core":[],"std":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.20.0 {"name":"object","vers":"0.20.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"1ab52be62400ca80aa00285d25253d7f7c437b7375c4de678f5405d3afe82ca5","features":{"all":["read","write","std","compression","default"],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","coff","elf","macho","pe","wasm","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc"],"std":[],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.21.0 {"name":"object","vers":"0.21.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"1f6978886b25aeb1a0160743438b730b19afd789f967252f3d3b54aa208548e1","features":{"all":["read","write","std","compression","default"],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","coff","elf","macho","pe","wasm","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc"],"std":[],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.21.1 {"name":"object","vers":"0.21.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"37fd5004feb2ce328a52b0b3d01dbf4ffff72583493900ed15f22d4111c51693","features":{"all":["read","write","std","compression","default"],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","coff","elf","macho","pe","wasm","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc"],"std":[],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.22.0 {"name":"object","vers":"0.22.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"8d3b63360ec3cb337817c2dbd47ab4a0f170d285d8e5a2064600f3def1402397","features":{"all":["read","write","std","compression","default"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","wasm","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc"],"std":[],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.23.0 {"name":"object","vers":"0.23.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"a9a7ab5d64814df0fe4a4b5ead45ed6c5f181ee3ff04ba344313a6c80446c5d4","features":{"all":["read","write","std","compression","default"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","wasm","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc"],"std":[],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.24.0 {"name":"object","vers":"0.24.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memmap","req":"^0.7","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"1a5b3dd1c072ee7963717671d1ca129f1048fda25edea6b752bfc71ac8854170","features":{"all":["read","write","std","compression","default"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc"],"std":[],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.25.0 {"name":"object","vers":"0.25.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.2.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"4f26e9fafdc766a38d6929975130ddc0f3d4fda5c10a8ec7acf67fd01f13c1d1","features":{"all":["read","write","std","compression","default"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.25.1 {"name":"object","vers":"0.25.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.2.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"9023c1c0973b327f073c7f2fceb9bcc049862f93a7d14c6feb46c8a56460a0d5","features":{"all":["read","write","std","compression","default"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.25.2 {"name":"object","vers":"0.25.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.2.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"f8bc1d42047cf336f0f939c99e97183cf31551bf0f2865a2ec9c8d91fd4ffb5e","features":{"all":["read","write","std","compression","default"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.25.3 {"name":"object","vers":"0.25.3","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.2.2","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"a38f2be3697a57b4060074ff41b44c16870d916ad7877c17696e063257482bc7","features":{"all":["read","write","std","compression","default"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.26.0 {"name":"object","vers":"0.26.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"c55827317fb4c08822499848a14237d2874d6f139828893017237e7ab93eb386","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.26.1 {"name":"object","vers":"0.26.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"ee2766204889d09937d00bfbb7fec56bb2a199e2ade963cab19185d8a6104c7c","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.26.2 {"name":"object","vers":"0.26.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"memmap2","req":"^0.3","features":[],"optional":false,"default_features":true,"target":null,"kind":"dev"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"39f37e50073ccad23b6d09bcb5b263f4e76d3bb6038e4a3c08e52162ffa8abc2","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho"],"write_core":["crc32fast","indexmap","std"]},"yanked":false} 0.27.0 {"name":"object","vers":"0.27.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"c821014c18301591b89b843809ef953af9e3df0496c232d5c0611b0a52aac363","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_core","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap/std","std"]},"yanked":false} 0.27.1 {"name":"object","vers":"0.27.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"67ac1d3f9a1d3616fd9a60c8d74296f22406a238b6a72f5cc1e6f314df4ffbf9","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_core","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_core","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap/std","std"]},"yanked":false} 0.28.0 {"name":"object","vers":"0.28.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.11","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"89f85cf50c211fd8cf2fd250180d0111d21000d06b8461874b87ae544ddd9bde","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"]},"yanked":false} 0.28.1 {"name":"object","vers":"0.28.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.11","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"7ce8b38d41f9f3618fc23f908faae61510f8d8ce2d99cbe910641e8f1971f084","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"]},"yanked":false} 0.28.2 {"name":"object","vers":"0.28.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.11","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"1.6.*","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"09f5c1ce85647898562dda29150dc08145cd9dda93088da524f6e664fa33fbf1","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"]},"yanked":false} 0.28.3 {"name":"object","vers":"0.28.3","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.11","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.6","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"40bec70ba014595f99f7aa110b84331ffe1ee9aece7fe6f387cc7e3ecda4d456","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"]},"yanked":false} 0.28.4 {"name":"object","vers":"0.28.4","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.11","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.6","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"e42c982f2d955fac81dd7e1d0e1426a7d702acd9c98d19ab01083a6a0328c424","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"]},"yanked":false} 0.29.0 {"name":"object","vers":"0.29.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.12.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.6","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"21158b2c33aa6d4561f1c0a6ea283ca92bc54802a93b263e910746d679a7eb53","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"]},"yanked":false} 0.30.0 {"name":"object","vers":"0.30.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.13.1","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.6","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"239da7f290cfa979f43f85a8efeee9a8a76d0827c356d37f9d3d7254d6b537fb","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"unstable-all":["all","unstable","xcoff"],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"],"xcoff":[]},"yanked":false} 0.30.1 {"name":"object","vers":"0.30.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.13.1","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.6","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"8d864c91689fdc196779b98dba0aceac6118594c2df6ee5d943eb6a8df4d107a","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"unstable-all":["all","unstable","xcoff"],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"],"xcoff":[]},"yanked":false} 0.30.2 {"name":"object","vers":"0.30.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.13.1","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.6","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"2b8c786513eb403643f2a88c244c2aaa270ef2153f55094587d0c48a3cf22a83","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"unstable-all":["all","unstable","xcoff"],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"],"xcoff":[]},"yanked":false} 0.30.3 {"name":"object","vers":"0.30.3","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.13.1","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.6","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"ea86265d3d3dcb6a27fc51bd29a4bf387fae9d2986b823079d4986af253eb439","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"unstable-all":["all","unstable","xcoff"],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"],"xcoff":[]},"yanked":false} 0.31.0 {"name":"object","vers":"0.31.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.13.1","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.6","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.3.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.102.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"d08090140cfee2e09897d6be320b47a45b79eb68b414de87130f9532966e2f1d","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"unstable-all":["all","unstable"],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"],"xcoff":[]},"yanked":false} 0.31.1 {"name":"object","vers":"0.31.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.13.1","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.6","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.3.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.102.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"8bda667d9f2b5051b8833f59f3bf748b28ef54f850f4fcb389a252aa383866d1","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"unstable-all":["all","unstable"],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"],"xcoff":[]},"yanked":false} 0.30.4 {"name":"object","vers":"0.30.4","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.13.1","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^1.6","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.57","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"03b4680b86d9cfafba8fc491dc9b6df26b68cf40e9e6cd73909194759a63c385","features":{"all":["read","write","std","compression","wasm"],"archive":[],"cargo-all":[],"coff":[],"compression":["flate2","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm"],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"unstable-all":["all","unstable","xcoff"],"wasm":["wasmparser"],"write":["write_std","coff","elf","macho","pe"],"write_core":["crc32fast","indexmap","hashbrown"],"write_std":["write_core","std","indexmap/std","crc32fast/std"],"xcoff":[]},"yanked":false} 0.32.0 {"name":"object","vers":"0.32.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.14.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.4.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.110.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"77ac5bbd07aea88c60a577a1ce218075ffd59208b2d7ca97adf9bfc5aeb21ebe","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","std","compression","wasm"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.60","v":2} 0.32.1 {"name":"object","vers":"0.32.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.14.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.4.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.110.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"9cf5f9dd3933bd50a9e1f149ec995f39ae2c496d31fd772c1fd45ebc27e902b0","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","std","compression","wasm"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.60","v":2} 0.32.2 {"name":"object","vers":"0.32.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.14.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.5.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.118.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"a6a622008b6e321afc04970976f62ee297fdbaa6f95318ca343e3eebb9648441","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","std","compression","wasm"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.60","v":2} 0.33.0 {"name":"object","vers":"0.33.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.14.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.6.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.201.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"d8dd6c0cdf9429bce006e1362bfce61fa1bfd8c898a643ed8d2b471934701d3d","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.60","v":2} 0.34.0 {"name":"object","vers":"0.34.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.14.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.6.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.201.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"d7090bae93f8585aad99e595b7073c5de9ba89fbd6b4e9f0cdd7a10177273ac8","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.60","v":2} 0.35.0 {"name":"object","vers":"0.35.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.14.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.6.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.202.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"}],"cksum":"b8ec7ab813848ba4522158d5517a6093db1ded27575b070f4177b8d12b41db5e","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.36.0 {"name":"object","vers":"0.36.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.14.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.6.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.208.1","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"576dfe1fc8f9df304abb159d767a29d0476f7750fbf8aa7ad07816004a207434","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.36.1 {"name":"object","vers":"0.36.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.14.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.7.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.212.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"081b846d1d56ddfc18fdf1a922e4f6e07a11768ea1b92dec44e42b72712ccfce","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.36.2 {"name":"object","vers":"0.36.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.14.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.7.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.214.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"3f203fa8daa7bb185f760ae12bd8e097f63d17041dcdcaf675ac54cdf863170e","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.36.3 {"name":"object","vers":"0.36.3","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.14.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.7.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.215.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"27b64972346851a39438c60b341ebc01bba47464ae329e55cf343eb93964efd9","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.36.4 {"name":"object","vers":"0.36.4","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.14.0","features":["ahash"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.7.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.216.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"084f1a5821ac4c651660a94a7153d27ac9d8a53736203f58b31945ded098070a","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.36.5 {"name":"object","vers":"0.36.5","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.15.0","features":["default-hasher"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.7.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.218.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"aedf0a2d09c573ed1d8d85b30c119153926a2b36dce0ab28322c09a117a4683e","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.36.6 {"name":"object","vers":"0.36.6","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.15.0","features":["default-hasher"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.7.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.222.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"08dde84f120894086dbb40c7a0257f76fdc72b6f52cdb56c2b5121b2f81b0c83","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.36.7 {"name":"object","vers":"0.36.7","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.15.0","features":["default-hasher"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.7.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.222.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"62948e14d923ea95ea2c7c86c71013138b66525b86bdc08d2dcc262bdb497b87","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.37.0 {"name":"object","vers":"0.37.0","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"compiler_builtins","req":"^0.1.2","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.15.0","features":["default-hasher"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.8.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.232.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"6273adb7096cf9ab4335f258e627d8230e69d40d45567d678f552dcec6245215","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","compiler_builtins","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.37.1 {"name":"object","vers":"0.37.1","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.15.0","features":["default-hasher"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.8.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.234.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"03fd943161069e1768b4b3d050890ba48730e590f57e56d4aa04e7e090e61b4a","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.37.2 {"name":"object","vers":"0.37.2","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.15.0","features":["default-hasher"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.8.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.236.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"b3e3d0a7419f081f4a808147e845310313a39f322d7ae1f996b7f001d6cbed04","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 0.37.3 {"name":"object","vers":"0.37.3","deps":[{"name":"alloc","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-alloc"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"},{"name":"crc32fast","req":"^1.2","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"flate2","req":"^1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"hashbrown","req":"^0.15.0","features":["default-hasher"],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"indexmap","req":"^2.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"},{"name":"memchr","req":"^2.4.1","features":[],"optional":false,"default_features":false,"target":null,"kind":"normal"},{"name":"ruzstd","req":"^0.8.1","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"wasmparser","req":"^0.236.0","features":[],"optional":true,"default_features":false,"target":null,"kind":"normal"}],"cksum":"ff76201f031d8863c38aa7f905eca4f53abbfa15f609db4277d44cd8938f33fe","features":{"archive":[],"cargo-all":[],"coff":[],"elf":[],"macho":[],"pe":["coff"],"read":["read_core","archive","coff","elf","macho","pe","xcoff","unaligned"],"read_core":[],"rustc-dep-of-std":["core","alloc","memchr/rustc-dep-of-std"],"std":["memchr/std"],"unaligned":[],"unstable":[],"xcoff":[]},"features2":{"all":["read","write","build","std","compression","wasm"],"build":["build_core","write_std","elf"],"build_core":["read_core","write_core"],"compression":["dep:flate2","dep:ruzstd","std"],"default":["read","compression"],"doc":["read_core","write_std","build_core","std","compression","archive","coff","elf","macho","pe","wasm","xcoff"],"unstable-all":["all","unstable"],"wasm":["dep:wasmparser"],"write":["write_std","coff","elf","macho","pe","xcoff"],"write_core":["dep:crc32fast","dep:indexmap","dep:hashbrown"],"write_std":["write_core","std","indexmap?/std","crc32fast?/std"]},"yanked":false,"rust_version":"1.65","v":2} 