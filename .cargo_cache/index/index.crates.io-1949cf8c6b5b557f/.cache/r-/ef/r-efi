   etag: W/"2f7eb29a0333654e5540b73cff3da485" 0.1.0 {"name":"r-efi","vers":"0.1.0","deps":[],"cksum":"d875cbb5867ce0ade6af7f62b4f31ff23f2b41839339fc277c1b4c743df1b7bf","features":{},"yanked":false} 0.1.1 {"name":"r-efi","vers":"0.1.1","deps":[],"cksum":"c0ab882806dad5f5cadef3f5bb83a08de491443db8769d1afa98a66a10461f2a","features":{"examples":[]},"yanked":false} 1.0.0 {"name":"r-efi","vers":"1.0.0","deps":[],"cksum":"2c77043e200c7bc7023276b41392ad182024f8c0e86fb7906b8746b3fa5a7e62","features":{"examples":[]},"yanked":false} 2.0.0 {"name":"r-efi","vers":"2.0.0","deps":[],"cksum":"0d8e6707f86fcdb3ea6a187844664862a2618b3279e1cc2d607b482a45f0e405","features":{"examples":[]},"yanked":false} 2.1.0 {"name":"r-efi","vers":"2.1.0","deps":[],"cksum":"8f2c7f9e57367053a4c9d2f235e715b7a4fa8b774b78eb3e477b2345dc2bb33d","features":{"examples":[]},"yanked":false} 2.2.0 {"name":"r-efi","vers":"2.2.0","deps":[],"cksum":"7b4aceea558d694eb2d5a0d9f3a94f30b8f9a7d4469983ddea62077716364788","features":{"examples":[]},"yanked":false} 3.0.0 {"name":"r-efi","vers":"3.0.0","deps":[],"cksum":"f0d843c83754796be1dc049a1b9bb3e7282f95df2510177161f71998eced399f","features":{"examples":[]},"yanked":false} 3.1.0 {"name":"r-efi","vers":"3.1.0","deps":[],"cksum":"6eaab81f2b90c518541382de3e3a0689a0426d0798d0fdef56f3df69636e83dd","features":{"examples":[]},"yanked":false} 3.2.0 {"name":"r-efi","vers":"3.2.0","deps":[],"cksum":"6198999a900fd9cf051f2109ec3b9589b5c67cbfc77ff82c9fdff24aec83aa7b","features":{"examples":[]},"yanked":false} 4.0.0 {"name":"r-efi","vers":"4.0.0","deps":[],"cksum":"6c4257a6ffb364d7f938c0311733a793327639b3d34c0b4af0b3bb775ef3e1b8","features":{"examples":[]},"yanked":false} 4.1.0 {"name":"r-efi","vers":"4.1.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"9e7345c622833c6745e7b027a28aa95618813dc1f3c3de396206410267dce6f3","features":{"efiapi":[],"examples":[],"rustc-dep-of-std":["compiler_builtins/rustc-dep-of-std","core"]},"yanked":false} 4.2.0 {"name":"r-efi","vers":"4.2.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"575fc2d9b3da54adbdfaddf6eca48fec256d977c8630a1750b8991347d1ac911","features":{"efiapi":[],"examples":["native"],"native":[],"rustc-dep-of-std":["compiler_builtins/rustc-dep-of-std","core"]},"yanked":false} 4.3.0 {"name":"r-efi","vers":"4.3.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"0e244f96e03a3067f9e521d3167bd42657594cb8588c8d3a2db01545dc1af2e0","features":{"efiapi":[],"examples":["native"],"native":[],"rustc-dep-of-std":["compiler_builtins/rustc-dep-of-std","core"]},"yanked":false,"rust_version":"1.68"} 4.4.0 {"name":"r-efi","vers":"4.4.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"c47196f636c4cc0634b73b0405323d177753c2e15e866952c64ea22902567a34","features":{"efiapi":[],"examples":["native"],"native":[],"rustc-dep-of-std":["compiler_builtins/rustc-dep-of-std","core"]},"yanked":false,"rust_version":"1.68"} 4.5.0 {"name":"r-efi","vers":"4.5.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"e9e935efc5854715dfc0a4c9ef18dc69dee0ec3bf9cc3ab740db831c0fdd86a3","features":{"efiapi":[],"examples":["native"],"native":[],"rustc-dep-of-std":["compiler_builtins/rustc-dep-of-std","core"]},"yanked":false,"rust_version":"1.68"} 5.0.0 {"name":"r-efi","vers":"5.0.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"37bc43dd39c4165ded8ffe15f0e0653ce7b73e86fd032eff18121589cbc648a5","features":{"efiapi":[],"examples":["native"],"native":[],"rustc-dep-of-std":["compiler_builtins/rustc-dep-of-std","core"]},"yanked":false,"rust_version":"1.68"} 5.1.0 {"name":"r-efi","vers":"5.1.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"5a2af567ac9e1e7d2d11097ca14a1e463a5c06a2ee84c23c009b489b00afc1fc","features":{"efiapi":[],"examples":["native"],"native":[],"rustc-dep-of-std":["compiler_builtins/rustc-dep-of-std","core"]},"yanked":false,"rust_version":"1.68"} 5.2.0 {"name":"r-efi","vers":"5.2.0","deps":[{"name":"compiler_builtins","req":"^0.1.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal"},{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"74765f6d916ee2faa39bc8e68e4f3ed8949b48cccdac59983d287a7cb71ce9c5","features":{"efiapi":[],"examples":["native"],"native":[],"rustc-dep-of-std":["compiler_builtins/rustc-dep-of-std","core"]},"yanked":false,"rust_version":"1.68"} 5.3.0 {"name":"r-efi","vers":"5.3.0","deps":[{"name":"core","req":"^1.0.0","features":[],"optional":true,"default_features":true,"target":null,"kind":"normal","package":"rustc-std-workspace-core"}],"cksum":"69cdb34c158ceb288df11e18b4bd39de994f6657d83847bdffdbd7f346754b0f","features":{"efiapi":[],"examples":["native"],"native":[],"rustc-dep-of-std":["core"]},"yanked":false,"rust_version":"1.68"} 