   etag: W/"267ab08bcaf5f380c9191d80e772a5ac" 0.0.0 {"name":"windows-targets","vers":"0.0.0","deps":[],"cksum":"13506c0449adefc2f1017f700af8f48a27741efe2f9a21e98b53c2cabb9ee902","features":{},"yanked":false} 0.42.1 {"name":"windows-targets","vers":"0.42.1","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-msvc","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"aarch64-uwp-windows-msvc","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-gnu","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"i686-uwp-windows-gnu","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-msvc","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"i686-uwp-windows-msvc","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnu","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"x86_64-uwp-windows-gnu","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-msvc","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.42.1","features":[],"optional":false,"default_features":true,"target":"x86_64-uwp-windows-msvc","kind":"normal"}],"cksum":"8e2522491fbfcd58cc84d47aeb2958948c4b8982e9a2d8a2a35bbaed431390e7","features":{},"yanked":false} 0.42.2 {"name":"windows-targets","vers":"0.42.2","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-msvc","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"aarch64-uwp-windows-msvc","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-gnu","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"i686-uwp-windows-gnu","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-msvc","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"i686-uwp-windows-msvc","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnu","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"x86_64-uwp-windows-gnu","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-msvc","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.42.2","features":[],"optional":false,"default_features":true,"target":"x86_64-uwp-windows-msvc","kind":"normal"}],"cksum":"8e5180c00cd44c9b1c88adb3693291f1cd93605ded80c250a75d472756b4d071","features":{},"yanked":false} 0.47.0 {"name":"windows-targets","vers":"0.47.0","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-msvc","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"aarch64-uwp-windows-msvc","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-gnu","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"i686-uwp-windows-gnu","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-msvc","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"i686-uwp-windows-msvc","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnu","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"x86_64-uwp-windows-gnu","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-msvc","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.47.0","features":[],"optional":false,"default_features":true,"target":"x86_64-uwp-windows-msvc","kind":"normal"}],"cksum":"2f8996d3f43b4b2d44327cd71b7b0efd1284ab60e6e9d0e8b630e18555d87d3e","features":{},"yanked":true} 0.48.0 {"name":"windows-targets","vers":"0.48.0","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"gnu\", target_abi = \"llvm\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", target_abi = \"llvm\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"7b1eb6f0cd7c80c79759c929114ef071b87354ce476d9d94271031c0497adfd5","features":{},"yanked":false} 0.48.1 {"name":"windows-targets","vers":"0.48.1","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.48.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"05d4b17490f70499f20b9e791dcf6a299785ce8af4d709018206dc5b4953e95f","features":{},"yanked":false} 0.48.2 {"name":"windows-targets","vers":"0.48.2","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.48.2","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.48.2","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.48.2","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.48.2","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.48.2","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.48.2","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.48.2","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"d1eeca1c172a285ee6c2c84c341ccea837e7c01b12fbb2d0fe3c9e550ce49ec8","features":{},"yanked":false} 0.48.3 {"name":"windows-targets","vers":"0.48.3","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.48.3","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.48.3","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.48.3","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.48.3","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.48.3","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.48.3","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.48.3","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"27f51fb4c64f8b770a823c043c7fad036323e1c48f55287b7bbb7987b2fcdf3b","features":{},"yanked":false} 0.48.4 {"name":"windows-targets","vers":"0.48.4","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.48.4","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.48.4","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.48.4","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.48.4","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.48.4","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.48.4","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.48.4","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"d92ecb8ae0317859f509f17b19adc74b0763b0fa3b085dea8ed01085c8dac222","features":{},"yanked":false} 0.48.5 {"name":"windows-targets","vers":"0.48.5","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.48.5","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.48.5","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.48.5","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.48.5","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.48.5","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.48.5","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.48.5","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"9a2fa6e2155d7247be68c096456083145c183cbbbc2764150dda45a87197940c","features":{},"yanked":false} 0.52.0 {"name":"windows-targets","vers":"0.52.0","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.52.0","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.52.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.52.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.52.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.52.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.52.0","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.52.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"8a18201040b24831fbb9e4eb208f8892e1f50a37feb53cc7ff887feb8f50e7cd","features":{},"yanked":false,"rust_version":"1.56"} 0.52.1 {"name":"windows-targets","vers":"0.52.1","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"56eb995bed789027c5fa9be885994e944989ce9f1b02956b4bf8744c3dbf449b","features":{},"yanked":true,"rust_version":"1.60"} 0.52.2 {"name":"windows-targets","vers":"0.52.2","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.52.1","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"d98532992affa02e52709d5b4d145a3668ae10d9081eea4a7f26f719a8476f71","features":{},"yanked":true,"rust_version":"1.60"} 0.52.3 {"name":"windows-targets","vers":"0.52.3","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.52.3","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.52.3","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.52.3","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.52.3","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.52.3","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.52.3","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.52.3","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"d380ba1dc7187569a8a9e91ed34b8ccfc33123bbacb8c0aed2d1ad7f3ef2dc5f","features":{},"yanked":false,"rust_version":"1.60"} 0.52.4 {"name":"windows-targets","vers":"0.52.4","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.52.4","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.52.4","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.52.4","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.52.4","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.52.4","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.52.4","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.52.4","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"7dd37b7e5ab9018759f893a1952c9420d060016fc19a472b4bb20d1bdd694d1b","features":{},"yanked":false,"rust_version":"1.56"} 0.52.5 {"name":"windows-targets","vers":"0.52.5","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.52.5","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.52.5","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.52.5","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnullvm","req":"^0.52.5","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-gnullvm","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.52.5","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.52.5","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.52.5","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.52.5","features":[],"optional":false,"default_features":true,"target":"cfg(all(any(target_arch = \"x86_64\", target_arch = \"arm64ec\"), target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"6f0713a46559409d202e70e28227288446bf7841d3211583a4b53e3f6d96e7eb","features":{},"yanked":false,"rust_version":"1.56"} 0.52.6 {"name":"windows-targets","vers":"0.52.6","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.52.6","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.52.6","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.52.6","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnullvm","req":"^0.52.6","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-gnullvm","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.52.6","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.52.6","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.52.6","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.52.6","features":[],"optional":false,"default_features":true,"target":"cfg(all(any(target_arch = \"x86_64\", target_arch = \"arm64ec\"), target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"9b724f72796e036ab90c1021d4780d4d3d648aca59e491e6b98e725b84e99973","features":{},"yanked":false,"rust_version":"1.56"} 0.53.0 {"name":"windows-targets","vers":"0.53.0","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-gnullvm","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(any(target_arch = \"x86_64\", target_arch = \"arm64ec\"), target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"b1e4c7e8ceaaf9cb7d7507c974735728ab453b67ef8f18febdd7c11fe59dca8b","features":{},"yanked":false,"rust_version":"1.60"} 0.53.1 {"name":"windows-targets","vers":"0.53.1","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-gnullvm","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(any(target_arch = \"x86_64\", target_arch = \"arm64ec\"), target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"30357ec391cde730f8fbfcdc29adc47518b06504528df977ab5af02ef23fdee9","features":{},"yanked":true,"rust_version":"1.60"} 0.53.2 {"name":"windows-targets","vers":"0.53.2","deps":[{"name":"windows_aarch64_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-gnullvm","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(any(target_arch = \"x86_64\", target_arch = \"arm64ec\"), target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"c66f69fcc9ce11da9966ddb31a40968cad001c5bedeb5c2b82ede4253ab48aef","features":{},"yanked":false,"rust_version":"1.60"} 0.53.3 {"name":"windows-targets","vers":"0.53.3","deps":[{"name":"windows-link","req":"^0.1.3","features":[],"optional":false,"default_features":false,"target":"cfg(windows_raw_dylib)","kind":"normal"},{"name":"windows_aarch64_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"aarch64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_aarch64_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"aarch64\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnu","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_i686_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"i686-pc-windows-gnullvm","kind":"normal"},{"name":"windows_i686_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86\", target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnu","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(target_arch = \"x86_64\", target_env = \"gnu\", not(target_abi = \"llvm\"), not(windows_raw_dylib)))","kind":"normal"},{"name":"windows_x86_64_gnullvm","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"x86_64-pc-windows-gnullvm","kind":"normal"},{"name":"windows_x86_64_msvc","req":"^0.53.0","features":[],"optional":false,"default_features":true,"target":"cfg(all(any(target_arch = \"x86_64\", target_arch = \"arm64ec\"), target_env = \"msvc\", not(windows_raw_dylib)))","kind":"normal"}],"cksum":"d5fe6031c4041849d7c496a8ded650796e7b6ecc19df1a431c1a363342e5dc91","features":{},"yanked":false,"rust_version":"1.60"} 