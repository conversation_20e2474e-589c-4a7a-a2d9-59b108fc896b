[package]
name = "addr2line"
version = "0.24.2"
description = "A cross-platform symbolication library written in Rust, using `gimli`"
documentation = "https://docs.rs/addr2line"
keywords = ["DWARF", "debug", "elf", "symbolicate", "atos"]
categories = ["development-tools::debugging"]
license = "Apache-2.0 OR MIT"
readme = "./README.md"
repository = "https://github.com/gimli-rs/addr2line"
edition = "2018"
rust-version = "1.65"
include = [
    "/CHANGELOG.md",
    "/Cargo.lock",
    "/Cargo.toml",
    "/LICENSE-APACHE",
    "/LICENSE-MIT",
    "/README.md",
    "/src",
]

[dependencies]
gimli = { version = "0.31.1", default-features = false, features = ["read"] }
fallible-iterator = { version = "0.3.0", default-features = false, optional = true }
smallvec = { version = "1", default-features = false, optional = true }
rustc-demangle = { version = "0.1", optional = true }
cpp_demangle = { version = "0.4", default-features = false, features = ["alloc"], optional = true }

# loader dependencies
object = { version = "0.36.0", default-features = false, features = ["read", "compression"], optional = true }
memmap2 = { version = "0.9.4", optional = true }
typed-arena = { version = "2", optional = true }

# bin dependencies
clap = { version = "4.3.21", features = ["wrap_help"], optional = true }

# Internal feature, only used when building as part of libstd, not part of the
# stable interface of this crate.
core = { version = '1.0.0', optional = true, package = 'rustc-std-workspace-core' }
alloc = { version = '1.0.0', optional = true, package = 'rustc-std-workspace-alloc' }
compiler_builtins = { version = '0.1.2', optional = true }

[dev-dependencies]
backtrace = "0.3.13"
findshlibs = "0.10"
libtest-mimic = "0.7.2"
auxiliary = { path = "tests/auxiliary" }

[profile.release]
debug = true

[profile.bench]
debug = true
codegen-units = 1

[features]
default = ["rustc-demangle", "cpp_demangle", "loader", "fallible-iterator", "smallvec"]
std = ["gimli/std"]
loader = ["std", "dep:object", "dep:memmap2", "dep:typed-arena"]
bin = ["loader", "rustc-demangle", "cpp_demangle", "fallible-iterator", "smallvec", "dep:clap"]
all = ["bin"]

# Use of --all-features is not supported.
# This is a dummy feature to detect when --all-features is used.
cargo-all = []

# Internal feature, only used when building as part of libstd, not part of the
# stable interface of this crate.
rustc-dep-of-std = ['core', 'alloc', 'compiler_builtins', 'gimli/rustc-dep-of-std']

[[test]]
name = "testinput"
harness = false
required-features = ["bin"]

[[test]]
name = "correctness"
required-features = ["loader", "fallible-iterator"]

[[test]]
name = "parse"
required-features = ["loader"]

[[bin]]
name = "addr2line"
required-features = ["bin"]
