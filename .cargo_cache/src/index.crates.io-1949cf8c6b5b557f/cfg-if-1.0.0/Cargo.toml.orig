[package]
name = "cfg-if"
version = "1.0.0"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT/Apache-2.0"
readme = "README.md"
repository = "https://github.com/alexcrichton/cfg-if"
homepage = "https://github.com/alexcrichton/cfg-if"
documentation = "https://docs.rs/cfg-if"
description = """
A macro to ergonomically define an item depending on a large number of #[cfg]
parameters. Structured like an if-else chain, the first matching branch is the
item that gets emitted.
"""
edition = "2018"

[badges]
travis-ci = { repository = "alexcrichton/cfg-if" }

[dependencies]
core = { version = "1.0.0", optional = true, package = 'rustc-std-workspace-core' }
compiler_builtins = { version = '0.1.2', optional = true }

[features]
rustc-dep-of-std = ['core', 'compiler_builtins']
