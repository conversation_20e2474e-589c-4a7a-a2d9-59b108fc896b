# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "compiler_builtins"
version = "0.1.158"
authors = ["<PERSON> <<EMAIL>>"]
build = "build.rs"
links = "compiler-rt"
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Compiler intrinsics used by the Rust compiler."
homepage = "https://github.com/rust-lang/compiler-builtins"
documentation = "https://docs.rs/compiler_builtins"
readme = "README.md"
license = "MIT AND Apache-2.0 WITH LLVM-exception AND (MIT OR Apache-2.0)"
repository = "https://github.com/rust-lang/compiler-builtins"

[features]
c = ["dep:cc"]
compiler-builtins = []
default = ["compiler-builtins"]
mangled-names = []
mem = []
no-asm = []
no-f16-f128 = []
rustc-dep-of-std = [
    "compiler-builtins",
    "dep:core",
]
unstable-public-internals = []

[lib]
name = "compiler_builtins"
path = "src/lib.rs"
test = false
doctest = false
bench = false

[dependencies.core]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-core"

[dev-dependencies]

[build-dependencies.cc]
version = "1.0"
optional = true

[lints.rust.unexpected_cfgs]
level = "warn"
priority = 0
check-cfg = [
    "cfg(bootstrap)",
    'cfg(target_os, values("cygwin"))',
]
