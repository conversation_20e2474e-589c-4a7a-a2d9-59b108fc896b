# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies
#
# If you believe there's an error in this file please file an
# issue against the rust-lang/cargo repository. If you're
# editing this file be aware that the upstream Cargo.toml
# will likely look very different (and much more reasonable)

[package]
name = "getopts"
version = "0.2.21"
authors = ["The Rust Project Developers"]
description = "getopts-like option parsing.\n"
homepage = "https://github.com/rust-lang/getopts"
documentation = "https://doc.rust-lang.org/getopts"
readme = "README.md"
categories = ["command-line-interface"]
license = "MIT/Apache-2.0"
repository = "https://github.com/rust-lang/getopts"
[dependencies.core]
version = "1.0"
optional = true
package = "rustc-std-workspace-core"

[dependencies.std]
version = "1.0"
optional = true
package = "rustc-std-workspace-std"

[dependencies.unicode-width]
version = "0.1.5"
[dev-dependencies.log]
version = "0.4"

[features]
rustc-dep-of-std = ["unicode-width/rustc-dep-of-std", "std", "core"]
