[package]

name = "getopts"
version = "0.2.21" # don't forget to update html_root_url
authors = ["The Rust Project Developers"]
license = "MIT/Apache-2.0"
readme = "README.md"
repository = "https://github.com/rust-lang/getopts"
documentation = "https://doc.rust-lang.org/getopts"
homepage = "https://github.com/rust-lang/getopts"
description = """
getopts-like option parsing.
"""
categories = ["command-line-interface"]

[dependencies]
unicode-width = "0.1.5"
std = { version = "1.0", package = "rustc-std-workspace-std", optional = true }
core = { version = "1.0", package = "rustc-std-workspace-core", optional = true }

[dev-dependencies]
log = "0.4"

[features]
rustc-dep-of-std = ['unicode-width/rustc-dep-of-std', 'std', 'core']
