# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.60"
name = "gimli"
version = "0.31.1"
build = false
include = [
    "/CHANGELOG.md",
    "/Cargo.toml",
    "/LICENSE-APACHE",
    "/LICENSE-MIT",
    "/README.md",
    "/src",
]
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A library for reading and writing the DWARF debugging format."
documentation = "https://docs.rs/gimli"
readme = "README.md"
keywords = [
    "DWARF",
    "debug",
    "ELF",
    "eh_frame",
]
categories = [
    "development-tools::debugging",
    "development-tools::profiling",
    "parser-implementations",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/gimli-rs/gimli"
resolver = "2"

[profile.bench]
codegen-units = 1
debug = 2
split-debuginfo = "packed"

[profile.test]
split-debuginfo = "packed"

[lib]
name = "gimli"
path = "src/lib.rs"

[dependencies.alloc]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-alloc"

[dependencies.compiler_builtins]
version = "0.1.2"
optional = true

[dependencies.core]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-core"

[dependencies.fallible-iterator]
version = "0.3.0"
optional = true
default-features = false

[dependencies.indexmap]
version = "2.0.0"
optional = true

[dependencies.stable_deref_trait]
version = "1.1.0"
optional = true
default-features = false

[dev-dependencies.test-assembler]
version = "0.1.3"

[features]
default = [
    "read-all",
    "write",
]
endian-reader = [
    "read",
    "dep:stable_deref_trait",
]
fallible-iterator = ["dep:fallible-iterator"]
read = ["read-core"]
read-all = [
    "read",
    "std",
    "fallible-iterator",
    "endian-reader",
]
read-core = []
rustc-dep-of-std = [
    "dep:core",
    "dep:alloc",
    "dep:compiler_builtins",
]
std = [
    "fallible-iterator?/std",
    "stable_deref_trait?/std",
]
write = ["dep:indexmap"]
