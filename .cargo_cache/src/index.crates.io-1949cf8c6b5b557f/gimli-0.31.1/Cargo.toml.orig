[package]
name = "gimli"
version = "0.31.1"
categories = ["development-tools::debugging", "development-tools::profiling", "parser-implementations"]
description = "A library for reading and writing the DWARF debugging format."
documentation = "https://docs.rs/gimli"
edition = "2018"
include = [
    "/CHANGELOG.md",
    "/Cargo.toml",
    "/LICENSE-APACHE",
    "/LICENSE-MIT",
    "/README.md",
    "/src",
]
keywords = ["DWARF", "debug", "ELF", "eh_frame"]
license = "MIT OR Apache-2.0"
readme = "./README.md"
repository = "https://github.com/gimli-rs/gimli"
rust-version = "1.60"

[dependencies]
fallible-iterator = { version = "0.3.0", default-features = false, optional = true }
indexmap = { version = "2.0.0", optional = true }
stable_deref_trait = { version = "1.1.0", default-features = false, optional = true }

# Internal feature, only used when building as part of libstd, not part of the
# stable interface of this crate.
core = { version = "1.0.0", optional = true, package = "rustc-std-workspace-core" }
alloc = { version = "1.0.0", optional = true, package = "rustc-std-workspace-alloc" }
compiler_builtins = { version = "0.1.2", optional = true }

[dev-dependencies]
test-assembler = "0.1.3"

[features]
read-core = []
read = ["read-core"]
read-all = ["read", "std", "fallible-iterator", "endian-reader"]
endian-reader = ["read", "dep:stable_deref_trait"]
fallible-iterator = ["dep:fallible-iterator"]
write = ["dep:indexmap"]
std = ["fallible-iterator?/std", "stable_deref_trait?/std"]
default = ["read-all", "write"]

# Internal feature, only used when building as part of libstd, not part of the
# stable interface of this crate.
rustc-dep-of-std = ["dep:core", "dep:alloc", "dep:compiler_builtins"]

[profile.test]
split-debuginfo = "packed"

[profile.bench]
debug = true
codegen-units = 1
split-debuginfo = "packed"

[workspace]
members = ["crates/examples"]
default-members = [".", "crates/examples"]
resolver = "2"
