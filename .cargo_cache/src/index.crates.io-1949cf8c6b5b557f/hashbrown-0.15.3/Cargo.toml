# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.65.0"
name = "hashbrown"
version = "0.15.3"
authors = ["<PERSON>ani<PERSON>'Antras <<EMAIL>>"]
build = false
exclude = [
    ".github",
    "/ci/*",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A Rust port of Google's SwissTable hash map"
readme = "README.md"
keywords = [
    "hash",
    "no_std",
    "hashmap",
    "swisstable",
]
categories = [
    "data-structures",
    "no-std",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/rust-lang/hashbrown"

[package.metadata.docs.rs]
features = [
    "nightly",
    "rayon",
    "serde",
    "raw-entry",
]
rustdoc-args = ["--generate-link-to-definition"]

[features]
default = [
    "default-hasher",
    "inline-more",
    "allocator-api2",
    "equivalent",
    "raw-entry",
]
default-hasher = ["dep:foldhash"]
inline-more = []
nightly = ["bumpalo/allocator_api"]
raw-entry = []
rustc-dep-of-std = [
    "nightly",
    "core",
    "compiler_builtins",
    "alloc",
    "rustc-internal-api",
]
rustc-internal-api = []

[lib]
name = "hashbrown"
path = "src/lib.rs"

[[test]]
name = "equivalent_trait"
path = "tests/equivalent_trait.rs"

[[test]]
name = "hasher"
path = "tests/hasher.rs"

[[test]]
name = "rayon"
path = "tests/rayon.rs"

[[test]]
name = "serde"
path = "tests/serde.rs"

[[test]]
name = "set"
path = "tests/set.rs"

[[bench]]
name = "bench"
path = "benches/bench.rs"

[[bench]]
name = "insert_unique_unchecked"
path = "benches/insert_unique_unchecked.rs"

[[bench]]
name = "set_ops"
path = "benches/set_ops.rs"

[dependencies.alloc]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-alloc"

[dependencies.allocator-api2]
version = "0.2.9"
features = ["alloc"]
optional = true
default-features = false

[dependencies.compiler_builtins]
version = "0.1.2"
optional = true

[dependencies.core]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-core"

[dependencies.equivalent]
version = "1.0"
optional = true
default-features = false

[dependencies.foldhash]
version = "0.1.2"
optional = true
default-features = false

[dependencies.rayon]
version = "1.2"
optional = true

[dependencies.serde]
version = "1.0.25"
optional = true
default-features = false

[dev-dependencies.bumpalo]
version = "3.13.0"
features = ["allocator-api2"]

[dev-dependencies.doc-comment]
version = "0.3.1"

[dev-dependencies.fnv]
version = "1.0.7"

[dev-dependencies.lazy_static]
version = "1.4"

[dev-dependencies.rand]
version = "0.9.0"
features = ["small_rng"]

[dev-dependencies.rayon]
version = "1.2"

[dev-dependencies.serde_test]
version = "1.0"
