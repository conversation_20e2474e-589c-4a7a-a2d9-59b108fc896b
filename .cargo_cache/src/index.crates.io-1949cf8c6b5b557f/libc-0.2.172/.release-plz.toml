[workspace]
git_release_name = "{{ version }}"
git_tag_name = "{{ version }}"

[changelog]
body = """
## [{{ version | trim_start_matches(pat="v") }}]\
    {%- if release_link -%}\
        ({{ release_link }})\
    {% endif %} \
    - {{ timestamp | date(format="%Y-%m-%d") }}
{% for group, commits in commits | group_by(attribute="group") %}
### {{ group | upper_first }}
    {% for commit in commits %}
        - {% if commit.scope -%}{{ commit.scope | upper_first }}: {% endif %}
        {%- if commit.breaking %}[**breaking**] {% endif %}
        {{- commit.message }}
        {%- if commit.links %} ([{{ commit.links.1.text }}]({{ commit.links.1.href }})){% endif -%}
    {% endfor %}
{% endfor %}
{%- if github -%}
{% if github.contributors | filter(attribute="is_first_time", value=true) | length != 0 %}
  ## New Contributors ❤️
{% endif %}\
{% for contributor in github.contributors | filter(attribute="is_first_time", value=true) %}
  * @{{ contributor.username }} made their first contribution
    {%- if contributor.pr_number %} in \
      [#{{ contributor.pr_number }}]({{ self::remote_url() }}/pull/{{ contributor.pr_number }}) \
    {%- endif %}
{%- endfor -%}
{%- endif %}
"""

commit_parsers = [
  { message = '(?i)^(\w+: )?feat', group = "added" },
  { message = '(?i)^(\w+: )?add', group = "added" },
  { message = '(?i)^(\w+: )?change', group = "changed" },
  { message = '(?i)^(\w+: )?cleanup', group = "cleanup" },
  { message = '(?i)^(\w+: )?deprecate', group = "deprecated" },
  { message = '(?i)^(\w+: )?remove', group = "removed" },
  { message = '(?i)^(\w+: )?fix', group = "fixed" },
  { message = '(?i)^(\w+: )?fix', group = "fixed" },
  { message = '^.*', group = "other" },
]

link_parsers = [
    # Extract backport patterns
    { pattern = '\(backport <.*/(\d+)>\)', text = "#$1", href = "https://github.com/rust-lang/libc/pull/$1"}
]
