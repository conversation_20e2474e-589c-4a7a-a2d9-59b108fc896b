[package]
name = "libc"
version = "0.2.172"
authors = ["The Rust Project Developers"]
license = "MIT OR Apache-2.0"
readme = "README.md"
edition = "2021"
repository = "https://github.com/rust-lang/libc"
homepage = "https://github.com/rust-lang/libc"
documentation = "https://docs.rs/libc/"
keywords = ["libc", "ffi", "bindings", "operating", "system"]
categories = ["external-ffi-bindings", "no-std", "os"]
build = "build.rs"
exclude = ["/ci/*", "/.github/*", "/.cirrus.yml", "/triagebot.toml"]
rust-version = "1.63"
description = """
Raw FFI bindings to platform libraries like libc.
"""

[package.metadata.docs.rs]
features = ["extra_traits"]
default-target = "x86_64-unknown-linux-gnu"
targets = [
    "aarch64-apple-darwin",
    "aarch64-apple-ios",
    "aarch64-linux-android",
    "aarch64-pc-windows-msvc",
    "aarch64-unknown-freebsd",
    "aarch64-unknown-fuchsia",
    "aarch64-unknown-hermit",
    "aarch64-unknown-linux-gnu",
    "aarch64-unknown-linux-musl",
    "aarch64-unknown-netbsd",
    "aarch64-unknown-openbsd",
    "aarch64-wrs-vxworks",
    "arm-linux-androideabi",
    "arm-unknown-linux-gnueabi",
    "arm-unknown-linux-gnueabihf",
    "arm-unknown-linux-musleabi",
    "arm-unknown-linux-musleabihf",
    "armebv7r-none-eabi",
    "armebv7r-none-eabihf",
    "armv5te-unknown-linux-gnueabi",
    "armv5te-unknown-linux-musleabi",
    "armv7-linux-androideabi",
    "armv7-unknown-linux-gnueabihf",
    "armv7-unknown-linux-musleabihf",
    "armv7-wrs-vxworks-eabihf",
    "armv7r-none-eabi",
    "armv7r-none-eabihf",
    # FIXME(hexagon): excluded due to duplicate symbol errors
    # "hexagon-unknown-linux-musl",
    "i586-unknown-linux-gnu",
    "i586-unknown-linux-musl",
    "i686-linux-android",
    "i686-pc-windows-gnu",
    "i686-pc-windows-msvc",
    "i686-pc-windows-msvc",
    "i686-unknown-freebsd",
    "i686-unknown-haiku",
    "i686-unknown-linux-gnu",
    "i686-unknown-linux-musl",
    "i686-unknown-netbsd",
    "i686-unknown-openbsd",
    "i686-wrs-vxworks",
    "mips-unknown-linux-gnu",
    "mips-unknown-linux-musl",
    "mips64-unknown-linux-gnuabi64",
    "mips64-unknown-linux-muslabi64",
    "mips64el-unknown-linux-gnuabi64",
    "mips64el-unknown-linux-muslabi64",
    "mipsel-sony-psp",
    "mipsel-unknown-linux-gnu",
    "mipsel-unknown-linux-musl",
    "nvptx64-nvidia-cuda",
    "powerpc-unknown-linux-gnu",
    "powerpc-unknown-linux-gnuspe",
    "powerpc-unknown-netbsd",
    "powerpc-wrs-vxworks",
    "powerpc-wrs-vxworks-spe",
    "powerpc64-unknown-freebsd",
    "powerpc64-unknown-linux-gnu",
    "powerpc64-wrs-vxworks",
    "powerpc64le-unknown-linux-gnu",
    "riscv32gc-unknown-linux-gnu",
    "riscv32i-unknown-none-elf",
    "riscv32imac-unknown-none-elf",
    "riscv32imc-unknown-none-elf",
    "riscv32-wrs-vxworks",
    "riscv64gc-unknown-freebsd",
    "riscv64gc-unknown-hermit",
    "riscv64gc-unknown-linux-gnu",
    "riscv64gc-unknown-linux-musl",
    "riscv64gc-unknown-none-elf",
    "riscv64imac-unknown-none-elf",
    "riscv64-wrs-vxworks",
    "s390x-unknown-linux-gnu",
    "s390x-unknown-linux-musl",
    "sparc-unknown-linux-gnu",
    "sparc64-unknown-linux-gnu",
    "sparc64-unknown-netbsd",
    "sparcv9-sun-solaris",
    "thumbv6m-none-eabi",
    "thumbv7em-none-eabi",
    "thumbv7em-none-eabihf",
    "thumbv7m-none-eabi",
    "thumbv7neon-linux-androideabi",
    "thumbv7neon-unknown-linux-gnueabihf",
    "wasm32-unknown-emscripten",
    "wasm32-unknown-unknown",
    "x86_64-apple-darwin",
    "x86_64-apple-ios",
    "x86_64-fortanix-unknown-sgx",
    "x86_64-linux-android",
    "x86_64-pc-solaris",
    "x86_64-pc-windows-gnu",
    "x86_64-pc-windows-msvc",
    "x86_64-unknown-dragonfly",
    "x86_64-unknown-freebsd",
    "x86_64-unknown-fuchsia",
    "x86_64-unknown-haiku",
    "x86_64-unknown-hermit",
    "x86_64-unknown-illumos",
    "x86_64-unknown-l4re-uclibc",
    "x86_64-unknown-linux-gnu",
    "x86_64-unknown-linux-gnux32",
    "x86_64-unknown-linux-musl",
    "x86_64-unknown-netbsd",
    "x86_64-unknown-openbsd",
    "x86_64-unknown-redox",
    "x86_64-wrs-vxworks"
]
cargo-args = ["-Zbuild-std=core"]

[dependencies]
rustc-std-workspace-core = { version = "1.0.0", optional = true }

[features]
default = ["std"]
std = []
rustc-dep-of-std = ['align', 'rustc-std-workspace-core']
extra_traits = []

# `const-extern-function` is deprecated and no longer does anything
const-extern-fn = []

# `align` is deprecated and no longer does anything
align = []

# use_std is deprecated, use `std` instead
use_std = ['std']

[workspace]
members = [
    "ctest",
    "libc-test",
]
