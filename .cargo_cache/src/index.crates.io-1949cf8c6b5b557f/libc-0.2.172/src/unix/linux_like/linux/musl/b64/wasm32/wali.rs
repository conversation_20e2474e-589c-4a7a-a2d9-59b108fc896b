//! WebAssembly Linux Interface syscall specification

// --- Autogenerated from WALI/scripts/autogen.py ---
#[link(wasm_import_module = "wali")]
extern "C" {
    /* 0 */
    #[link_name = "SYS_read"]
    pub fn __syscall_SYS_read(a1: i32, a2: i32, a3: u32) -> ::c_long;
    /* 1 */
    #[link_name = "SYS_write"]
    pub fn __syscall_SYS_write(a1: i32, a2: i32, a3: u32) -> ::c_long;
    /* 2 */
    #[link_name = "SYS_open"]
    pub fn __syscall_SYS_open(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 3 */
    #[link_name = "SYS_close"]
    pub fn __syscall_SYS_close(a1: i32) -> ::c_long;
    /* 4 */
    #[link_name = "SYS_stat"]
    pub fn __syscall_SYS_stat(a1: i32, a2: i32) -> ::c_long;
    /* 5 */
    #[link_name = "SYS_fstat"]
    pub fn __syscall_SYS_fstat(a1: i32, a2: i32) -> ::c_long;
    /* 6 */
    #[link_name = "SYS_lstat"]
    pub fn __syscall_SYS_lstat(a1: i32, a2: i32) -> ::c_long;
    /* 7 */
    #[link_name = "SYS_poll"]
    pub fn __syscall_SYS_poll(a1: i32, a2: u32, a3: i32) -> ::c_long;
    /* 8 */
    #[link_name = "SYS_lseek"]
    pub fn __syscall_SYS_lseek(a1: i32, a2: i64, a3: i32) -> ::c_long;
    /* 9 */
    #[link_name = "SYS_mmap"]
    pub fn __syscall_SYS_mmap(a1: i32, a2: u32, a3: i32, a4: i32, a5: i32, a6: i64) -> ::c_long;
    /* 10 */
    #[link_name = "SYS_mprotect"]
    pub fn __syscall_SYS_mprotect(a1: i32, a2: u32, a3: i32) -> ::c_long;
    /* 11 */
    #[link_name = "SYS_munmap"]
    pub fn __syscall_SYS_munmap(a1: i32, a2: u32) -> ::c_long;
    /* 12 */
    #[link_name = "SYS_brk"]
    pub fn __syscall_SYS_brk(a1: i32) -> ::c_long;
    /* 13 */
    #[link_name = "SYS_rt_sigaction"]
    pub fn __syscall_SYS_rt_sigaction(a1: i32, a2: i32, a3: i32, a4: u32) -> ::c_long;
    /* 14 */
    #[link_name = "SYS_rt_sigprocmask"]
    pub fn __syscall_SYS_rt_sigprocmask(a1: i32, a2: i32, a3: i32, a4: u32) -> ::c_long;
    /* 15 */
    #[link_name = "SYS_rt_sigreturn"]
    pub fn __syscall_SYS_rt_sigreturn(a1: i64) -> ::c_long;
    /* 16 */
    #[link_name = "SYS_ioctl"]
    pub fn __syscall_SYS_ioctl(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 17 */
    #[link_name = "SYS_pread64"]
    pub fn __syscall_SYS_pread64(a1: i32, a2: i32, a3: u32, a4: i64) -> ::c_long;
    /* 18 */
    #[link_name = "SYS_pwrite64"]
    pub fn __syscall_SYS_pwrite64(a1: i32, a2: i32, a3: u32, a4: i64) -> ::c_long;
    /* 19 */
    #[link_name = "SYS_readv"]
    pub fn __syscall_SYS_readv(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 20 */
    #[link_name = "SYS_writev"]
    pub fn __syscall_SYS_writev(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 21 */
    #[link_name = "SYS_access"]
    pub fn __syscall_SYS_access(a1: i32, a2: i32) -> ::c_long;
    /* 22 */
    #[link_name = "SYS_pipe"]
    pub fn __syscall_SYS_pipe(a1: i32) -> ::c_long;
    /* 23 */
    #[link_name = "SYS_select"]
    pub fn __syscall_SYS_select(a1: i32, a2: i32, a3: i32, a4: i32, a5: i32) -> ::c_long;
    /* 24 */
    #[link_name = "SYS_sched_yield"]
    pub fn __syscall_SYS_sched_yield() -> ::c_long;
    /* 25 */
    #[link_name = "SYS_mremap"]
    pub fn __syscall_SYS_mremap(a1: i32, a2: u32, a3: u32, a4: i32, a5: i32) -> ::c_long;
    /* 26 */
    #[link_name = "SYS_msync"]
    pub fn __syscall_SYS_msync(a1: i32, a2: u32, a3: i32) -> ::c_long;
    /* 28 */
    #[link_name = "SYS_madvise"]
    pub fn __syscall_SYS_madvise(a1: i32, a2: u32, a3: i32) -> ::c_long;
    /* 32 */
    #[link_name = "SYS_dup"]
    pub fn __syscall_SYS_dup(a1: i32) -> ::c_long;
    /* 33 */
    #[link_name = "SYS_dup2"]
    pub fn __syscall_SYS_dup2(a1: i32, a2: i32) -> ::c_long;
    /* 35 */
    #[link_name = "SYS_nanosleep"]
    pub fn __syscall_SYS_nanosleep(a1: i32, a2: i32) -> ::c_long;
    /* 37 */
    #[link_name = "SYS_alarm"]
    pub fn __syscall_SYS_alarm(a1: i32) -> ::c_long;
    /* 38 */
    #[link_name = "SYS_setitimer"]
    pub fn __syscall_SYS_setitimer(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 39 */
    #[link_name = "SYS_getpid"]
    pub fn __syscall_SYS_getpid() -> ::c_long;
    /* 41 */
    #[link_name = "SYS_socket"]
    pub fn __syscall_SYS_socket(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 42 */
    #[link_name = "SYS_connect"]
    pub fn __syscall_SYS_connect(a1: i32, a2: i32, a3: u32) -> ::c_long;
    /* 43 */
    #[link_name = "SYS_accept"]
    pub fn __syscall_SYS_accept(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 44 */
    #[link_name = "SYS_sendto"]
    pub fn __syscall_SYS_sendto(a1: i32, a2: i32, a3: u32, a4: i32, a5: i32, a6: u32) -> ::c_long;
    /* 45 */
    #[link_name = "SYS_recvfrom"]
    pub fn __syscall_SYS_recvfrom(a1: i32, a2: i32, a3: u32, a4: i32, a5: i32, a6: i32)
        -> ::c_long;
    /* 46 */
    #[link_name = "SYS_sendmsg"]
    pub fn __syscall_SYS_sendmsg(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 47 */
    #[link_name = "SYS_recvmsg"]
    pub fn __syscall_SYS_recvmsg(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 48 */
    #[link_name = "SYS_shutdown"]
    pub fn __syscall_SYS_shutdown(a1: i32, a2: i32) -> ::c_long;
    /* 49 */
    #[link_name = "SYS_bind"]
    pub fn __syscall_SYS_bind(a1: i32, a2: i32, a3: u32) -> ::c_long;
    /* 50 */
    #[link_name = "SYS_listen"]
    pub fn __syscall_SYS_listen(a1: i32, a2: i32) -> ::c_long;
    /* 51 */
    #[link_name = "SYS_getsockname"]
    pub fn __syscall_SYS_getsockname(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 52 */
    #[link_name = "SYS_getpeername"]
    pub fn __syscall_SYS_getpeername(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 53 */
    #[link_name = "SYS_socketpair"]
    pub fn __syscall_SYS_socketpair(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
    /* 54 */
    #[link_name = "SYS_setsockopt"]
    pub fn __syscall_SYS_setsockopt(a1: i32, a2: i32, a3: i32, a4: i32, a5: u32) -> ::c_long;
    /* 55 */
    #[link_name = "SYS_getsockopt"]
    pub fn __syscall_SYS_getsockopt(a1: i32, a2: i32, a3: i32, a4: i32, a5: i32) -> ::c_long;
    /* 57 */
    #[link_name = "SYS_fork"]
    pub fn __syscall_SYS_fork() -> ::c_long;
    /* 59 */
    #[link_name = "SYS_execve"]
    pub fn __syscall_SYS_execve(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 60 */
    #[link_name = "SYS_exit"]
    pub fn __syscall_SYS_exit(a1: i32) -> ::c_long;
    /* 61 */
    #[link_name = "SYS_wait4"]
    pub fn __syscall_SYS_wait4(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
    /* 62 */
    #[link_name = "SYS_kill"]
    pub fn __syscall_SYS_kill(a1: i32, a2: i32) -> ::c_long;
    /* 63 */
    #[link_name = "SYS_uname"]
    pub fn __syscall_SYS_uname(a1: i32) -> ::c_long;
    /* 72 */
    #[link_name = "SYS_fcntl"]
    pub fn __syscall_SYS_fcntl(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 73 */
    #[link_name = "SYS_flock"]
    pub fn __syscall_SYS_flock(a1: i32, a2: i32) -> ::c_long;
    /* 74 */
    #[link_name = "SYS_fsync"]
    pub fn __syscall_SYS_fsync(a1: i32) -> ::c_long;
    /* 75 */
    #[link_name = "SYS_fdatasync"]
    pub fn __syscall_SYS_fdatasync(a1: i32) -> ::c_long;
    /* 77 */
    #[link_name = "SYS_ftruncate"]
    pub fn __syscall_SYS_ftruncate(a1: i32, a2: i64) -> ::c_long;
    /* 78 */
    #[link_name = "SYS_getdents"]
    pub fn __syscall_SYS_getdents(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 79 */
    #[link_name = "SYS_getcwd"]
    pub fn __syscall_SYS_getcwd(a1: i32, a2: u32) -> ::c_long;
    /* 80 */
    #[link_name = "SYS_chdir"]
    pub fn __syscall_SYS_chdir(a1: i32) -> ::c_long;
    /* 81 */
    #[link_name = "SYS_fchdir"]
    pub fn __syscall_SYS_fchdir(a1: i32) -> ::c_long;
    /* 82 */
    #[link_name = "SYS_rename"]
    pub fn __syscall_SYS_rename(a1: i32, a2: i32) -> ::c_long;
    /* 83 */
    #[link_name = "SYS_mkdir"]
    pub fn __syscall_SYS_mkdir(a1: i32, a2: i32) -> ::c_long;
    /* 84 */
    #[link_name = "SYS_rmdir"]
    pub fn __syscall_SYS_rmdir(a1: i32) -> ::c_long;
    /* 86 */
    #[link_name = "SYS_link"]
    pub fn __syscall_SYS_link(a1: i32, a2: i32) -> ::c_long;
    /* 87 */
    #[link_name = "SYS_unlink"]
    pub fn __syscall_SYS_unlink(a1: i32) -> ::c_long;
    /* 88 */
    #[link_name = "SYS_symlink"]
    pub fn __syscall_SYS_symlink(a1: i32, a2: i32) -> ::c_long;
    /* 89 */
    #[link_name = "SYS_readlink"]
    pub fn __syscall_SYS_readlink(a1: i32, a2: i32, a3: u32) -> ::c_long;
    /* 90 */
    #[link_name = "SYS_chmod"]
    pub fn __syscall_SYS_chmod(a1: i32, a2: i32) -> ::c_long;
    /* 91 */
    #[link_name = "SYS_fchmod"]
    pub fn __syscall_SYS_fchmod(a1: i32, a2: i32) -> ::c_long;
    /* 92 */
    #[link_name = "SYS_chown"]
    pub fn __syscall_SYS_chown(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 93 */
    #[link_name = "SYS_fchown"]
    pub fn __syscall_SYS_fchown(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 95 */
    #[link_name = "SYS_umask"]
    pub fn __syscall_SYS_umask(a1: i32) -> ::c_long;
    /* 97 */
    #[link_name = "SYS_getrlimit"]
    pub fn __syscall_SYS_getrlimit(a1: i32, a2: i32) -> ::c_long;
    /* 98 */
    #[link_name = "SYS_getrusage"]
    pub fn __syscall_SYS_getrusage(a1: i32, a2: i32) -> ::c_long;
    /* 99 */
    #[link_name = "SYS_sysinfo"]
    pub fn __syscall_SYS_sysinfo(a1: i32) -> ::c_long;
    /* 102 */
    #[link_name = "SYS_getuid"]
    pub fn __syscall_SYS_getuid() -> ::c_long;
    /* 104 */
    #[link_name = "SYS_getgid"]
    pub fn __syscall_SYS_getgid() -> ::c_long;
    /* 105 */
    #[link_name = "SYS_setuid"]
    pub fn __syscall_SYS_setuid(a1: i32) -> ::c_long;
    /* 106 */
    #[link_name = "SYS_setgid"]
    pub fn __syscall_SYS_setgid(a1: i32) -> ::c_long;
    /* 107 */
    #[link_name = "SYS_geteuid"]
    pub fn __syscall_SYS_geteuid() -> ::c_long;
    /* 108 */
    #[link_name = "SYS_getegid"]
    pub fn __syscall_SYS_getegid() -> ::c_long;
    /* 109 */
    #[link_name = "SYS_setpgid"]
    pub fn __syscall_SYS_setpgid(a1: i32, a2: i32) -> ::c_long;
    /* 110 */
    #[link_name = "SYS_getppid"]
    pub fn __syscall_SYS_getppid() -> ::c_long;
    /* 112 */
    #[link_name = "SYS_setsid"]
    pub fn __syscall_SYS_setsid() -> ::c_long;
    /* 113 */
    #[link_name = "SYS_setreuid"]
    pub fn __syscall_SYS_setreuid(a1: i32, a2: i32) -> ::c_long;
    /* 114 */
    #[link_name = "SYS_setregid"]
    pub fn __syscall_SYS_setregid(a1: i32, a2: i32) -> ::c_long;
    /* 115 */
    #[link_name = "SYS_getgroups"]
    pub fn __syscall_SYS_getgroups(a1: u32, a2: i32) -> ::c_long;
    /* 116 */
    #[link_name = "SYS_setgroups"]
    pub fn __syscall_SYS_setgroups(a1: u32, a2: i32) -> ::c_long;
    /* 117 */
    #[link_name = "SYS_setresuid"]
    pub fn __syscall_SYS_setresuid(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 119 */
    #[link_name = "SYS_setresgid"]
    pub fn __syscall_SYS_setresgid(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 121 */
    #[link_name = "SYS_getpgid"]
    pub fn __syscall_SYS_getpgid(a1: i32) -> ::c_long;
    /* 124 */
    #[link_name = "SYS_getsid"]
    pub fn __syscall_SYS_getsid(a1: i32) -> ::c_long;
    /* 127 */
    #[link_name = "SYS_rt_sigpending"]
    pub fn __syscall_SYS_rt_sigpending(a1: i32, a2: u32) -> ::c_long;
    /* 130 */
    #[link_name = "SYS_rt_sigsuspend"]
    pub fn __syscall_SYS_rt_sigsuspend(a1: i32, a2: u32) -> ::c_long;
    /* 131 */
    #[link_name = "SYS_sigaltstack"]
    pub fn __syscall_SYS_sigaltstack(a1: i32, a2: i32) -> ::c_long;
    /* 132 */
    #[link_name = "SYS_utime"]
    pub fn __syscall_SYS_utime(a1: i32, a2: i32) -> ::c_long;
    /* 137 */
    #[link_name = "SYS_statfs"]
    pub fn __syscall_SYS_statfs(a1: i32, a2: i32) -> ::c_long;
    /* 138 */
    #[link_name = "SYS_fstatfs"]
    pub fn __syscall_SYS_fstatfs(a1: i32, a2: i32) -> ::c_long;
    /* 157 */
    #[link_name = "SYS_prctl"]
    pub fn __syscall_SYS_prctl(a1: i32, a2: u64, a3: u64, a4: u64, a5: u64) -> ::c_long;
    /* 160 */
    #[link_name = "SYS_setrlimit"]
    pub fn __syscall_SYS_setrlimit(a1: i32, a2: i32) -> ::c_long;
    /* 161 */
    #[link_name = "SYS_chroot"]
    pub fn __syscall_SYS_chroot(a1: i32) -> ::c_long;
    /* 186 */
    #[link_name = "SYS_gettid"]
    pub fn __syscall_SYS_gettid() -> ::c_long;
    /* 200 */
    #[link_name = "SYS_tkill"]
    pub fn __syscall_SYS_tkill(a1: i32, a2: i32) -> ::c_long;
    /* 202 */
    #[link_name = "SYS_futex"]
    pub fn __syscall_SYS_futex(a1: i32, a2: i32, a3: i32, a4: i32, a5: i32, a6: i32) -> ::c_long;
    /* 204 */
    #[link_name = "SYS_sched_getaffinity"]
    pub fn __syscall_SYS_sched_getaffinity(a1: i32, a2: u32, a3: i32) -> ::c_long;
    /* 217 */
    #[link_name = "SYS_getdents64"]
    pub fn __syscall_SYS_getdents64(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 218 */
    #[link_name = "SYS_set_tid_address"]
    pub fn __syscall_SYS_set_tid_address(a1: i32) -> ::c_long;
    /* 221 */
    #[link_name = "SYS_fadvise"]
    pub fn __syscall_SYS_fadvise(a1: i32, a2: i64, a3: i64, a4: i32) -> ::c_long;
    /* 228 */
    #[link_name = "SYS_clock_gettime"]
    pub fn __syscall_SYS_clock_gettime(a1: i32, a2: i32) -> ::c_long;
    /* 229 */
    #[link_name = "SYS_clock_getres"]
    pub fn __syscall_SYS_clock_getres(a1: i32, a2: i32) -> ::c_long;
    /* 230 */
    #[link_name = "SYS_clock_nanosleep"]
    pub fn __syscall_SYS_clock_nanosleep(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
    /* 231 */
    #[link_name = "SYS_exit_group"]
    pub fn __syscall_SYS_exit_group(a1: i32) -> ::c_long;
    /* 233 */
    #[link_name = "SYS_epoll_ctl"]
    pub fn __syscall_SYS_epoll_ctl(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
    /* 257 */
    #[link_name = "SYS_openat"]
    pub fn __syscall_SYS_openat(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
    /* 258 */
    #[link_name = "SYS_mkdirat"]
    pub fn __syscall_SYS_mkdirat(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 260 */
    #[link_name = "SYS_fchownat"]
    pub fn __syscall_SYS_fchownat(a1: i32, a2: i32, a3: i32, a4: i32, a5: i32) -> ::c_long;
    /* 262 */
    #[link_name = "SYS_fstatat"]
    pub fn __syscall_SYS_fstatat(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
    /* 263 */
    #[link_name = "SYS_unlinkat"]
    pub fn __syscall_SYS_unlinkat(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 265 */
    #[link_name = "SYS_linkat"]
    pub fn __syscall_SYS_linkat(a1: i32, a2: i32, a3: i32, a4: i32, a5: i32) -> ::c_long;
    /* 266 */
    #[link_name = "SYS_symlinkat"]
    pub fn __syscall_SYS_symlinkat(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 267 */
    #[link_name = "SYS_readlinkat"]
    pub fn __syscall_SYS_readlinkat(a1: i32, a2: i32, a3: i32, a4: u32) -> ::c_long;
    /* 268 */
    #[link_name = "SYS_fchmodat"]
    pub fn __syscall_SYS_fchmodat(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
    /* 269 */
    #[link_name = "SYS_faccessat"]
    pub fn __syscall_SYS_faccessat(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
    /* 270 */
    #[link_name = "SYS_pselect6"]
    pub fn __syscall_SYS_pselect6(a1: i32, a2: i32, a3: i32, a4: i32, a5: i32, a6: i32)
        -> ::c_long;
    /* 271 */
    #[link_name = "SYS_ppoll"]
    pub fn __syscall_SYS_ppoll(a1: i32, a2: u32, a3: i32, a4: i32, a5: u32) -> ::c_long;
    /* 280 */
    #[link_name = "SYS_utimensat"]
    pub fn __syscall_SYS_utimensat(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
    /* 281 */
    #[link_name = "SYS_epoll_pwait"]
    pub fn __syscall_SYS_epoll_pwait(
        a1: i32,
        a2: i32,
        a3: i32,
        a4: i32,
        a5: i32,
        a6: u32,
    ) -> ::c_long;
    /* 284 */
    #[link_name = "SYS_eventfd"]
    pub fn __syscall_SYS_eventfd(a1: i32) -> ::c_long;
    /* 288 */
    #[link_name = "SYS_accept4"]
    pub fn __syscall_SYS_accept4(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
    /* 290 */
    #[link_name = "SYS_eventfd2"]
    pub fn __syscall_SYS_eventfd2(a1: i32, a2: i32) -> ::c_long;
    /* 291 */
    #[link_name = "SYS_epoll_create1"]
    pub fn __syscall_SYS_epoll_create1(a1: i32) -> ::c_long;
    /* 292 */
    #[link_name = "SYS_dup3"]
    pub fn __syscall_SYS_dup3(a1: i32, a2: i32, a3: i32) -> ::c_long;
    /* 293 */
    #[link_name = "SYS_pipe2"]
    pub fn __syscall_SYS_pipe2(a1: i32, a2: i32) -> ::c_long;
    /* 302 */
    #[link_name = "SYS_prlimit64"]
    pub fn __syscall_SYS_prlimit64(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
    /* 316 */
    #[link_name = "SYS_renameat2"]
    pub fn __syscall_SYS_renameat2(a1: i32, a2: i32, a3: i32, a4: i32, a5: i32) -> ::c_long;
    /* 318 */
    #[link_name = "SYS_getrandom"]
    pub fn __syscall_SYS_getrandom(a1: i32, a2: u32, a3: i32) -> ::c_long;
    /* 332 */
    #[link_name = "SYS_statx"]
    pub fn __syscall_SYS_statx(a1: i32, a2: i32, a3: i32, a4: i32, a5: i32) -> ::c_long;
    /* 439 */
    #[link_name = "SYS_faccessat2"]
    pub fn __syscall_SYS_faccessat2(a1: i32, a2: i32, a3: i32, a4: i32) -> ::c_long;
}
