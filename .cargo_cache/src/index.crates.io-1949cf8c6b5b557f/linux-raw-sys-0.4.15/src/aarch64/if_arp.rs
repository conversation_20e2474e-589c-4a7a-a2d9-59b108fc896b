/* automatically generated by rust-bindgen 0.70.1 */

pub type __s8 = crate::ctypes::c_schar;
pub type __u8 = crate::ctypes::c_uchar;
pub type __s16 = crate::ctypes::c_short;
pub type __u16 = crate::ctypes::c_ushort;
pub type __s32 = crate::ctypes::c_int;
pub type __u32 = crate::ctypes::c_uint;
pub type __s64 = crate::ctypes::c_longlong;
pub type __u64 = crate::ctypes::c_ulonglong;
pub type __kernel_key_t = crate::ctypes::c_int;
pub type __kernel_mqd_t = crate::ctypes::c_int;
pub type __kernel_old_uid_t = crate::ctypes::c_ushort;
pub type __kernel_old_gid_t = crate::ctypes::c_ushort;
pub type __kernel_long_t = crate::ctypes::c_long;
pub type __kernel_ulong_t = crate::ctypes::c_ulong;
pub type __kernel_ino_t = __kernel_ulong_t;
pub type __kernel_mode_t = crate::ctypes::c_uint;
pub type __kernel_pid_t = crate::ctypes::c_int;
pub type __kernel_ipc_pid_t = crate::ctypes::c_int;
pub type __kernel_uid_t = crate::ctypes::c_uint;
pub type __kernel_gid_t = crate::ctypes::c_uint;
pub type __kernel_suseconds_t = __kernel_long_t;
pub type __kernel_daddr_t = crate::ctypes::c_int;
pub type __kernel_uid32_t = crate::ctypes::c_uint;
pub type __kernel_gid32_t = crate::ctypes::c_uint;
pub type __kernel_old_dev_t = crate::ctypes::c_uint;
pub type __kernel_size_t = __kernel_ulong_t;
pub type __kernel_ssize_t = __kernel_long_t;
pub type __kernel_ptrdiff_t = __kernel_long_t;
pub type __kernel_off_t = __kernel_long_t;
pub type __kernel_loff_t = crate::ctypes::c_longlong;
pub type __kernel_old_time_t = __kernel_long_t;
pub type __kernel_time_t = __kernel_long_t;
pub type __kernel_time64_t = crate::ctypes::c_longlong;
pub type __kernel_clock_t = __kernel_long_t;
pub type __kernel_timer_t = crate::ctypes::c_int;
pub type __kernel_clockid_t = crate::ctypes::c_int;
pub type __kernel_caddr_t = *mut crate::ctypes::c_char;
pub type __kernel_uid16_t = crate::ctypes::c_ushort;
pub type __kernel_gid16_t = crate::ctypes::c_ushort;
pub type __le16 = __u16;
pub type __be16 = __u16;
pub type __le32 = __u32;
pub type __be32 = __u32;
pub type __le64 = __u64;
pub type __be64 = __u64;
pub type __sum16 = __u16;
pub type __wsum = __u32;
pub type __poll_t = crate::ctypes::c_uint;
pub type __kernel_sa_family_t = crate::ctypes::c_ushort;
#[repr(C)]
#[derive(Copy, Clone)]
pub struct __kernel_sockaddr_storage {
pub __bindgen_anon_1: __kernel_sockaddr_storage__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_sockaddr_storage__bindgen_ty_1__bindgen_ty_1 {
pub ss_family: __kernel_sa_family_t,
pub __data: [crate::ctypes::c_char; 126usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct sockaddr {
pub __storage: __kernel_sockaddr_storage,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sync_serial_settings {
pub clock_rate: crate::ctypes::c_uint,
pub clock_type: crate::ctypes::c_uint,
pub loopback: crate::ctypes::c_ushort,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct te1_settings {
pub clock_rate: crate::ctypes::c_uint,
pub clock_type: crate::ctypes::c_uint,
pub loopback: crate::ctypes::c_ushort,
pub slot_map: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct raw_hdlc_proto {
pub encoding: crate::ctypes::c_ushort,
pub parity: crate::ctypes::c_ushort,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fr_proto {
pub t391: crate::ctypes::c_uint,
pub t392: crate::ctypes::c_uint,
pub n391: crate::ctypes::c_uint,
pub n392: crate::ctypes::c_uint,
pub n393: crate::ctypes::c_uint,
pub lmi: crate::ctypes::c_ushort,
pub dce: crate::ctypes::c_ushort,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fr_proto_pvc {
pub dlci: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fr_proto_pvc_info {
pub dlci: crate::ctypes::c_uint,
pub master: [crate::ctypes::c_char; 16usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct cisco_proto {
pub interval: crate::ctypes::c_uint,
pub timeout: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct x25_hdlc_proto {
pub dce: crate::ctypes::c_ushort,
pub modulo: crate::ctypes::c_uint,
pub window: crate::ctypes::c_uint,
pub t1: crate::ctypes::c_uint,
pub t2: crate::ctypes::c_uint,
pub n2: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifmap {
pub mem_start: crate::ctypes::c_ulong,
pub mem_end: crate::ctypes::c_ulong,
pub base_addr: crate::ctypes::c_ushort,
pub irq: crate::ctypes::c_uchar,
pub dma: crate::ctypes::c_uchar,
pub port: crate::ctypes::c_uchar,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct if_settings {
pub type_: crate::ctypes::c_uint,
pub size: crate::ctypes::c_uint,
pub ifs_ifsu: if_settings__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct ifreq {
pub ifr_ifrn: ifreq__bindgen_ty_1,
pub ifr_ifru: ifreq__bindgen_ty_2,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct ifconf {
pub ifc_len: crate::ctypes::c_int,
pub ifc_ifcu: ifconf__bindgen_ty_1,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct ethhdr {
pub h_dest: [crate::ctypes::c_uchar; 6usize],
pub h_source: [crate::ctypes::c_uchar; 6usize],
pub h_proto: __be16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sockaddr_pkt {
pub spkt_family: crate::ctypes::c_ushort,
pub spkt_device: [crate::ctypes::c_uchar; 14usize],
pub spkt_protocol: __be16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sockaddr_ll {
pub sll_family: crate::ctypes::c_ushort,
pub sll_protocol: __be16,
pub sll_ifindex: crate::ctypes::c_int,
pub sll_hatype: crate::ctypes::c_ushort,
pub sll_pkttype: crate::ctypes::c_uchar,
pub sll_halen: crate::ctypes::c_uchar,
pub sll_addr: [crate::ctypes::c_uchar; 8usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tpacket_stats {
pub tp_packets: crate::ctypes::c_uint,
pub tp_drops: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tpacket_stats_v3 {
pub tp_packets: crate::ctypes::c_uint,
pub tp_drops: crate::ctypes::c_uint,
pub tp_freeze_q_cnt: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tpacket_rollover_stats {
pub tp_all: __u64,
pub tp_huge: __u64,
pub tp_failed: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tpacket_auxdata {
pub tp_status: __u32,
pub tp_len: __u32,
pub tp_snaplen: __u32,
pub tp_mac: __u16,
pub tp_net: __u16,
pub tp_vlan_tci: __u16,
pub tp_vlan_tpid: __u16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tpacket_hdr {
pub tp_status: crate::ctypes::c_ulong,
pub tp_len: crate::ctypes::c_uint,
pub tp_snaplen: crate::ctypes::c_uint,
pub tp_mac: crate::ctypes::c_ushort,
pub tp_net: crate::ctypes::c_ushort,
pub tp_sec: crate::ctypes::c_uint,
pub tp_usec: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tpacket2_hdr {
pub tp_status: __u32,
pub tp_len: __u32,
pub tp_snaplen: __u32,
pub tp_mac: __u16,
pub tp_net: __u16,
pub tp_sec: __u32,
pub tp_nsec: __u32,
pub tp_vlan_tci: __u16,
pub tp_vlan_tpid: __u16,
pub tp_padding: [__u8; 4usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tpacket_hdr_variant1 {
pub tp_rxhash: __u32,
pub tp_vlan_tci: __u32,
pub tp_vlan_tpid: __u16,
pub tp_padding: __u16,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct tpacket3_hdr {
pub tp_next_offset: __u32,
pub tp_sec: __u32,
pub tp_nsec: __u32,
pub tp_snaplen: __u32,
pub tp_len: __u32,
pub tp_status: __u32,
pub tp_mac: __u16,
pub tp_net: __u16,
pub __bindgen_anon_1: tpacket3_hdr__bindgen_ty_1,
pub tp_padding: [__u8; 8usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct tpacket_bd_ts {
pub ts_sec: crate::ctypes::c_uint,
pub __bindgen_anon_1: tpacket_bd_ts__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct tpacket_hdr_v1 {
pub block_status: __u32,
pub num_pkts: __u32,
pub offset_to_first_pkt: __u32,
pub blk_len: __u32,
pub seq_num: __u64,
pub ts_first_pkt: tpacket_bd_ts,
pub ts_last_pkt: tpacket_bd_ts,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct tpacket_block_desc {
pub version: __u32,
pub offset_to_priv: __u32,
pub hdr: tpacket_bd_header_u,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tpacket_req {
pub tp_block_size: crate::ctypes::c_uint,
pub tp_block_nr: crate::ctypes::c_uint,
pub tp_frame_size: crate::ctypes::c_uint,
pub tp_frame_nr: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tpacket_req3 {
pub tp_block_size: crate::ctypes::c_uint,
pub tp_block_nr: crate::ctypes::c_uint,
pub tp_frame_size: crate::ctypes::c_uint,
pub tp_frame_nr: crate::ctypes::c_uint,
pub tp_retire_blk_tov: crate::ctypes::c_uint,
pub tp_sizeof_priv: crate::ctypes::c_uint,
pub tp_feature_req_word: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct packet_mreq {
pub mr_ifindex: crate::ctypes::c_int,
pub mr_type: crate::ctypes::c_ushort,
pub mr_alen: crate::ctypes::c_ushort,
pub mr_address: [crate::ctypes::c_uchar; 8usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fanout_args {
pub id: __u16,
pub type_flags: __u16,
pub max_num_members: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sockaddr_nl {
pub nl_family: __kernel_sa_family_t,
pub nl_pad: crate::ctypes::c_ushort,
pub nl_pid: __u32,
pub nl_groups: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nlmsghdr {
pub nlmsg_len: __u32,
pub nlmsg_type: __u16,
pub nlmsg_flags: __u16,
pub nlmsg_seq: __u32,
pub nlmsg_pid: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nlmsgerr {
pub error: crate::ctypes::c_int,
pub msg: nlmsghdr,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nl_pktinfo {
pub group: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nl_mmap_req {
pub nm_block_size: crate::ctypes::c_uint,
pub nm_block_nr: crate::ctypes::c_uint,
pub nm_frame_size: crate::ctypes::c_uint,
pub nm_frame_nr: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nl_mmap_hdr {
pub nm_status: crate::ctypes::c_uint,
pub nm_len: crate::ctypes::c_uint,
pub nm_group: __u32,
pub nm_pid: __u32,
pub nm_uid: __u32,
pub nm_gid: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nlattr {
pub nla_len: __u16,
pub nla_type: __u16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nla_bitfield32 {
pub value: __u32,
pub selector: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtnl_link_stats {
pub rx_packets: __u32,
pub tx_packets: __u32,
pub rx_bytes: __u32,
pub tx_bytes: __u32,
pub rx_errors: __u32,
pub tx_errors: __u32,
pub rx_dropped: __u32,
pub tx_dropped: __u32,
pub multicast: __u32,
pub collisions: __u32,
pub rx_length_errors: __u32,
pub rx_over_errors: __u32,
pub rx_crc_errors: __u32,
pub rx_frame_errors: __u32,
pub rx_fifo_errors: __u32,
pub rx_missed_errors: __u32,
pub tx_aborted_errors: __u32,
pub tx_carrier_errors: __u32,
pub tx_fifo_errors: __u32,
pub tx_heartbeat_errors: __u32,
pub tx_window_errors: __u32,
pub rx_compressed: __u32,
pub tx_compressed: __u32,
pub rx_nohandler: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtnl_link_stats64 {
pub rx_packets: __u64,
pub tx_packets: __u64,
pub rx_bytes: __u64,
pub tx_bytes: __u64,
pub rx_errors: __u64,
pub tx_errors: __u64,
pub rx_dropped: __u64,
pub tx_dropped: __u64,
pub multicast: __u64,
pub collisions: __u64,
pub rx_length_errors: __u64,
pub rx_over_errors: __u64,
pub rx_crc_errors: __u64,
pub rx_frame_errors: __u64,
pub rx_fifo_errors: __u64,
pub rx_missed_errors: __u64,
pub tx_aborted_errors: __u64,
pub tx_carrier_errors: __u64,
pub tx_fifo_errors: __u64,
pub tx_heartbeat_errors: __u64,
pub tx_window_errors: __u64,
pub rx_compressed: __u64,
pub tx_compressed: __u64,
pub rx_nohandler: __u64,
pub rx_otherhost_dropped: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtnl_hw_stats64 {
pub rx_packets: __u64,
pub tx_packets: __u64,
pub rx_bytes: __u64,
pub tx_bytes: __u64,
pub rx_errors: __u64,
pub tx_errors: __u64,
pub rx_dropped: __u64,
pub tx_dropped: __u64,
pub multicast: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtnl_link_ifmap {
pub mem_start: __u64,
pub mem_end: __u64,
pub base_addr: __u64,
pub irq: __u16,
pub dma: __u8,
pub port: __u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_bridge_id {
pub prio: [__u8; 2usize],
pub addr: [__u8; 6usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_cacheinfo {
pub max_reasm_len: __u32,
pub tstamp: __u32,
pub reachable_time: __u32,
pub retrans_time: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vlan_flags {
pub flags: __u32,
pub mask: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vlan_qos_mapping {
pub from: __u32,
pub to: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tunnel_msg {
pub family: __u8,
pub flags: __u8,
pub reserved2: __u16,
pub ifindex: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vxlan_port_range {
pub low: __be16,
pub high: __be16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_mac {
pub vf: __u32,
pub mac: [__u8; 32usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_broadcast {
pub broadcast: [__u8; 32usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_vlan {
pub vf: __u32,
pub vlan: __u32,
pub qos: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_vlan_info {
pub vf: __u32,
pub vlan: __u32,
pub qos: __u32,
pub vlan_proto: __be16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_tx_rate {
pub vf: __u32,
pub rate: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_rate {
pub vf: __u32,
pub min_tx_rate: __u32,
pub max_tx_rate: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_spoofchk {
pub vf: __u32,
pub setting: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_guid {
pub vf: __u32,
pub guid: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_link_state {
pub vf: __u32,
pub link_state: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_rss_query_en {
pub vf: __u32,
pub setting: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_trust {
pub vf: __u32,
pub setting: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_port_vsi {
pub vsi_mgr_id: __u8,
pub vsi_type_id: [__u8; 3usize],
pub vsi_type_version: __u8,
pub pad: [__u8; 3usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct if_stats_msg {
pub family: __u8,
pub pad1: __u8,
pub pad2: __u16,
pub ifindex: __u32,
pub filter_mask: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_rmnet_flags {
pub flags: __u32,
pub mask: __u32,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct arpreq {
pub arp_pa: sockaddr,
pub arp_ha: sockaddr,
pub arp_flags: crate::ctypes::c_int,
pub arp_netmask: sockaddr,
pub arp_dev: [crate::ctypes::c_char; 16usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct arpreq_old {
pub arp_pa: sockaddr,
pub arp_ha: sockaddr,
pub arp_flags: crate::ctypes::c_int,
pub arp_netmask: sockaddr,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct arphdr {
pub ar_hrd: __be16,
pub ar_pro: __be16,
pub ar_hln: crate::ctypes::c_uchar,
pub ar_pln: crate::ctypes::c_uchar,
pub ar_op: __be16,
}
pub const _K_SS_MAXSIZE: u32 = 128;
pub const SOCK_SNDBUF_LOCK: u32 = 1;
pub const SOCK_RCVBUF_LOCK: u32 = 2;
pub const SOCK_BUF_LOCK_MASK: u32 = 3;
pub const SOCK_TXREHASH_DEFAULT: u32 = 255;
pub const SOCK_TXREHASH_DISABLED: u32 = 0;
pub const SOCK_TXREHASH_ENABLED: u32 = 1;
pub const IFNAMSIZ: u32 = 16;
pub const IFALIASZ: u32 = 256;
pub const ALTIFNAMSIZ: u32 = 128;
pub const GENERIC_HDLC_VERSION: u32 = 4;
pub const CLOCK_DEFAULT: u32 = 0;
pub const CLOCK_EXT: u32 = 1;
pub const CLOCK_INT: u32 = 2;
pub const CLOCK_TXINT: u32 = 3;
pub const CLOCK_TXFROMRX: u32 = 4;
pub const ENCODING_DEFAULT: u32 = 0;
pub const ENCODING_NRZ: u32 = 1;
pub const ENCODING_NRZI: u32 = 2;
pub const ENCODING_FM_MARK: u32 = 3;
pub const ENCODING_FM_SPACE: u32 = 4;
pub const ENCODING_MANCHESTER: u32 = 5;
pub const PARITY_DEFAULT: u32 = 0;
pub const PARITY_NONE: u32 = 1;
pub const PARITY_CRC16_PR0: u32 = 2;
pub const PARITY_CRC16_PR1: u32 = 3;
pub const PARITY_CRC16_PR0_CCITT: u32 = 4;
pub const PARITY_CRC16_PR1_CCITT: u32 = 5;
pub const PARITY_CRC32_PR0_CCITT: u32 = 6;
pub const PARITY_CRC32_PR1_CCITT: u32 = 7;
pub const LMI_DEFAULT: u32 = 0;
pub const LMI_NONE: u32 = 1;
pub const LMI_ANSI: u32 = 2;
pub const LMI_CCITT: u32 = 3;
pub const LMI_CISCO: u32 = 4;
pub const IF_GET_IFACE: u32 = 1;
pub const IF_GET_PROTO: u32 = 2;
pub const IF_IFACE_V35: u32 = 4096;
pub const IF_IFACE_V24: u32 = 4097;
pub const IF_IFACE_X21: u32 = 4098;
pub const IF_IFACE_T1: u32 = 4099;
pub const IF_IFACE_E1: u32 = 4100;
pub const IF_IFACE_SYNC_SERIAL: u32 = 4101;
pub const IF_IFACE_X21D: u32 = 4102;
pub const IF_PROTO_HDLC: u32 = 8192;
pub const IF_PROTO_PPP: u32 = 8193;
pub const IF_PROTO_CISCO: u32 = 8194;
pub const IF_PROTO_FR: u32 = 8195;
pub const IF_PROTO_FR_ADD_PVC: u32 = 8196;
pub const IF_PROTO_FR_DEL_PVC: u32 = 8197;
pub const IF_PROTO_X25: u32 = 8198;
pub const IF_PROTO_HDLC_ETH: u32 = 8199;
pub const IF_PROTO_FR_ADD_ETH_PVC: u32 = 8200;
pub const IF_PROTO_FR_DEL_ETH_PVC: u32 = 8201;
pub const IF_PROTO_FR_PVC: u32 = 8202;
pub const IF_PROTO_FR_ETH_PVC: u32 = 8203;
pub const IF_PROTO_RAW: u32 = 8204;
pub const IFHWADDRLEN: u32 = 6;
pub const ETH_ALEN: u32 = 6;
pub const ETH_TLEN: u32 = 2;
pub const ETH_HLEN: u32 = 14;
pub const ETH_ZLEN: u32 = 60;
pub const ETH_DATA_LEN: u32 = 1500;
pub const ETH_FRAME_LEN: u32 = 1514;
pub const ETH_FCS_LEN: u32 = 4;
pub const ETH_MIN_MTU: u32 = 68;
pub const ETH_MAX_MTU: u32 = 65535;
pub const ETH_P_LOOP: u32 = 96;
pub const ETH_P_PUP: u32 = 512;
pub const ETH_P_PUPAT: u32 = 513;
pub const ETH_P_TSN: u32 = 8944;
pub const ETH_P_ERSPAN2: u32 = 8939;
pub const ETH_P_IP: u32 = 2048;
pub const ETH_P_X25: u32 = 2053;
pub const ETH_P_ARP: u32 = 2054;
pub const ETH_P_BPQ: u32 = 2303;
pub const ETH_P_IEEEPUP: u32 = 2560;
pub const ETH_P_IEEEPUPAT: u32 = 2561;
pub const ETH_P_BATMAN: u32 = 17157;
pub const ETH_P_DEC: u32 = 24576;
pub const ETH_P_DNA_DL: u32 = 24577;
pub const ETH_P_DNA_RC: u32 = 24578;
pub const ETH_P_DNA_RT: u32 = 24579;
pub const ETH_P_LAT: u32 = 24580;
pub const ETH_P_DIAG: u32 = 24581;
pub const ETH_P_CUST: u32 = 24582;
pub const ETH_P_SCA: u32 = 24583;
pub const ETH_P_TEB: u32 = 25944;
pub const ETH_P_RARP: u32 = 32821;
pub const ETH_P_ATALK: u32 = 32923;
pub const ETH_P_AARP: u32 = 33011;
pub const ETH_P_8021Q: u32 = 33024;
pub const ETH_P_ERSPAN: u32 = 35006;
pub const ETH_P_IPX: u32 = 33079;
pub const ETH_P_IPV6: u32 = 34525;
pub const ETH_P_PAUSE: u32 = 34824;
pub const ETH_P_SLOW: u32 = 34825;
pub const ETH_P_WCCP: u32 = 34878;
pub const ETH_P_MPLS_UC: u32 = 34887;
pub const ETH_P_MPLS_MC: u32 = 34888;
pub const ETH_P_ATMMPOA: u32 = 34892;
pub const ETH_P_PPP_DISC: u32 = 34915;
pub const ETH_P_PPP_SES: u32 = 34916;
pub const ETH_P_LINK_CTL: u32 = 34924;
pub const ETH_P_ATMFATE: u32 = 34948;
pub const ETH_P_PAE: u32 = 34958;
pub const ETH_P_PROFINET: u32 = 34962;
pub const ETH_P_REALTEK: u32 = 34969;
pub const ETH_P_AOE: u32 = 34978;
pub const ETH_P_ETHERCAT: u32 = 34980;
pub const ETH_P_8021AD: u32 = 34984;
pub const ETH_P_802_EX1: u32 = 34997;
pub const ETH_P_PREAUTH: u32 = 35015;
pub const ETH_P_TIPC: u32 = 35018;
pub const ETH_P_LLDP: u32 = 35020;
pub const ETH_P_MRP: u32 = 35043;
pub const ETH_P_MACSEC: u32 = 35045;
pub const ETH_P_8021AH: u32 = 35047;
pub const ETH_P_MVRP: u32 = 35061;
pub const ETH_P_1588: u32 = 35063;
pub const ETH_P_NCSI: u32 = 35064;
pub const ETH_P_PRP: u32 = 35067;
pub const ETH_P_CFM: u32 = 35074;
pub const ETH_P_FCOE: u32 = 35078;
pub const ETH_P_IBOE: u32 = 35093;
pub const ETH_P_TDLS: u32 = 35085;
pub const ETH_P_FIP: u32 = 35092;
pub const ETH_P_80221: u32 = 35095;
pub const ETH_P_HSR: u32 = 35119;
pub const ETH_P_NSH: u32 = 35151;
pub const ETH_P_LOOPBACK: u32 = 36864;
pub const ETH_P_QINQ1: u32 = 37120;
pub const ETH_P_QINQ2: u32 = 37376;
pub const ETH_P_QINQ3: u32 = 37632;
pub const ETH_P_EDSA: u32 = 56026;
pub const ETH_P_DSA_8021Q: u32 = 56027;
pub const ETH_P_DSA_A5PSW: u32 = 57345;
pub const ETH_P_IFE: u32 = 60734;
pub const ETH_P_AF_IUCV: u32 = 64507;
pub const ETH_P_802_3_MIN: u32 = 1536;
pub const ETH_P_802_3: u32 = 1;
pub const ETH_P_AX25: u32 = 2;
pub const ETH_P_ALL: u32 = 3;
pub const ETH_P_802_2: u32 = 4;
pub const ETH_P_SNAP: u32 = 5;
pub const ETH_P_DDCMP: u32 = 6;
pub const ETH_P_WAN_PPP: u32 = 7;
pub const ETH_P_PPP_MP: u32 = 8;
pub const ETH_P_LOCALTALK: u32 = 9;
pub const ETH_P_CAN: u32 = 12;
pub const ETH_P_CANFD: u32 = 13;
pub const ETH_P_CANXL: u32 = 14;
pub const ETH_P_PPPTALK: u32 = 16;
pub const ETH_P_TR_802_2: u32 = 17;
pub const ETH_P_MOBITEX: u32 = 21;
pub const ETH_P_CONTROL: u32 = 22;
pub const ETH_P_IRDA: u32 = 23;
pub const ETH_P_ECONET: u32 = 24;
pub const ETH_P_HDLC: u32 = 25;
pub const ETH_P_ARCNET: u32 = 26;
pub const ETH_P_DSA: u32 = 27;
pub const ETH_P_TRAILER: u32 = 28;
pub const ETH_P_PHONET: u32 = 245;
pub const ETH_P_IEEE802154: u32 = 246;
pub const ETH_P_CAIF: u32 = 247;
pub const ETH_P_XDSA: u32 = 248;
pub const ETH_P_MAP: u32 = 249;
pub const ETH_P_MCTP: u32 = 250;
pub const __LITTLE_ENDIAN: u32 = 1234;
pub const PACKET_HOST: u32 = 0;
pub const PACKET_BROADCAST: u32 = 1;
pub const PACKET_MULTICAST: u32 = 2;
pub const PACKET_OTHERHOST: u32 = 3;
pub const PACKET_OUTGOING: u32 = 4;
pub const PACKET_LOOPBACK: u32 = 5;
pub const PACKET_USER: u32 = 6;
pub const PACKET_KERNEL: u32 = 7;
pub const PACKET_FASTROUTE: u32 = 6;
pub const PACKET_ADD_MEMBERSHIP: u32 = 1;
pub const PACKET_DROP_MEMBERSHIP: u32 = 2;
pub const PACKET_RECV_OUTPUT: u32 = 3;
pub const PACKET_RX_RING: u32 = 5;
pub const PACKET_STATISTICS: u32 = 6;
pub const PACKET_COPY_THRESH: u32 = 7;
pub const PACKET_AUXDATA: u32 = 8;
pub const PACKET_ORIGDEV: u32 = 9;
pub const PACKET_VERSION: u32 = 10;
pub const PACKET_HDRLEN: u32 = 11;
pub const PACKET_RESERVE: u32 = 12;
pub const PACKET_TX_RING: u32 = 13;
pub const PACKET_LOSS: u32 = 14;
pub const PACKET_VNET_HDR: u32 = 15;
pub const PACKET_TX_TIMESTAMP: u32 = 16;
pub const PACKET_TIMESTAMP: u32 = 17;
pub const PACKET_FANOUT: u32 = 18;
pub const PACKET_TX_HAS_OFF: u32 = 19;
pub const PACKET_QDISC_BYPASS: u32 = 20;
pub const PACKET_ROLLOVER_STATS: u32 = 21;
pub const PACKET_FANOUT_DATA: u32 = 22;
pub const PACKET_IGNORE_OUTGOING: u32 = 23;
pub const PACKET_FANOUT_HASH: u32 = 0;
pub const PACKET_FANOUT_LB: u32 = 1;
pub const PACKET_FANOUT_CPU: u32 = 2;
pub const PACKET_FANOUT_ROLLOVER: u32 = 3;
pub const PACKET_FANOUT_RND: u32 = 4;
pub const PACKET_FANOUT_QM: u32 = 5;
pub const PACKET_FANOUT_CBPF: u32 = 6;
pub const PACKET_FANOUT_EBPF: u32 = 7;
pub const PACKET_FANOUT_FLAG_ROLLOVER: u32 = 4096;
pub const PACKET_FANOUT_FLAG_UNIQUEID: u32 = 8192;
pub const PACKET_FANOUT_FLAG_IGNORE_OUTGOING: u32 = 16384;
pub const PACKET_FANOUT_FLAG_DEFRAG: u32 = 32768;
pub const TP_STATUS_KERNEL: u32 = 0;
pub const TP_STATUS_USER: u32 = 1;
pub const TP_STATUS_COPY: u32 = 2;
pub const TP_STATUS_LOSING: u32 = 4;
pub const TP_STATUS_CSUMNOTREADY: u32 = 8;
pub const TP_STATUS_VLAN_VALID: u32 = 16;
pub const TP_STATUS_BLK_TMO: u32 = 32;
pub const TP_STATUS_VLAN_TPID_VALID: u32 = 64;
pub const TP_STATUS_CSUM_VALID: u32 = 128;
pub const TP_STATUS_GSO_TCP: u32 = 256;
pub const TP_STATUS_AVAILABLE: u32 = 0;
pub const TP_STATUS_SEND_REQUEST: u32 = 1;
pub const TP_STATUS_SENDING: u32 = 2;
pub const TP_STATUS_WRONG_FORMAT: u32 = 4;
pub const TP_STATUS_TS_SOFTWARE: u32 = 536870912;
pub const TP_STATUS_TS_SYS_HARDWARE: u32 = 1073741824;
pub const TP_STATUS_TS_RAW_HARDWARE: u32 = 2147483648;
pub const TP_FT_REQ_FILL_RXHASH: u32 = 1;
pub const TPACKET_ALIGNMENT: u32 = 16;
pub const PACKET_MR_MULTICAST: u32 = 0;
pub const PACKET_MR_PROMISC: u32 = 1;
pub const PACKET_MR_ALLMULTI: u32 = 2;
pub const PACKET_MR_UNICAST: u32 = 3;
pub const NETLINK_ROUTE: u32 = 0;
pub const NETLINK_UNUSED: u32 = 1;
pub const NETLINK_USERSOCK: u32 = 2;
pub const NETLINK_FIREWALL: u32 = 3;
pub const NETLINK_SOCK_DIAG: u32 = 4;
pub const NETLINK_NFLOG: u32 = 5;
pub const NETLINK_XFRM: u32 = 6;
pub const NETLINK_SELINUX: u32 = 7;
pub const NETLINK_ISCSI: u32 = 8;
pub const NETLINK_AUDIT: u32 = 9;
pub const NETLINK_FIB_LOOKUP: u32 = 10;
pub const NETLINK_CONNECTOR: u32 = 11;
pub const NETLINK_NETFILTER: u32 = 12;
pub const NETLINK_IP6_FW: u32 = 13;
pub const NETLINK_DNRTMSG: u32 = 14;
pub const NETLINK_KOBJECT_UEVENT: u32 = 15;
pub const NETLINK_GENERIC: u32 = 16;
pub const NETLINK_SCSITRANSPORT: u32 = 18;
pub const NETLINK_ECRYPTFS: u32 = 19;
pub const NETLINK_RDMA: u32 = 20;
pub const NETLINK_CRYPTO: u32 = 21;
pub const NETLINK_SMC: u32 = 22;
pub const NETLINK_INET_DIAG: u32 = 4;
pub const MAX_LINKS: u32 = 32;
pub const NLM_F_REQUEST: u32 = 1;
pub const NLM_F_MULTI: u32 = 2;
pub const NLM_F_ACK: u32 = 4;
pub const NLM_F_ECHO: u32 = 8;
pub const NLM_F_DUMP_INTR: u32 = 16;
pub const NLM_F_DUMP_FILTERED: u32 = 32;
pub const NLM_F_ROOT: u32 = 256;
pub const NLM_F_MATCH: u32 = 512;
pub const NLM_F_ATOMIC: u32 = 1024;
pub const NLM_F_DUMP: u32 = 768;
pub const NLM_F_REPLACE: u32 = 256;
pub const NLM_F_EXCL: u32 = 512;
pub const NLM_F_CREATE: u32 = 1024;
pub const NLM_F_APPEND: u32 = 2048;
pub const NLM_F_NONREC: u32 = 256;
pub const NLM_F_BULK: u32 = 512;
pub const NLM_F_CAPPED: u32 = 256;
pub const NLM_F_ACK_TLVS: u32 = 512;
pub const NLMSG_ALIGNTO: u32 = 4;
pub const NLMSG_NOOP: u32 = 1;
pub const NLMSG_ERROR: u32 = 2;
pub const NLMSG_DONE: u32 = 3;
pub const NLMSG_OVERRUN: u32 = 4;
pub const NLMSG_MIN_TYPE: u32 = 16;
pub const NETLINK_ADD_MEMBERSHIP: u32 = 1;
pub const NETLINK_DROP_MEMBERSHIP: u32 = 2;
pub const NETLINK_PKTINFO: u32 = 3;
pub const NETLINK_BROADCAST_ERROR: u32 = 4;
pub const NETLINK_NO_ENOBUFS: u32 = 5;
pub const NETLINK_RX_RING: u32 = 6;
pub const NETLINK_TX_RING: u32 = 7;
pub const NETLINK_LISTEN_ALL_NSID: u32 = 8;
pub const NETLINK_LIST_MEMBERSHIPS: u32 = 9;
pub const NETLINK_CAP_ACK: u32 = 10;
pub const NETLINK_EXT_ACK: u32 = 11;
pub const NETLINK_GET_STRICT_CHK: u32 = 12;
pub const NL_MMAP_MSG_ALIGNMENT: u32 = 4;
pub const NET_MAJOR: u32 = 36;
pub const NLA_F_NESTED: u32 = 32768;
pub const NLA_F_NET_BYTEORDER: u32 = 16384;
pub const NLA_TYPE_MASK: i32 = -49153;
pub const NLA_ALIGNTO: u32 = 4;
pub const MACVLAN_FLAG_NOPROMISC: u32 = 1;
pub const MACVLAN_FLAG_NODST: u32 = 2;
pub const IPVLAN_F_PRIVATE: u32 = 1;
pub const IPVLAN_F_VEPA: u32 = 2;
pub const TUNNEL_MSG_FLAG_STATS: u32 = 1;
pub const TUNNEL_MSG_VALID_USER_FLAGS: u32 = 1;
pub const MAX_VLAN_LIST_LEN: u32 = 1;
pub const PORT_PROFILE_MAX: u32 = 40;
pub const PORT_UUID_MAX: u32 = 16;
pub const PORT_SELF_VF: i32 = -1;
pub const XDP_FLAGS_UPDATE_IF_NOEXIST: u32 = 1;
pub const XDP_FLAGS_SKB_MODE: u32 = 2;
pub const XDP_FLAGS_DRV_MODE: u32 = 4;
pub const XDP_FLAGS_HW_MODE: u32 = 8;
pub const XDP_FLAGS_REPLACE: u32 = 16;
pub const XDP_FLAGS_MODES: u32 = 14;
pub const XDP_FLAGS_MASK: u32 = 31;
pub const RMNET_FLAGS_INGRESS_DEAGGREGATION: u32 = 1;
pub const RMNET_FLAGS_INGRESS_MAP_COMMANDS: u32 = 2;
pub const RMNET_FLAGS_INGRESS_MAP_CKSUMV4: u32 = 4;
pub const RMNET_FLAGS_EGRESS_MAP_CKSUMV4: u32 = 8;
pub const RMNET_FLAGS_INGRESS_MAP_CKSUMV5: u32 = 16;
pub const RMNET_FLAGS_EGRESS_MAP_CKSUMV5: u32 = 32;
pub const MAX_ADDR_LEN: u32 = 32;
pub const INIT_NETDEV_GROUP: u32 = 0;
pub const NET_NAME_UNKNOWN: u32 = 0;
pub const NET_NAME_ENUM: u32 = 1;
pub const NET_NAME_PREDICTABLE: u32 = 2;
pub const NET_NAME_USER: u32 = 3;
pub const NET_NAME_RENAMED: u32 = 4;
pub const NET_ADDR_PERM: u32 = 0;
pub const NET_ADDR_RANDOM: u32 = 1;
pub const NET_ADDR_STOLEN: u32 = 2;
pub const NET_ADDR_SET: u32 = 3;
pub const ARPHRD_NETROM: u32 = 0;
pub const ARPHRD_ETHER: u32 = 1;
pub const ARPHRD_EETHER: u32 = 2;
pub const ARPHRD_AX25: u32 = 3;
pub const ARPHRD_PRONET: u32 = 4;
pub const ARPHRD_CHAOS: u32 = 5;
pub const ARPHRD_IEEE802: u32 = 6;
pub const ARPHRD_ARCNET: u32 = 7;
pub const ARPHRD_APPLETLK: u32 = 8;
pub const ARPHRD_DLCI: u32 = 15;
pub const ARPHRD_ATM: u32 = 19;
pub const ARPHRD_METRICOM: u32 = 23;
pub const ARPHRD_IEEE1394: u32 = 24;
pub const ARPHRD_EUI64: u32 = 27;
pub const ARPHRD_INFINIBAND: u32 = 32;
pub const ARPHRD_SLIP: u32 = 256;
pub const ARPHRD_CSLIP: u32 = 257;
pub const ARPHRD_SLIP6: u32 = 258;
pub const ARPHRD_CSLIP6: u32 = 259;
pub const ARPHRD_RSRVD: u32 = 260;
pub const ARPHRD_ADAPT: u32 = 264;
pub const ARPHRD_ROSE: u32 = 270;
pub const ARPHRD_X25: u32 = 271;
pub const ARPHRD_HWX25: u32 = 272;
pub const ARPHRD_CAN: u32 = 280;
pub const ARPHRD_MCTP: u32 = 290;
pub const ARPHRD_PPP: u32 = 512;
pub const ARPHRD_CISCO: u32 = 513;
pub const ARPHRD_HDLC: u32 = 513;
pub const ARPHRD_LAPB: u32 = 516;
pub const ARPHRD_DDCMP: u32 = 517;
pub const ARPHRD_RAWHDLC: u32 = 518;
pub const ARPHRD_RAWIP: u32 = 519;
pub const ARPHRD_TUNNEL: u32 = 768;
pub const ARPHRD_TUNNEL6: u32 = 769;
pub const ARPHRD_FRAD: u32 = 770;
pub const ARPHRD_SKIP: u32 = 771;
pub const ARPHRD_LOOPBACK: u32 = 772;
pub const ARPHRD_LOCALTLK: u32 = 773;
pub const ARPHRD_FDDI: u32 = 774;
pub const ARPHRD_BIF: u32 = 775;
pub const ARPHRD_SIT: u32 = 776;
pub const ARPHRD_IPDDP: u32 = 777;
pub const ARPHRD_IPGRE: u32 = 778;
pub const ARPHRD_PIMREG: u32 = 779;
pub const ARPHRD_HIPPI: u32 = 780;
pub const ARPHRD_ASH: u32 = 781;
pub const ARPHRD_ECONET: u32 = 782;
pub const ARPHRD_IRDA: u32 = 783;
pub const ARPHRD_FCPP: u32 = 784;
pub const ARPHRD_FCAL: u32 = 785;
pub const ARPHRD_FCPL: u32 = 786;
pub const ARPHRD_FCFABRIC: u32 = 787;
pub const ARPHRD_IEEE802_TR: u32 = 800;
pub const ARPHRD_IEEE80211: u32 = 801;
pub const ARPHRD_IEEE80211_PRISM: u32 = 802;
pub const ARPHRD_IEEE80211_RADIOTAP: u32 = 803;
pub const ARPHRD_IEEE802154: u32 = 804;
pub const ARPHRD_IEEE802154_MONITOR: u32 = 805;
pub const ARPHRD_PHONET: u32 = 820;
pub const ARPHRD_PHONET_PIPE: u32 = 821;
pub const ARPHRD_CAIF: u32 = 822;
pub const ARPHRD_IP6GRE: u32 = 823;
pub const ARPHRD_NETLINK: u32 = 824;
pub const ARPHRD_6LOWPAN: u32 = 825;
pub const ARPHRD_VSOCKMON: u32 = 826;
pub const ARPHRD_VOID: u32 = 65535;
pub const ARPHRD_NONE: u32 = 65534;
pub const ARPOP_REQUEST: u32 = 1;
pub const ARPOP_REPLY: u32 = 2;
pub const ARPOP_RREQUEST: u32 = 3;
pub const ARPOP_RREPLY: u32 = 4;
pub const ARPOP_InREQUEST: u32 = 8;
pub const ARPOP_InREPLY: u32 = 9;
pub const ARPOP_NAK: u32 = 10;
pub const ATF_COM: u32 = 2;
pub const ATF_PERM: u32 = 4;
pub const ATF_PUBL: u32 = 8;
pub const ATF_USETRAILERS: u32 = 16;
pub const ATF_NETMASK: u32 = 32;
pub const ATF_DONTPUB: u32 = 64;
pub const IF_OPER_UNKNOWN: _bindgen_ty_1 = _bindgen_ty_1::IF_OPER_UNKNOWN;
pub const IF_OPER_NOTPRESENT: _bindgen_ty_1 = _bindgen_ty_1::IF_OPER_NOTPRESENT;
pub const IF_OPER_DOWN: _bindgen_ty_1 = _bindgen_ty_1::IF_OPER_DOWN;
pub const IF_OPER_LOWERLAYERDOWN: _bindgen_ty_1 = _bindgen_ty_1::IF_OPER_LOWERLAYERDOWN;
pub const IF_OPER_TESTING: _bindgen_ty_1 = _bindgen_ty_1::IF_OPER_TESTING;
pub const IF_OPER_DORMANT: _bindgen_ty_1 = _bindgen_ty_1::IF_OPER_DORMANT;
pub const IF_OPER_UP: _bindgen_ty_1 = _bindgen_ty_1::IF_OPER_UP;
pub const IF_LINK_MODE_DEFAULT: _bindgen_ty_2 = _bindgen_ty_2::IF_LINK_MODE_DEFAULT;
pub const IF_LINK_MODE_DORMANT: _bindgen_ty_2 = _bindgen_ty_2::IF_LINK_MODE_DORMANT;
pub const IF_LINK_MODE_TESTING: _bindgen_ty_2 = _bindgen_ty_2::IF_LINK_MODE_TESTING;
pub const NETLINK_UNCONNECTED: _bindgen_ty_3 = _bindgen_ty_3::NETLINK_UNCONNECTED;
pub const NETLINK_CONNECTED: _bindgen_ty_3 = _bindgen_ty_3::NETLINK_CONNECTED;
pub const IFLA_UNSPEC: _bindgen_ty_4 = _bindgen_ty_4::IFLA_UNSPEC;
pub const IFLA_ADDRESS: _bindgen_ty_4 = _bindgen_ty_4::IFLA_ADDRESS;
pub const IFLA_BROADCAST: _bindgen_ty_4 = _bindgen_ty_4::IFLA_BROADCAST;
pub const IFLA_IFNAME: _bindgen_ty_4 = _bindgen_ty_4::IFLA_IFNAME;
pub const IFLA_MTU: _bindgen_ty_4 = _bindgen_ty_4::IFLA_MTU;
pub const IFLA_LINK: _bindgen_ty_4 = _bindgen_ty_4::IFLA_LINK;
pub const IFLA_QDISC: _bindgen_ty_4 = _bindgen_ty_4::IFLA_QDISC;
pub const IFLA_STATS: _bindgen_ty_4 = _bindgen_ty_4::IFLA_STATS;
pub const IFLA_COST: _bindgen_ty_4 = _bindgen_ty_4::IFLA_COST;
pub const IFLA_PRIORITY: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PRIORITY;
pub const IFLA_MASTER: _bindgen_ty_4 = _bindgen_ty_4::IFLA_MASTER;
pub const IFLA_WIRELESS: _bindgen_ty_4 = _bindgen_ty_4::IFLA_WIRELESS;
pub const IFLA_PROTINFO: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PROTINFO;
pub const IFLA_TXQLEN: _bindgen_ty_4 = _bindgen_ty_4::IFLA_TXQLEN;
pub const IFLA_MAP: _bindgen_ty_4 = _bindgen_ty_4::IFLA_MAP;
pub const IFLA_WEIGHT: _bindgen_ty_4 = _bindgen_ty_4::IFLA_WEIGHT;
pub const IFLA_OPERSTATE: _bindgen_ty_4 = _bindgen_ty_4::IFLA_OPERSTATE;
pub const IFLA_LINKMODE: _bindgen_ty_4 = _bindgen_ty_4::IFLA_LINKMODE;
pub const IFLA_LINKINFO: _bindgen_ty_4 = _bindgen_ty_4::IFLA_LINKINFO;
pub const IFLA_NET_NS_PID: _bindgen_ty_4 = _bindgen_ty_4::IFLA_NET_NS_PID;
pub const IFLA_IFALIAS: _bindgen_ty_4 = _bindgen_ty_4::IFLA_IFALIAS;
pub const IFLA_NUM_VF: _bindgen_ty_4 = _bindgen_ty_4::IFLA_NUM_VF;
pub const IFLA_VFINFO_LIST: _bindgen_ty_4 = _bindgen_ty_4::IFLA_VFINFO_LIST;
pub const IFLA_STATS64: _bindgen_ty_4 = _bindgen_ty_4::IFLA_STATS64;
pub const IFLA_VF_PORTS: _bindgen_ty_4 = _bindgen_ty_4::IFLA_VF_PORTS;
pub const IFLA_PORT_SELF: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PORT_SELF;
pub const IFLA_AF_SPEC: _bindgen_ty_4 = _bindgen_ty_4::IFLA_AF_SPEC;
pub const IFLA_GROUP: _bindgen_ty_4 = _bindgen_ty_4::IFLA_GROUP;
pub const IFLA_NET_NS_FD: _bindgen_ty_4 = _bindgen_ty_4::IFLA_NET_NS_FD;
pub const IFLA_EXT_MASK: _bindgen_ty_4 = _bindgen_ty_4::IFLA_EXT_MASK;
pub const IFLA_PROMISCUITY: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PROMISCUITY;
pub const IFLA_NUM_TX_QUEUES: _bindgen_ty_4 = _bindgen_ty_4::IFLA_NUM_TX_QUEUES;
pub const IFLA_NUM_RX_QUEUES: _bindgen_ty_4 = _bindgen_ty_4::IFLA_NUM_RX_QUEUES;
pub const IFLA_CARRIER: _bindgen_ty_4 = _bindgen_ty_4::IFLA_CARRIER;
pub const IFLA_PHYS_PORT_ID: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PHYS_PORT_ID;
pub const IFLA_CARRIER_CHANGES: _bindgen_ty_4 = _bindgen_ty_4::IFLA_CARRIER_CHANGES;
pub const IFLA_PHYS_SWITCH_ID: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PHYS_SWITCH_ID;
pub const IFLA_LINK_NETNSID: _bindgen_ty_4 = _bindgen_ty_4::IFLA_LINK_NETNSID;
pub const IFLA_PHYS_PORT_NAME: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PHYS_PORT_NAME;
pub const IFLA_PROTO_DOWN: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PROTO_DOWN;
pub const IFLA_GSO_MAX_SEGS: _bindgen_ty_4 = _bindgen_ty_4::IFLA_GSO_MAX_SEGS;
pub const IFLA_GSO_MAX_SIZE: _bindgen_ty_4 = _bindgen_ty_4::IFLA_GSO_MAX_SIZE;
pub const IFLA_PAD: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PAD;
pub const IFLA_XDP: _bindgen_ty_4 = _bindgen_ty_4::IFLA_XDP;
pub const IFLA_EVENT: _bindgen_ty_4 = _bindgen_ty_4::IFLA_EVENT;
pub const IFLA_NEW_NETNSID: _bindgen_ty_4 = _bindgen_ty_4::IFLA_NEW_NETNSID;
pub const IFLA_IF_NETNSID: _bindgen_ty_4 = _bindgen_ty_4::IFLA_IF_NETNSID;
pub const IFLA_TARGET_NETNSID: _bindgen_ty_4 = _bindgen_ty_4::IFLA_IF_NETNSID;
pub const IFLA_CARRIER_UP_COUNT: _bindgen_ty_4 = _bindgen_ty_4::IFLA_CARRIER_UP_COUNT;
pub const IFLA_CARRIER_DOWN_COUNT: _bindgen_ty_4 = _bindgen_ty_4::IFLA_CARRIER_DOWN_COUNT;
pub const IFLA_NEW_IFINDEX: _bindgen_ty_4 = _bindgen_ty_4::IFLA_NEW_IFINDEX;
pub const IFLA_MIN_MTU: _bindgen_ty_4 = _bindgen_ty_4::IFLA_MIN_MTU;
pub const IFLA_MAX_MTU: _bindgen_ty_4 = _bindgen_ty_4::IFLA_MAX_MTU;
pub const IFLA_PROP_LIST: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PROP_LIST;
pub const IFLA_ALT_IFNAME: _bindgen_ty_4 = _bindgen_ty_4::IFLA_ALT_IFNAME;
pub const IFLA_PERM_ADDRESS: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PERM_ADDRESS;
pub const IFLA_PROTO_DOWN_REASON: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PROTO_DOWN_REASON;
pub const IFLA_PARENT_DEV_NAME: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PARENT_DEV_NAME;
pub const IFLA_PARENT_DEV_BUS_NAME: _bindgen_ty_4 = _bindgen_ty_4::IFLA_PARENT_DEV_BUS_NAME;
pub const IFLA_GRO_MAX_SIZE: _bindgen_ty_4 = _bindgen_ty_4::IFLA_GRO_MAX_SIZE;
pub const IFLA_TSO_MAX_SIZE: _bindgen_ty_4 = _bindgen_ty_4::IFLA_TSO_MAX_SIZE;
pub const IFLA_TSO_MAX_SEGS: _bindgen_ty_4 = _bindgen_ty_4::IFLA_TSO_MAX_SEGS;
pub const IFLA_ALLMULTI: _bindgen_ty_4 = _bindgen_ty_4::IFLA_ALLMULTI;
pub const IFLA_DEVLINK_PORT: _bindgen_ty_4 = _bindgen_ty_4::IFLA_DEVLINK_PORT;
pub const IFLA_GSO_IPV4_MAX_SIZE: _bindgen_ty_4 = _bindgen_ty_4::IFLA_GSO_IPV4_MAX_SIZE;
pub const IFLA_GRO_IPV4_MAX_SIZE: _bindgen_ty_4 = _bindgen_ty_4::IFLA_GRO_IPV4_MAX_SIZE;
pub const __IFLA_MAX: _bindgen_ty_4 = _bindgen_ty_4::__IFLA_MAX;
pub const IFLA_PROTO_DOWN_REASON_UNSPEC: _bindgen_ty_5 = _bindgen_ty_5::IFLA_PROTO_DOWN_REASON_UNSPEC;
pub const IFLA_PROTO_DOWN_REASON_MASK: _bindgen_ty_5 = _bindgen_ty_5::IFLA_PROTO_DOWN_REASON_MASK;
pub const IFLA_PROTO_DOWN_REASON_VALUE: _bindgen_ty_5 = _bindgen_ty_5::IFLA_PROTO_DOWN_REASON_VALUE;
pub const __IFLA_PROTO_DOWN_REASON_CNT: _bindgen_ty_5 = _bindgen_ty_5::__IFLA_PROTO_DOWN_REASON_CNT;
pub const IFLA_PROTO_DOWN_REASON_MAX: _bindgen_ty_5 = _bindgen_ty_5::IFLA_PROTO_DOWN_REASON_VALUE;
pub const IFLA_INET_UNSPEC: _bindgen_ty_6 = _bindgen_ty_6::IFLA_INET_UNSPEC;
pub const IFLA_INET_CONF: _bindgen_ty_6 = _bindgen_ty_6::IFLA_INET_CONF;
pub const __IFLA_INET_MAX: _bindgen_ty_6 = _bindgen_ty_6::__IFLA_INET_MAX;
pub const IFLA_INET6_UNSPEC: _bindgen_ty_7 = _bindgen_ty_7::IFLA_INET6_UNSPEC;
pub const IFLA_INET6_FLAGS: _bindgen_ty_7 = _bindgen_ty_7::IFLA_INET6_FLAGS;
pub const IFLA_INET6_CONF: _bindgen_ty_7 = _bindgen_ty_7::IFLA_INET6_CONF;
pub const IFLA_INET6_STATS: _bindgen_ty_7 = _bindgen_ty_7::IFLA_INET6_STATS;
pub const IFLA_INET6_MCAST: _bindgen_ty_7 = _bindgen_ty_7::IFLA_INET6_MCAST;
pub const IFLA_INET6_CACHEINFO: _bindgen_ty_7 = _bindgen_ty_7::IFLA_INET6_CACHEINFO;
pub const IFLA_INET6_ICMP6STATS: _bindgen_ty_7 = _bindgen_ty_7::IFLA_INET6_ICMP6STATS;
pub const IFLA_INET6_TOKEN: _bindgen_ty_7 = _bindgen_ty_7::IFLA_INET6_TOKEN;
pub const IFLA_INET6_ADDR_GEN_MODE: _bindgen_ty_7 = _bindgen_ty_7::IFLA_INET6_ADDR_GEN_MODE;
pub const IFLA_INET6_RA_MTU: _bindgen_ty_7 = _bindgen_ty_7::IFLA_INET6_RA_MTU;
pub const __IFLA_INET6_MAX: _bindgen_ty_7 = _bindgen_ty_7::__IFLA_INET6_MAX;
pub const IFLA_BR_UNSPEC: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_UNSPEC;
pub const IFLA_BR_FORWARD_DELAY: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_FORWARD_DELAY;
pub const IFLA_BR_HELLO_TIME: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_HELLO_TIME;
pub const IFLA_BR_MAX_AGE: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MAX_AGE;
pub const IFLA_BR_AGEING_TIME: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_AGEING_TIME;
pub const IFLA_BR_STP_STATE: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_STP_STATE;
pub const IFLA_BR_PRIORITY: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_PRIORITY;
pub const IFLA_BR_VLAN_FILTERING: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_VLAN_FILTERING;
pub const IFLA_BR_VLAN_PROTOCOL: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_VLAN_PROTOCOL;
pub const IFLA_BR_GROUP_FWD_MASK: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_GROUP_FWD_MASK;
pub const IFLA_BR_ROOT_ID: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_ROOT_ID;
pub const IFLA_BR_BRIDGE_ID: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_BRIDGE_ID;
pub const IFLA_BR_ROOT_PORT: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_ROOT_PORT;
pub const IFLA_BR_ROOT_PATH_COST: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_ROOT_PATH_COST;
pub const IFLA_BR_TOPOLOGY_CHANGE: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_TOPOLOGY_CHANGE;
pub const IFLA_BR_TOPOLOGY_CHANGE_DETECTED: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_TOPOLOGY_CHANGE_DETECTED;
pub const IFLA_BR_HELLO_TIMER: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_HELLO_TIMER;
pub const IFLA_BR_TCN_TIMER: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_TCN_TIMER;
pub const IFLA_BR_TOPOLOGY_CHANGE_TIMER: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_TOPOLOGY_CHANGE_TIMER;
pub const IFLA_BR_GC_TIMER: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_GC_TIMER;
pub const IFLA_BR_GROUP_ADDR: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_GROUP_ADDR;
pub const IFLA_BR_FDB_FLUSH: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_FDB_FLUSH;
pub const IFLA_BR_MCAST_ROUTER: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_ROUTER;
pub const IFLA_BR_MCAST_SNOOPING: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_SNOOPING;
pub const IFLA_BR_MCAST_QUERY_USE_IFADDR: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_QUERY_USE_IFADDR;
pub const IFLA_BR_MCAST_QUERIER: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_QUERIER;
pub const IFLA_BR_MCAST_HASH_ELASTICITY: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_HASH_ELASTICITY;
pub const IFLA_BR_MCAST_HASH_MAX: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_HASH_MAX;
pub const IFLA_BR_MCAST_LAST_MEMBER_CNT: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_LAST_MEMBER_CNT;
pub const IFLA_BR_MCAST_STARTUP_QUERY_CNT: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_STARTUP_QUERY_CNT;
pub const IFLA_BR_MCAST_LAST_MEMBER_INTVL: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_LAST_MEMBER_INTVL;
pub const IFLA_BR_MCAST_MEMBERSHIP_INTVL: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_MEMBERSHIP_INTVL;
pub const IFLA_BR_MCAST_QUERIER_INTVL: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_QUERIER_INTVL;
pub const IFLA_BR_MCAST_QUERY_INTVL: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_QUERY_INTVL;
pub const IFLA_BR_MCAST_QUERY_RESPONSE_INTVL: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_QUERY_RESPONSE_INTVL;
pub const IFLA_BR_MCAST_STARTUP_QUERY_INTVL: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_STARTUP_QUERY_INTVL;
pub const IFLA_BR_NF_CALL_IPTABLES: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_NF_CALL_IPTABLES;
pub const IFLA_BR_NF_CALL_IP6TABLES: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_NF_CALL_IP6TABLES;
pub const IFLA_BR_NF_CALL_ARPTABLES: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_NF_CALL_ARPTABLES;
pub const IFLA_BR_VLAN_DEFAULT_PVID: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_VLAN_DEFAULT_PVID;
pub const IFLA_BR_PAD: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_PAD;
pub const IFLA_BR_VLAN_STATS_ENABLED: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_VLAN_STATS_ENABLED;
pub const IFLA_BR_MCAST_STATS_ENABLED: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_STATS_ENABLED;
pub const IFLA_BR_MCAST_IGMP_VERSION: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_IGMP_VERSION;
pub const IFLA_BR_MCAST_MLD_VERSION: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_MLD_VERSION;
pub const IFLA_BR_VLAN_STATS_PER_PORT: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_VLAN_STATS_PER_PORT;
pub const IFLA_BR_MULTI_BOOLOPT: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MULTI_BOOLOPT;
pub const IFLA_BR_MCAST_QUERIER_STATE: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BR_MCAST_QUERIER_STATE;
pub const __IFLA_BR_MAX: _bindgen_ty_8 = _bindgen_ty_8::__IFLA_BR_MAX;
pub const BRIDGE_MODE_UNSPEC: _bindgen_ty_9 = _bindgen_ty_9::BRIDGE_MODE_UNSPEC;
pub const BRIDGE_MODE_HAIRPIN: _bindgen_ty_9 = _bindgen_ty_9::BRIDGE_MODE_HAIRPIN;
pub const IFLA_BRPORT_UNSPEC: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_UNSPEC;
pub const IFLA_BRPORT_STATE: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_STATE;
pub const IFLA_BRPORT_PRIORITY: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_PRIORITY;
pub const IFLA_BRPORT_COST: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_COST;
pub const IFLA_BRPORT_MODE: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MODE;
pub const IFLA_BRPORT_GUARD: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_GUARD;
pub const IFLA_BRPORT_PROTECT: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_PROTECT;
pub const IFLA_BRPORT_FAST_LEAVE: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_FAST_LEAVE;
pub const IFLA_BRPORT_LEARNING: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_LEARNING;
pub const IFLA_BRPORT_UNICAST_FLOOD: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_UNICAST_FLOOD;
pub const IFLA_BRPORT_PROXYARP: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_PROXYARP;
pub const IFLA_BRPORT_LEARNING_SYNC: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_LEARNING_SYNC;
pub const IFLA_BRPORT_PROXYARP_WIFI: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_PROXYARP_WIFI;
pub const IFLA_BRPORT_ROOT_ID: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_ROOT_ID;
pub const IFLA_BRPORT_BRIDGE_ID: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_BRIDGE_ID;
pub const IFLA_BRPORT_DESIGNATED_PORT: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_DESIGNATED_PORT;
pub const IFLA_BRPORT_DESIGNATED_COST: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_DESIGNATED_COST;
pub const IFLA_BRPORT_ID: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_ID;
pub const IFLA_BRPORT_NO: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_NO;
pub const IFLA_BRPORT_TOPOLOGY_CHANGE_ACK: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_TOPOLOGY_CHANGE_ACK;
pub const IFLA_BRPORT_CONFIG_PENDING: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_CONFIG_PENDING;
pub const IFLA_BRPORT_MESSAGE_AGE_TIMER: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MESSAGE_AGE_TIMER;
pub const IFLA_BRPORT_FORWARD_DELAY_TIMER: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_FORWARD_DELAY_TIMER;
pub const IFLA_BRPORT_HOLD_TIMER: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_HOLD_TIMER;
pub const IFLA_BRPORT_FLUSH: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_FLUSH;
pub const IFLA_BRPORT_MULTICAST_ROUTER: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MULTICAST_ROUTER;
pub const IFLA_BRPORT_PAD: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_PAD;
pub const IFLA_BRPORT_MCAST_FLOOD: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MCAST_FLOOD;
pub const IFLA_BRPORT_MCAST_TO_UCAST: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MCAST_TO_UCAST;
pub const IFLA_BRPORT_VLAN_TUNNEL: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_VLAN_TUNNEL;
pub const IFLA_BRPORT_BCAST_FLOOD: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_BCAST_FLOOD;
pub const IFLA_BRPORT_GROUP_FWD_MASK: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_GROUP_FWD_MASK;
pub const IFLA_BRPORT_NEIGH_SUPPRESS: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_NEIGH_SUPPRESS;
pub const IFLA_BRPORT_ISOLATED: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_ISOLATED;
pub const IFLA_BRPORT_BACKUP_PORT: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_BACKUP_PORT;
pub const IFLA_BRPORT_MRP_RING_OPEN: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MRP_RING_OPEN;
pub const IFLA_BRPORT_MRP_IN_OPEN: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MRP_IN_OPEN;
pub const IFLA_BRPORT_MCAST_EHT_HOSTS_LIMIT: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MCAST_EHT_HOSTS_LIMIT;
pub const IFLA_BRPORT_MCAST_EHT_HOSTS_CNT: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MCAST_EHT_HOSTS_CNT;
pub const IFLA_BRPORT_LOCKED: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_LOCKED;
pub const IFLA_BRPORT_MAB: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MAB;
pub const IFLA_BRPORT_MCAST_N_GROUPS: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MCAST_N_GROUPS;
pub const IFLA_BRPORT_MCAST_MAX_GROUPS: _bindgen_ty_10 = _bindgen_ty_10::IFLA_BRPORT_MCAST_MAX_GROUPS;
pub const __IFLA_BRPORT_MAX: _bindgen_ty_10 = _bindgen_ty_10::__IFLA_BRPORT_MAX;
pub const IFLA_INFO_UNSPEC: _bindgen_ty_11 = _bindgen_ty_11::IFLA_INFO_UNSPEC;
pub const IFLA_INFO_KIND: _bindgen_ty_11 = _bindgen_ty_11::IFLA_INFO_KIND;
pub const IFLA_INFO_DATA: _bindgen_ty_11 = _bindgen_ty_11::IFLA_INFO_DATA;
pub const IFLA_INFO_XSTATS: _bindgen_ty_11 = _bindgen_ty_11::IFLA_INFO_XSTATS;
pub const IFLA_INFO_SLAVE_KIND: _bindgen_ty_11 = _bindgen_ty_11::IFLA_INFO_SLAVE_KIND;
pub const IFLA_INFO_SLAVE_DATA: _bindgen_ty_11 = _bindgen_ty_11::IFLA_INFO_SLAVE_DATA;
pub const __IFLA_INFO_MAX: _bindgen_ty_11 = _bindgen_ty_11::__IFLA_INFO_MAX;
pub const IFLA_VLAN_UNSPEC: _bindgen_ty_12 = _bindgen_ty_12::IFLA_VLAN_UNSPEC;
pub const IFLA_VLAN_ID: _bindgen_ty_12 = _bindgen_ty_12::IFLA_VLAN_ID;
pub const IFLA_VLAN_FLAGS: _bindgen_ty_12 = _bindgen_ty_12::IFLA_VLAN_FLAGS;
pub const IFLA_VLAN_EGRESS_QOS: _bindgen_ty_12 = _bindgen_ty_12::IFLA_VLAN_EGRESS_QOS;
pub const IFLA_VLAN_INGRESS_QOS: _bindgen_ty_12 = _bindgen_ty_12::IFLA_VLAN_INGRESS_QOS;
pub const IFLA_VLAN_PROTOCOL: _bindgen_ty_12 = _bindgen_ty_12::IFLA_VLAN_PROTOCOL;
pub const __IFLA_VLAN_MAX: _bindgen_ty_12 = _bindgen_ty_12::__IFLA_VLAN_MAX;
pub const IFLA_VLAN_QOS_UNSPEC: _bindgen_ty_13 = _bindgen_ty_13::IFLA_VLAN_QOS_UNSPEC;
pub const IFLA_VLAN_QOS_MAPPING: _bindgen_ty_13 = _bindgen_ty_13::IFLA_VLAN_QOS_MAPPING;
pub const __IFLA_VLAN_QOS_MAX: _bindgen_ty_13 = _bindgen_ty_13::__IFLA_VLAN_QOS_MAX;
pub const IFLA_MACVLAN_UNSPEC: _bindgen_ty_14 = _bindgen_ty_14::IFLA_MACVLAN_UNSPEC;
pub const IFLA_MACVLAN_MODE: _bindgen_ty_14 = _bindgen_ty_14::IFLA_MACVLAN_MODE;
pub const IFLA_MACVLAN_FLAGS: _bindgen_ty_14 = _bindgen_ty_14::IFLA_MACVLAN_FLAGS;
pub const IFLA_MACVLAN_MACADDR_MODE: _bindgen_ty_14 = _bindgen_ty_14::IFLA_MACVLAN_MACADDR_MODE;
pub const IFLA_MACVLAN_MACADDR: _bindgen_ty_14 = _bindgen_ty_14::IFLA_MACVLAN_MACADDR;
pub const IFLA_MACVLAN_MACADDR_DATA: _bindgen_ty_14 = _bindgen_ty_14::IFLA_MACVLAN_MACADDR_DATA;
pub const IFLA_MACVLAN_MACADDR_COUNT: _bindgen_ty_14 = _bindgen_ty_14::IFLA_MACVLAN_MACADDR_COUNT;
pub const IFLA_MACVLAN_BC_QUEUE_LEN: _bindgen_ty_14 = _bindgen_ty_14::IFLA_MACVLAN_BC_QUEUE_LEN;
pub const IFLA_MACVLAN_BC_QUEUE_LEN_USED: _bindgen_ty_14 = _bindgen_ty_14::IFLA_MACVLAN_BC_QUEUE_LEN_USED;
pub const __IFLA_MACVLAN_MAX: _bindgen_ty_14 = _bindgen_ty_14::__IFLA_MACVLAN_MAX;
pub const IFLA_VRF_UNSPEC: _bindgen_ty_15 = _bindgen_ty_15::IFLA_VRF_UNSPEC;
pub const IFLA_VRF_TABLE: _bindgen_ty_15 = _bindgen_ty_15::IFLA_VRF_TABLE;
pub const __IFLA_VRF_MAX: _bindgen_ty_15 = _bindgen_ty_15::__IFLA_VRF_MAX;
pub const IFLA_VRF_PORT_UNSPEC: _bindgen_ty_16 = _bindgen_ty_16::IFLA_VRF_PORT_UNSPEC;
pub const IFLA_VRF_PORT_TABLE: _bindgen_ty_16 = _bindgen_ty_16::IFLA_VRF_PORT_TABLE;
pub const __IFLA_VRF_PORT_MAX: _bindgen_ty_16 = _bindgen_ty_16::__IFLA_VRF_PORT_MAX;
pub const IFLA_MACSEC_UNSPEC: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_UNSPEC;
pub const IFLA_MACSEC_SCI: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_SCI;
pub const IFLA_MACSEC_PORT: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_PORT;
pub const IFLA_MACSEC_ICV_LEN: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_ICV_LEN;
pub const IFLA_MACSEC_CIPHER_SUITE: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_CIPHER_SUITE;
pub const IFLA_MACSEC_WINDOW: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_WINDOW;
pub const IFLA_MACSEC_ENCODING_SA: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_ENCODING_SA;
pub const IFLA_MACSEC_ENCRYPT: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_ENCRYPT;
pub const IFLA_MACSEC_PROTECT: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_PROTECT;
pub const IFLA_MACSEC_INC_SCI: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_INC_SCI;
pub const IFLA_MACSEC_ES: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_ES;
pub const IFLA_MACSEC_SCB: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_SCB;
pub const IFLA_MACSEC_REPLAY_PROTECT: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_REPLAY_PROTECT;
pub const IFLA_MACSEC_VALIDATION: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_VALIDATION;
pub const IFLA_MACSEC_PAD: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_PAD;
pub const IFLA_MACSEC_OFFLOAD: _bindgen_ty_17 = _bindgen_ty_17::IFLA_MACSEC_OFFLOAD;
pub const __IFLA_MACSEC_MAX: _bindgen_ty_17 = _bindgen_ty_17::__IFLA_MACSEC_MAX;
pub const IFLA_XFRM_UNSPEC: _bindgen_ty_18 = _bindgen_ty_18::IFLA_XFRM_UNSPEC;
pub const IFLA_XFRM_LINK: _bindgen_ty_18 = _bindgen_ty_18::IFLA_XFRM_LINK;
pub const IFLA_XFRM_IF_ID: _bindgen_ty_18 = _bindgen_ty_18::IFLA_XFRM_IF_ID;
pub const IFLA_XFRM_COLLECT_METADATA: _bindgen_ty_18 = _bindgen_ty_18::IFLA_XFRM_COLLECT_METADATA;
pub const __IFLA_XFRM_MAX: _bindgen_ty_18 = _bindgen_ty_18::__IFLA_XFRM_MAX;
pub const IFLA_IPVLAN_UNSPEC: _bindgen_ty_19 = _bindgen_ty_19::IFLA_IPVLAN_UNSPEC;
pub const IFLA_IPVLAN_MODE: _bindgen_ty_19 = _bindgen_ty_19::IFLA_IPVLAN_MODE;
pub const IFLA_IPVLAN_FLAGS: _bindgen_ty_19 = _bindgen_ty_19::IFLA_IPVLAN_FLAGS;
pub const __IFLA_IPVLAN_MAX: _bindgen_ty_19 = _bindgen_ty_19::__IFLA_IPVLAN_MAX;
pub const VNIFILTER_ENTRY_STATS_UNSPEC: _bindgen_ty_20 = _bindgen_ty_20::VNIFILTER_ENTRY_STATS_UNSPEC;
pub const VNIFILTER_ENTRY_STATS_RX_BYTES: _bindgen_ty_20 = _bindgen_ty_20::VNIFILTER_ENTRY_STATS_RX_BYTES;
pub const VNIFILTER_ENTRY_STATS_RX_PKTS: _bindgen_ty_20 = _bindgen_ty_20::VNIFILTER_ENTRY_STATS_RX_PKTS;
pub const VNIFILTER_ENTRY_STATS_RX_DROPS: _bindgen_ty_20 = _bindgen_ty_20::VNIFILTER_ENTRY_STATS_RX_DROPS;
pub const VNIFILTER_ENTRY_STATS_RX_ERRORS: _bindgen_ty_20 = _bindgen_ty_20::VNIFILTER_ENTRY_STATS_RX_ERRORS;
pub const VNIFILTER_ENTRY_STATS_TX_BYTES: _bindgen_ty_20 = _bindgen_ty_20::VNIFILTER_ENTRY_STATS_TX_BYTES;
pub const VNIFILTER_ENTRY_STATS_TX_PKTS: _bindgen_ty_20 = _bindgen_ty_20::VNIFILTER_ENTRY_STATS_TX_PKTS;
pub const VNIFILTER_ENTRY_STATS_TX_DROPS: _bindgen_ty_20 = _bindgen_ty_20::VNIFILTER_ENTRY_STATS_TX_DROPS;
pub const VNIFILTER_ENTRY_STATS_TX_ERRORS: _bindgen_ty_20 = _bindgen_ty_20::VNIFILTER_ENTRY_STATS_TX_ERRORS;
pub const VNIFILTER_ENTRY_STATS_PAD: _bindgen_ty_20 = _bindgen_ty_20::VNIFILTER_ENTRY_STATS_PAD;
pub const __VNIFILTER_ENTRY_STATS_MAX: _bindgen_ty_20 = _bindgen_ty_20::__VNIFILTER_ENTRY_STATS_MAX;
pub const VXLAN_VNIFILTER_ENTRY_UNSPEC: _bindgen_ty_21 = _bindgen_ty_21::VXLAN_VNIFILTER_ENTRY_UNSPEC;
pub const VXLAN_VNIFILTER_ENTRY_START: _bindgen_ty_21 = _bindgen_ty_21::VXLAN_VNIFILTER_ENTRY_START;
pub const VXLAN_VNIFILTER_ENTRY_END: _bindgen_ty_21 = _bindgen_ty_21::VXLAN_VNIFILTER_ENTRY_END;
pub const VXLAN_VNIFILTER_ENTRY_GROUP: _bindgen_ty_21 = _bindgen_ty_21::VXLAN_VNIFILTER_ENTRY_GROUP;
pub const VXLAN_VNIFILTER_ENTRY_GROUP6: _bindgen_ty_21 = _bindgen_ty_21::VXLAN_VNIFILTER_ENTRY_GROUP6;
pub const VXLAN_VNIFILTER_ENTRY_STATS: _bindgen_ty_21 = _bindgen_ty_21::VXLAN_VNIFILTER_ENTRY_STATS;
pub const __VXLAN_VNIFILTER_ENTRY_MAX: _bindgen_ty_21 = _bindgen_ty_21::__VXLAN_VNIFILTER_ENTRY_MAX;
pub const VXLAN_VNIFILTER_UNSPEC: _bindgen_ty_22 = _bindgen_ty_22::VXLAN_VNIFILTER_UNSPEC;
pub const VXLAN_VNIFILTER_ENTRY: _bindgen_ty_22 = _bindgen_ty_22::VXLAN_VNIFILTER_ENTRY;
pub const __VXLAN_VNIFILTER_MAX: _bindgen_ty_22 = _bindgen_ty_22::__VXLAN_VNIFILTER_MAX;
pub const IFLA_VXLAN_UNSPEC: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_UNSPEC;
pub const IFLA_VXLAN_ID: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_ID;
pub const IFLA_VXLAN_GROUP: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_GROUP;
pub const IFLA_VXLAN_LINK: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_LINK;
pub const IFLA_VXLAN_LOCAL: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_LOCAL;
pub const IFLA_VXLAN_TTL: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_TTL;
pub const IFLA_VXLAN_TOS: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_TOS;
pub const IFLA_VXLAN_LEARNING: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_LEARNING;
pub const IFLA_VXLAN_AGEING: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_AGEING;
pub const IFLA_VXLAN_LIMIT: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_LIMIT;
pub const IFLA_VXLAN_PORT_RANGE: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_PORT_RANGE;
pub const IFLA_VXLAN_PROXY: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_PROXY;
pub const IFLA_VXLAN_RSC: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_RSC;
pub const IFLA_VXLAN_L2MISS: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_L2MISS;
pub const IFLA_VXLAN_L3MISS: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_L3MISS;
pub const IFLA_VXLAN_PORT: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_PORT;
pub const IFLA_VXLAN_GROUP6: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_GROUP6;
pub const IFLA_VXLAN_LOCAL6: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_LOCAL6;
pub const IFLA_VXLAN_UDP_CSUM: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_UDP_CSUM;
pub const IFLA_VXLAN_UDP_ZERO_CSUM6_TX: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_UDP_ZERO_CSUM6_TX;
pub const IFLA_VXLAN_UDP_ZERO_CSUM6_RX: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_UDP_ZERO_CSUM6_RX;
pub const IFLA_VXLAN_REMCSUM_TX: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_REMCSUM_TX;
pub const IFLA_VXLAN_REMCSUM_RX: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_REMCSUM_RX;
pub const IFLA_VXLAN_GBP: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_GBP;
pub const IFLA_VXLAN_REMCSUM_NOPARTIAL: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_REMCSUM_NOPARTIAL;
pub const IFLA_VXLAN_COLLECT_METADATA: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_COLLECT_METADATA;
pub const IFLA_VXLAN_LABEL: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_LABEL;
pub const IFLA_VXLAN_GPE: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_GPE;
pub const IFLA_VXLAN_TTL_INHERIT: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_TTL_INHERIT;
pub const IFLA_VXLAN_DF: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_DF;
pub const IFLA_VXLAN_VNIFILTER: _bindgen_ty_23 = _bindgen_ty_23::IFLA_VXLAN_VNIFILTER;
pub const __IFLA_VXLAN_MAX: _bindgen_ty_23 = _bindgen_ty_23::__IFLA_VXLAN_MAX;
pub const IFLA_GENEVE_UNSPEC: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_UNSPEC;
pub const IFLA_GENEVE_ID: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_ID;
pub const IFLA_GENEVE_REMOTE: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_REMOTE;
pub const IFLA_GENEVE_TTL: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_TTL;
pub const IFLA_GENEVE_TOS: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_TOS;
pub const IFLA_GENEVE_PORT: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_PORT;
pub const IFLA_GENEVE_COLLECT_METADATA: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_COLLECT_METADATA;
pub const IFLA_GENEVE_REMOTE6: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_REMOTE6;
pub const IFLA_GENEVE_UDP_CSUM: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_UDP_CSUM;
pub const IFLA_GENEVE_UDP_ZERO_CSUM6_TX: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_UDP_ZERO_CSUM6_TX;
pub const IFLA_GENEVE_UDP_ZERO_CSUM6_RX: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_UDP_ZERO_CSUM6_RX;
pub const IFLA_GENEVE_LABEL: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_LABEL;
pub const IFLA_GENEVE_TTL_INHERIT: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_TTL_INHERIT;
pub const IFLA_GENEVE_DF: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_DF;
pub const IFLA_GENEVE_INNER_PROTO_INHERIT: _bindgen_ty_24 = _bindgen_ty_24::IFLA_GENEVE_INNER_PROTO_INHERIT;
pub const __IFLA_GENEVE_MAX: _bindgen_ty_24 = _bindgen_ty_24::__IFLA_GENEVE_MAX;
pub const IFLA_BAREUDP_UNSPEC: _bindgen_ty_25 = _bindgen_ty_25::IFLA_BAREUDP_UNSPEC;
pub const IFLA_BAREUDP_PORT: _bindgen_ty_25 = _bindgen_ty_25::IFLA_BAREUDP_PORT;
pub const IFLA_BAREUDP_ETHERTYPE: _bindgen_ty_25 = _bindgen_ty_25::IFLA_BAREUDP_ETHERTYPE;
pub const IFLA_BAREUDP_SRCPORT_MIN: _bindgen_ty_25 = _bindgen_ty_25::IFLA_BAREUDP_SRCPORT_MIN;
pub const IFLA_BAREUDP_MULTIPROTO_MODE: _bindgen_ty_25 = _bindgen_ty_25::IFLA_BAREUDP_MULTIPROTO_MODE;
pub const __IFLA_BAREUDP_MAX: _bindgen_ty_25 = _bindgen_ty_25::__IFLA_BAREUDP_MAX;
pub const IFLA_PPP_UNSPEC: _bindgen_ty_26 = _bindgen_ty_26::IFLA_PPP_UNSPEC;
pub const IFLA_PPP_DEV_FD: _bindgen_ty_26 = _bindgen_ty_26::IFLA_PPP_DEV_FD;
pub const __IFLA_PPP_MAX: _bindgen_ty_26 = _bindgen_ty_26::__IFLA_PPP_MAX;
pub const IFLA_GTP_UNSPEC: _bindgen_ty_27 = _bindgen_ty_27::IFLA_GTP_UNSPEC;
pub const IFLA_GTP_FD0: _bindgen_ty_27 = _bindgen_ty_27::IFLA_GTP_FD0;
pub const IFLA_GTP_FD1: _bindgen_ty_27 = _bindgen_ty_27::IFLA_GTP_FD1;
pub const IFLA_GTP_PDP_HASHSIZE: _bindgen_ty_27 = _bindgen_ty_27::IFLA_GTP_PDP_HASHSIZE;
pub const IFLA_GTP_ROLE: _bindgen_ty_27 = _bindgen_ty_27::IFLA_GTP_ROLE;
pub const IFLA_GTP_CREATE_SOCKETS: _bindgen_ty_27 = _bindgen_ty_27::IFLA_GTP_CREATE_SOCKETS;
pub const IFLA_GTP_RESTART_COUNT: _bindgen_ty_27 = _bindgen_ty_27::IFLA_GTP_RESTART_COUNT;
pub const __IFLA_GTP_MAX: _bindgen_ty_27 = _bindgen_ty_27::__IFLA_GTP_MAX;
pub const IFLA_BOND_UNSPEC: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_UNSPEC;
pub const IFLA_BOND_MODE: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_MODE;
pub const IFLA_BOND_ACTIVE_SLAVE: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_ACTIVE_SLAVE;
pub const IFLA_BOND_MIIMON: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_MIIMON;
pub const IFLA_BOND_UPDELAY: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_UPDELAY;
pub const IFLA_BOND_DOWNDELAY: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_DOWNDELAY;
pub const IFLA_BOND_USE_CARRIER: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_USE_CARRIER;
pub const IFLA_BOND_ARP_INTERVAL: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_ARP_INTERVAL;
pub const IFLA_BOND_ARP_IP_TARGET: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_ARP_IP_TARGET;
pub const IFLA_BOND_ARP_VALIDATE: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_ARP_VALIDATE;
pub const IFLA_BOND_ARP_ALL_TARGETS: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_ARP_ALL_TARGETS;
pub const IFLA_BOND_PRIMARY: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_PRIMARY;
pub const IFLA_BOND_PRIMARY_RESELECT: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_PRIMARY_RESELECT;
pub const IFLA_BOND_FAIL_OVER_MAC: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_FAIL_OVER_MAC;
pub const IFLA_BOND_XMIT_HASH_POLICY: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_XMIT_HASH_POLICY;
pub const IFLA_BOND_RESEND_IGMP: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_RESEND_IGMP;
pub const IFLA_BOND_NUM_PEER_NOTIF: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_NUM_PEER_NOTIF;
pub const IFLA_BOND_ALL_SLAVES_ACTIVE: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_ALL_SLAVES_ACTIVE;
pub const IFLA_BOND_MIN_LINKS: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_MIN_LINKS;
pub const IFLA_BOND_LP_INTERVAL: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_LP_INTERVAL;
pub const IFLA_BOND_PACKETS_PER_SLAVE: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_PACKETS_PER_SLAVE;
pub const IFLA_BOND_AD_LACP_RATE: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_LACP_RATE;
pub const IFLA_BOND_AD_SELECT: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_SELECT;
pub const IFLA_BOND_AD_INFO: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_INFO;
pub const IFLA_BOND_AD_ACTOR_SYS_PRIO: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_ACTOR_SYS_PRIO;
pub const IFLA_BOND_AD_USER_PORT_KEY: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_USER_PORT_KEY;
pub const IFLA_BOND_AD_ACTOR_SYSTEM: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_ACTOR_SYSTEM;
pub const IFLA_BOND_TLB_DYNAMIC_LB: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_TLB_DYNAMIC_LB;
pub const IFLA_BOND_PEER_NOTIF_DELAY: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_PEER_NOTIF_DELAY;
pub const IFLA_BOND_AD_LACP_ACTIVE: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_LACP_ACTIVE;
pub const IFLA_BOND_MISSED_MAX: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_MISSED_MAX;
pub const IFLA_BOND_NS_IP6_TARGET: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_NS_IP6_TARGET;
pub const __IFLA_BOND_MAX: _bindgen_ty_28 = _bindgen_ty_28::__IFLA_BOND_MAX;
pub const IFLA_BOND_AD_INFO_UNSPEC: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_AD_INFO_UNSPEC;
pub const IFLA_BOND_AD_INFO_AGGREGATOR: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_AD_INFO_AGGREGATOR;
pub const IFLA_BOND_AD_INFO_NUM_PORTS: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_AD_INFO_NUM_PORTS;
pub const IFLA_BOND_AD_INFO_ACTOR_KEY: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_AD_INFO_ACTOR_KEY;
pub const IFLA_BOND_AD_INFO_PARTNER_KEY: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_AD_INFO_PARTNER_KEY;
pub const IFLA_BOND_AD_INFO_PARTNER_MAC: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_AD_INFO_PARTNER_MAC;
pub const __IFLA_BOND_AD_INFO_MAX: _bindgen_ty_29 = _bindgen_ty_29::__IFLA_BOND_AD_INFO_MAX;
pub const IFLA_BOND_SLAVE_UNSPEC: _bindgen_ty_30 = _bindgen_ty_30::IFLA_BOND_SLAVE_UNSPEC;
pub const IFLA_BOND_SLAVE_STATE: _bindgen_ty_30 = _bindgen_ty_30::IFLA_BOND_SLAVE_STATE;
pub const IFLA_BOND_SLAVE_MII_STATUS: _bindgen_ty_30 = _bindgen_ty_30::IFLA_BOND_SLAVE_MII_STATUS;
pub const IFLA_BOND_SLAVE_LINK_FAILURE_COUNT: _bindgen_ty_30 = _bindgen_ty_30::IFLA_BOND_SLAVE_LINK_FAILURE_COUNT;
pub const IFLA_BOND_SLAVE_PERM_HWADDR: _bindgen_ty_30 = _bindgen_ty_30::IFLA_BOND_SLAVE_PERM_HWADDR;
pub const IFLA_BOND_SLAVE_QUEUE_ID: _bindgen_ty_30 = _bindgen_ty_30::IFLA_BOND_SLAVE_QUEUE_ID;
pub const IFLA_BOND_SLAVE_AD_AGGREGATOR_ID: _bindgen_ty_30 = _bindgen_ty_30::IFLA_BOND_SLAVE_AD_AGGREGATOR_ID;
pub const IFLA_BOND_SLAVE_AD_ACTOR_OPER_PORT_STATE: _bindgen_ty_30 = _bindgen_ty_30::IFLA_BOND_SLAVE_AD_ACTOR_OPER_PORT_STATE;
pub const IFLA_BOND_SLAVE_AD_PARTNER_OPER_PORT_STATE: _bindgen_ty_30 = _bindgen_ty_30::IFLA_BOND_SLAVE_AD_PARTNER_OPER_PORT_STATE;
pub const IFLA_BOND_SLAVE_PRIO: _bindgen_ty_30 = _bindgen_ty_30::IFLA_BOND_SLAVE_PRIO;
pub const __IFLA_BOND_SLAVE_MAX: _bindgen_ty_30 = _bindgen_ty_30::__IFLA_BOND_SLAVE_MAX;
pub const IFLA_VF_INFO_UNSPEC: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_INFO_UNSPEC;
pub const IFLA_VF_INFO: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_INFO;
pub const __IFLA_VF_INFO_MAX: _bindgen_ty_31 = _bindgen_ty_31::__IFLA_VF_INFO_MAX;
pub const IFLA_VF_UNSPEC: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_UNSPEC;
pub const IFLA_VF_MAC: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_MAC;
pub const IFLA_VF_VLAN: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_VLAN;
pub const IFLA_VF_TX_RATE: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_TX_RATE;
pub const IFLA_VF_SPOOFCHK: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_SPOOFCHK;
pub const IFLA_VF_LINK_STATE: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_LINK_STATE;
pub const IFLA_VF_RATE: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_RATE;
pub const IFLA_VF_RSS_QUERY_EN: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_RSS_QUERY_EN;
pub const IFLA_VF_STATS: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_STATS;
pub const IFLA_VF_TRUST: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_TRUST;
pub const IFLA_VF_IB_NODE_GUID: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_IB_NODE_GUID;
pub const IFLA_VF_IB_PORT_GUID: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_IB_PORT_GUID;
pub const IFLA_VF_VLAN_LIST: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_VLAN_LIST;
pub const IFLA_VF_BROADCAST: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_BROADCAST;
pub const __IFLA_VF_MAX: _bindgen_ty_32 = _bindgen_ty_32::__IFLA_VF_MAX;
pub const IFLA_VF_VLAN_INFO_UNSPEC: _bindgen_ty_33 = _bindgen_ty_33::IFLA_VF_VLAN_INFO_UNSPEC;
pub const IFLA_VF_VLAN_INFO: _bindgen_ty_33 = _bindgen_ty_33::IFLA_VF_VLAN_INFO;
pub const __IFLA_VF_VLAN_INFO_MAX: _bindgen_ty_33 = _bindgen_ty_33::__IFLA_VF_VLAN_INFO_MAX;
pub const IFLA_VF_LINK_STATE_AUTO: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_LINK_STATE_AUTO;
pub const IFLA_VF_LINK_STATE_ENABLE: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_LINK_STATE_ENABLE;
pub const IFLA_VF_LINK_STATE_DISABLE: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_LINK_STATE_DISABLE;
pub const __IFLA_VF_LINK_STATE_MAX: _bindgen_ty_34 = _bindgen_ty_34::__IFLA_VF_LINK_STATE_MAX;
pub const IFLA_VF_STATS_RX_PACKETS: _bindgen_ty_35 = _bindgen_ty_35::IFLA_VF_STATS_RX_PACKETS;
pub const IFLA_VF_STATS_TX_PACKETS: _bindgen_ty_35 = _bindgen_ty_35::IFLA_VF_STATS_TX_PACKETS;
pub const IFLA_VF_STATS_RX_BYTES: _bindgen_ty_35 = _bindgen_ty_35::IFLA_VF_STATS_RX_BYTES;
pub const IFLA_VF_STATS_TX_BYTES: _bindgen_ty_35 = _bindgen_ty_35::IFLA_VF_STATS_TX_BYTES;
pub const IFLA_VF_STATS_BROADCAST: _bindgen_ty_35 = _bindgen_ty_35::IFLA_VF_STATS_BROADCAST;
pub const IFLA_VF_STATS_MULTICAST: _bindgen_ty_35 = _bindgen_ty_35::IFLA_VF_STATS_MULTICAST;
pub const IFLA_VF_STATS_PAD: _bindgen_ty_35 = _bindgen_ty_35::IFLA_VF_STATS_PAD;
pub const IFLA_VF_STATS_RX_DROPPED: _bindgen_ty_35 = _bindgen_ty_35::IFLA_VF_STATS_RX_DROPPED;
pub const IFLA_VF_STATS_TX_DROPPED: _bindgen_ty_35 = _bindgen_ty_35::IFLA_VF_STATS_TX_DROPPED;
pub const __IFLA_VF_STATS_MAX: _bindgen_ty_35 = _bindgen_ty_35::__IFLA_VF_STATS_MAX;
pub const IFLA_VF_PORT_UNSPEC: _bindgen_ty_36 = _bindgen_ty_36::IFLA_VF_PORT_UNSPEC;
pub const IFLA_VF_PORT: _bindgen_ty_36 = _bindgen_ty_36::IFLA_VF_PORT;
pub const __IFLA_VF_PORT_MAX: _bindgen_ty_36 = _bindgen_ty_36::__IFLA_VF_PORT_MAX;
pub const IFLA_PORT_UNSPEC: _bindgen_ty_37 = _bindgen_ty_37::IFLA_PORT_UNSPEC;
pub const IFLA_PORT_VF: _bindgen_ty_37 = _bindgen_ty_37::IFLA_PORT_VF;
pub const IFLA_PORT_PROFILE: _bindgen_ty_37 = _bindgen_ty_37::IFLA_PORT_PROFILE;
pub const IFLA_PORT_VSI_TYPE: _bindgen_ty_37 = _bindgen_ty_37::IFLA_PORT_VSI_TYPE;
pub const IFLA_PORT_INSTANCE_UUID: _bindgen_ty_37 = _bindgen_ty_37::IFLA_PORT_INSTANCE_UUID;
pub const IFLA_PORT_HOST_UUID: _bindgen_ty_37 = _bindgen_ty_37::IFLA_PORT_HOST_UUID;
pub const IFLA_PORT_REQUEST: _bindgen_ty_37 = _bindgen_ty_37::IFLA_PORT_REQUEST;
pub const IFLA_PORT_RESPONSE: _bindgen_ty_37 = _bindgen_ty_37::IFLA_PORT_RESPONSE;
pub const __IFLA_PORT_MAX: _bindgen_ty_37 = _bindgen_ty_37::__IFLA_PORT_MAX;
pub const PORT_REQUEST_PREASSOCIATE: _bindgen_ty_38 = _bindgen_ty_38::PORT_REQUEST_PREASSOCIATE;
pub const PORT_REQUEST_PREASSOCIATE_RR: _bindgen_ty_38 = _bindgen_ty_38::PORT_REQUEST_PREASSOCIATE_RR;
pub const PORT_REQUEST_ASSOCIATE: _bindgen_ty_38 = _bindgen_ty_38::PORT_REQUEST_ASSOCIATE;
pub const PORT_REQUEST_DISASSOCIATE: _bindgen_ty_38 = _bindgen_ty_38::PORT_REQUEST_DISASSOCIATE;
pub const PORT_VDP_RESPONSE_SUCCESS: _bindgen_ty_39 = _bindgen_ty_39::PORT_VDP_RESPONSE_SUCCESS;
pub const PORT_VDP_RESPONSE_INVALID_FORMAT: _bindgen_ty_39 = _bindgen_ty_39::PORT_VDP_RESPONSE_INVALID_FORMAT;
pub const PORT_VDP_RESPONSE_INSUFFICIENT_RESOURCES: _bindgen_ty_39 = _bindgen_ty_39::PORT_VDP_RESPONSE_INSUFFICIENT_RESOURCES;
pub const PORT_VDP_RESPONSE_UNUSED_VTID: _bindgen_ty_39 = _bindgen_ty_39::PORT_VDP_RESPONSE_UNUSED_VTID;
pub const PORT_VDP_RESPONSE_VTID_VIOLATION: _bindgen_ty_39 = _bindgen_ty_39::PORT_VDP_RESPONSE_VTID_VIOLATION;
pub const PORT_VDP_RESPONSE_VTID_VERSION_VIOALTION: _bindgen_ty_39 = _bindgen_ty_39::PORT_VDP_RESPONSE_VTID_VERSION_VIOALTION;
pub const PORT_VDP_RESPONSE_OUT_OF_SYNC: _bindgen_ty_39 = _bindgen_ty_39::PORT_VDP_RESPONSE_OUT_OF_SYNC;
pub const PORT_PROFILE_RESPONSE_SUCCESS: _bindgen_ty_39 = _bindgen_ty_39::PORT_PROFILE_RESPONSE_SUCCESS;
pub const PORT_PROFILE_RESPONSE_INPROGRESS: _bindgen_ty_39 = _bindgen_ty_39::PORT_PROFILE_RESPONSE_INPROGRESS;
pub const PORT_PROFILE_RESPONSE_INVALID: _bindgen_ty_39 = _bindgen_ty_39::PORT_PROFILE_RESPONSE_INVALID;
pub const PORT_PROFILE_RESPONSE_BADSTATE: _bindgen_ty_39 = _bindgen_ty_39::PORT_PROFILE_RESPONSE_BADSTATE;
pub const PORT_PROFILE_RESPONSE_INSUFFICIENT_RESOURCES: _bindgen_ty_39 = _bindgen_ty_39::PORT_PROFILE_RESPONSE_INSUFFICIENT_RESOURCES;
pub const PORT_PROFILE_RESPONSE_ERROR: _bindgen_ty_39 = _bindgen_ty_39::PORT_PROFILE_RESPONSE_ERROR;
pub const IFLA_IPOIB_UNSPEC: _bindgen_ty_40 = _bindgen_ty_40::IFLA_IPOIB_UNSPEC;
pub const IFLA_IPOIB_PKEY: _bindgen_ty_40 = _bindgen_ty_40::IFLA_IPOIB_PKEY;
pub const IFLA_IPOIB_MODE: _bindgen_ty_40 = _bindgen_ty_40::IFLA_IPOIB_MODE;
pub const IFLA_IPOIB_UMCAST: _bindgen_ty_40 = _bindgen_ty_40::IFLA_IPOIB_UMCAST;
pub const __IFLA_IPOIB_MAX: _bindgen_ty_40 = _bindgen_ty_40::__IFLA_IPOIB_MAX;
pub const IPOIB_MODE_DATAGRAM: _bindgen_ty_41 = _bindgen_ty_41::IPOIB_MODE_DATAGRAM;
pub const IPOIB_MODE_CONNECTED: _bindgen_ty_41 = _bindgen_ty_41::IPOIB_MODE_CONNECTED;
pub const HSR_PROTOCOL_HSR: _bindgen_ty_42 = _bindgen_ty_42::HSR_PROTOCOL_HSR;
pub const HSR_PROTOCOL_PRP: _bindgen_ty_42 = _bindgen_ty_42::HSR_PROTOCOL_PRP;
pub const HSR_PROTOCOL_MAX: _bindgen_ty_42 = _bindgen_ty_42::HSR_PROTOCOL_MAX;
pub const IFLA_HSR_UNSPEC: _bindgen_ty_43 = _bindgen_ty_43::IFLA_HSR_UNSPEC;
pub const IFLA_HSR_SLAVE1: _bindgen_ty_43 = _bindgen_ty_43::IFLA_HSR_SLAVE1;
pub const IFLA_HSR_SLAVE2: _bindgen_ty_43 = _bindgen_ty_43::IFLA_HSR_SLAVE2;
pub const IFLA_HSR_MULTICAST_SPEC: _bindgen_ty_43 = _bindgen_ty_43::IFLA_HSR_MULTICAST_SPEC;
pub const IFLA_HSR_SUPERVISION_ADDR: _bindgen_ty_43 = _bindgen_ty_43::IFLA_HSR_SUPERVISION_ADDR;
pub const IFLA_HSR_SEQ_NR: _bindgen_ty_43 = _bindgen_ty_43::IFLA_HSR_SEQ_NR;
pub const IFLA_HSR_VERSION: _bindgen_ty_43 = _bindgen_ty_43::IFLA_HSR_VERSION;
pub const IFLA_HSR_PROTOCOL: _bindgen_ty_43 = _bindgen_ty_43::IFLA_HSR_PROTOCOL;
pub const __IFLA_HSR_MAX: _bindgen_ty_43 = _bindgen_ty_43::__IFLA_HSR_MAX;
pub const IFLA_STATS_UNSPEC: _bindgen_ty_44 = _bindgen_ty_44::IFLA_STATS_UNSPEC;
pub const IFLA_STATS_LINK_64: _bindgen_ty_44 = _bindgen_ty_44::IFLA_STATS_LINK_64;
pub const IFLA_STATS_LINK_XSTATS: _bindgen_ty_44 = _bindgen_ty_44::IFLA_STATS_LINK_XSTATS;
pub const IFLA_STATS_LINK_XSTATS_SLAVE: _bindgen_ty_44 = _bindgen_ty_44::IFLA_STATS_LINK_XSTATS_SLAVE;
pub const IFLA_STATS_LINK_OFFLOAD_XSTATS: _bindgen_ty_44 = _bindgen_ty_44::IFLA_STATS_LINK_OFFLOAD_XSTATS;
pub const IFLA_STATS_AF_SPEC: _bindgen_ty_44 = _bindgen_ty_44::IFLA_STATS_AF_SPEC;
pub const __IFLA_STATS_MAX: _bindgen_ty_44 = _bindgen_ty_44::__IFLA_STATS_MAX;
pub const IFLA_STATS_GETSET_UNSPEC: _bindgen_ty_45 = _bindgen_ty_45::IFLA_STATS_GETSET_UNSPEC;
pub const IFLA_STATS_GET_FILTERS: _bindgen_ty_45 = _bindgen_ty_45::IFLA_STATS_GET_FILTERS;
pub const IFLA_STATS_SET_OFFLOAD_XSTATS_L3_STATS: _bindgen_ty_45 = _bindgen_ty_45::IFLA_STATS_SET_OFFLOAD_XSTATS_L3_STATS;
pub const __IFLA_STATS_GETSET_MAX: _bindgen_ty_45 = _bindgen_ty_45::__IFLA_STATS_GETSET_MAX;
pub const LINK_XSTATS_TYPE_UNSPEC: _bindgen_ty_46 = _bindgen_ty_46::LINK_XSTATS_TYPE_UNSPEC;
pub const LINK_XSTATS_TYPE_BRIDGE: _bindgen_ty_46 = _bindgen_ty_46::LINK_XSTATS_TYPE_BRIDGE;
pub const LINK_XSTATS_TYPE_BOND: _bindgen_ty_46 = _bindgen_ty_46::LINK_XSTATS_TYPE_BOND;
pub const __LINK_XSTATS_TYPE_MAX: _bindgen_ty_46 = _bindgen_ty_46::__LINK_XSTATS_TYPE_MAX;
pub const IFLA_OFFLOAD_XSTATS_UNSPEC: _bindgen_ty_47 = _bindgen_ty_47::IFLA_OFFLOAD_XSTATS_UNSPEC;
pub const IFLA_OFFLOAD_XSTATS_CPU_HIT: _bindgen_ty_47 = _bindgen_ty_47::IFLA_OFFLOAD_XSTATS_CPU_HIT;
pub const IFLA_OFFLOAD_XSTATS_HW_S_INFO: _bindgen_ty_47 = _bindgen_ty_47::IFLA_OFFLOAD_XSTATS_HW_S_INFO;
pub const IFLA_OFFLOAD_XSTATS_L3_STATS: _bindgen_ty_47 = _bindgen_ty_47::IFLA_OFFLOAD_XSTATS_L3_STATS;
pub const __IFLA_OFFLOAD_XSTATS_MAX: _bindgen_ty_47 = _bindgen_ty_47::__IFLA_OFFLOAD_XSTATS_MAX;
pub const IFLA_OFFLOAD_XSTATS_HW_S_INFO_UNSPEC: _bindgen_ty_48 = _bindgen_ty_48::IFLA_OFFLOAD_XSTATS_HW_S_INFO_UNSPEC;
pub const IFLA_OFFLOAD_XSTATS_HW_S_INFO_REQUEST: _bindgen_ty_48 = _bindgen_ty_48::IFLA_OFFLOAD_XSTATS_HW_S_INFO_REQUEST;
pub const IFLA_OFFLOAD_XSTATS_HW_S_INFO_USED: _bindgen_ty_48 = _bindgen_ty_48::IFLA_OFFLOAD_XSTATS_HW_S_INFO_USED;
pub const __IFLA_OFFLOAD_XSTATS_HW_S_INFO_MAX: _bindgen_ty_48 = _bindgen_ty_48::__IFLA_OFFLOAD_XSTATS_HW_S_INFO_MAX;
pub const XDP_ATTACHED_NONE: _bindgen_ty_49 = _bindgen_ty_49::XDP_ATTACHED_NONE;
pub const XDP_ATTACHED_DRV: _bindgen_ty_49 = _bindgen_ty_49::XDP_ATTACHED_DRV;
pub const XDP_ATTACHED_SKB: _bindgen_ty_49 = _bindgen_ty_49::XDP_ATTACHED_SKB;
pub const XDP_ATTACHED_HW: _bindgen_ty_49 = _bindgen_ty_49::XDP_ATTACHED_HW;
pub const XDP_ATTACHED_MULTI: _bindgen_ty_49 = _bindgen_ty_49::XDP_ATTACHED_MULTI;
pub const IFLA_XDP_UNSPEC: _bindgen_ty_50 = _bindgen_ty_50::IFLA_XDP_UNSPEC;
pub const IFLA_XDP_FD: _bindgen_ty_50 = _bindgen_ty_50::IFLA_XDP_FD;
pub const IFLA_XDP_ATTACHED: _bindgen_ty_50 = _bindgen_ty_50::IFLA_XDP_ATTACHED;
pub const IFLA_XDP_FLAGS: _bindgen_ty_50 = _bindgen_ty_50::IFLA_XDP_FLAGS;
pub const IFLA_XDP_PROG_ID: _bindgen_ty_50 = _bindgen_ty_50::IFLA_XDP_PROG_ID;
pub const IFLA_XDP_DRV_PROG_ID: _bindgen_ty_50 = _bindgen_ty_50::IFLA_XDP_DRV_PROG_ID;
pub const IFLA_XDP_SKB_PROG_ID: _bindgen_ty_50 = _bindgen_ty_50::IFLA_XDP_SKB_PROG_ID;
pub const IFLA_XDP_HW_PROG_ID: _bindgen_ty_50 = _bindgen_ty_50::IFLA_XDP_HW_PROG_ID;
pub const IFLA_XDP_EXPECTED_FD: _bindgen_ty_50 = _bindgen_ty_50::IFLA_XDP_EXPECTED_FD;
pub const __IFLA_XDP_MAX: _bindgen_ty_50 = _bindgen_ty_50::__IFLA_XDP_MAX;
pub const IFLA_EVENT_NONE: _bindgen_ty_51 = _bindgen_ty_51::IFLA_EVENT_NONE;
pub const IFLA_EVENT_REBOOT: _bindgen_ty_51 = _bindgen_ty_51::IFLA_EVENT_REBOOT;
pub const IFLA_EVENT_FEATURES: _bindgen_ty_51 = _bindgen_ty_51::IFLA_EVENT_FEATURES;
pub const IFLA_EVENT_BONDING_FAILOVER: _bindgen_ty_51 = _bindgen_ty_51::IFLA_EVENT_BONDING_FAILOVER;
pub const IFLA_EVENT_NOTIFY_PEERS: _bindgen_ty_51 = _bindgen_ty_51::IFLA_EVENT_NOTIFY_PEERS;
pub const IFLA_EVENT_IGMP_RESEND: _bindgen_ty_51 = _bindgen_ty_51::IFLA_EVENT_IGMP_RESEND;
pub const IFLA_EVENT_BONDING_OPTIONS: _bindgen_ty_51 = _bindgen_ty_51::IFLA_EVENT_BONDING_OPTIONS;
pub const IFLA_TUN_UNSPEC: _bindgen_ty_52 = _bindgen_ty_52::IFLA_TUN_UNSPEC;
pub const IFLA_TUN_OWNER: _bindgen_ty_52 = _bindgen_ty_52::IFLA_TUN_OWNER;
pub const IFLA_TUN_GROUP: _bindgen_ty_52 = _bindgen_ty_52::IFLA_TUN_GROUP;
pub const IFLA_TUN_TYPE: _bindgen_ty_52 = _bindgen_ty_52::IFLA_TUN_TYPE;
pub const IFLA_TUN_PI: _bindgen_ty_52 = _bindgen_ty_52::IFLA_TUN_PI;
pub const IFLA_TUN_VNET_HDR: _bindgen_ty_52 = _bindgen_ty_52::IFLA_TUN_VNET_HDR;
pub const IFLA_TUN_PERSIST: _bindgen_ty_52 = _bindgen_ty_52::IFLA_TUN_PERSIST;
pub const IFLA_TUN_MULTI_QUEUE: _bindgen_ty_52 = _bindgen_ty_52::IFLA_TUN_MULTI_QUEUE;
pub const IFLA_TUN_NUM_QUEUES: _bindgen_ty_52 = _bindgen_ty_52::IFLA_TUN_NUM_QUEUES;
pub const IFLA_TUN_NUM_DISABLED_QUEUES: _bindgen_ty_52 = _bindgen_ty_52::IFLA_TUN_NUM_DISABLED_QUEUES;
pub const __IFLA_TUN_MAX: _bindgen_ty_52 = _bindgen_ty_52::__IFLA_TUN_MAX;
pub const IFLA_RMNET_UNSPEC: _bindgen_ty_53 = _bindgen_ty_53::IFLA_RMNET_UNSPEC;
pub const IFLA_RMNET_MUX_ID: _bindgen_ty_53 = _bindgen_ty_53::IFLA_RMNET_MUX_ID;
pub const IFLA_RMNET_FLAGS: _bindgen_ty_53 = _bindgen_ty_53::IFLA_RMNET_FLAGS;
pub const __IFLA_RMNET_MAX: _bindgen_ty_53 = _bindgen_ty_53::__IFLA_RMNET_MAX;
pub const IFLA_MCTP_UNSPEC: _bindgen_ty_54 = _bindgen_ty_54::IFLA_MCTP_UNSPEC;
pub const IFLA_MCTP_NET: _bindgen_ty_54 = _bindgen_ty_54::IFLA_MCTP_NET;
pub const __IFLA_MCTP_MAX: _bindgen_ty_54 = _bindgen_ty_54::__IFLA_MCTP_MAX;
pub const IFLA_DSA_UNSPEC: _bindgen_ty_55 = _bindgen_ty_55::IFLA_DSA_UNSPEC;
pub const IFLA_DSA_MASTER: _bindgen_ty_55 = _bindgen_ty_55::IFLA_DSA_MASTER;
pub const __IFLA_DSA_MAX: _bindgen_ty_55 = _bindgen_ty_55::__IFLA_DSA_MAX;
pub const IF_PORT_UNKNOWN: _bindgen_ty_56 = _bindgen_ty_56::IF_PORT_UNKNOWN;
pub const IF_PORT_10BASE2: _bindgen_ty_56 = _bindgen_ty_56::IF_PORT_10BASE2;
pub const IF_PORT_10BASET: _bindgen_ty_56 = _bindgen_ty_56::IF_PORT_10BASET;
pub const IF_PORT_AUI: _bindgen_ty_56 = _bindgen_ty_56::IF_PORT_AUI;
pub const IF_PORT_100BASET: _bindgen_ty_56 = _bindgen_ty_56::IF_PORT_100BASET;
pub const IF_PORT_100BASETX: _bindgen_ty_56 = _bindgen_ty_56::IF_PORT_100BASETX;
pub const IF_PORT_100BASEFX: _bindgen_ty_56 = _bindgen_ty_56::IF_PORT_100BASEFX;
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum net_device_flags {
IFF_UP = 1,
IFF_BROADCAST = 2,
IFF_DEBUG = 4,
IFF_LOOPBACK = 8,
IFF_POINTOPOINT = 16,
IFF_NOTRAILERS = 32,
IFF_RUNNING = 64,
IFF_NOARP = 128,
IFF_PROMISC = 256,
IFF_ALLMULTI = 512,
IFF_MASTER = 1024,
IFF_SLAVE = 2048,
IFF_MULTICAST = 4096,
IFF_PORTSEL = 8192,
IFF_AUTOMEDIA = 16384,
IFF_DYNAMIC = 32768,
IFF_LOWER_UP = 65536,
IFF_DORMANT = 131072,
IFF_ECHO = 262144,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_1 {
IF_OPER_UNKNOWN = 0,
IF_OPER_NOTPRESENT = 1,
IF_OPER_DOWN = 2,
IF_OPER_LOWERLAYERDOWN = 3,
IF_OPER_TESTING = 4,
IF_OPER_DORMANT = 5,
IF_OPER_UP = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_2 {
IF_LINK_MODE_DEFAULT = 0,
IF_LINK_MODE_DORMANT = 1,
IF_LINK_MODE_TESTING = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum tpacket_versions {
TPACKET_V1 = 0,
TPACKET_V2 = 1,
TPACKET_V3 = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum nlmsgerr_attrs {
NLMSGERR_ATTR_UNUSED = 0,
NLMSGERR_ATTR_MSG = 1,
NLMSGERR_ATTR_OFFS = 2,
NLMSGERR_ATTR_COOKIE = 3,
NLMSGERR_ATTR_POLICY = 4,
NLMSGERR_ATTR_MISS_TYPE = 5,
NLMSGERR_ATTR_MISS_NEST = 6,
__NLMSGERR_ATTR_MAX = 7,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum nl_mmap_status {
NL_MMAP_STATUS_UNUSED = 0,
NL_MMAP_STATUS_RESERVED = 1,
NL_MMAP_STATUS_VALID = 2,
NL_MMAP_STATUS_COPY = 3,
NL_MMAP_STATUS_SKIP = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_3 {
NETLINK_UNCONNECTED = 0,
NETLINK_CONNECTED = 1,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum netlink_attribute_type {
NL_ATTR_TYPE_INVALID = 0,
NL_ATTR_TYPE_FLAG = 1,
NL_ATTR_TYPE_U8 = 2,
NL_ATTR_TYPE_U16 = 3,
NL_ATTR_TYPE_U32 = 4,
NL_ATTR_TYPE_U64 = 5,
NL_ATTR_TYPE_S8 = 6,
NL_ATTR_TYPE_S16 = 7,
NL_ATTR_TYPE_S32 = 8,
NL_ATTR_TYPE_S64 = 9,
NL_ATTR_TYPE_BINARY = 10,
NL_ATTR_TYPE_STRING = 11,
NL_ATTR_TYPE_NUL_STRING = 12,
NL_ATTR_TYPE_NESTED = 13,
NL_ATTR_TYPE_NESTED_ARRAY = 14,
NL_ATTR_TYPE_BITFIELD32 = 15,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum netlink_policy_type_attr {
NL_POLICY_TYPE_ATTR_UNSPEC = 0,
NL_POLICY_TYPE_ATTR_TYPE = 1,
NL_POLICY_TYPE_ATTR_MIN_VALUE_S = 2,
NL_POLICY_TYPE_ATTR_MAX_VALUE_S = 3,
NL_POLICY_TYPE_ATTR_MIN_VALUE_U = 4,
NL_POLICY_TYPE_ATTR_MAX_VALUE_U = 5,
NL_POLICY_TYPE_ATTR_MIN_LENGTH = 6,
NL_POLICY_TYPE_ATTR_MAX_LENGTH = 7,
NL_POLICY_TYPE_ATTR_POLICY_IDX = 8,
NL_POLICY_TYPE_ATTR_POLICY_MAXTYPE = 9,
NL_POLICY_TYPE_ATTR_BITFIELD32_MASK = 10,
NL_POLICY_TYPE_ATTR_PAD = 11,
NL_POLICY_TYPE_ATTR_MASK = 12,
__NL_POLICY_TYPE_ATTR_MAX = 13,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_4 {
IFLA_UNSPEC = 0,
IFLA_ADDRESS = 1,
IFLA_BROADCAST = 2,
IFLA_IFNAME = 3,
IFLA_MTU = 4,
IFLA_LINK = 5,
IFLA_QDISC = 6,
IFLA_STATS = 7,
IFLA_COST = 8,
IFLA_PRIORITY = 9,
IFLA_MASTER = 10,
IFLA_WIRELESS = 11,
IFLA_PROTINFO = 12,
IFLA_TXQLEN = 13,
IFLA_MAP = 14,
IFLA_WEIGHT = 15,
IFLA_OPERSTATE = 16,
IFLA_LINKMODE = 17,
IFLA_LINKINFO = 18,
IFLA_NET_NS_PID = 19,
IFLA_IFALIAS = 20,
IFLA_NUM_VF = 21,
IFLA_VFINFO_LIST = 22,
IFLA_STATS64 = 23,
IFLA_VF_PORTS = 24,
IFLA_PORT_SELF = 25,
IFLA_AF_SPEC = 26,
IFLA_GROUP = 27,
IFLA_NET_NS_FD = 28,
IFLA_EXT_MASK = 29,
IFLA_PROMISCUITY = 30,
IFLA_NUM_TX_QUEUES = 31,
IFLA_NUM_RX_QUEUES = 32,
IFLA_CARRIER = 33,
IFLA_PHYS_PORT_ID = 34,
IFLA_CARRIER_CHANGES = 35,
IFLA_PHYS_SWITCH_ID = 36,
IFLA_LINK_NETNSID = 37,
IFLA_PHYS_PORT_NAME = 38,
IFLA_PROTO_DOWN = 39,
IFLA_GSO_MAX_SEGS = 40,
IFLA_GSO_MAX_SIZE = 41,
IFLA_PAD = 42,
IFLA_XDP = 43,
IFLA_EVENT = 44,
IFLA_NEW_NETNSID = 45,
IFLA_IF_NETNSID = 46,
IFLA_CARRIER_UP_COUNT = 47,
IFLA_CARRIER_DOWN_COUNT = 48,
IFLA_NEW_IFINDEX = 49,
IFLA_MIN_MTU = 50,
IFLA_MAX_MTU = 51,
IFLA_PROP_LIST = 52,
IFLA_ALT_IFNAME = 53,
IFLA_PERM_ADDRESS = 54,
IFLA_PROTO_DOWN_REASON = 55,
IFLA_PARENT_DEV_NAME = 56,
IFLA_PARENT_DEV_BUS_NAME = 57,
IFLA_GRO_MAX_SIZE = 58,
IFLA_TSO_MAX_SIZE = 59,
IFLA_TSO_MAX_SEGS = 60,
IFLA_ALLMULTI = 61,
IFLA_DEVLINK_PORT = 62,
IFLA_GSO_IPV4_MAX_SIZE = 63,
IFLA_GRO_IPV4_MAX_SIZE = 64,
__IFLA_MAX = 65,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_5 {
IFLA_PROTO_DOWN_REASON_UNSPEC = 0,
IFLA_PROTO_DOWN_REASON_MASK = 1,
IFLA_PROTO_DOWN_REASON_VALUE = 2,
__IFLA_PROTO_DOWN_REASON_CNT = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_6 {
IFLA_INET_UNSPEC = 0,
IFLA_INET_CONF = 1,
__IFLA_INET_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_7 {
IFLA_INET6_UNSPEC = 0,
IFLA_INET6_FLAGS = 1,
IFLA_INET6_CONF = 2,
IFLA_INET6_STATS = 3,
IFLA_INET6_MCAST = 4,
IFLA_INET6_CACHEINFO = 5,
IFLA_INET6_ICMP6STATS = 6,
IFLA_INET6_TOKEN = 7,
IFLA_INET6_ADDR_GEN_MODE = 8,
IFLA_INET6_RA_MTU = 9,
__IFLA_INET6_MAX = 10,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum in6_addr_gen_mode {
IN6_ADDR_GEN_MODE_EUI64 = 0,
IN6_ADDR_GEN_MODE_NONE = 1,
IN6_ADDR_GEN_MODE_STABLE_PRIVACY = 2,
IN6_ADDR_GEN_MODE_RANDOM = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_8 {
IFLA_BR_UNSPEC = 0,
IFLA_BR_FORWARD_DELAY = 1,
IFLA_BR_HELLO_TIME = 2,
IFLA_BR_MAX_AGE = 3,
IFLA_BR_AGEING_TIME = 4,
IFLA_BR_STP_STATE = 5,
IFLA_BR_PRIORITY = 6,
IFLA_BR_VLAN_FILTERING = 7,
IFLA_BR_VLAN_PROTOCOL = 8,
IFLA_BR_GROUP_FWD_MASK = 9,
IFLA_BR_ROOT_ID = 10,
IFLA_BR_BRIDGE_ID = 11,
IFLA_BR_ROOT_PORT = 12,
IFLA_BR_ROOT_PATH_COST = 13,
IFLA_BR_TOPOLOGY_CHANGE = 14,
IFLA_BR_TOPOLOGY_CHANGE_DETECTED = 15,
IFLA_BR_HELLO_TIMER = 16,
IFLA_BR_TCN_TIMER = 17,
IFLA_BR_TOPOLOGY_CHANGE_TIMER = 18,
IFLA_BR_GC_TIMER = 19,
IFLA_BR_GROUP_ADDR = 20,
IFLA_BR_FDB_FLUSH = 21,
IFLA_BR_MCAST_ROUTER = 22,
IFLA_BR_MCAST_SNOOPING = 23,
IFLA_BR_MCAST_QUERY_USE_IFADDR = 24,
IFLA_BR_MCAST_QUERIER = 25,
IFLA_BR_MCAST_HASH_ELASTICITY = 26,
IFLA_BR_MCAST_HASH_MAX = 27,
IFLA_BR_MCAST_LAST_MEMBER_CNT = 28,
IFLA_BR_MCAST_STARTUP_QUERY_CNT = 29,
IFLA_BR_MCAST_LAST_MEMBER_INTVL = 30,
IFLA_BR_MCAST_MEMBERSHIP_INTVL = 31,
IFLA_BR_MCAST_QUERIER_INTVL = 32,
IFLA_BR_MCAST_QUERY_INTVL = 33,
IFLA_BR_MCAST_QUERY_RESPONSE_INTVL = 34,
IFLA_BR_MCAST_STARTUP_QUERY_INTVL = 35,
IFLA_BR_NF_CALL_IPTABLES = 36,
IFLA_BR_NF_CALL_IP6TABLES = 37,
IFLA_BR_NF_CALL_ARPTABLES = 38,
IFLA_BR_VLAN_DEFAULT_PVID = 39,
IFLA_BR_PAD = 40,
IFLA_BR_VLAN_STATS_ENABLED = 41,
IFLA_BR_MCAST_STATS_ENABLED = 42,
IFLA_BR_MCAST_IGMP_VERSION = 43,
IFLA_BR_MCAST_MLD_VERSION = 44,
IFLA_BR_VLAN_STATS_PER_PORT = 45,
IFLA_BR_MULTI_BOOLOPT = 46,
IFLA_BR_MCAST_QUERIER_STATE = 47,
__IFLA_BR_MAX = 48,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_9 {
BRIDGE_MODE_UNSPEC = 0,
BRIDGE_MODE_HAIRPIN = 1,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_10 {
IFLA_BRPORT_UNSPEC = 0,
IFLA_BRPORT_STATE = 1,
IFLA_BRPORT_PRIORITY = 2,
IFLA_BRPORT_COST = 3,
IFLA_BRPORT_MODE = 4,
IFLA_BRPORT_GUARD = 5,
IFLA_BRPORT_PROTECT = 6,
IFLA_BRPORT_FAST_LEAVE = 7,
IFLA_BRPORT_LEARNING = 8,
IFLA_BRPORT_UNICAST_FLOOD = 9,
IFLA_BRPORT_PROXYARP = 10,
IFLA_BRPORT_LEARNING_SYNC = 11,
IFLA_BRPORT_PROXYARP_WIFI = 12,
IFLA_BRPORT_ROOT_ID = 13,
IFLA_BRPORT_BRIDGE_ID = 14,
IFLA_BRPORT_DESIGNATED_PORT = 15,
IFLA_BRPORT_DESIGNATED_COST = 16,
IFLA_BRPORT_ID = 17,
IFLA_BRPORT_NO = 18,
IFLA_BRPORT_TOPOLOGY_CHANGE_ACK = 19,
IFLA_BRPORT_CONFIG_PENDING = 20,
IFLA_BRPORT_MESSAGE_AGE_TIMER = 21,
IFLA_BRPORT_FORWARD_DELAY_TIMER = 22,
IFLA_BRPORT_HOLD_TIMER = 23,
IFLA_BRPORT_FLUSH = 24,
IFLA_BRPORT_MULTICAST_ROUTER = 25,
IFLA_BRPORT_PAD = 26,
IFLA_BRPORT_MCAST_FLOOD = 27,
IFLA_BRPORT_MCAST_TO_UCAST = 28,
IFLA_BRPORT_VLAN_TUNNEL = 29,
IFLA_BRPORT_BCAST_FLOOD = 30,
IFLA_BRPORT_GROUP_FWD_MASK = 31,
IFLA_BRPORT_NEIGH_SUPPRESS = 32,
IFLA_BRPORT_ISOLATED = 33,
IFLA_BRPORT_BACKUP_PORT = 34,
IFLA_BRPORT_MRP_RING_OPEN = 35,
IFLA_BRPORT_MRP_IN_OPEN = 36,
IFLA_BRPORT_MCAST_EHT_HOSTS_LIMIT = 37,
IFLA_BRPORT_MCAST_EHT_HOSTS_CNT = 38,
IFLA_BRPORT_LOCKED = 39,
IFLA_BRPORT_MAB = 40,
IFLA_BRPORT_MCAST_N_GROUPS = 41,
IFLA_BRPORT_MCAST_MAX_GROUPS = 42,
__IFLA_BRPORT_MAX = 43,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_11 {
IFLA_INFO_UNSPEC = 0,
IFLA_INFO_KIND = 1,
IFLA_INFO_DATA = 2,
IFLA_INFO_XSTATS = 3,
IFLA_INFO_SLAVE_KIND = 4,
IFLA_INFO_SLAVE_DATA = 5,
__IFLA_INFO_MAX = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_12 {
IFLA_VLAN_UNSPEC = 0,
IFLA_VLAN_ID = 1,
IFLA_VLAN_FLAGS = 2,
IFLA_VLAN_EGRESS_QOS = 3,
IFLA_VLAN_INGRESS_QOS = 4,
IFLA_VLAN_PROTOCOL = 5,
__IFLA_VLAN_MAX = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_13 {
IFLA_VLAN_QOS_UNSPEC = 0,
IFLA_VLAN_QOS_MAPPING = 1,
__IFLA_VLAN_QOS_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_14 {
IFLA_MACVLAN_UNSPEC = 0,
IFLA_MACVLAN_MODE = 1,
IFLA_MACVLAN_FLAGS = 2,
IFLA_MACVLAN_MACADDR_MODE = 3,
IFLA_MACVLAN_MACADDR = 4,
IFLA_MACVLAN_MACADDR_DATA = 5,
IFLA_MACVLAN_MACADDR_COUNT = 6,
IFLA_MACVLAN_BC_QUEUE_LEN = 7,
IFLA_MACVLAN_BC_QUEUE_LEN_USED = 8,
__IFLA_MACVLAN_MAX = 9,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum macvlan_mode {
MACVLAN_MODE_PRIVATE = 1,
MACVLAN_MODE_VEPA = 2,
MACVLAN_MODE_BRIDGE = 4,
MACVLAN_MODE_PASSTHRU = 8,
MACVLAN_MODE_SOURCE = 16,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum macvlan_macaddr_mode {
MACVLAN_MACADDR_ADD = 0,
MACVLAN_MACADDR_DEL = 1,
MACVLAN_MACADDR_FLUSH = 2,
MACVLAN_MACADDR_SET = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_15 {
IFLA_VRF_UNSPEC = 0,
IFLA_VRF_TABLE = 1,
__IFLA_VRF_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_16 {
IFLA_VRF_PORT_UNSPEC = 0,
IFLA_VRF_PORT_TABLE = 1,
__IFLA_VRF_PORT_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_17 {
IFLA_MACSEC_UNSPEC = 0,
IFLA_MACSEC_SCI = 1,
IFLA_MACSEC_PORT = 2,
IFLA_MACSEC_ICV_LEN = 3,
IFLA_MACSEC_CIPHER_SUITE = 4,
IFLA_MACSEC_WINDOW = 5,
IFLA_MACSEC_ENCODING_SA = 6,
IFLA_MACSEC_ENCRYPT = 7,
IFLA_MACSEC_PROTECT = 8,
IFLA_MACSEC_INC_SCI = 9,
IFLA_MACSEC_ES = 10,
IFLA_MACSEC_SCB = 11,
IFLA_MACSEC_REPLAY_PROTECT = 12,
IFLA_MACSEC_VALIDATION = 13,
IFLA_MACSEC_PAD = 14,
IFLA_MACSEC_OFFLOAD = 15,
__IFLA_MACSEC_MAX = 16,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_18 {
IFLA_XFRM_UNSPEC = 0,
IFLA_XFRM_LINK = 1,
IFLA_XFRM_IF_ID = 2,
IFLA_XFRM_COLLECT_METADATA = 3,
__IFLA_XFRM_MAX = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum macsec_validation_type {
MACSEC_VALIDATE_DISABLED = 0,
MACSEC_VALIDATE_CHECK = 1,
MACSEC_VALIDATE_STRICT = 2,
__MACSEC_VALIDATE_END = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum macsec_offload {
MACSEC_OFFLOAD_OFF = 0,
MACSEC_OFFLOAD_PHY = 1,
MACSEC_OFFLOAD_MAC = 2,
__MACSEC_OFFLOAD_END = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_19 {
IFLA_IPVLAN_UNSPEC = 0,
IFLA_IPVLAN_MODE = 1,
IFLA_IPVLAN_FLAGS = 2,
__IFLA_IPVLAN_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum ipvlan_mode {
IPVLAN_MODE_L2 = 0,
IPVLAN_MODE_L3 = 1,
IPVLAN_MODE_L3S = 2,
IPVLAN_MODE_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_20 {
VNIFILTER_ENTRY_STATS_UNSPEC = 0,
VNIFILTER_ENTRY_STATS_RX_BYTES = 1,
VNIFILTER_ENTRY_STATS_RX_PKTS = 2,
VNIFILTER_ENTRY_STATS_RX_DROPS = 3,
VNIFILTER_ENTRY_STATS_RX_ERRORS = 4,
VNIFILTER_ENTRY_STATS_TX_BYTES = 5,
VNIFILTER_ENTRY_STATS_TX_PKTS = 6,
VNIFILTER_ENTRY_STATS_TX_DROPS = 7,
VNIFILTER_ENTRY_STATS_TX_ERRORS = 8,
VNIFILTER_ENTRY_STATS_PAD = 9,
__VNIFILTER_ENTRY_STATS_MAX = 10,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_21 {
VXLAN_VNIFILTER_ENTRY_UNSPEC = 0,
VXLAN_VNIFILTER_ENTRY_START = 1,
VXLAN_VNIFILTER_ENTRY_END = 2,
VXLAN_VNIFILTER_ENTRY_GROUP = 3,
VXLAN_VNIFILTER_ENTRY_GROUP6 = 4,
VXLAN_VNIFILTER_ENTRY_STATS = 5,
__VXLAN_VNIFILTER_ENTRY_MAX = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_22 {
VXLAN_VNIFILTER_UNSPEC = 0,
VXLAN_VNIFILTER_ENTRY = 1,
__VXLAN_VNIFILTER_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_23 {
IFLA_VXLAN_UNSPEC = 0,
IFLA_VXLAN_ID = 1,
IFLA_VXLAN_GROUP = 2,
IFLA_VXLAN_LINK = 3,
IFLA_VXLAN_LOCAL = 4,
IFLA_VXLAN_TTL = 5,
IFLA_VXLAN_TOS = 6,
IFLA_VXLAN_LEARNING = 7,
IFLA_VXLAN_AGEING = 8,
IFLA_VXLAN_LIMIT = 9,
IFLA_VXLAN_PORT_RANGE = 10,
IFLA_VXLAN_PROXY = 11,
IFLA_VXLAN_RSC = 12,
IFLA_VXLAN_L2MISS = 13,
IFLA_VXLAN_L3MISS = 14,
IFLA_VXLAN_PORT = 15,
IFLA_VXLAN_GROUP6 = 16,
IFLA_VXLAN_LOCAL6 = 17,
IFLA_VXLAN_UDP_CSUM = 18,
IFLA_VXLAN_UDP_ZERO_CSUM6_TX = 19,
IFLA_VXLAN_UDP_ZERO_CSUM6_RX = 20,
IFLA_VXLAN_REMCSUM_TX = 21,
IFLA_VXLAN_REMCSUM_RX = 22,
IFLA_VXLAN_GBP = 23,
IFLA_VXLAN_REMCSUM_NOPARTIAL = 24,
IFLA_VXLAN_COLLECT_METADATA = 25,
IFLA_VXLAN_LABEL = 26,
IFLA_VXLAN_GPE = 27,
IFLA_VXLAN_TTL_INHERIT = 28,
IFLA_VXLAN_DF = 29,
IFLA_VXLAN_VNIFILTER = 30,
__IFLA_VXLAN_MAX = 31,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum ifla_vxlan_df {
VXLAN_DF_UNSET = 0,
VXLAN_DF_SET = 1,
VXLAN_DF_INHERIT = 2,
__VXLAN_DF_END = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_24 {
IFLA_GENEVE_UNSPEC = 0,
IFLA_GENEVE_ID = 1,
IFLA_GENEVE_REMOTE = 2,
IFLA_GENEVE_TTL = 3,
IFLA_GENEVE_TOS = 4,
IFLA_GENEVE_PORT = 5,
IFLA_GENEVE_COLLECT_METADATA = 6,
IFLA_GENEVE_REMOTE6 = 7,
IFLA_GENEVE_UDP_CSUM = 8,
IFLA_GENEVE_UDP_ZERO_CSUM6_TX = 9,
IFLA_GENEVE_UDP_ZERO_CSUM6_RX = 10,
IFLA_GENEVE_LABEL = 11,
IFLA_GENEVE_TTL_INHERIT = 12,
IFLA_GENEVE_DF = 13,
IFLA_GENEVE_INNER_PROTO_INHERIT = 14,
__IFLA_GENEVE_MAX = 15,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum ifla_geneve_df {
GENEVE_DF_UNSET = 0,
GENEVE_DF_SET = 1,
GENEVE_DF_INHERIT = 2,
__GENEVE_DF_END = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_25 {
IFLA_BAREUDP_UNSPEC = 0,
IFLA_BAREUDP_PORT = 1,
IFLA_BAREUDP_ETHERTYPE = 2,
IFLA_BAREUDP_SRCPORT_MIN = 3,
IFLA_BAREUDP_MULTIPROTO_MODE = 4,
__IFLA_BAREUDP_MAX = 5,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_26 {
IFLA_PPP_UNSPEC = 0,
IFLA_PPP_DEV_FD = 1,
__IFLA_PPP_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum ifla_gtp_role {
GTP_ROLE_GGSN = 0,
GTP_ROLE_SGSN = 1,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_27 {
IFLA_GTP_UNSPEC = 0,
IFLA_GTP_FD0 = 1,
IFLA_GTP_FD1 = 2,
IFLA_GTP_PDP_HASHSIZE = 3,
IFLA_GTP_ROLE = 4,
IFLA_GTP_CREATE_SOCKETS = 5,
IFLA_GTP_RESTART_COUNT = 6,
__IFLA_GTP_MAX = 7,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_28 {
IFLA_BOND_UNSPEC = 0,
IFLA_BOND_MODE = 1,
IFLA_BOND_ACTIVE_SLAVE = 2,
IFLA_BOND_MIIMON = 3,
IFLA_BOND_UPDELAY = 4,
IFLA_BOND_DOWNDELAY = 5,
IFLA_BOND_USE_CARRIER = 6,
IFLA_BOND_ARP_INTERVAL = 7,
IFLA_BOND_ARP_IP_TARGET = 8,
IFLA_BOND_ARP_VALIDATE = 9,
IFLA_BOND_ARP_ALL_TARGETS = 10,
IFLA_BOND_PRIMARY = 11,
IFLA_BOND_PRIMARY_RESELECT = 12,
IFLA_BOND_FAIL_OVER_MAC = 13,
IFLA_BOND_XMIT_HASH_POLICY = 14,
IFLA_BOND_RESEND_IGMP = 15,
IFLA_BOND_NUM_PEER_NOTIF = 16,
IFLA_BOND_ALL_SLAVES_ACTIVE = 17,
IFLA_BOND_MIN_LINKS = 18,
IFLA_BOND_LP_INTERVAL = 19,
IFLA_BOND_PACKETS_PER_SLAVE = 20,
IFLA_BOND_AD_LACP_RATE = 21,
IFLA_BOND_AD_SELECT = 22,
IFLA_BOND_AD_INFO = 23,
IFLA_BOND_AD_ACTOR_SYS_PRIO = 24,
IFLA_BOND_AD_USER_PORT_KEY = 25,
IFLA_BOND_AD_ACTOR_SYSTEM = 26,
IFLA_BOND_TLB_DYNAMIC_LB = 27,
IFLA_BOND_PEER_NOTIF_DELAY = 28,
IFLA_BOND_AD_LACP_ACTIVE = 29,
IFLA_BOND_MISSED_MAX = 30,
IFLA_BOND_NS_IP6_TARGET = 31,
__IFLA_BOND_MAX = 32,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_29 {
IFLA_BOND_AD_INFO_UNSPEC = 0,
IFLA_BOND_AD_INFO_AGGREGATOR = 1,
IFLA_BOND_AD_INFO_NUM_PORTS = 2,
IFLA_BOND_AD_INFO_ACTOR_KEY = 3,
IFLA_BOND_AD_INFO_PARTNER_KEY = 4,
IFLA_BOND_AD_INFO_PARTNER_MAC = 5,
__IFLA_BOND_AD_INFO_MAX = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_30 {
IFLA_BOND_SLAVE_UNSPEC = 0,
IFLA_BOND_SLAVE_STATE = 1,
IFLA_BOND_SLAVE_MII_STATUS = 2,
IFLA_BOND_SLAVE_LINK_FAILURE_COUNT = 3,
IFLA_BOND_SLAVE_PERM_HWADDR = 4,
IFLA_BOND_SLAVE_QUEUE_ID = 5,
IFLA_BOND_SLAVE_AD_AGGREGATOR_ID = 6,
IFLA_BOND_SLAVE_AD_ACTOR_OPER_PORT_STATE = 7,
IFLA_BOND_SLAVE_AD_PARTNER_OPER_PORT_STATE = 8,
IFLA_BOND_SLAVE_PRIO = 9,
__IFLA_BOND_SLAVE_MAX = 10,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_31 {
IFLA_VF_INFO_UNSPEC = 0,
IFLA_VF_INFO = 1,
__IFLA_VF_INFO_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_32 {
IFLA_VF_UNSPEC = 0,
IFLA_VF_MAC = 1,
IFLA_VF_VLAN = 2,
IFLA_VF_TX_RATE = 3,
IFLA_VF_SPOOFCHK = 4,
IFLA_VF_LINK_STATE = 5,
IFLA_VF_RATE = 6,
IFLA_VF_RSS_QUERY_EN = 7,
IFLA_VF_STATS = 8,
IFLA_VF_TRUST = 9,
IFLA_VF_IB_NODE_GUID = 10,
IFLA_VF_IB_PORT_GUID = 11,
IFLA_VF_VLAN_LIST = 12,
IFLA_VF_BROADCAST = 13,
__IFLA_VF_MAX = 14,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_33 {
IFLA_VF_VLAN_INFO_UNSPEC = 0,
IFLA_VF_VLAN_INFO = 1,
__IFLA_VF_VLAN_INFO_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_34 {
IFLA_VF_LINK_STATE_AUTO = 0,
IFLA_VF_LINK_STATE_ENABLE = 1,
IFLA_VF_LINK_STATE_DISABLE = 2,
__IFLA_VF_LINK_STATE_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_35 {
IFLA_VF_STATS_RX_PACKETS = 0,
IFLA_VF_STATS_TX_PACKETS = 1,
IFLA_VF_STATS_RX_BYTES = 2,
IFLA_VF_STATS_TX_BYTES = 3,
IFLA_VF_STATS_BROADCAST = 4,
IFLA_VF_STATS_MULTICAST = 5,
IFLA_VF_STATS_PAD = 6,
IFLA_VF_STATS_RX_DROPPED = 7,
IFLA_VF_STATS_TX_DROPPED = 8,
__IFLA_VF_STATS_MAX = 9,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_36 {
IFLA_VF_PORT_UNSPEC = 0,
IFLA_VF_PORT = 1,
__IFLA_VF_PORT_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_37 {
IFLA_PORT_UNSPEC = 0,
IFLA_PORT_VF = 1,
IFLA_PORT_PROFILE = 2,
IFLA_PORT_VSI_TYPE = 3,
IFLA_PORT_INSTANCE_UUID = 4,
IFLA_PORT_HOST_UUID = 5,
IFLA_PORT_REQUEST = 6,
IFLA_PORT_RESPONSE = 7,
__IFLA_PORT_MAX = 8,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_38 {
PORT_REQUEST_PREASSOCIATE = 0,
PORT_REQUEST_PREASSOCIATE_RR = 1,
PORT_REQUEST_ASSOCIATE = 2,
PORT_REQUEST_DISASSOCIATE = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_39 {
PORT_VDP_RESPONSE_SUCCESS = 0,
PORT_VDP_RESPONSE_INVALID_FORMAT = 1,
PORT_VDP_RESPONSE_INSUFFICIENT_RESOURCES = 2,
PORT_VDP_RESPONSE_UNUSED_VTID = 3,
PORT_VDP_RESPONSE_VTID_VIOLATION = 4,
PORT_VDP_RESPONSE_VTID_VERSION_VIOALTION = 5,
PORT_VDP_RESPONSE_OUT_OF_SYNC = 6,
PORT_PROFILE_RESPONSE_SUCCESS = 256,
PORT_PROFILE_RESPONSE_INPROGRESS = 257,
PORT_PROFILE_RESPONSE_INVALID = 258,
PORT_PROFILE_RESPONSE_BADSTATE = 259,
PORT_PROFILE_RESPONSE_INSUFFICIENT_RESOURCES = 260,
PORT_PROFILE_RESPONSE_ERROR = 261,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_40 {
IFLA_IPOIB_UNSPEC = 0,
IFLA_IPOIB_PKEY = 1,
IFLA_IPOIB_MODE = 2,
IFLA_IPOIB_UMCAST = 3,
__IFLA_IPOIB_MAX = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_41 {
IPOIB_MODE_DATAGRAM = 0,
IPOIB_MODE_CONNECTED = 1,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_42 {
HSR_PROTOCOL_HSR = 0,
HSR_PROTOCOL_PRP = 1,
HSR_PROTOCOL_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_43 {
IFLA_HSR_UNSPEC = 0,
IFLA_HSR_SLAVE1 = 1,
IFLA_HSR_SLAVE2 = 2,
IFLA_HSR_MULTICAST_SPEC = 3,
IFLA_HSR_SUPERVISION_ADDR = 4,
IFLA_HSR_SEQ_NR = 5,
IFLA_HSR_VERSION = 6,
IFLA_HSR_PROTOCOL = 7,
__IFLA_HSR_MAX = 8,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_44 {
IFLA_STATS_UNSPEC = 0,
IFLA_STATS_LINK_64 = 1,
IFLA_STATS_LINK_XSTATS = 2,
IFLA_STATS_LINK_XSTATS_SLAVE = 3,
IFLA_STATS_LINK_OFFLOAD_XSTATS = 4,
IFLA_STATS_AF_SPEC = 5,
__IFLA_STATS_MAX = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_45 {
IFLA_STATS_GETSET_UNSPEC = 0,
IFLA_STATS_GET_FILTERS = 1,
IFLA_STATS_SET_OFFLOAD_XSTATS_L3_STATS = 2,
__IFLA_STATS_GETSET_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_46 {
LINK_XSTATS_TYPE_UNSPEC = 0,
LINK_XSTATS_TYPE_BRIDGE = 1,
LINK_XSTATS_TYPE_BOND = 2,
__LINK_XSTATS_TYPE_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_47 {
IFLA_OFFLOAD_XSTATS_UNSPEC = 0,
IFLA_OFFLOAD_XSTATS_CPU_HIT = 1,
IFLA_OFFLOAD_XSTATS_HW_S_INFO = 2,
IFLA_OFFLOAD_XSTATS_L3_STATS = 3,
__IFLA_OFFLOAD_XSTATS_MAX = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_48 {
IFLA_OFFLOAD_XSTATS_HW_S_INFO_UNSPEC = 0,
IFLA_OFFLOAD_XSTATS_HW_S_INFO_REQUEST = 1,
IFLA_OFFLOAD_XSTATS_HW_S_INFO_USED = 2,
__IFLA_OFFLOAD_XSTATS_HW_S_INFO_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_49 {
XDP_ATTACHED_NONE = 0,
XDP_ATTACHED_DRV = 1,
XDP_ATTACHED_SKB = 2,
XDP_ATTACHED_HW = 3,
XDP_ATTACHED_MULTI = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_50 {
IFLA_XDP_UNSPEC = 0,
IFLA_XDP_FD = 1,
IFLA_XDP_ATTACHED = 2,
IFLA_XDP_FLAGS = 3,
IFLA_XDP_PROG_ID = 4,
IFLA_XDP_DRV_PROG_ID = 5,
IFLA_XDP_SKB_PROG_ID = 6,
IFLA_XDP_HW_PROG_ID = 7,
IFLA_XDP_EXPECTED_FD = 8,
__IFLA_XDP_MAX = 9,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_51 {
IFLA_EVENT_NONE = 0,
IFLA_EVENT_REBOOT = 1,
IFLA_EVENT_FEATURES = 2,
IFLA_EVENT_BONDING_FAILOVER = 3,
IFLA_EVENT_NOTIFY_PEERS = 4,
IFLA_EVENT_IGMP_RESEND = 5,
IFLA_EVENT_BONDING_OPTIONS = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_52 {
IFLA_TUN_UNSPEC = 0,
IFLA_TUN_OWNER = 1,
IFLA_TUN_GROUP = 2,
IFLA_TUN_TYPE = 3,
IFLA_TUN_PI = 4,
IFLA_TUN_VNET_HDR = 5,
IFLA_TUN_PERSIST = 6,
IFLA_TUN_MULTI_QUEUE = 7,
IFLA_TUN_NUM_QUEUES = 8,
IFLA_TUN_NUM_DISABLED_QUEUES = 9,
__IFLA_TUN_MAX = 10,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_53 {
IFLA_RMNET_UNSPEC = 0,
IFLA_RMNET_MUX_ID = 1,
IFLA_RMNET_FLAGS = 2,
__IFLA_RMNET_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_54 {
IFLA_MCTP_UNSPEC = 0,
IFLA_MCTP_NET = 1,
__IFLA_MCTP_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_55 {
IFLA_DSA_UNSPEC = 0,
IFLA_DSA_MASTER = 1,
__IFLA_DSA_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_56 {
IF_PORT_UNKNOWN = 0,
IF_PORT_10BASE2 = 1,
IF_PORT_10BASET = 2,
IF_PORT_AUI = 3,
IF_PORT_100BASET = 4,
IF_PORT_100BASETX = 5,
IF_PORT_100BASEFX = 6,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union __kernel_sockaddr_storage__bindgen_ty_1 {
pub __bindgen_anon_1: __kernel_sockaddr_storage__bindgen_ty_1__bindgen_ty_1,
pub __align: *mut crate::ctypes::c_void,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union if_settings__bindgen_ty_1 {
pub raw_hdlc: *mut raw_hdlc_proto,
pub cisco: *mut cisco_proto,
pub fr: *mut fr_proto,
pub fr_pvc: *mut fr_proto_pvc,
pub fr_pvc_info: *mut fr_proto_pvc_info,
pub x25: *mut x25_hdlc_proto,
pub sync: *mut sync_serial_settings,
pub te1: *mut te1_settings,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union ifreq__bindgen_ty_1 {
pub ifrn_name: [crate::ctypes::c_char; 16usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union ifreq__bindgen_ty_2 {
pub ifru_addr: sockaddr,
pub ifru_dstaddr: sockaddr,
pub ifru_broadaddr: sockaddr,
pub ifru_netmask: sockaddr,
pub ifru_hwaddr: sockaddr,
pub ifru_flags: crate::ctypes::c_short,
pub ifru_ivalue: crate::ctypes::c_int,
pub ifru_mtu: crate::ctypes::c_int,
pub ifru_map: ifmap,
pub ifru_slave: [crate::ctypes::c_char; 16usize],
pub ifru_newname: [crate::ctypes::c_char; 16usize],
pub ifru_data: *mut crate::ctypes::c_void,
pub ifru_settings: if_settings,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union ifconf__bindgen_ty_1 {
pub ifcu_buf: *mut crate::ctypes::c_char,
pub ifcu_req: *mut ifreq,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union tpacket_stats_u {
pub stats1: tpacket_stats,
pub stats3: tpacket_stats_v3,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union tpacket3_hdr__bindgen_ty_1 {
pub hv1: tpacket_hdr_variant1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union tpacket_bd_ts__bindgen_ty_1 {
pub ts_usec: crate::ctypes::c_uint,
pub ts_nsec: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union tpacket_bd_header_u {
pub bh1: tpacket_hdr_v1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union tpacket_req_u {
pub req: tpacket_req,
pub req3: tpacket_req3,
}
impl nlmsgerr_attrs {
pub const NLMSGERR_ATTR_MAX: nlmsgerr_attrs = nlmsgerr_attrs::NLMSGERR_ATTR_MISS_NEST;
}
impl netlink_policy_type_attr {
pub const NL_POLICY_TYPE_ATTR_MAX: netlink_policy_type_attr = netlink_policy_type_attr::NL_POLICY_TYPE_ATTR_MASK;
}
impl macsec_validation_type {
pub const MACSEC_VALIDATE_MAX: macsec_validation_type = macsec_validation_type::MACSEC_VALIDATE_STRICT;
}
impl macsec_offload {
pub const MACSEC_OFFLOAD_MAX: macsec_offload = macsec_offload::MACSEC_OFFLOAD_MAC;
}
impl ifla_vxlan_df {
pub const VXLAN_DF_MAX: ifla_vxlan_df = ifla_vxlan_df::VXLAN_DF_INHERIT;
}
impl ifla_geneve_df {
pub const GENEVE_DF_MAX: ifla_geneve_df = ifla_geneve_df::GENEVE_DF_INHERIT;
}
