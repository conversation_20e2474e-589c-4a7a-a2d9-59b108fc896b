/* automatically generated by rust-bindgen 0.71.1 */

pub type __s8 = crate::ctypes::c_schar;
pub type __u8 = crate::ctypes::c_uchar;
pub type __s16 = crate::ctypes::c_short;
pub type __u16 = crate::ctypes::c_ushort;
pub type __s32 = crate::ctypes::c_int;
pub type __u32 = crate::ctypes::c_uint;
pub type __s64 = crate::ctypes::c_longlong;
pub type __u64 = crate::ctypes::c_ulonglong;
pub type __kernel_key_t = crate::ctypes::c_int;
pub type __kernel_mqd_t = crate::ctypes::c_int;
pub type __kernel_old_uid_t = crate::ctypes::c_ushort;
pub type __kernel_old_gid_t = crate::ctypes::c_ushort;
pub type __kernel_long_t = crate::ctypes::c_long;
pub type __kernel_ulong_t = crate::ctypes::c_ulong;
pub type __kernel_ino_t = __kernel_ulong_t;
pub type __kernel_mode_t = crate::ctypes::c_uint;
pub type __kernel_pid_t = crate::ctypes::c_int;
pub type __kernel_ipc_pid_t = crate::ctypes::c_int;
pub type __kernel_uid_t = crate::ctypes::c_uint;
pub type __kernel_gid_t = crate::ctypes::c_uint;
pub type __kernel_suseconds_t = __kernel_long_t;
pub type __kernel_daddr_t = crate::ctypes::c_int;
pub type __kernel_uid32_t = crate::ctypes::c_uint;
pub type __kernel_gid32_t = crate::ctypes::c_uint;
pub type __kernel_old_dev_t = crate::ctypes::c_uint;
pub type __kernel_size_t = __kernel_ulong_t;
pub type __kernel_ssize_t = __kernel_long_t;
pub type __kernel_ptrdiff_t = __kernel_long_t;
pub type __kernel_off_t = __kernel_long_t;
pub type __kernel_loff_t = crate::ctypes::c_longlong;
pub type __kernel_old_time_t = __kernel_long_t;
pub type __kernel_time_t = __kernel_long_t;
pub type __kernel_time64_t = crate::ctypes::c_longlong;
pub type __kernel_clock_t = __kernel_long_t;
pub type __kernel_timer_t = crate::ctypes::c_int;
pub type __kernel_clockid_t = crate::ctypes::c_int;
pub type __kernel_caddr_t = *mut crate::ctypes::c_char;
pub type __kernel_uid16_t = crate::ctypes::c_ushort;
pub type __kernel_gid16_t = crate::ctypes::c_ushort;
pub type __s128 = i128;
pub type __u128 = u128;
pub type __le16 = __u16;
pub type __be16 = __u16;
pub type __le32 = __u32;
pub type __be32 = __u32;
pub type __le64 = __u64;
pub type __be64 = __u64;
pub type __sum16 = __u16;
pub type __wsum = __u32;
pub type __poll_t = crate::ctypes::c_uint;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct arm64_image_header {
pub code0: __le32,
pub code1: __le32,
pub text_offset: __le64,
pub image_size: __le64,
pub flags: __le64,
pub res2: __le64,
pub res3: __le64,
pub res4: __le64,
pub magic: __le32,
pub res5: __le32,
}
pub const __BITS_PER_LONG_LONG: u32 = 64;
pub const ARM64_IMAGE_MAGIC: &[u8; 5] = b"ARMd\0";
pub const ARM64_IMAGE_FLAG_BE_SHIFT: u32 = 0;
pub const ARM64_IMAGE_FLAG_PAGE_SIZE_SHIFT: u32 = 1;
pub const ARM64_IMAGE_FLAG_PHYS_BASE_SHIFT: u32 = 3;
pub const ARM64_IMAGE_FLAG_BE_MASK: u32 = 1;
pub const ARM64_IMAGE_FLAG_PAGE_SIZE_MASK: u32 = 3;
pub const ARM64_IMAGE_FLAG_PHYS_BASE_MASK: u32 = 1;
pub const ARM64_IMAGE_FLAG_LE: u32 = 0;
pub const ARM64_IMAGE_FLAG_BE: u32 = 1;
pub const ARM64_IMAGE_FLAG_PAGE_SIZE_4K: u32 = 1;
pub const ARM64_IMAGE_FLAG_PAGE_SIZE_16K: u32 = 2;
pub const ARM64_IMAGE_FLAG_PAGE_SIZE_64K: u32 = 3;
pub const ARM64_IMAGE_FLAG_PHYS_BASE: u32 = 1;
