/* automatically generated by rust-bindgen 0.71.1 */

pub const FIONREAD: u32 = 21531;
pub const FIONBIO: u32 = 21537;
pub const FIOCLEX: u32 = 21585;
pub const FIONCLEX: u32 = 21584;
pub const FIOASYNC: u32 = 21586;
pub const FIOQSIZE: u32 = 21600;
pub const TCXONC: u32 = 21514;
pub const TCFLSH: u32 = 21515;
pub const TIOCSCTTY: u32 = 21518;
pub const TIOCSPGRP: u32 = 21520;
pub const TIOCOUTQ: u32 = 21521;
pub const TIOCSTI: u32 = 21522;
pub const TIOCSWINSZ: u32 = 21524;
pub const TIOCMGET: u32 = 21525;
pub const TIOCMBIS: u32 = 21526;
pub const TIOCMBIC: u32 = 21527;
pub const TIOCMSET: u32 = 21528;
pub const TIOCSSOFTCAR: u32 = 21530;
pub const TIOCLINUX: u32 = 21532;
pub const TIOCCONS: u32 = 21533;
pub const TIOCSSERIAL: u32 = 21535;
pub const TIOCPKT: u32 = 21536;
pub const TIOCNOTTY: u32 = 21538;
pub const TIOCSETD: u32 = 21539;
pub const TIOCSBRK: u32 = 21543;
pub const TIOCCBRK: u32 = 21544;
pub const TIOCSRS485: u32 = 21551;
pub const TIOCSPTLCK: u32 = 1074025521;
pub const TIOCSIG: u32 = 1074025526;
pub const TIOCVHANGUP: u32 = 21559;
pub const TIOCSERCONFIG: u32 = 21587;
pub const TIOCSERGWILD: u32 = 21588;
pub const TIOCSERSWILD: u32 = 21589;
pub const TIOCSLCKTRMIOS: u32 = 21591;
pub const TIOCSERGSTRUCT: u32 = 21592;
pub const TIOCSERGETLSR: u32 = 21593;
pub const TIOCSERGETMULTI: u32 = 21594;
pub const TIOCSERSETMULTI: u32 = 21595;
pub const TIOCMIWAIT: u32 = 21596;
pub const TCGETS: u32 = 21505;
pub const TCGETA: u32 = 21509;
pub const TCSBRK: u32 = 21513;
pub const TCSBRKP: u32 = 21541;
pub const TCSETA: u32 = 21510;
pub const TCSETAF: u32 = 21512;
pub const TCSETAW: u32 = 21511;
pub const TIOCEXCL: u32 = 21516;
pub const TIOCNXCL: u32 = 21517;
pub const TIOCGDEV: u32 = 2147767346;
pub const TIOCGEXCL: u32 = 2147767360;
pub const TIOCGICOUNT: u32 = 21597;
pub const TIOCGLCKTRMIOS: u32 = 21590;
pub const TIOCGPGRP: u32 = 21519;
pub const TIOCGPKT: u32 = 2147767352;
pub const TIOCGPTLCK: u32 = 2147767353;
pub const TIOCGPTN: u32 = 2147767344;
pub const TIOCGPTPEER: u32 = 21569;
pub const TIOCGRS485: u32 = 21550;
pub const TIOCGSERIAL: u32 = 21534;
pub const TIOCGSID: u32 = 21545;
pub const TIOCGSOFTCAR: u32 = 21529;
pub const TIOCGWINSZ: u32 = 21523;
pub const TCGETS2: u32 = 2150388778;
pub const TCGETX: u32 = 21554;
pub const TCSETS: u32 = 21506;
pub const TCSETS2: u32 = 1076646955;
pub const TCSETSF: u32 = 21508;
pub const TCSETSF2: u32 = 1076646957;
pub const TCSETSW: u32 = 21507;
pub const TCSETSW2: u32 = 1076646956;
pub const TCSETX: u32 = 21555;
pub const TCSETXF: u32 = 21556;
pub const TCSETXW: u32 = 21557;
pub const TIOCGETD: u32 = 21540;
pub const MTIOCGET: u32 = 2150657282;
pub const BLKSSZGET: u32 = 4712;
pub const BLKPBSZGET: u32 = 4731;
pub const BLKROSET: u32 = 4701;
pub const BLKROGET: u32 = 4702;
pub const BLKRRPART: u32 = 4703;
pub const BLKGETSIZE: u32 = 4704;
pub const BLKFLSBUF: u32 = 4705;
pub const BLKRASET: u32 = 4706;
pub const BLKRAGET: u32 = 4707;
pub const BLKFRASET: u32 = 4708;
pub const BLKFRAGET: u32 = 4709;
pub const BLKSECTSET: u32 = 4710;
pub const BLKSECTGET: u32 = 4711;
pub const BLKPG: u32 = 4713;
pub const BLKBSZGET: u32 = 2148012656;
pub const BLKBSZSET: u32 = 1074270833;
pub const BLKGETSIZE64: u32 = 2148012658;
pub const BLKTRACESETUP: u32 = 3225948787;
pub const BLKTRACESTART: u32 = 4724;
pub const BLKTRACESTOP: u32 = 4725;
pub const BLKTRACETEARDOWN: u32 = 4726;
pub const BLKDISCARD: u32 = 4727;
pub const BLKIOMIN: u32 = 4728;
pub const BLKIOOPT: u32 = 4729;
pub const BLKALIGNOFF: u32 = 4730;
pub const BLKDISCARDZEROES: u32 = 4732;
pub const BLKSECDISCARD: u32 = 4733;
pub const BLKROTATIONAL: u32 = 4734;
pub const BLKZEROOUT: u32 = 4735;
pub const FIEMAP_MAX_OFFSET: i32 = -1;
pub const FIEMAP_FLAG_SYNC: u32 = 1;
pub const FIEMAP_FLAG_XATTR: u32 = 2;
pub const FIEMAP_FLAG_CACHE: u32 = 4;
pub const FIEMAP_FLAGS_COMPAT: u32 = 3;
pub const FIEMAP_EXTENT_LAST: u32 = 1;
pub const FIEMAP_EXTENT_UNKNOWN: u32 = 2;
pub const FIEMAP_EXTENT_DELALLOC: u32 = 4;
pub const FIEMAP_EXTENT_ENCODED: u32 = 8;
pub const FIEMAP_EXTENT_DATA_ENCRYPTED: u32 = 128;
pub const FIEMAP_EXTENT_NOT_ALIGNED: u32 = 256;
pub const FIEMAP_EXTENT_DATA_INLINE: u32 = 512;
pub const FIEMAP_EXTENT_DATA_TAIL: u32 = 1024;
pub const FIEMAP_EXTENT_UNWRITTEN: u32 = 2048;
pub const FIEMAP_EXTENT_MERGED: u32 = 4096;
pub const FIEMAP_EXTENT_SHARED: u32 = 8192;
pub const UFFDIO_REGISTER: u32 = 3223366144;
pub const UFFDIO_UNREGISTER: u32 = 2148575745;
pub const UFFDIO_WAKE: u32 = 2148575746;
pub const UFFDIO_COPY: u32 = 3223890435;
pub const UFFDIO_ZEROPAGE: u32 = 3223366148;
pub const UFFDIO_WRITEPROTECT: u32 = 3222841862;
pub const UFFDIO_API: u32 = 3222841919;
pub const NS_GET_USERNS: u32 = 46849;
pub const NS_GET_PARENT: u32 = 46850;
pub const NS_GET_NSTYPE: u32 = 46851;
pub const KDGETLED: u32 = 19249;
pub const KDSETLED: u32 = 19250;
pub const KDGKBLED: u32 = 19300;
pub const KDSKBLED: u32 = 19301;
pub const KDGKBTYPE: u32 = 19251;
pub const KDADDIO: u32 = 19252;
pub const KDDELIO: u32 = 19253;
pub const KDENABIO: u32 = 19254;
pub const KDDISABIO: u32 = 19255;
pub const KDSETMODE: u32 = 19258;
pub const KDGETMODE: u32 = 19259;
pub const KDMKTONE: u32 = 19248;
pub const KIOCSOUND: u32 = 19247;
pub const GIO_CMAP: u32 = 19312;
pub const PIO_CMAP: u32 = 19313;
pub const GIO_FONT: u32 = 19296;
pub const GIO_FONTX: u32 = 19307;
pub const PIO_FONT: u32 = 19297;
pub const PIO_FONTX: u32 = 19308;
pub const PIO_FONTRESET: u32 = 19309;
pub const GIO_SCRNMAP: u32 = 19264;
pub const GIO_UNISCRNMAP: u32 = 19305;
pub const PIO_SCRNMAP: u32 = 19265;
pub const PIO_UNISCRNMAP: u32 = 19306;
pub const GIO_UNIMAP: u32 = 19302;
pub const PIO_UNIMAP: u32 = 19303;
pub const PIO_UNIMAPCLR: u32 = 19304;
pub const KDGKBMODE: u32 = 19268;
pub const KDSKBMODE: u32 = 19269;
pub const KDGKBMETA: u32 = 19298;
pub const KDSKBMETA: u32 = 19299;
pub const KDGKBENT: u32 = 19270;
pub const KDSKBENT: u32 = 19271;
pub const KDGKBSENT: u32 = 19272;
pub const KDSKBSENT: u32 = 19273;
pub const KDGKBDIACR: u32 = 19274;
pub const KDGETKEYCODE: u32 = 19276;
pub const KDSETKEYCODE: u32 = 19277;
pub const KDSIGACCEPT: u32 = 19278;
pub const VT_OPENQRY: u32 = 22016;
pub const VT_GETMODE: u32 = 22017;
pub const VT_SETMODE: u32 = 22018;
pub const VT_GETSTATE: u32 = 22019;
pub const VT_RELDISP: u32 = 22021;
pub const VT_ACTIVATE: u32 = 22022;
pub const VT_WAITACTIVE: u32 = 22023;
pub const VT_DISALLOCATE: u32 = 22024;
pub const VT_RESIZE: u32 = 22025;
pub const VT_RESIZEX: u32 = 22026;
pub const FIOSETOWN: u32 = 35073;
pub const SIOCSPGRP: u32 = 35074;
pub const FIOGETOWN: u32 = 35075;
pub const SIOCGPGRP: u32 = 35076;
pub const SIOCATMARK: u32 = 35077;
pub const SIOCGSTAMP: u32 = 35078;
pub const TIOCINQ: u32 = 21531;
pub const SIOCADDRT: u32 = 35083;
pub const SIOCDELRT: u32 = 35084;
pub const SIOCGIFNAME: u32 = 35088;
pub const SIOCSIFLINK: u32 = 35089;
pub const SIOCGIFCONF: u32 = 35090;
pub const SIOCGIFFLAGS: u32 = 35091;
pub const SIOCSIFFLAGS: u32 = 35092;
pub const SIOCGIFADDR: u32 = 35093;
pub const SIOCSIFADDR: u32 = 35094;
pub const SIOCGIFDSTADDR: u32 = 35095;
pub const SIOCSIFDSTADDR: u32 = 35096;
pub const SIOCGIFBRDADDR: u32 = 35097;
pub const SIOCSIFBRDADDR: u32 = 35098;
pub const SIOCGIFNETMASK: u32 = 35099;
pub const SIOCSIFNETMASK: u32 = 35100;
pub const SIOCGIFMETRIC: u32 = 35101;
pub const SIOCSIFMETRIC: u32 = 35102;
pub const SIOCGIFMEM: u32 = 35103;
pub const SIOCSIFMEM: u32 = 35104;
pub const SIOCGIFMTU: u32 = 35105;
pub const SIOCSIFMTU: u32 = 35106;
pub const SIOCSIFHWADDR: u32 = 35108;
pub const SIOCGIFENCAP: u32 = 35109;
pub const SIOCSIFENCAP: u32 = 35110;
pub const SIOCGIFHWADDR: u32 = 35111;
pub const SIOCGIFSLAVE: u32 = 35113;
pub const SIOCSIFSLAVE: u32 = 35120;
pub const SIOCADDMULTI: u32 = 35121;
pub const SIOCDELMULTI: u32 = 35122;
pub const SIOCDARP: u32 = 35155;
pub const SIOCGARP: u32 = 35156;
pub const SIOCSARP: u32 = 35157;
pub const SIOCDRARP: u32 = 35168;
pub const SIOCGRARP: u32 = 35169;
pub const SIOCSRARP: u32 = 35170;
pub const SIOCGIFMAP: u32 = 35184;
pub const SIOCSIFMAP: u32 = 35185;
pub const SIOCRTMSG: u32 = 35085;
pub const SIOCSIFNAME: u32 = 35107;
pub const SIOCGIFINDEX: u32 = 35123;
pub const SIOGIFINDEX: u32 = 35123;
pub const SIOCSIFPFLAGS: u32 = 35124;
pub const SIOCGIFPFLAGS: u32 = 35125;
pub const SIOCDIFADDR: u32 = 35126;
pub const SIOCSIFHWBROADCAST: u32 = 35127;
pub const SIOCGIFCOUNT: u32 = 35128;
pub const SIOCGIFBR: u32 = 35136;
pub const SIOCSIFBR: u32 = 35137;
pub const SIOCGIFTXQLEN: u32 = 35138;
pub const SIOCSIFTXQLEN: u32 = 35139;
pub const SIOCADDDLCI: u32 = 35200;
pub const SIOCDELDLCI: u32 = 35201;
pub const SIOCDEVPRIVATE: u32 = 35312;
pub const SIOCPROTOPRIVATE: u32 = 35296;
pub const FIBMAP: u32 = 1;
pub const FIGETBSZ: u32 = 2;
pub const FIFREEZE: u32 = 3221510263;
pub const FITHAW: u32 = 3221510264;
pub const FITRIM: u32 = 3222820985;
pub const FICLONE: u32 = 1074041865;
pub const FICLONERANGE: u32 = 1075876877;
pub const FIDEDUPERANGE: u32 = 3222836278;
pub const FS_IOC_GETFLAGS: u32 = 2148034049;
pub const FS_IOC_SETFLAGS: u32 = 1074292226;
pub const FS_IOC_GETVERSION: u32 = 2148038145;
pub const FS_IOC_SETVERSION: u32 = 1074296322;
pub const FS_IOC_FIEMAP: u32 = 3223348747;
pub const FS_IOC32_GETFLAGS: u32 = 2147771905;
pub const FS_IOC32_SETFLAGS: u32 = 1074030082;
pub const FS_IOC32_GETVERSION: u32 = 2147776001;
pub const FS_IOC32_SETVERSION: u32 = 1074034178;
pub const FS_IOC_FSGETXATTR: u32 = 2149341215;
pub const FS_IOC_FSSETXATTR: u32 = 1075599392;
pub const FS_IOC_GETFSLABEL: u32 = 2164298801;
pub const FS_IOC_SETFSLABEL: u32 = 1090556978;
pub const EXT4_IOC_GETVERSION: u32 = 2148034051;
pub const EXT4_IOC_SETVERSION: u32 = 1074292228;
pub const EXT4_IOC_GETVERSION_OLD: u32 = 2148038145;
pub const EXT4_IOC_SETVERSION_OLD: u32 = 1074296322;
pub const EXT4_IOC_GETRSVSZ: u32 = 2148034053;
pub const EXT4_IOC_SETRSVSZ: u32 = 1074292230;
pub const EXT4_IOC_GROUP_EXTEND: u32 = 1074292231;
pub const EXT4_IOC_MIGRATE: u32 = 26121;
pub const EXT4_IOC_ALLOC_DA_BLKS: u32 = 26124;
pub const EXT4_IOC_RESIZE_FS: u32 = 1074292240;
pub const EXT4_IOC_SWAP_BOOT: u32 = 26129;
pub const EXT4_IOC_PRECACHE_EXTENTS: u32 = 26130;
pub const EXT4_IOC_CLEAR_ES_CACHE: u32 = 26152;
pub const EXT4_IOC_GETSTATE: u32 = 1074030121;
pub const EXT4_IOC_GET_ES_CACHE: u32 = 3223348778;
pub const EXT4_IOC_CHECKPOINT: u32 = 1074030123;
pub const EXT4_IOC_SHUTDOWN: u32 = 2147768445;
pub const EXT4_IOC32_GETVERSION: u32 = 2147771907;
pub const EXT4_IOC32_SETVERSION: u32 = 1074030084;
pub const EXT4_IOC32_GETRSVSZ: u32 = 2147771909;
pub const EXT4_IOC32_SETRSVSZ: u32 = 1074030086;
pub const EXT4_IOC32_GROUP_EXTEND: u32 = 1074030087;
pub const EXT4_IOC32_GETVERSION_OLD: u32 = 2147776001;
pub const EXT4_IOC32_SETVERSION_OLD: u32 = 1074034178;
pub const VIDIOC_SUBDEV_QUERYSTD: u32 = 2148030015;
pub const AUTOFS_DEV_IOCTL_CLOSEMOUNT: u32 = 3222836085;
pub const LIRC_SET_SEND_CARRIER: u32 = 1074030867;
pub const AUTOFS_IOC_PROTOSUBVER: u32 = 2147783527;
pub const PTP_SYS_OFFSET_PRECISE: u32 = 3225435400;
pub const FSI_SCOM_WRITE: u32 = 3223352066;
pub const ATM_GETCIRANGE: u32 = 1074815370;
pub const DMA_BUF_SET_NAME_B: u32 = 1074291201;
pub const RIO_CM_EP_GET_LIST_SIZE: u32 = 3221512961;
pub const TUNSETPERSIST: u32 = 1074025675;
pub const FS_IOC_GET_ENCRYPTION_POLICY: u32 = 1074554389;
pub const CEC_RECEIVE: u32 = 3224920326;
pub const MGSL_IOCGPARAMS: u32 = 2150657281;
pub const ENI_SETMULT: u32 = 1074815335;
pub const RIO_GET_EVENT_MASK: u32 = 2147773710;
pub const LIRC_GET_MAX_TIMEOUT: u32 = 2147772681;
pub const USBDEVFS_CLAIMINTERFACE: u32 = 2147767567;
pub const CHIOMOVE: u32 = 1075077889;
pub const SONYPI_IOCGBATFLAGS: u32 = 2147579399;
pub const BTRFS_IOC_SYNC: u32 = 37896;
pub const VIDIOC_TRY_FMT: u32 = 3234879040;
pub const LIRC_SET_REC_MODE: u32 = 1074030866;
pub const VIDIOC_DQEVENT: u32 = 2156418649;
pub const RPMSG_DESTROY_EPT_IOCTL: u32 = 46338;
pub const UVCIOC_CTRL_MAP: u32 = 3227546912;
pub const VHOST_SET_BACKEND_FEATURES: u32 = 1074310949;
pub const VHOST_VSOCK_SET_GUEST_CID: u32 = 1074311008;
pub const UI_SET_KEYBIT: u32 = 1074025829;
pub const LIRC_SET_REC_TIMEOUT: u32 = 1074030872;
pub const FS_IOC_GET_ENCRYPTION_KEY_STATUS: u32 = 3229640218;
pub const BTRFS_IOC_TREE_SEARCH_V2: u32 = 3228603409;
pub const VHOST_SET_VRING_BASE: u32 = 1074310930;
pub const RIO_ENABLE_DOORBELL_RANGE: u32 = 1074294025;
pub const VIDIOC_TRY_EXT_CTRLS: u32 = 3223344713;
pub const LIRC_GET_REC_MODE: u32 = 2147772674;
pub const PPGETTIME: u32 = 2148561045;
pub const BTRFS_IOC_RM_DEV: u32 = 1342215179;
pub const ATM_SETBACKEND: u32 = 1073897970;
pub const FSL_HV_IOCTL_PARTITION_START: u32 = 3222318851;
pub const FBIO_WAITEVENT: u32 = 18056;
pub const SWITCHTEC_IOCTL_PORT_TO_PFF: u32 = 3222034245;
pub const NVME_IOCTL_IO_CMD: u32 = 3225964099;
pub const IPMICTL_RECEIVE_MSG_TRUNC: u32 = 3224398091;
pub const FDTWADDLE: u32 = 601;
pub const NVME_IOCTL_SUBMIT_IO: u32 = 1076907586;
pub const NILFS_IOCTL_SYNC: u32 = 2148036234;
pub const VIDIOC_SUBDEV_S_DV_TIMINGS: u32 = 3229898327;
pub const ASPEED_LPC_CTRL_IOCTL_GET_SIZE: u32 = 3222319616;
pub const DM_DEV_STATUS: u32 = 3241737479;
pub const TEE_IOC_CLOSE_SESSION: u32 = 2147787781;
pub const NS_GETPSTAT: u32 = 3222298977;
pub const UI_SET_PROPBIT: u32 = 1074025838;
pub const TUNSETFILTEREBPF: u32 = 2147767521;
pub const RIO_MPORT_MAINT_COMPTAG_SET: u32 = 1074031874;
pub const AUTOFS_DEV_IOCTL_VERSION: u32 = 3222836081;
pub const WDIOC_SETOPTIONS: u32 = 2147768068;
pub const VHOST_SCSI_SET_ENDPOINT: u32 = 1088991040;
pub const MGSL_IOCGTXIDLE: u32 = 27907;
pub const ATM_ADDLECSADDR: u32 = 1074815374;
pub const FSL_HV_IOCTL_GETPROP: u32 = 3223891719;
pub const FDGETPRM: u32 = 2149581316;
pub const HIDIOCAPPLICATION: u32 = 18434;
pub const ENI_MEMDUMP: u32 = 1074815328;
pub const PTP_SYS_OFFSET2: u32 = 1128283406;
pub const VIDIOC_SUBDEV_G_DV_TIMINGS: u32 = 3229898328;
pub const DMA_BUF_SET_NAME_A: u32 = 1074029057;
pub const PTP_PIN_GETFUNC: u32 = 3227532550;
pub const PTP_SYS_OFFSET_EXTENDED: u32 = 3300932873;
pub const DFL_FPGA_PORT_UINT_SET_IRQ: u32 = 1074312776;
pub const RTC_EPOCH_READ: u32 = 2148036621;
pub const VIDIOC_SUBDEV_S_SELECTION: u32 = 3225441854;
pub const VIDIOC_QUERY_EXT_CTRL: u32 = 3236451943;
pub const ATM_GETLECSADDR: u32 = 1074815376;
pub const FSL_HV_IOCTL_PARTITION_STOP: u32 = 3221794564;
pub const SONET_GETDIAG: u32 = 2147770644;
pub const ATMMPC_DATA: u32 = 25049;
pub const IPMICTL_UNREGISTER_FOR_CMD_CHANS: u32 = 2148296989;
pub const HIDIOCGCOLLECTIONINDEX: u32 = 1075333136;
pub const RPMSG_CREATE_EPT_IOCTL: u32 = 1076409601;
pub const GPIOHANDLE_GET_LINE_VALUES_IOCTL: u32 = 3225465864;
pub const UI_DEV_SETUP: u32 = 1079792899;
pub const ISST_IF_IO_CMD: u32 = 1074331138;
pub const RIO_MPORT_MAINT_READ_REMOTE: u32 = 2149084423;
pub const VIDIOC_OMAP3ISP_HIST_CFG: u32 = 3224393412;
pub const BLKGETNRZONES: u32 = 2147750533;
pub const VIDIOC_G_MODULATOR: u32 = 3225703990;
pub const VBG_IOCTL_WRITE_CORE_DUMP: u32 = 3223082515;
pub const USBDEVFS_SETINTERFACE: u32 = 2148029700;
pub const PPPIOCGCHAN: u32 = 2147775543;
pub const EVIOCGVERSION: u32 = 2147763457;
pub const VHOST_NET_SET_BACKEND: u32 = 1074310960;
pub const USBDEVFS_REAPURBNDELAY: u32 = 1074287885;
pub const RNDZAPENTCNT: u32 = 20996;
pub const VIDIOC_G_PARM: u32 = 3234616853;
pub const TUNGETDEVNETNS: u32 = 21731;
pub const LIRC_SET_MEASURE_CARRIER_MODE: u32 = 1074030877;
pub const VHOST_SET_VRING_ERR: u32 = 1074310946;
pub const VDUSE_VQ_SETUP: u32 = 1075872020;
pub const AUTOFS_IOC_SETTIMEOUT: u32 = 3221787492;
pub const VIDIOC_S_FREQUENCY: u32 = 1076647481;
pub const F2FS_IOC_SEC_TRIM_FILE: u32 = 1075377428;
pub const FS_IOC_REMOVE_ENCRYPTION_KEY: u32 = 3225445912;
pub const WDIOC_GETPRETIMEOUT: u32 = 2147768073;
pub const USBDEVFS_DROP_PRIVILEGES: u32 = 1074025758;
pub const BTRFS_IOC_SNAP_CREATE_V2: u32 = 1342215191;
pub const VHOST_VSOCK_SET_RUNNING: u32 = 1074048865;
pub const STP_SET_OPTIONS: u32 = 1074275586;
pub const FBIO_RADEON_GET_MIRROR: u32 = 2148024323;
pub const IVTVFB_IOC_DMA_FRAME: u32 = 1075336896;
pub const IPMICTL_SEND_COMMAND: u32 = 2150131981;
pub const VIDIOC_G_ENC_INDEX: u32 = 2283296332;
pub const DFL_FPGA_FME_PORT_PR: u32 = 46720;
pub const CHIOSVOLTAG: u32 = 1076912914;
pub const ATM_SETESIF: u32 = 1074815373;
pub const FW_CDEV_IOC_SEND_RESPONSE: u32 = 1075323652;
pub const PMU_IOC_GET_MODEL: u32 = 2148024835;
pub const JSIOCGBTNMAP: u32 = 2214619700;
pub const USBDEVFS_HUB_PORTINFO: u32 = 2155894035;
pub const VBG_IOCTL_INTERRUPT_ALL_WAIT_FOR_EVENTS: u32 = 3222820363;
pub const FDCLRPRM: u32 = 577;
pub const BTRFS_IOC_SCRUB: u32 = 3288372251;
pub const USBDEVFS_DISCONNECT: u32 = 21782;
pub const TUNSETVNETBE: u32 = 1074025694;
pub const ATMTCP_REMOVE: u32 = 24975;
pub const VHOST_VDPA_GET_CONFIG: u32 = 2148052851;
pub const PPPIOCGNPMODE: u32 = 3221779532;
pub const FDGETDRVPRM: u32 = 2155872785;
pub const TUNSETVNETLE: u32 = 1074025692;
pub const PHN_SETREG: u32 = 1074294790;
pub const PPPIOCDETACH: u32 = 1074033724;
pub const MMTIMER_GETRES: u32 = 2148035841;
pub const VIDIOC_SUBDEV_ENUMSTD: u32 = 3225966105;
pub const PPGETFLAGS: u32 = 2147774618;
pub const VDUSE_DEV_GET_FEATURES: u32 = 2148040977;
pub const CAPI_MANUFACTURER_CMD: u32 = 3222291232;
pub const VIDIOC_G_TUNER: u32 = 3226752541;
pub const DM_TABLE_STATUS: u32 = 3241737484;
pub const DM_DEV_ARM_POLL: u32 = 3241737488;
pub const NE_CREATE_VM: u32 = 2148052512;
pub const MEDIA_IOC_ENUM_LINKS: u32 = 3223878658;
pub const F2FS_IOC_PRECACHE_EXTENTS: u32 = 62735;
pub const DFL_FPGA_PORT_DMA_MAP: u32 = 46659;
pub const MGSL_IOCGXCTRL: u32 = 27926;
pub const FW_CDEV_IOC_SEND_REQUEST: u32 = 1076372225;
pub const SONYPI_IOCGBLUE: u32 = 2147579400;
pub const F2FS_IOC_DECOMPRESS_FILE: u32 = 62743;
pub const I2OHTML: u32 = 3224398089;
pub const VFIO_GET_API_VERSION: u32 = 15204;
pub const IDT77105_GETSTATZ: u32 = 1074815283;
pub const I2OPARMSET: u32 = 3223873795;
pub const TEE_IOC_CANCEL: u32 = 2148049924;
pub const PTP_SYS_OFFSET_PRECISE2: u32 = 3225435409;
pub const DFL_FPGA_PORT_RESET: u32 = 46656;
pub const PPPIOCGASYNCMAP: u32 = 2147775576;
pub const EVIOCGKEYCODE_V2: u32 = 2150122756;
pub const DM_DEV_SET_GEOMETRY: u32 = 3241737487;
pub const HIDIOCSUSAGE: u32 = 1075333132;
pub const FW_CDEV_IOC_DEALLOCATE_ISO_RESOURCE_ONCE: u32 = 1075323664;
pub const PTP_EXTTS_REQUEST: u32 = 1074806018;
pub const SWITCHTEC_IOCTL_EVENT_CTL: u32 = 3223869251;
pub const WDIOC_SETPRETIMEOUT: u32 = 3221509896;
pub const VHOST_SCSI_CLEAR_ENDPOINT: u32 = 1088991041;
pub const JSIOCGAXES: u32 = 2147576337;
pub const HIDIOCSFLAG: u32 = 1074022415;
pub const PTP_PEROUT_REQUEST2: u32 = 1077427468;
pub const PPWDATA: u32 = 1073836166;
pub const PTP_CLOCK_GETCAPS: u32 = 2152742145;
pub const FDGETMAXERRS: u32 = 2148794894;
pub const TUNSETQUEUE: u32 = 1074025689;
pub const PTP_ENABLE_PPS: u32 = 1074019588;
pub const SIOCSIFATMTCP: u32 = 24960;
pub const CEC_ADAP_G_LOG_ADDRS: u32 = 2153537795;
pub const ND_IOCTL_ARS_CAP: u32 = 3223342593;
pub const NBD_SET_BLKSIZE: u32 = 43777;
pub const NBD_SET_TIMEOUT: u32 = 43785;
pub const VHOST_SCSI_GET_ABI_VERSION: u32 = 1074048834;
pub const RIO_UNMAP_INBOUND: u32 = 1074294034;
pub const ATM_QUERYLOOP: u32 = 1074815316;
pub const DFL_FPGA_GET_API_VERSION: u32 = 46592;
pub const USBDEVFS_WAIT_FOR_RESUME: u32 = 21795;
pub const FBIO_CURSOR: u32 = 3228059144;
pub const RNDCLEARPOOL: u32 = 20998;
pub const VIDIOC_QUERYSTD: u32 = 2148030015;
pub const DMA_BUF_IOCTL_SYNC: u32 = 1074291200;
pub const SCIF_RECV: u32 = 3222827783;
pub const PTP_PIN_GETFUNC2: u32 = 3227532559;
pub const FW_CDEV_IOC_ALLOCATE: u32 = 3223331586;
pub const CEC_ADAP_G_CAPS: u32 = 3226231040;
pub const VIDIOC_G_FBUF: u32 = 2150651402;
pub const PTP_ENABLE_PPS2: u32 = 1074019597;
pub const PCITEST_CLEAR_IRQ: u32 = 20496;
pub const IPMICTL_SET_GETS_EVENTS_CMD: u32 = 2147772688;
pub const BTRFS_IOC_DEVICES_READY: u32 = 2415957031;
pub const JSIOCGAXMAP: u32 = 2151705138;
pub const FW_CDEV_IOC_GET_CYCLE_TIMER: u32 = 2148541196;
pub const FW_CDEV_IOC_SET_ISO_CHANNELS: u32 = 1074799383;
pub const RTC_WIE_OFF: u32 = 28688;
pub const PPGETMODE: u32 = 2147774616;
pub const VIDIOC_DBG_G_REGISTER: u32 = 3224917584;
pub const PTP_SYS_OFFSET: u32 = 1128283397;
pub const BTRFS_IOC_SPACE_INFO: u32 = 3222311956;
pub const VIDIOC_SUBDEV_ENUM_FRAME_SIZE: u32 = 3225441866;
pub const ND_IOCTL_VENDOR: u32 = 3221769737;
pub const SCIF_VREADFROM: u32 = 3223876364;
pub const BTRFS_IOC_TRANS_START: u32 = 37894;
pub const INOTIFY_IOC_SETNEXTWD: u32 = 1074022656;
pub const SNAPSHOT_GET_IMAGE_SIZE: u32 = 2148021006;
pub const TUNDETACHFILTER: u32 = 1074812118;
pub const ND_IOCTL_CLEAR_ERROR: u32 = 3223342596;
pub const IOC_PR_CLEAR: u32 = 1074819277;
pub const SCIF_READFROM: u32 = 3223876362;
pub const PPPIOCGDEBUG: u32 = 2147775553;
pub const BLKGETZONESZ: u32 = 2147750532;
pub const HIDIOCGUSAGES: u32 = 3491514387;
pub const SONYPI_IOCGTEMP: u32 = 2147579404;
pub const UI_SET_MSCBIT: u32 = 1074025832;
pub const APM_IOC_SUSPEND: u32 = 16642;
pub const BTRFS_IOC_TREE_SEARCH: u32 = 3489698833;
pub const RTC_PLL_GET: u32 = 2149609489;
pub const RIO_CM_EP_GET_LIST: u32 = 3221512962;
pub const USBDEVFS_DISCSIGNAL: u32 = 2148553998;
pub const LIRC_GET_MIN_TIMEOUT: u32 = 2147772680;
pub const SWITCHTEC_IOCTL_EVENT_SUMMARY_LEGACY: u32 = 2174244674;
pub const DM_TARGET_MSG: u32 = 3241737486;
pub const SONYPI_IOCGBAT1REM: u32 = 2147644931;
pub const EVIOCSFF: u32 = 1076905344;
pub const TUNSETGROUP: u32 = 1074025678;
pub const EVIOCGKEYCODE: u32 = 2148025604;
pub const KCOV_REMOTE_ENABLE: u32 = 1075340134;
pub const ND_IOCTL_GET_CONFIG_SIZE: u32 = 3222031876;
pub const FDEJECT: u32 = 602;
pub const TUNSETOFFLOAD: u32 = 1074025680;
pub const PPPIOCCONNECT: u32 = 1074033722;
pub const ATM_ADDADDR: u32 = 1074815368;
pub const VDUSE_DEV_INJECT_CONFIG_IRQ: u32 = 33043;
pub const AUTOFS_DEV_IOCTL_ASKUMOUNT: u32 = 3222836093;
pub const VHOST_VDPA_GET_STATUS: u32 = 2147594097;
pub const CCISS_PASSTHRU: u32 = 3227009547;
pub const MGSL_IOCCLRMODCOUNT: u32 = 27919;
pub const TEE_IOC_SUPPL_SEND: u32 = 2148574215;
pub const ATMARPD_CTRL: u32 = 25057;
pub const UI_ABS_SETUP: u32 = 1075598596;
pub const UI_DEV_DESTROY: u32 = 21762;
pub const BTRFS_IOC_QUOTA_CTL: u32 = 3222311976;
pub const RTC_AIE_ON: u32 = 28673;
pub const AUTOFS_IOC_EXPIRE: u32 = 2165085029;
pub const PPPIOCSDEBUG: u32 = 1074033728;
pub const GPIO_V2_LINE_SET_VALUES_IOCTL: u32 = 3222320143;
pub const PPPIOCSMRU: u32 = 1074033746;
pub const CCISS_DEREGDISK: u32 = 16908;
pub const UI_DEV_CREATE: u32 = 21761;
pub const FUSE_DEV_IOC_CLONE: u32 = 2147804416;
pub const BTRFS_IOC_START_SYNC: u32 = 2148045848;
pub const NILFS_IOCTL_DELETE_CHECKPOINT: u32 = 1074294401;
pub const SNAPSHOT_AVAIL_SWAP_SIZE: u32 = 2148021011;
pub const DM_TABLE_CLEAR: u32 = 3241737482;
pub const CCISS_GETINTINFO: u32 = 2148024834;
pub const PPPIOCSASYNCMAP: u32 = 1074033751;
pub const I2OEVTGET: u32 = 2154326283;
pub const NVME_IOCTL_RESET: u32 = 20036;
pub const PPYIELD: u32 = 28813;
pub const NVME_IOCTL_IO64_CMD: u32 = 3226488392;
pub const TUNSETCARRIER: u32 = 1074025698;
pub const DM_DEV_WAIT: u32 = 3241737480;
pub const RTC_WIE_ON: u32 = 28687;
pub const MEDIA_IOC_DEVICE_INFO: u32 = 3238034432;
pub const RIO_CM_CHAN_CREATE: u32 = 3221381891;
pub const MGSL_IOCSPARAMS: u32 = 1076915456;
pub const RTC_SET_TIME: u32 = 1076129802;
pub const VHOST_RESET_OWNER: u32 = 44802;
pub const IOC_OPAL_PSID_REVERT_TPR: u32 = 1091072232;
pub const AUTOFS_DEV_IOCTL_OPENMOUNT: u32 = 3222836084;
pub const UDF_GETEABLOCK: u32 = 2148035649;
pub const VFIO_IOMMU_MAP_DMA: u32 = 15217;
pub const VIDIOC_SUBSCRIBE_EVENT: u32 = 1075861082;
pub const HIDIOCGFLAG: u32 = 2147764238;
pub const HIDIOCGUCODE: u32 = 3222816781;
pub const VIDIOC_OMAP3ISP_AF_CFG: u32 = 3226228421;
pub const DM_REMOVE_ALL: u32 = 3241737473;
pub const ASPEED_LPC_CTRL_IOCTL_MAP: u32 = 1074835969;
pub const CCISS_GETFIRMVER: u32 = 2147762696;
pub const ND_IOCTL_ARS_START: u32 = 3223342594;
pub const PPPIOCSMRRU: u32 = 1074033723;
pub const CEC_ADAP_S_LOG_ADDRS: u32 = 3227279620;
pub const RPROC_GET_SHUTDOWN_ON_RELEASE: u32 = 2147792642;
pub const DMA_HEAP_IOCTL_ALLOC: u32 = 3222816768;
pub const PPSETTIME: u32 = 1074819222;
pub const RTC_ALM_READ: u32 = 2149871624;
pub const VDUSE_SET_API_VERSION: u32 = 1074299137;
pub const RIO_MPORT_MAINT_WRITE_REMOTE: u32 = 1075342600;
pub const VIDIOC_SUBDEV_S_CROP: u32 = 3224917564;
pub const USBDEVFS_CONNECT: u32 = 21783;
pub const SYNC_IOC_FILE_INFO: u32 = 3224911364;
pub const ATMARP_MKIP: u32 = 25058;
pub const VFIO_IOMMU_SPAPR_TCE_GET_INFO: u32 = 15216;
pub const CCISS_GETHEARTBEAT: u32 = 2147762694;
pub const ATM_RSTADDR: u32 = 1074815367;
pub const NBD_SET_SIZE: u32 = 43778;
pub const UDF_GETVOLIDENT: u32 = 2148035650;
pub const GPIO_V2_LINE_GET_VALUES_IOCTL: u32 = 3222320142;
pub const MGSL_IOCSTXIDLE: u32 = 27906;
pub const FSL_HV_IOCTL_SETPROP: u32 = 3223891720;
pub const BTRFS_IOC_GET_DEV_STATS: u32 = 3288896564;
pub const PPRSTATUS: u32 = 2147577985;
pub const MGSL_IOCTXENABLE: u32 = 27908;
pub const UDF_GETEASIZE: u32 = 2147773504;
pub const NVME_IOCTL_ADMIN64_CMD: u32 = 3226488391;
pub const VHOST_SET_OWNER: u32 = 44801;
pub const RIO_ALLOC_DMA: u32 = 3222826259;
pub const RIO_CM_CHAN_ACCEPT: u32 = 3221775111;
pub const I2OHRTGET: u32 = 3222825217;
pub const ATM_SETCIRANGE: u32 = 1074815371;
pub const HPET_IE_ON: u32 = 26625;
pub const PERF_EVENT_IOC_ID: u32 = 2148017159;
pub const TUNSETSNDBUF: u32 = 1074025684;
pub const PTP_PIN_SETFUNC: u32 = 1080048903;
pub const PPPIOCDISCONN: u32 = 29753;
pub const VIDIOC_QUERYCTRL: u32 = 3225703972;
pub const PPEXCL: u32 = 28815;
pub const PCITEST_MSI: u32 = 1074024451;
pub const FDWERRORCLR: u32 = 598;
pub const AUTOFS_IOC_FAIL: u32 = 37729;
pub const USBDEVFS_IOCTL: u32 = 3222295826;
pub const VIDIOC_S_STD: u32 = 1074288152;
pub const F2FS_IOC_RESIZE_FS: u32 = 1074328848;
pub const SONET_SETDIAG: u32 = 3221512466;
pub const BTRFS_IOC_DEFRAG: u32 = 1342215170;
pub const CCISS_GETDRIVVER: u32 = 2147762697;
pub const IPMICTL_GET_TIMING_PARMS_CMD: u32 = 2148034839;
pub const HPET_IRQFREQ: u32 = 1074292742;
pub const ATM_GETESI: u32 = 1074815365;
pub const CCISS_GETLUNINFO: u32 = 2148286993;
pub const AUTOFS_DEV_IOCTL_ISMOUNTPOINT: u32 = 3222836094;
pub const TEE_IOC_SHM_ALLOC: u32 = 3222316033;
pub const PERF_EVENT_IOC_SET_BPF: u32 = 1074013192;
pub const UDMABUF_CREATE_LIST: u32 = 1074296131;
pub const VHOST_SET_LOG_BASE: u32 = 1074310916;
pub const ZATM_GETPOOL: u32 = 1074815329;
pub const BR2684_SETFILT: u32 = 1075601808;
pub const RNDGETPOOL: u32 = 2148028930;
pub const PPS_GETPARAMS: u32 = 2148036769;
pub const IOC_PR_RESERVE: u32 = 1074819273;
pub const VIDIOC_TRY_DECODER_CMD: u32 = 3225966177;
pub const RIO_CM_CHAN_CLOSE: u32 = 1073898244;
pub const VIDIOC_DV_TIMINGS_CAP: u32 = 3230684772;
pub const IOCTL_MEI_CONNECT_CLIENT_VTAG: u32 = 3222554628;
pub const PMU_IOC_GET_BACKLIGHT: u32 = 2148024833;
pub const USBDEVFS_GET_CAPABILITIES: u32 = 2147767578;
pub const SCIF_WRITETO: u32 = 3223876363;
pub const UDF_RELOCATE_BLOCKS: u32 = 3221777475;
pub const FSL_HV_IOCTL_PARTITION_RESTART: u32 = 3221794561;
pub const CCISS_REGNEWD: u32 = 16910;
pub const FAT_IOCTL_SET_ATTRIBUTES: u32 = 1074033169;
pub const VIDIOC_CREATE_BUFS: u32 = 3238024796;
pub const CAPI_GET_VERSION: u32 = 3222291207;
pub const SWITCHTEC_IOCTL_EVENT_SUMMARY: u32 = 2228770626;
pub const VFIO_EEH_PE_OP: u32 = 15225;
pub const FW_CDEV_IOC_CREATE_ISO_CONTEXT: u32 = 3223331592;
pub const F2FS_IOC_RELEASE_COMPRESS_BLOCKS: u32 = 2148070674;
pub const NBD_SET_SIZE_BLOCKS: u32 = 43783;
pub const IPMI_BMC_IOCTL_SET_SMS_ATN: u32 = 45312;
pub const ASPEED_P2A_CTRL_IOCTL_GET_MEMORY_CONFIG: u32 = 3222319873;
pub const VIDIOC_S_AUDOUT: u32 = 1077171762;
pub const VIDIOC_S_FMT: u32 = 3234878981;
pub const PPPIOCATTACH: u32 = 1074033725;
pub const VHOST_GET_VRING_BUSYLOOP_TIMEOUT: u32 = 1074310948;
pub const FS_IOC_MEASURE_VERITY: u32 = 3221513862;
pub const CCISS_BIG_PASSTHRU: u32 = 3227533842;
pub const IPMICTL_SET_MY_LUN_CMD: u32 = 2147772691;
pub const PCITEST_LEGACY_IRQ: u32 = 20482;
pub const USBDEVFS_SUBMITURB: u32 = 2151175434;
pub const AUTOFS_IOC_READY: u32 = 37728;
pub const BTRFS_IOC_SEND: u32 = 1078498342;
pub const VIDIOC_G_EXT_CTRLS: u32 = 3223344711;
pub const JSIOCSBTNMAP: u32 = 1140877875;
pub const PPPIOCSFLAGS: u32 = 1074033753;
pub const NVRAM_INIT: u32 = 28736;
pub const RFKILL_IOCTL_NOINPUT: u32 = 20993;
pub const BTRFS_IOC_BALANCE: u32 = 1342215180;
pub const FS_IOC_GETFSMAP: u32 = 3233830971;
pub const IPMICTL_GET_MY_CHANNEL_LUN_CMD: u32 = 2147772699;
pub const STP_POLICY_ID_GET: u32 = 2148541697;
pub const PPSETFLAGS: u32 = 1074032795;
pub const CEC_ADAP_S_PHYS_ADDR: u32 = 1073897730;
pub const ATMTCP_CREATE: u32 = 24974;
pub const IPMI_BMC_IOCTL_FORCE_ABORT: u32 = 45314;
pub const PPPIOCGXASYNCMAP: u32 = 2149610576;
pub const VHOST_SET_VRING_CALL: u32 = 1074310945;
pub const LIRC_GET_FEATURES: u32 = 2147772672;
pub const GSMIOC_DISABLE_NET: u32 = 18179;
pub const AUTOFS_IOC_CATATONIC: u32 = 37730;
pub const NBD_DO_IT: u32 = 43779;
pub const LIRC_SET_REC_CARRIER_RANGE: u32 = 1074030879;
pub const IPMICTL_GET_MY_CHANNEL_ADDRESS_CMD: u32 = 2147772697;
pub const EVIOCSCLOCKID: u32 = 1074021792;
pub const USBDEVFS_FREE_STREAMS: u32 = 2148029725;
pub const FSI_SCOM_RESET: u32 = 1074033411;
pub const PMU_IOC_GRAB_BACKLIGHT: u32 = 2148024838;
pub const VIDIOC_SUBDEV_S_FMT: u32 = 3227014661;
pub const FDDEFPRM: u32 = 1075839555;
pub const TEE_IOC_INVOKE: u32 = 2148574211;
pub const USBDEVFS_BULK: u32 = 3222820098;
pub const SCIF_VWRITETO: u32 = 3223876365;
pub const SONYPI_IOCSBRT: u32 = 1073837568;
pub const BTRFS_IOC_FILE_EXTENT_SAME: u32 = 3222836278;
pub const RTC_PIE_ON: u32 = 28677;
pub const BTRFS_IOC_SCAN_DEV: u32 = 1342215172;
pub const PPPIOCXFERUNIT: u32 = 29774;
pub const WDIOC_GETTIMEOUT: u32 = 2147768071;
pub const BTRFS_IOC_SET_RECEIVED_SUBVOL: u32 = 3234370597;
pub const DFL_FPGA_PORT_ERR_SET_IRQ: u32 = 1074312774;
pub const FBIO_WAITFORVSYNC: u32 = 1074021920;
pub const RTC_PIE_OFF: u32 = 28678;
pub const EVIOCGRAB: u32 = 1074021776;
pub const PMU_IOC_SET_BACKLIGHT: u32 = 1074283010;
pub const EVIOCGREP: u32 = 2148025603;
pub const PERF_EVENT_IOC_MODIFY_ATTRIBUTES: u32 = 1074275339;
pub const UFFDIO_CONTINUE: u32 = 3223366151;
pub const VDUSE_GET_API_VERSION: u32 = 2148040960;
pub const RTC_RD_TIME: u32 = 2149871625;
pub const FDMSGOFF: u32 = 582;
pub const IPMICTL_REGISTER_FOR_CMD_CHANS: u32 = 2148296988;
pub const CAPI_GET_ERRCODE: u32 = 2147631905;
pub const PCITEST_SET_IRQTYPE: u32 = 1074024456;
pub const VIDIOC_SUBDEV_S_EDID: u32 = 3223868969;
pub const MATROXFB_SET_OUTPUT_MODE: u32 = 1074294522;
pub const RIO_DEV_ADD: u32 = 1075866903;
pub const VIDIOC_ENUM_FREQ_BANDS: u32 = 3225441893;
pub const FBIO_RADEON_SET_MIRROR: u32 = 1074282500;
pub const PCITEST_GET_IRQTYPE: u32 = 20489;
pub const JSIOCGVERSION: u32 = 2147772929;
pub const SONYPI_IOCSBLUE: u32 = 1073837577;
pub const SNAPSHOT_PREF_IMAGE_SIZE: u32 = 13074;
pub const F2FS_IOC_GET_FEATURES: u32 = 2147808524;
pub const SCIF_REG: u32 = 3223876360;
pub const NILFS_IOCTL_CLEAN_SEGMENTS: u32 = 1081634440;
pub const FW_CDEV_IOC_INITIATE_BUS_RESET: u32 = 1074012933;
pub const RIO_WAIT_FOR_ASYNC: u32 = 1074294038;
pub const VHOST_SET_VRING_NUM: u32 = 1074310928;
pub const AUTOFS_DEV_IOCTL_PROTOVER: u32 = 3222836082;
pub const RIO_FREE_DMA: u32 = 1074294036;
pub const MGSL_IOCRXENABLE: u32 = 27909;
pub const IOCTL_VM_SOCKETS_GET_LOCAL_CID: u32 = 1977;
pub const IPMICTL_SET_TIMING_PARMS_CMD: u32 = 2148034838;
pub const PPPIOCGL2TPSTATS: u32 = 2152231990;
pub const PERF_EVENT_IOC_PERIOD: u32 = 1074275332;
pub const PTP_PIN_SETFUNC2: u32 = 1080048912;
pub const CHIOEXCHANGE: u32 = 1075602178;
pub const NILFS_IOCTL_GET_SUINFO: u32 = 2149084804;
pub const CEC_DQEVENT: u32 = 3226493191;
pub const UI_SET_SWBIT: u32 = 1074025837;
pub const VHOST_VDPA_SET_CONFIG: u32 = 1074311028;
pub const TUNSETIFF: u32 = 1074025674;
pub const CHIOPOSITION: u32 = 1074553603;
pub const IPMICTL_SET_MAINTENANCE_MODE_CMD: u32 = 1074030879;
pub const BTRFS_IOC_DEFAULT_SUBVOL: u32 = 1074304019;
pub const RIO_UNMAP_OUTBOUND: u32 = 1076391184;
pub const CAPI_CLR_FLAGS: u32 = 2147762981;
pub const FW_CDEV_IOC_ALLOCATE_ISO_RESOURCE_ONCE: u32 = 1075323663;
pub const MATROXFB_GET_OUTPUT_CONNECTION: u32 = 2148036344;
pub const EVIOCSMASK: u32 = 1074808211;
pub const BTRFS_IOC_FORGET_DEV: u32 = 1342215173;
pub const CXL_MEM_QUERY_COMMANDS: u32 = 2148060673;
pub const CEC_S_MODE: u32 = 1074028809;
pub const MGSL_IOCSIF: u32 = 27914;
pub const SWITCHTEC_IOCTL_PFF_TO_PORT: u32 = 3222034244;
pub const PPSETMODE: u32 = 1074032768;
pub const VFIO_DEVICE_SET_IRQS: u32 = 15214;
pub const VIDIOC_PREPARE_BUF: u32 = 3227014749;
pub const CEC_ADAP_G_CONNECTOR_INFO: u32 = 2151964938;
pub const IOC_OPAL_WRITE_SHADOW_MBR: u32 = 1092645098;
pub const VIDIOC_SUBDEV_ENUM_FRAME_INTERVAL: u32 = 3225441867;
pub const UDMABUF_CREATE: u32 = 1075344706;
pub const SONET_CLRDIAG: u32 = 3221512467;
pub const PHN_SET_REG: u32 = 1074294785;
pub const RNDADDTOENTCNT: u32 = 1074024961;
pub const VBG_IOCTL_CHECK_BALLOON: u32 = 3223344657;
pub const VIDIOC_OMAP3ISP_STAT_REQ: u32 = 3223869126;
pub const PPS_FETCH: u32 = 3221778596;
pub const RTC_AIE_OFF: u32 = 28674;
pub const VFIO_GROUP_SET_CONTAINER: u32 = 15208;
pub const FW_CDEV_IOC_RECEIVE_PHY_PACKETS: u32 = 1074275094;
pub const VFIO_IOMMU_SPAPR_TCE_REMOVE: u32 = 15224;
pub const VFIO_IOMMU_GET_INFO: u32 = 15216;
pub const DM_DEV_SUSPEND: u32 = 3241737478;
pub const F2FS_IOC_GET_COMPRESS_OPTION: u32 = 2147677461;
pub const FW_CDEV_IOC_STOP_ISO: u32 = 1074012939;
pub const GPIO_V2_GET_LINEINFO_IOCTL: u32 = 3238048773;
pub const ATMMPC_CTRL: u32 = 25048;
pub const PPPIOCSXASYNCMAP: u32 = 1075868751;
pub const CHIOGSTATUS: u32 = 1074815752;
pub const FW_CDEV_IOC_ALLOCATE_ISO_RESOURCE: u32 = 3222807309;
pub const RIO_MPORT_MAINT_PORT_IDX_GET: u32 = 2147773699;
pub const CAPI_SET_FLAGS: u32 = 2147762980;
pub const VFIO_GROUP_GET_DEVICE_FD: u32 = 15210;
pub const VHOST_SET_MEM_TABLE: u32 = 1074310915;
pub const MATROXFB_SET_OUTPUT_CONNECTION: u32 = 1074294520;
pub const DFL_FPGA_PORT_GET_REGION_INFO: u32 = 46658;
pub const VHOST_GET_FEATURES: u32 = 2148052736;
pub const LIRC_GET_REC_RESOLUTION: u32 = 2147772679;
pub const PACKET_CTRL_CMD: u32 = 3222820865;
pub const LIRC_SET_TRANSMITTER_MASK: u32 = 1074030871;
pub const BTRFS_IOC_ADD_DEV: u32 = 1342215178;
pub const JSIOCGCORR: u32 = 2149870114;
pub const VIDIOC_G_FMT: u32 = 3234878980;
pub const RTC_EPOCH_SET: u32 = 1074294798;
pub const CAPI_GET_PROFILE: u32 = 3225436937;
pub const ATM_GETLOOP: u32 = 1074815314;
pub const SCIF_LISTEN: u32 = 1074033410;
pub const NBD_CLEAR_QUE: u32 = 43781;
pub const F2FS_IOC_MOVE_RANGE: u32 = 3223385353;
pub const LIRC_GET_LENGTH: u32 = 2147772687;
pub const I8K_SET_FAN: u32 = 3221776775;
pub const FDSETMAXERRS: u32 = 1075053132;
pub const VIDIOC_SUBDEV_QUERYCAP: u32 = 2151699968;
pub const SNAPSHOT_SET_SWAP_AREA: u32 = 1074541325;
pub const LIRC_GET_REC_TIMEOUT: u32 = 2147772708;
pub const EVIOCRMFF: u32 = 1074021761;
pub const GPIO_GET_LINEEVENT_IOCTL: u32 = 3224417284;
pub const PPRDATA: u32 = 2147577989;
pub const RIO_MPORT_GET_PROPERTIES: u32 = 2150657284;
pub const TUNSETVNETHDRSZ: u32 = 1074025688;
pub const GPIO_GET_LINEINFO_IOCTL: u32 = 3225990146;
pub const GSMIOC_GETCONF: u32 = 2152482560;
pub const LIRC_GET_SEND_MODE: u32 = 2147772673;
pub const PPPIOCSACTIVE: u32 = 1074820166;
pub const SIOCGSTAMPNS_NEW: u32 = 2148567303;
pub const IPMICTL_RECEIVE_MSG: u32 = 3224398092;
pub const LIRC_SET_SEND_DUTY_CYCLE: u32 = 1074030869;
pub const UI_END_FF_ERASE: u32 = 1074550219;
pub const SWITCHTEC_IOCTL_FLASH_PART_INFO: u32 = 3222296385;
pub const FW_CDEV_IOC_SEND_PHY_PACKET: u32 = 3222807317;
pub const NBD_SET_FLAGS: u32 = 43786;
pub const VFIO_DEVICE_GET_REGION_INFO: u32 = 15212;
pub const REISERFS_IOC_UNPACK: u32 = 1074318593;
pub const FW_CDEV_IOC_REMOVE_DESCRIPTOR: u32 = 1074012935;
pub const RIO_SET_EVENT_MASK: u32 = 1074031885;
pub const SNAPSHOT_ALLOC_SWAP_PAGE: u32 = 2148021012;
pub const VDUSE_VQ_INJECT_IRQ: u32 = 1074037015;
pub const I2OPASSTHRU: u32 = 2148559116;
pub const IOC_OPAL_SET_PW: u32 = 1109422304;
pub const FSI_SCOM_READ: u32 = 3223352065;
pub const VHOST_VDPA_GET_DEVICE_ID: u32 = 2147790704;
pub const VIDIOC_QBUF: u32 = 3227014671;
pub const VIDIOC_S_TUNER: u32 = 1079268894;
pub const TUNGETVNETHDRSZ: u32 = 2147767511;
pub const CAPI_NCCI_GETUNIT: u32 = 2147762983;
pub const DFL_FPGA_PORT_UINT_GET_IRQ_NUM: u32 = 2147792455;
pub const VIDIOC_OMAP3ISP_STAT_EN: u32 = 3221771975;
pub const GPIO_V2_LINE_SET_CONFIG_IOCTL: u32 = 3239097357;
pub const TEE_IOC_VERSION: u32 = 2148312064;
pub const VIDIOC_LOG_STATUS: u32 = 22086;
pub const IPMICTL_SEND_COMMAND_SETTIME: u32 = 2150656277;
pub const VHOST_SET_LOG_FD: u32 = 1074048775;
pub const SCIF_SEND: u32 = 3222827782;
pub const VIDIOC_SUBDEV_G_FMT: u32 = 3227014660;
pub const NS_ADJBUFLEV: u32 = 24931;
pub const VIDIOC_DBG_S_REGISTER: u32 = 1077433935;
pub const NILFS_IOCTL_RESIZE: u32 = 1074294411;
pub const PHN_GETREG: u32 = 3221778437;
pub const I2OSWDL: u32 = 3224398085;
pub const VBG_IOCTL_VMMDEV_REQUEST_BIG: u32 = 22019;
pub const JSIOCGBUTTONS: u32 = 2147576338;
pub const VFIO_IOMMU_ENABLE: u32 = 15219;
pub const DM_DEV_RENAME: u32 = 3241737477;
pub const MEDIA_IOC_SETUP_LINK: u32 = 3224665091;
pub const VIDIOC_ENUMOUTPUT: u32 = 3225966128;
pub const STP_POLICY_ID_SET: u32 = 3222283520;
pub const VHOST_VDPA_SET_CONFIG_CALL: u32 = 1074048887;
pub const VIDIOC_SUBDEV_G_CROP: u32 = 3224917563;
pub const VIDIOC_S_CROP: u32 = 1075074620;
pub const WDIOC_GETTEMP: u32 = 2147768067;
pub const IOC_OPAL_ADD_USR_TO_LR: u32 = 1092120804;
pub const UI_SET_LEDBIT: u32 = 1074025833;
pub const NBD_SET_SOCK: u32 = 43776;
pub const BTRFS_IOC_SNAP_DESTROY_V2: u32 = 1342215231;
pub const HIDIOCGCOLLECTIONINFO: u32 = 3222292497;
pub const I2OSWUL: u32 = 3224398086;
pub const IOCTL_MEI_NOTIFY_GET: u32 = 2147764227;
pub const FDFMTTRK: u32 = 1074528840;
pub const MMTIMER_GETBITS: u32 = 27908;
pub const VIDIOC_ENUMSTD: u32 = 3225966105;
pub const VHOST_GET_VRING_BASE: u32 = 3221794578;
pub const VFIO_DEVICE_IOEVENTFD: u32 = 15220;
pub const ATMARP_SETENTRY: u32 = 25059;
pub const CCISS_REVALIDVOLS: u32 = 16906;
pub const MGSL_IOCLOOPTXDONE: u32 = 27913;
pub const RTC_VL_READ: u32 = 2147774483;
pub const ND_IOCTL_ARS_STATUS: u32 = 3224391171;
pub const RIO_DEV_DEL: u32 = 1075866904;
pub const VBG_IOCTL_ACQUIRE_GUEST_CAPABILITIES: u32 = 3223606797;
pub const VIDIOC_SUBDEV_DV_TIMINGS_CAP: u32 = 3230684772;
pub const SONYPI_IOCSFAN: u32 = 1073837579;
pub const SPIOCSTYPE: u32 = 1074295041;
pub const IPMICTL_REGISTER_FOR_CMD: u32 = 2147641614;
pub const I8K_GET_FAN: u32 = 3221776774;
pub const TUNGETVNETBE: u32 = 2147767519;
pub const AUTOFS_DEV_IOCTL_FAIL: u32 = 3222836087;
pub const UI_END_FF_UPLOAD: u32 = 1080579529;
pub const TOSH_SMM: u32 = 3222828176;
pub const SONYPI_IOCGBAT2REM: u32 = 2147644933;
pub const F2FS_IOC_GET_COMPRESS_BLOCKS: u32 = 2148070673;
pub const PPPIOCSNPMODE: u32 = 1074295883;
pub const USBDEVFS_CONTROL: u32 = 3222820096;
pub const HIDIOCGUSAGE: u32 = 3222816779;
pub const TUNSETTXFILTER: u32 = 1074025681;
pub const TUNGETVNETLE: u32 = 2147767517;
pub const VIDIOC_ENUM_DV_TIMINGS: u32 = 3230946914;
pub const BTRFS_IOC_INO_PATHS: u32 = 3224933411;
pub const MGSL_IOCGXSYNC: u32 = 27924;
pub const HIDIOCGFIELDINFO: u32 = 3224913930;
pub const VIDIOC_SUBDEV_G_STD: u32 = 2148029975;
pub const I2OVALIDATE: u32 = 2147772680;
pub const VIDIOC_TRY_ENCODER_CMD: u32 = 3223869006;
pub const NILFS_IOCTL_GET_CPINFO: u32 = 2149084802;
pub const VIDIOC_G_FREQUENCY: u32 = 3224131128;
pub const VFAT_IOCTL_READDIR_SHORT: u32 = 2184212994;
pub const ND_IOCTL_GET_CONFIG_DATA: u32 = 3222031877;
pub const F2FS_IOC_RESERVE_COMPRESS_BLOCKS: u32 = 2148070675;
pub const FDGETDRVSTAT: u32 = 2152727058;
pub const SYNC_IOC_MERGE: u32 = 3224387075;
pub const VIDIOC_S_DV_TIMINGS: u32 = 3229898327;
pub const PPPIOCBRIDGECHAN: u32 = 1074033717;
pub const LIRC_SET_SEND_MODE: u32 = 1074030865;
pub const RIO_ENABLE_PORTWRITE_RANGE: u32 = 1074818315;
pub const ATM_GETTYPE: u32 = 1074815364;
pub const PHN_GETREGS: u32 = 3223875591;
pub const FDSETEMSGTRESH: u32 = 586;
pub const NILFS_IOCTL_GET_VINFO: u32 = 3222826630;
pub const MGSL_IOCWAITEVENT: u32 = 3221515528;
pub const CAPI_INSTALLED: u32 = 2147631906;
pub const EVIOCGMASK: u32 = 2148550034;
pub const BTRFS_IOC_SUBVOL_GETFLAGS: u32 = 2148045849;
pub const FSL_HV_IOCTL_PARTITION_GET_STATUS: u32 = 3222056706;
pub const MEDIA_IOC_ENUM_ENTITIES: u32 = 3238034433;
pub const GSMIOC_GETFIRST: u32 = 2147763972;
pub const FW_CDEV_IOC_FLUSH_ISO: u32 = 1074012952;
pub const VIDIOC_DBG_G_CHIP_INFO: u32 = 3234354790;
pub const F2FS_IOC_RELEASE_VOLATILE_WRITE: u32 = 62724;
pub const CAPI_GET_SERIAL: u32 = 3221504776;
pub const FDSETDRVPRM: u32 = 1082131088;
pub const IOC_OPAL_SAVE: u32 = 1092120796;
pub const VIDIOC_G_DV_TIMINGS: u32 = 3229898328;
pub const TUNSETIFINDEX: u32 = 1074025690;
pub const CCISS_SETINTINFO: u32 = 1074283011;
pub const RTC_VL_CLR: u32 = 28692;
pub const VIDIOC_REQBUFS: u32 = 3222558216;
pub const USBDEVFS_REAPURBNDELAY32: u32 = 1074025741;
pub const TEE_IOC_SHM_REGISTER: u32 = 3222840329;
pub const USBDEVFS_SETCONFIGURATION: u32 = 2147767557;
pub const CCISS_GETNODENAME: u32 = 2148549124;
pub const VIDIOC_SUBDEV_S_FRAME_INTERVAL: u32 = 3224393238;
pub const VIDIOC_ENUM_FRAMESIZES: u32 = 3224131146;
pub const VFIO_DEVICE_PCI_HOT_RESET: u32 = 15217;
pub const FW_CDEV_IOC_SEND_BROADCAST_REQUEST: u32 = 1076372242;
pub const LPSETTIMEOUT_NEW: u32 = 1074791951;
pub const RIO_CM_MPORT_GET_LIST: u32 = 3221512971;
pub const FW_CDEV_IOC_QUEUE_ISO: u32 = 3222807305;
pub const FDRAWCMD: u32 = 600;
pub const SCIF_UNREG: u32 = 3222303497;
pub const PPPIOCGIDLE64: u32 = 2148561983;
pub const USBDEVFS_RELEASEINTERFACE: u32 = 2147767568;
pub const VIDIOC_CROPCAP: u32 = 3224131130;
pub const DFL_FPGA_PORT_GET_INFO: u32 = 46657;
pub const PHN_SET_REGS: u32 = 1074294787;
pub const ATMLEC_DATA: u32 = 25041;
pub const PPPOEIOCDFWD: u32 = 45313;
pub const VIDIOC_S_SELECTION: u32 = 3225441887;
pub const SNAPSHOT_FREE_SWAP_PAGES: u32 = 13065;
pub const BTRFS_IOC_LOGICAL_INO: u32 = 3224933412;
pub const VIDIOC_S_CTRL: u32 = 3221771804;
pub const ZATM_SETPOOL: u32 = 1074815331;
pub const MTIOCPOS: u32 = 2148035843;
pub const PMU_IOC_SLEEP: u32 = 16896;
pub const AUTOFS_DEV_IOCTL_PROTOSUBVER: u32 = 3222836083;
pub const VBG_IOCTL_CHANGE_FILTER_MASK: u32 = 3223344652;
pub const NILFS_IOCTL_GET_SUSTAT: u32 = 2150657669;
pub const VIDIOC_QUERYCAP: u32 = 2154321408;
pub const HPET_INFO: u32 = 2149083139;
pub const VIDIOC_AM437X_CCDC_CFG: u32 = 1074288321;
pub const DM_LIST_DEVICES: u32 = 3241737474;
pub const TUNSETOWNER: u32 = 1074025676;
pub const VBG_IOCTL_CHANGE_GUEST_CAPABILITIES: u32 = 3223344654;
pub const RNDADDENTROPY: u32 = 1074287107;
pub const USBDEVFS_RESET: u32 = 21780;
pub const BTRFS_IOC_SUBVOL_CREATE: u32 = 1342215182;
pub const USBDEVFS_FORBID_SUSPEND: u32 = 21793;
pub const FDGETDRVTYP: u32 = 2148532751;
pub const PPWCONTROL: u32 = 1073836164;
pub const VIDIOC_ENUM_FRAMEINTERVALS: u32 = 3224655435;
pub const KCOV_DISABLE: u32 = 25445;
pub const IOC_OPAL_ACTIVATE_LSP: u32 = 1092120799;
pub const VHOST_VDPA_GET_IOVA_RANGE: u32 = 2148577144;
pub const PPPIOCSPASS: u32 = 1074820167;
pub const RIO_CM_CHAN_CONNECT: u32 = 1074291464;
pub const I2OSWDEL: u32 = 3224398087;
pub const FS_IOC_SET_ENCRYPTION_POLICY: u32 = 2148296211;
pub const IOC_OPAL_MBR_DONE: u32 = 1091596521;
pub const PPPIOCSMAXCID: u32 = 1074033745;
pub const PPSETPHASE: u32 = 1074032788;
pub const VHOST_VDPA_SET_VRING_ENABLE: u32 = 1074311029;
pub const USBDEVFS_GET_SPEED: u32 = 21791;
pub const SONET_GETFRAMING: u32 = 2147770646;
pub const VIDIOC_QUERYBUF: u32 = 3227014665;
pub const VIDIOC_S_EDID: u32 = 3223868969;
pub const BTRFS_IOC_QGROUP_ASSIGN: u32 = 1075352617;
pub const PPS_GETCAP: u32 = 2148036771;
pub const SNAPSHOT_PLATFORM_SUPPORT: u32 = 13071;
pub const LIRC_SET_REC_TIMEOUT_REPORTS: u32 = 1074030873;
pub const SCIF_GET_NODEIDS: u32 = 3222827790;
pub const NBD_DISCONNECT: u32 = 43784;
pub const VIDIOC_SUBDEV_G_FRAME_INTERVAL: u32 = 3224393237;
pub const VFIO_IOMMU_DISABLE: u32 = 15220;
pub const SNAPSHOT_CREATE_IMAGE: u32 = 1074017041;
pub const SNAPSHOT_POWER_OFF: u32 = 13072;
pub const APM_IOC_STANDBY: u32 = 16641;
pub const PPPIOCGUNIT: u32 = 2147775574;
pub const AUTOFS_IOC_EXPIRE_MULTI: u32 = 1074041702;
pub const SCIF_BIND: u32 = 3221779201;
pub const IOC_WATCH_QUEUE_SET_SIZE: u32 = 22368;
pub const NILFS_IOCTL_CHANGE_CPMODE: u32 = 1074818688;
pub const IOC_OPAL_LOCK_UNLOCK: u32 = 1092120797;
pub const F2FS_IOC_SET_PIN_FILE: u32 = 1074066701;
pub const PPPIOCGRASYNCMAP: u32 = 2147775573;
pub const MMTIMER_MMAPAVAIL: u32 = 27910;
pub const I2OPASSTHRU32: u32 = 2148034828;
pub const DFL_FPGA_FME_PORT_RELEASE: u32 = 1074050689;
pub const VIDIOC_SUBDEV_QUERY_DV_TIMINGS: u32 = 2156156515;
pub const UI_SET_SNDBIT: u32 = 1074025834;
pub const VIDIOC_G_AUDOUT: u32 = 2150913585;
pub const RTC_PLL_SET: u32 = 1075867666;
pub const VIDIOC_ENUMAUDIO: u32 = 3224655425;
pub const AUTOFS_DEV_IOCTL_TIMEOUT: u32 = 3222836090;
pub const VBG_IOCTL_DRIVER_VERSION_INFO: u32 = 3224131072;
pub const VHOST_SCSI_GET_EVENTS_MISSED: u32 = 1074048836;
pub const VHOST_SET_VRING_ADDR: u32 = 1076408081;
pub const VDUSE_CREATE_DEV: u32 = 1095794946;
pub const FDFLUSH: u32 = 587;
pub const VBG_IOCTL_WAIT_FOR_EVENTS: u32 = 3223344650;
pub const DFL_FPGA_FME_ERR_SET_IRQ: u32 = 1074312836;
pub const F2FS_IOC_GET_PIN_FILE: u32 = 2147808526;
pub const SCIF_CONNECT: u32 = 3221779203;
pub const BLKREPORTZONE: u32 = 3222278786;
pub const AUTOFS_IOC_ASKUMOUNT: u32 = 2147783536;
pub const ATM_ADDPARTY: u32 = 1074815476;
pub const FDSETPRM: u32 = 1075839554;
pub const ATM_GETSTATZ: u32 = 1074815313;
pub const ISST_IF_MSR_COMMAND: u32 = 3221814788;
pub const BTRFS_IOC_GET_SUBVOL_INFO: u32 = 2180551740;
pub const VIDIOC_UNSUBSCRIBE_EVENT: u32 = 1075861083;
pub const SEV_ISSUE_CMD: u32 = 3222295296;
pub const GPIOHANDLE_SET_LINE_VALUES_IOCTL: u32 = 3225465865;
pub const PCITEST_COPY: u32 = 1074286598;
pub const IPMICTL_GET_MY_ADDRESS_CMD: u32 = 2147772690;
pub const CHIOGPICKER: u32 = 2147771140;
pub const CAPI_NCCI_OPENCOUNT: u32 = 2147762982;
pub const CXL_MEM_SEND_COMMAND: u32 = 3224423938;
pub const PERF_EVENT_IOC_SET_FILTER: u32 = 1074275334;
pub const IOC_OPAL_REVERT_TPR: u32 = 1091072226;
pub const CHIOGVPARAMS: u32 = 2154849043;
pub const PTP_PEROUT_REQUEST: u32 = 1077427459;
pub const FSI_SCOM_CHECK: u32 = 2147775232;
pub const RTC_IRQP_READ: u32 = 2148036619;
pub const RIO_MPORT_MAINT_READ_LOCAL: u32 = 2149084421;
pub const HIDIOCGRDESCSIZE: u32 = 2147764225;
pub const UI_GET_VERSION: u32 = 2147767597;
pub const NILFS_IOCTL_GET_CPSTAT: u32 = 2149084803;
pub const CCISS_GETBUSTYPES: u32 = 2147762695;
pub const VFIO_IOMMU_SPAPR_TCE_CREATE: u32 = 15223;
pub const VIDIOC_EXPBUF: u32 = 3225441808;
pub const UI_SET_RELBIT: u32 = 1074025830;
pub const VFIO_SET_IOMMU: u32 = 15206;
pub const VIDIOC_S_MODULATOR: u32 = 1078220343;
pub const TUNGETFILTER: u32 = 2148553947;
pub const CCISS_SETNODENAME: u32 = 1074807301;
pub const FBIO_GETCONTROL2: u32 = 2148025993;
pub const TUNSETDEBUG: u32 = 1074025673;
pub const DM_DEV_REMOVE: u32 = 3241737476;
pub const HIDIOCSUSAGES: u32 = 1344030740;
pub const FS_IOC_ADD_ENCRYPTION_KEY: u32 = 3226494487;
pub const FBIOGET_VBLANK: u32 = 2149598738;
pub const ATM_GETSTAT: u32 = 1074815312;
pub const VIDIOC_G_JPEGCOMP: u32 = 2156680765;
pub const TUNATTACHFILTER: u32 = 1074812117;
pub const UI_SET_ABSBIT: u32 = 1074025831;
pub const DFL_FPGA_PORT_ERR_GET_IRQ_NUM: u32 = 2147792453;
pub const USBDEVFS_REAPURB32: u32 = 1074025740;
pub const BTRFS_IOC_TRANS_END: u32 = 37895;
pub const CAPI_REGISTER: u32 = 1074545409;
pub const F2FS_IOC_COMPRESS_FILE: u32 = 62744;
pub const USBDEVFS_DISCARDURB: u32 = 21771;
pub const HE_GET_REG: u32 = 1074815328;
pub const ATM_SETLOOP: u32 = 1074815315;
pub const ATMSIGD_CTRL: u32 = 25072;
pub const CIOC_KERNEL_VERSION: u32 = 3221775114;
pub const BTRFS_IOC_CLONE_RANGE: u32 = 1075876877;
pub const SNAPSHOT_UNFREEZE: u32 = 13058;
pub const F2FS_IOC_START_VOLATILE_WRITE: u32 = 62723;
pub const PMU_IOC_HAS_ADB: u32 = 2148024836;
pub const I2OGETIOPS: u32 = 2149607680;
pub const VIDIOC_S_FBUF: u32 = 1076909579;
pub const PPRCONTROL: u32 = 2147577987;
pub const CHIOSPICKER: u32 = 1074029317;
pub const VFIO_IOMMU_SPAPR_REGISTER_MEMORY: u32 = 15221;
pub const TUNGETSNDBUF: u32 = 2147767507;
pub const GSMIOC_SETCONF: u32 = 1078740737;
pub const IOC_PR_PREEMPT: u32 = 1075343563;
pub const KCOV_INIT_TRACE: u32 = 2148033281;
pub const SONYPI_IOCGBAT1CAP: u32 = 2147644930;
pub const SWITCHTEC_IOCTL_FLASH_INFO: u32 = 2148554560;
pub const MTIOCTOP: u32 = 1074294017;
pub const VHOST_VDPA_SET_STATUS: u32 = 1073852274;
pub const VHOST_SCSI_SET_EVENTS_MISSED: u32 = 1074048835;
pub const VFIO_IOMMU_DIRTY_PAGES: u32 = 15221;
pub const BTRFS_IOC_SCRUB_PROGRESS: u32 = 3288372253;
pub const PPPIOCGMRU: u32 = 2147775571;
pub const BTRFS_IOC_DEV_REPLACE: u32 = 3391657013;
pub const PPPIOCGFLAGS: u32 = 2147775578;
pub const NILFS_IOCTL_SET_SUINFO: u32 = 1075342989;
pub const FW_CDEV_IOC_GET_CYCLE_TIMER2: u32 = 3222807316;
pub const ATM_DELLECSADDR: u32 = 1074815375;
pub const FW_CDEV_IOC_GET_SPEED: u32 = 8977;
pub const PPPIOCGIDLE32: u32 = 2148037695;
pub const VFIO_DEVICE_RESET: u32 = 15215;
pub const GPIO_GET_LINEINFO_UNWATCH_IOCTL: u32 = 3221533708;
pub const WDIOC_GETSTATUS: u32 = 2147768065;
pub const BTRFS_IOC_SET_FEATURES: u32 = 1076925497;
pub const IOCTL_MEI_CONNECT_CLIENT: u32 = 3222292481;
pub const VIDIOC_OMAP3ISP_AEWB_CFG: u32 = 3223344835;
pub const PCITEST_READ: u32 = 1074286597;
pub const VFIO_GROUP_GET_STATUS: u32 = 15207;
pub const MATROXFB_GET_ALL_OUTPUTS: u32 = 2148036347;
pub const USBDEVFS_CLEAR_HALT: u32 = 2147767573;
pub const VIDIOC_DECODER_CMD: u32 = 3225966176;
pub const VIDIOC_G_AUDIO: u32 = 2150913569;
pub const CCISS_RESCANDISK: u32 = 16912;
pub const RIO_DISABLE_PORTWRITE_RANGE: u32 = 1074818316;
pub const IOC_OPAL_SECURE_ERASE_LR: u32 = 1091596519;
pub const USBDEVFS_REAPURB: u32 = 1074287884;
pub const DFL_FPGA_CHECK_EXTENSION: u32 = 46593;
pub const AUTOFS_IOC_PROTOVER: u32 = 2147783523;
pub const FSL_HV_IOCTL_MEMCPY: u32 = 3223891717;
pub const BTRFS_IOC_GET_FEATURES: u32 = 2149094457;
pub const PCITEST_MSIX: u32 = 1074024455;
pub const BTRFS_IOC_DEFRAG_RANGE: u32 = 1076925456;
pub const UI_BEGIN_FF_ERASE: u32 = 3222033866;
pub const DM_GET_TARGET_VERSION: u32 = 3241737489;
pub const PPPIOCGIDLE: u32 = 2148561983;
pub const NVRAM_SETCKS: u32 = 28737;
pub const WDIOC_GETSUPPORT: u32 = 2150127360;
pub const GSMIOC_ENABLE_NET: u32 = 1077167874;
pub const GPIO_GET_CHIPINFO_IOCTL: u32 = 2151986177;
pub const NE_ADD_VCPU: u32 = 3221532193;
pub const EVIOCSKEYCODE_V2: u32 = 1076380932;
pub const PTP_SYS_OFFSET_EXTENDED2: u32 = 3300932882;
pub const SCIF_FENCE_WAIT: u32 = 3221517072;
pub const RIO_TRANSFER: u32 = 3222826261;
pub const FSL_HV_IOCTL_DOORBELL: u32 = 3221794566;
pub const RIO_MPORT_MAINT_WRITE_LOCAL: u32 = 1075342598;
pub const I2OEVTREG: u32 = 1074555146;
pub const I2OPARMGET: u32 = 3223873796;
pub const EVIOCGID: u32 = 2148025602;
pub const BTRFS_IOC_QGROUP_CREATE: u32 = 1074828330;
pub const AUTOFS_DEV_IOCTL_SETPIPEFD: u32 = 3222836088;
pub const VIDIOC_S_PARM: u32 = 3234616854;
pub const TUNSETSTEERINGEBPF: u32 = 2147767520;
pub const ATM_GETNAMES: u32 = 1074815363;
pub const VIDIOC_QUERYMENU: u32 = 3224131109;
pub const DFL_FPGA_PORT_DMA_UNMAP: u32 = 46660;
pub const I2OLCTGET: u32 = 3222825218;
pub const FS_IOC_GET_ENCRYPTION_PWSALT: u32 = 1074816532;
pub const NS_SETBUFLEV: u32 = 1074815330;
pub const BLKCLOSEZONE: u32 = 1074795143;
pub const SONET_GETFRSENSE: u32 = 2147901719;
pub const UI_SET_EVBIT: u32 = 1074025828;
pub const DM_LIST_VERSIONS: u32 = 3241737485;
pub const HIDIOCGSTRING: u32 = 2164541444;
pub const PPPIOCATTCHAN: u32 = 1074033720;
pub const VDUSE_DEV_SET_CONFIG: u32 = 1074299154;
pub const TUNGETFEATURES: u32 = 2147767503;
pub const VFIO_GROUP_UNSET_CONTAINER: u32 = 15209;
pub const IPMICTL_SET_MY_ADDRESS_CMD: u32 = 2147772689;
pub const CCISS_REGNEWDISK: u32 = 1074020877;
pub const VIDIOC_QUERY_DV_TIMINGS: u32 = 2156156515;
pub const PHN_SETREGS: u32 = 1076391944;
pub const FAT_IOCTL_GET_ATTRIBUTES: u32 = 2147774992;
pub const FSL_MC_SEND_MC_COMMAND: u32 = 3225440992;
pub const TUNGETIFF: u32 = 2147767506;
pub const PTP_CLOCK_GETCAPS2: u32 = 2152742154;
pub const BTRFS_IOC_RESIZE: u32 = 1342215171;
pub const VHOST_SET_VRING_ENDIAN: u32 = 1074310931;
pub const PPS_KC_BIND: u32 = 1074294949;
pub const F2FS_IOC_WRITE_CHECKPOINT: u32 = 62727;
pub const UI_SET_FFBIT: u32 = 1074025835;
pub const IPMICTL_GET_MY_LUN_CMD: u32 = 2147772692;
pub const CEC_ADAP_G_PHYS_ADDR: u32 = 2147639553;
pub const CEC_G_MODE: u32 = 2147770632;
pub const USBDEVFS_RESETEP: u32 = 2147767555;
pub const MEDIA_REQUEST_IOC_QUEUE: u32 = 31872;
pub const USBDEVFS_ALLOC_STREAMS: u32 = 2148029724;
pub const MGSL_IOCSXCTRL: u32 = 27925;
pub const MEDIA_IOC_G_TOPOLOGY: u32 = 3225975812;
pub const PPPIOCUNBRIDGECHAN: u32 = 29748;
pub const F2FS_IOC_COMMIT_ATOMIC_WRITE: u32 = 62722;
pub const ISST_IF_GET_PLATFORM_INFO: u32 = 2148072960;
pub const SCIF_FENCE_MARK: u32 = 3222303503;
pub const USBDEVFS_RELEASE_PORT: u32 = 2147767577;
pub const VFIO_CHECK_EXTENSION: u32 = 15205;
pub const BTRFS_IOC_QGROUP_LIMIT: u32 = 2150667307;
pub const FAT_IOCTL_GET_VOLUME_ID: u32 = 2147774995;
pub const UI_SET_PHYS: u32 = 1074287980;
pub const FDWERRORGET: u32 = 2150105623;
pub const VIDIOC_SUBDEV_G_EDID: u32 = 3223868968;
pub const MGSL_IOCGSTATS: u32 = 27911;
pub const RPROC_SET_SHUTDOWN_ON_RELEASE: u32 = 1074050817;
pub const SIOCGSTAMP_NEW: u32 = 2148567302;
pub const RTC_WKALM_RD: u32 = 2150133776;
pub const PHN_GET_REG: u32 = 3221778432;
pub const DELL_WMI_SMBIOS_CMD: u32 = 3224655616;
pub const PHN_NOT_OH: u32 = 28676;
pub const PPGETMODES: u32 = 2147774615;
pub const CHIOGPARAMS: u32 = 2148819718;
pub const VFIO_DEVICE_GET_GFX_DMABUF: u32 = 15219;
pub const VHOST_SET_VRING_BUSYLOOP_TIMEOUT: u32 = 1074310947;
pub const VIDIOC_SUBDEV_G_SELECTION: u32 = 3225441853;
pub const BTRFS_IOC_RM_DEV_V2: u32 = 1342215226;
pub const MGSL_IOCWAITGPIO: u32 = 3222301970;
pub const PMU_IOC_CAN_SLEEP: u32 = 2148024837;
pub const KCOV_ENABLE: u32 = 25444;
pub const BTRFS_IOC_CLONE: u32 = 1074041865;
pub const F2FS_IOC_DEFRAGMENT: u32 = 3222336776;
pub const FW_CDEV_IOC_DEALLOCATE_ISO_RESOURCE: u32 = 1074012942;
pub const AGPIOC_ALLOCATE: u32 = 3221766406;
pub const NE_SET_USER_MEMORY_REGION: u32 = 1075359267;
pub const MGSL_IOCTXABORT: u32 = 27910;
pub const MGSL_IOCSGPIO: u32 = 1074818320;
pub const LIRC_SET_REC_CARRIER: u32 = 1074030868;
pub const F2FS_IOC_FLUSH_DEVICE: u32 = 1074328842;
pub const SNAPSHOT_ATOMIC_RESTORE: u32 = 13060;
pub const RTC_UIE_OFF: u32 = 28676;
pub const BT_BMC_IOCTL_SMS_ATN: u32 = 45312;
pub const NVME_IOCTL_ID: u32 = 20032;
pub const NE_START_ENCLAVE: u32 = 3222318628;
pub const VIDIOC_STREAMON: u32 = 1074026002;
pub const FDPOLLDRVSTAT: u32 = 2152727059;
pub const AUTOFS_DEV_IOCTL_READY: u32 = 3222836086;
pub const VIDIOC_ENUMAUDOUT: u32 = 3224655426;
pub const VIDIOC_SUBDEV_S_STD: u32 = 1074288152;
pub const WDIOC_GETTIMELEFT: u32 = 2147768074;
pub const ATM_GETLINKRATE: u32 = 1074815361;
pub const RTC_WKALM_SET: u32 = 1076391951;
pub const VHOST_GET_BACKEND_FEATURES: u32 = 2148052774;
pub const ATMARP_ENCAP: u32 = 25061;
pub const CAPI_GET_FLAGS: u32 = 2147762979;
pub const IPMICTL_SET_MY_CHANNEL_ADDRESS_CMD: u32 = 2147772696;
pub const DFL_FPGA_FME_PORT_ASSIGN: u32 = 1074050690;
pub const NS_GET_OWNER_UID: u32 = 46852;
pub const VIDIOC_OVERLAY: u32 = 1074025998;
pub const BTRFS_IOC_WAIT_SYNC: u32 = 1074304022;
pub const GPIOHANDLE_SET_CONFIG_IOCTL: u32 = 3226776586;
pub const VHOST_GET_VRING_ENDIAN: u32 = 1074310932;
pub const ATM_GETADDR: u32 = 1074815366;
pub const PHN_GET_REGS: u32 = 3221778434;
pub const AUTOFS_DEV_IOCTL_REQUESTER: u32 = 3222836091;
pub const AUTOFS_DEV_IOCTL_EXPIRE: u32 = 3222836092;
pub const SNAPSHOT_S2RAM: u32 = 13067;
pub const JSIOCSAXMAP: u32 = 1077963313;
pub const F2FS_IOC_SET_COMPRESS_OPTION: u32 = 1073935638;
pub const VBG_IOCTL_HGCM_DISCONNECT: u32 = 3223082501;
pub const SCIF_FENCE_SIGNAL: u32 = 3223876369;
pub const VFIO_DEVICE_GET_PCI_HOT_RESET_INFO: u32 = 15216;
pub const VIDIOC_SUBDEV_ENUM_MBUS_CODE: u32 = 3224393218;
pub const MMTIMER_GETOFFSET: u32 = 27904;
pub const RIO_CM_CHAN_LISTEN: u32 = 1073898246;
pub const ATM_SETSC: u32 = 1074029041;
pub const F2FS_IOC_SHUTDOWN: u32 = 2147768445;
pub const NVME_IOCTL_RESCAN: u32 = 20038;
pub const BLKOPENZONE: u32 = 1074795142;
pub const DM_VERSION: u32 = 3241737472;
pub const CEC_TRANSMIT: u32 = 3224920325;
pub const FS_IOC_GET_ENCRYPTION_POLICY_EX: u32 = 3221841430;
pub const SIOCMKCLIP: u32 = 25056;
pub const IPMI_BMC_IOCTL_CLEAR_SMS_ATN: u32 = 45313;
pub const HIDIOCGVERSION: u32 = 2147764225;
pub const VIDIOC_S_INPUT: u32 = 3221509671;
pub const VIDIOC_G_CROP: u32 = 3222558267;
pub const LIRC_SET_WIDEBAND_RECEIVER: u32 = 1074030883;
pub const EVIOCGEFFECTS: u32 = 2147763588;
pub const UVCIOC_CTRL_QUERY: u32 = 3222304033;
pub const IOC_OPAL_GENERIC_TABLE_RW: u32 = 1094217963;
pub const FS_IOC_READ_VERITY_METADATA: u32 = 3223873159;
pub const ND_IOCTL_SET_CONFIG_DATA: u32 = 3221769734;
pub const USBDEVFS_GETDRIVER: u32 = 1090802952;
pub const IDT77105_GETSTAT: u32 = 1074815282;
pub const HIDIOCINITREPORT: u32 = 18437;
pub const VFIO_DEVICE_GET_INFO: u32 = 15211;
pub const RIO_CM_CHAN_RECEIVE: u32 = 3222299402;
pub const RNDGETENTCNT: u32 = 2147766784;
pub const PPPIOCNEWUNIT: u32 = 3221517374;
pub const BTRFS_IOC_INO_LOOKUP: u32 = 3489698834;
pub const FDRESET: u32 = 596;
pub const IOC_PR_REGISTER: u32 = 1075343560;
pub const HIDIOCSREPORT: u32 = 1074546696;
pub const TEE_IOC_OPEN_SESSION: u32 = 2148574210;
pub const TEE_IOC_SUPPL_RECV: u32 = 2148574214;
pub const BTRFS_IOC_BALANCE_CTL: u32 = 1074041889;
pub const GPIO_GET_LINEINFO_WATCH_IOCTL: u32 = 3225990155;
pub const HIDIOCGRAWINFO: u32 = 2148026371;
pub const PPPIOCSCOMPRESS: u32 = 1074820173;
pub const USBDEVFS_CONNECTINFO: u32 = 1074287889;
pub const BLKRESETZONE: u32 = 1074795139;
pub const CHIOINITELEM: u32 = 25361;
pub const NILFS_IOCTL_SET_ALLOC_RANGE: u32 = 1074818700;
pub const AUTOFS_DEV_IOCTL_CATATONIC: u32 = 3222836089;
pub const RIO_MPORT_MAINT_HDID_SET: u32 = 1073900801;
pub const PPGETPHASE: u32 = 2147774617;
pub const USBDEVFS_DISCONNECT_CLAIM: u32 = 2164806939;
pub const FDMSGON: u32 = 581;
pub const VIDIOC_G_SLICED_VBI_CAP: u32 = 3228849733;
pub const BTRFS_IOC_BALANCE_V2: u32 = 3288372256;
pub const MEDIA_REQUEST_IOC_REINIT: u32 = 31873;
pub const IOC_OPAL_ERASE_LR: u32 = 1091596518;
pub const FDFMTBEG: u32 = 583;
pub const RNDRESEEDCRNG: u32 = 20999;
pub const ISST_IF_GET_PHY_ID: u32 = 3221814785;
pub const TUNSETNOCSUM: u32 = 1074025672;
pub const SONET_GETSTAT: u32 = 2149867792;
pub const TFD_IOC_SET_TICKS: u32 = 1074287616;
pub const PPDATADIR: u32 = 1074032784;
pub const IOC_OPAL_ENABLE_DISABLE_MBR: u32 = 1091596517;
pub const GPIO_V2_GET_LINE_IOCTL: u32 = 3260068871;
pub const RIO_CM_CHAN_SEND: u32 = 1074815753;
pub const PPWCTLONIRQ: u32 = 1073836178;
pub const SONYPI_IOCGBRT: u32 = 2147579392;
pub const IOC_PR_RELEASE: u32 = 1074819274;
pub const PPCLRIRQ: u32 = 2147774611;
pub const IPMICTL_SET_MY_CHANNEL_LUN_CMD: u32 = 2147772698;
pub const MGSL_IOCSXSYNC: u32 = 27923;
pub const HPET_IE_OFF: u32 = 26626;
pub const IOC_OPAL_ACTIVATE_USR: u32 = 1091596513;
pub const SONET_SETFRAMING: u32 = 1074028821;
pub const PERF_EVENT_IOC_PAUSE_OUTPUT: u32 = 1074013193;
pub const BTRFS_IOC_LOGICAL_INO_V2: u32 = 3224933435;
pub const VBG_IOCTL_HGCM_CONNECT: u32 = 3231471108;
pub const BLKFINISHZONE: u32 = 1074795144;
pub const EVIOCREVOKE: u32 = 1074021777;
pub const VFIO_DEVICE_FEATURE: u32 = 15221;
pub const CCISS_GETPCIINFO: u32 = 2148024833;
pub const ISST_IF_MBOX_COMMAND: u32 = 3221814787;
pub const SCIF_ACCEPTREQ: u32 = 3222303492;
pub const PERF_EVENT_IOC_QUERY_BPF: u32 = 3221758986;
pub const VIDIOC_STREAMOFF: u32 = 1074026003;
pub const VDUSE_DESTROY_DEV: u32 = 1090552067;
pub const FDGETFDCSTAT: u32 = 2150105621;
pub const VIDIOC_S_PRIORITY: u32 = 1074026052;
pub const SNAPSHOT_FREEZE: u32 = 13057;
pub const VIDIOC_ENUMINPUT: u32 = 3226490394;
pub const ZATM_GETPOOLZ: u32 = 1074815330;
pub const RIO_DISABLE_DOORBELL_RANGE: u32 = 1074294026;
pub const GPIO_V2_GET_LINEINFO_WATCH_IOCTL: u32 = 3238048774;
pub const VIDIOC_G_STD: u32 = 2148029975;
pub const USBDEVFS_ALLOW_SUSPEND: u32 = 21794;
pub const SONET_GETSTATZ: u32 = 2149867793;
pub const SCIF_ACCEPTREG: u32 = 3221779205;
pub const VIDIOC_ENCODER_CMD: u32 = 3223869005;
pub const PPPIOCSRASYNCMAP: u32 = 1074033748;
pub const IOCTL_MEI_NOTIFY_SET: u32 = 1074022402;
pub const BTRFS_IOC_QUOTA_RESCAN_STATUS: u32 = 2151715885;
pub const F2FS_IOC_GARBAGE_COLLECT: u32 = 1074066694;
pub const ATMLEC_CTRL: u32 = 25040;
pub const MATROXFB_GET_AVAILABLE_OUTPUTS: u32 = 2148036345;
pub const DM_DEV_CREATE: u32 = 3241737475;
pub const VHOST_VDPA_GET_VRING_NUM: u32 = 2147659638;
pub const VIDIOC_G_CTRL: u32 = 3221771803;
pub const NBD_CLEAR_SOCK: u32 = 43780;
pub const VFIO_DEVICE_QUERY_GFX_PLANE: u32 = 15218;
pub const WDIOC_KEEPALIVE: u32 = 2147768069;
pub const NVME_IOCTL_SUBSYS_RESET: u32 = 20037;
pub const PTP_EXTTS_REQUEST2: u32 = 1074806027;
pub const PCITEST_BAR: u32 = 20481;
pub const MGSL_IOCGGPIO: u32 = 2148560145;
pub const EVIOCSREP: u32 = 1074283779;
pub const VFIO_DEVICE_GET_IRQ_INFO: u32 = 15213;
pub const HPET_DPI: u32 = 26629;
pub const VDUSE_VQ_SETUP_KICKFD: u32 = 1074299158;
pub const ND_IOCTL_CALL: u32 = 3225439754;
pub const HIDIOCGDEVINFO: u32 = 2149337091;
pub const DM_TABLE_DEPS: u32 = 3241737483;
pub const BTRFS_IOC_DEV_INFO: u32 = 3489698846;
pub const VDUSE_IOTLB_GET_FD: u32 = 3223355664;
pub const FW_CDEV_IOC_GET_INFO: u32 = 3223855872;
pub const VIDIOC_G_PRIORITY: u32 = 2147767875;
pub const ATM_NEWBACKENDIF: u32 = 1073897971;
pub const VIDIOC_S_EXT_CTRLS: u32 = 3223344712;
pub const VIDIOC_SUBDEV_ENUM_DV_TIMINGS: u32 = 3230946914;
pub const VIDIOC_OMAP3ISP_CCDC_CFG: u32 = 3224917697;
pub const VIDIOC_S_HW_FREQ_SEEK: u32 = 1076909650;
pub const DM_TABLE_LOAD: u32 = 3241737481;
pub const F2FS_IOC_START_ATOMIC_WRITE: u32 = 62721;
pub const VIDIOC_G_OUTPUT: u32 = 2147767854;
pub const ATM_DROPPARTY: u32 = 1074029045;
pub const CHIOGELEM: u32 = 1080845072;
pub const BTRFS_IOC_GET_SUPPORTED_FEATURES: u32 = 2152240185;
pub const EVIOCSKEYCODE: u32 = 1074283780;
pub const NE_GET_IMAGE_LOAD_INFO: u32 = 3222318626;
pub const TUNSETLINK: u32 = 1074025677;
pub const FW_CDEV_IOC_ADD_DESCRIPTOR: u32 = 3222807302;
pub const BTRFS_IOC_SCRUB_CANCEL: u32 = 37916;
pub const PPS_SETPARAMS: u32 = 1074294946;
pub const IOC_OPAL_LR_SETUP: u32 = 1093169379;
pub const FW_CDEV_IOC_DEALLOCATE: u32 = 1074012931;
pub const WDIOC_SETTIMEOUT: u32 = 3221509894;
pub const IOC_WATCH_QUEUE_SET_FILTER: u32 = 22369;
pub const CAPI_GET_MANUFACTURER: u32 = 3221504774;
pub const VFIO_IOMMU_SPAPR_UNREGISTER_MEMORY: u32 = 15222;
pub const ASPEED_P2A_CTRL_IOCTL_SET_WINDOW: u32 = 1074836224;
pub const VIDIOC_G_EDID: u32 = 3223868968;
pub const F2FS_IOC_GARBAGE_COLLECT_RANGE: u32 = 1075377419;
pub const RIO_MAP_INBOUND: u32 = 3223874833;
pub const IOC_OPAL_TAKE_OWNERSHIP: u32 = 1091072222;
pub const USBDEVFS_CLAIM_PORT: u32 = 2147767576;
pub const VIDIOC_S_AUDIO: u32 = 1077171746;
pub const FS_IOC_GET_ENCRYPTION_NONCE: u32 = 2148558363;
pub const FW_CDEV_IOC_SEND_STREAM_PACKET: u32 = 1076372243;
pub const BTRFS_IOC_SNAP_DESTROY: u32 = 1342215183;
pub const SNAPSHOT_FREE: u32 = 13061;
pub const I8K_GET_SPEED: u32 = 3221776773;
pub const HIDIOCGREPORT: u32 = 1074546695;
pub const HPET_EPI: u32 = 26628;
pub const JSIOCSCORR: u32 = 1076128289;
pub const IOC_PR_PREEMPT_ABORT: u32 = 1075343564;
pub const RIO_MAP_OUTBOUND: u32 = 3223874831;
pub const ATM_SETESI: u32 = 1074815372;
pub const FW_CDEV_IOC_START_ISO: u32 = 1074799370;
pub const ATM_DELADDR: u32 = 1074815369;
pub const PPFCONTROL: u32 = 1073901710;
pub const SONYPI_IOCGFAN: u32 = 2147579402;
pub const RTC_IRQP_SET: u32 = 1074294796;
pub const PCITEST_WRITE: u32 = 1074286596;
pub const PPCLAIM: u32 = 28811;
pub const VIDIOC_S_JPEGCOMP: u32 = 1082938942;
pub const IPMICTL_UNREGISTER_FOR_CMD: u32 = 2147641615;
pub const VHOST_SET_FEATURES: u32 = 1074310912;
pub const TOSHIBA_ACPI_SCI: u32 = 3222828177;
pub const VIDIOC_DQBUF: u32 = 3227014673;
pub const BTRFS_IOC_BALANCE_PROGRESS: u32 = 2214630434;
pub const BTRFS_IOC_SUBVOL_SETFLAGS: u32 = 1074304026;
pub const ATMLEC_MCAST: u32 = 25042;
pub const MMTIMER_GETFREQ: u32 = 2148035842;
pub const VIDIOC_G_SELECTION: u32 = 3225441886;
pub const RTC_ALM_SET: u32 = 1076129799;
pub const PPPOEIOCSFWD: u32 = 1074311424;
pub const IPMICTL_GET_MAINTENANCE_MODE_CMD: u32 = 2147772702;
pub const FS_IOC_ENABLE_VERITY: u32 = 1082156677;
pub const NILFS_IOCTL_GET_BDESCS: u32 = 3222826631;
pub const FDFMTEND: u32 = 585;
pub const DMA_BUF_SET_NAME: u32 = 1074291201;
pub const UI_BEGIN_FF_UPLOAD: u32 = 3228063176;
pub const RTC_UIE_ON: u32 = 28675;
pub const PPRELEASE: u32 = 28812;
pub const VFIO_IOMMU_UNMAP_DMA: u32 = 15218;
pub const VIDIOC_OMAP3ISP_PRV_CFG: u32 = 3228587714;
pub const GPIO_GET_LINEHANDLE_IOCTL: u32 = 3245126659;
pub const VFAT_IOCTL_READDIR_BOTH: u32 = 2184212993;
pub const NVME_IOCTL_ADMIN_CMD: u32 = 3225964097;
pub const VHOST_SET_VRING_KICK: u32 = 1074310944;
pub const BTRFS_IOC_SUBVOL_CREATE_V2: u32 = 1342215192;
pub const BTRFS_IOC_SNAP_CREATE: u32 = 1342215169;
pub const SONYPI_IOCGBAT2CAP: u32 = 2147644932;
pub const PPNEGOT: u32 = 1074032785;
pub const NBD_PRINT_DEBUG: u32 = 43782;
pub const BTRFS_IOC_INO_LOOKUP_USER: u32 = 3489698878;
pub const BTRFS_IOC_GET_SUBVOL_ROOTREF: u32 = 3489698877;
pub const FS_IOC_REMOVE_ENCRYPTION_KEY_ALL_USERS: u32 = 3225445913;
pub const BTRFS_IOC_FS_INFO: u32 = 2214630431;
pub const VIDIOC_ENUM_FMT: u32 = 3225441794;
pub const VIDIOC_G_INPUT: u32 = 2147767846;
pub const VTPM_PROXY_IOC_NEW_DEV: u32 = 3222577408;
pub const DFL_FPGA_FME_ERR_GET_IRQ_NUM: u32 = 2147792515;
pub const ND_IOCTL_DIMM_FLAGS: u32 = 3221769731;
pub const BTRFS_IOC_QUOTA_RESCAN: u32 = 1077974060;
pub const MMTIMER_GETCOUNTER: u32 = 2148035849;
pub const MATROXFB_GET_OUTPUT_MODE: u32 = 3221778170;
pub const BTRFS_IOC_QUOTA_RESCAN_WAIT: u32 = 37934;
pub const RIO_CM_CHAN_BIND: u32 = 1074291461;
pub const HIDIOCGRDESC: u32 = 2416199682;
pub const MGSL_IOCGIF: u32 = 27915;
pub const VIDIOC_S_OUTPUT: u32 = 3221509679;
pub const HIDIOCGREPORTINFO: u32 = 3222030345;
pub const WDIOC_GETBOOTSTATUS: u32 = 2147768066;
pub const VDUSE_VQ_GET_INFO: u32 = 3224404245;
pub const ACRN_IOCTL_ASSIGN_PCIDEV: u32 = 1076142677;
pub const BLKGETDISKSEQ: u32 = 2148012672;
pub const ACRN_IOCTL_PM_GET_CPU_STATE: u32 = 3221791328;
pub const ACRN_IOCTL_DESTROY_VM: u32 = 41489;
pub const ACRN_IOCTL_SET_PTDEV_INTR: u32 = 1075094099;
pub const ACRN_IOCTL_CREATE_IOREQ_CLIENT: u32 = 41522;
pub const ACRN_IOCTL_IRQFD: u32 = 1075356273;
pub const ACRN_IOCTL_CREATE_VM: u32 = 3224412688;
pub const ACRN_IOCTL_INJECT_MSI: u32 = 1074831907;
pub const ACRN_IOCTL_ATTACH_IOREQ_CLIENT: u32 = 41523;
pub const ACRN_IOCTL_RESET_PTDEV_INTR: u32 = 1075094100;
pub const ACRN_IOCTL_NOTIFY_REQUEST_FINISH: u32 = 1074307633;
pub const ACRN_IOCTL_SET_IRQLINE: u32 = 1074307621;
pub const ACRN_IOCTL_START_VM: u32 = 41490;
pub const ACRN_IOCTL_SET_VCPU_REGS: u32 = 1093181974;
pub const ACRN_IOCTL_SET_MEMSEG: u32 = 1075880513;
pub const ACRN_IOCTL_PAUSE_VM: u32 = 41491;
pub const ACRN_IOCTL_CLEAR_VM_IOREQ: u32 = 41525;
pub const ACRN_IOCTL_UNSET_MEMSEG: u32 = 1075880514;
pub const ACRN_IOCTL_IOEVENTFD: u32 = 1075880560;
pub const ACRN_IOCTL_DEASSIGN_PCIDEV: u32 = 1076142678;
pub const ACRN_IOCTL_RESET_VM: u32 = 41493;
pub const ACRN_IOCTL_DESTROY_IOREQ_CLIENT: u32 = 41524;
pub const ACRN_IOCTL_VM_INTR_MONITOR: u32 = 1074307620;
