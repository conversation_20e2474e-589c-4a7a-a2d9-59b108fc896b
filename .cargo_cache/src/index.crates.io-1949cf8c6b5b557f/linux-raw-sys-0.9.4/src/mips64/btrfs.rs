/* automatically generated by rust-bindgen 0.71.1 */

pub type __s8 = crate::ctypes::c_schar;
pub type __u8 = crate::ctypes::c_uchar;
pub type __s16 = crate::ctypes::c_short;
pub type __u16 = crate::ctypes::c_ushort;
pub type __s32 = crate::ctypes::c_int;
pub type __u32 = crate::ctypes::c_uint;
pub type __s64 = crate::ctypes::c_long;
pub type __u64 = crate::ctypes::c_ulong;
pub type __kernel_key_t = crate::ctypes::c_int;
pub type __kernel_mqd_t = crate::ctypes::c_int;
pub type __kernel_daddr_t = crate::ctypes::c_long;
pub type __kernel_long_t = crate::ctypes::c_long;
pub type __kernel_ulong_t = crate::ctypes::c_ulong;
pub type __kernel_ino_t = __kernel_ulong_t;
pub type __kernel_mode_t = crate::ctypes::c_uint;
pub type __kernel_pid_t = crate::ctypes::c_int;
pub type __kernel_ipc_pid_t = crate::ctypes::c_int;
pub type __kernel_uid_t = crate::ctypes::c_uint;
pub type __kernel_gid_t = crate::ctypes::c_uint;
pub type __kernel_suseconds_t = __kernel_long_t;
pub type __kernel_uid32_t = crate::ctypes::c_uint;
pub type __kernel_gid32_t = crate::ctypes::c_uint;
pub type __kernel_old_uid_t = __kernel_uid_t;
pub type __kernel_old_gid_t = __kernel_gid_t;
pub type __kernel_old_dev_t = crate::ctypes::c_uint;
pub type __kernel_size_t = __kernel_ulong_t;
pub type __kernel_ssize_t = __kernel_long_t;
pub type __kernel_ptrdiff_t = __kernel_long_t;
pub type __kernel_off_t = __kernel_long_t;
pub type __kernel_loff_t = crate::ctypes::c_longlong;
pub type __kernel_old_time_t = __kernel_long_t;
pub type __kernel_time_t = __kernel_long_t;
pub type __kernel_time64_t = crate::ctypes::c_longlong;
pub type __kernel_clock_t = __kernel_long_t;
pub type __kernel_timer_t = crate::ctypes::c_int;
pub type __kernel_clockid_t = crate::ctypes::c_int;
pub type __kernel_caddr_t = *mut crate::ctypes::c_char;
pub type __kernel_uid16_t = crate::ctypes::c_ushort;
pub type __kernel_gid16_t = crate::ctypes::c_ushort;
pub type __s128 = i128;
pub type __u128 = u128;
pub type __le16 = __u16;
pub type __be16 = __u16;
pub type __le32 = __u32;
pub type __be32 = __u32;
pub type __le64 = __u64;
pub type __be64 = __u64;
pub type __sum16 = __u16;
pub type __wsum = __u32;
pub type __poll_t = crate::ctypes::c_uint;
pub type __kernel_rwf_t = crate::ctypes::c_int;
#[repr(C)]
#[derive(Default)]
pub struct __IncompleteArrayField<T>(::core::marker::PhantomData<T>, [T; 0]);
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fscrypt_policy_v1 {
pub version: __u8,
pub contents_encryption_mode: __u8,
pub filenames_encryption_mode: __u8,
pub flags: __u8,
pub master_key_descriptor: [__u8; 8usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fscrypt_key {
pub mode: __u32,
pub raw: [__u8; 64usize],
pub size: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fscrypt_policy_v2 {
pub version: __u8,
pub contents_encryption_mode: __u8,
pub filenames_encryption_mode: __u8,
pub flags: __u8,
pub log2_data_unit_size: __u8,
pub __reserved: [__u8; 3usize],
pub master_key_identifier: [__u8; 16usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct fscrypt_get_policy_ex_arg {
pub policy_size: __u64,
pub policy: fscrypt_get_policy_ex_arg__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct fscrypt_key_specifier {
pub type_: __u32,
pub __reserved: __u32,
pub u: fscrypt_key_specifier__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug)]
pub struct fscrypt_provisioning_key_payload {
pub type_: __u32,
pub __reserved: __u32,
pub raw: __IncompleteArrayField<__u8>,
}
#[repr(C)]
pub struct fscrypt_add_key_arg {
pub key_spec: fscrypt_key_specifier,
pub raw_size: __u32,
pub key_id: __u32,
pub __reserved: [__u32; 8usize],
pub raw: __IncompleteArrayField<__u8>,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct fscrypt_remove_key_arg {
pub key_spec: fscrypt_key_specifier,
pub removal_status_flags: __u32,
pub __reserved: [__u32; 5usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct fscrypt_get_key_status_arg {
pub key_spec: fscrypt_key_specifier,
pub __reserved: [__u32; 6usize],
pub status: __u32,
pub status_flags: __u32,
pub user_count: __u32,
pub __out_reserved: [__u32; 13usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mount_attr {
pub attr_set: __u64,
pub attr_clr: __u64,
pub propagation: __u64,
pub userns_fd: __u64,
}
#[repr(C)]
#[derive(Debug)]
pub struct statmount {
pub size: __u32,
pub mnt_opts: __u32,
pub mask: __u64,
pub sb_dev_major: __u32,
pub sb_dev_minor: __u32,
pub sb_magic: __u64,
pub sb_flags: __u32,
pub fs_type: __u32,
pub mnt_id: __u64,
pub mnt_parent_id: __u64,
pub mnt_id_old: __u32,
pub mnt_parent_id_old: __u32,
pub mnt_attr: __u64,
pub mnt_propagation: __u64,
pub mnt_peer_group: __u64,
pub mnt_master: __u64,
pub propagate_from: __u64,
pub mnt_root: __u32,
pub mnt_point: __u32,
pub mnt_ns_id: __u64,
pub fs_subtype: __u32,
pub sb_source: __u32,
pub opt_num: __u32,
pub opt_array: __u32,
pub opt_sec_num: __u32,
pub opt_sec_array: __u32,
pub __spare2: [__u64; 46usize],
pub str_: __IncompleteArrayField<crate::ctypes::c_char>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mnt_id_req {
pub size: __u32,
pub spare: __u32,
pub mnt_id: __u64,
pub param: __u64,
pub mnt_ns_id: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct file_clone_range {
pub src_fd: __s64,
pub src_offset: __u64,
pub src_length: __u64,
pub dest_offset: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fstrim_range {
pub start: __u64,
pub len: __u64,
pub minlen: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fsuuid2 {
pub len: __u8,
pub uuid: [__u8; 16usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fs_sysfs_path {
pub len: __u8,
pub name: [__u8; 128usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct file_dedupe_range_info {
pub dest_fd: __s64,
pub dest_offset: __u64,
pub bytes_deduped: __u64,
pub status: __s32,
pub reserved: __u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct file_dedupe_range {
pub src_offset: __u64,
pub src_length: __u64,
pub dest_count: __u16,
pub reserved1: __u16,
pub reserved2: __u32,
pub info: __IncompleteArrayField<file_dedupe_range_info>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct files_stat_struct {
pub nr_files: crate::ctypes::c_ulong,
pub nr_free_files: crate::ctypes::c_ulong,
pub max_files: crate::ctypes::c_ulong,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct inodes_stat_t {
pub nr_inodes: crate::ctypes::c_long,
pub nr_unused: crate::ctypes::c_long,
pub dummy: [crate::ctypes::c_long; 5usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fsxattr {
pub fsx_xflags: __u32,
pub fsx_extsize: __u32,
pub fsx_nextents: __u32,
pub fsx_projid: __u32,
pub fsx_cowextsize: __u32,
pub fsx_pad: [crate::ctypes::c_uchar; 8usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct page_region {
pub start: __u64,
pub end: __u64,
pub categories: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct pm_scan_arg {
pub size: __u64,
pub flags: __u64,
pub start: __u64,
pub end: __u64,
pub walk_end: __u64,
pub vec: __u64,
pub vec_len: __u64,
pub max_pages: __u64,
pub category_inverted: __u64,
pub category_mask: __u64,
pub category_anyof_mask: __u64,
pub return_mask: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct procmap_query {
pub size: __u64,
pub query_flags: __u64,
pub query_addr: __u64,
pub vma_start: __u64,
pub vma_end: __u64,
pub vma_flags: __u64,
pub vma_page_size: __u64,
pub vma_offset: __u64,
pub inode: __u64,
pub dev_major: __u32,
pub dev_minor: __u32,
pub vma_name_size: __u32,
pub build_id_size: __u32,
pub vma_name_addr: __u64,
pub build_id_addr: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_vol_args {
pub fd: __s64,
pub name: [crate::ctypes::c_char; 4088usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_qgroup_limit {
pub flags: __u64,
pub max_rfer: __u64,
pub max_excl: __u64,
pub rsv_rfer: __u64,
pub rsv_excl: __u64,
}
#[repr(C)]
#[derive(Debug)]
pub struct btrfs_qgroup_inherit {
pub flags: __u64,
pub num_qgroups: __u64,
pub num_ref_copies: __u64,
pub num_excl_copies: __u64,
pub lim: btrfs_qgroup_limit,
pub qgroups: __IncompleteArrayField<__u64>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_qgroup_limit_args {
pub qgroupid: __u64,
pub lim: btrfs_qgroup_limit,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct btrfs_ioctl_vol_args_v2 {
pub fd: __s64,
pub transid: __u64,
pub flags: __u64,
pub __bindgen_anon_1: btrfs_ioctl_vol_args_v2__bindgen_ty_1,
pub __bindgen_anon_2: btrfs_ioctl_vol_args_v2__bindgen_ty_2,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_vol_args_v2__bindgen_ty_1__bindgen_ty_1 {
pub size: __u64,
pub qgroup_inherit: *mut btrfs_qgroup_inherit,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_scrub_progress {
pub data_extents_scrubbed: __u64,
pub tree_extents_scrubbed: __u64,
pub data_bytes_scrubbed: __u64,
pub tree_bytes_scrubbed: __u64,
pub read_errors: __u64,
pub csum_errors: __u64,
pub verify_errors: __u64,
pub no_csum: __u64,
pub csum_discards: __u64,
pub super_errors: __u64,
pub malloc_errors: __u64,
pub uncorrectable_errors: __u64,
pub corrected_errors: __u64,
pub last_physical: __u64,
pub unverified_errors: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_scrub_args {
pub devid: __u64,
pub start: __u64,
pub end: __u64,
pub flags: __u64,
pub progress: btrfs_scrub_progress,
pub unused: [__u64; 109usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_dev_replace_start_params {
pub srcdevid: __u64,
pub cont_reading_from_srcdev_mode: __u64,
pub srcdev_name: [__u8; 1025usize],
pub tgtdev_name: [__u8; 1025usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_dev_replace_status_params {
pub replace_state: __u64,
pub progress_1000: __u64,
pub time_started: __u64,
pub time_stopped: __u64,
pub num_write_errors: __u64,
pub num_uncorrectable_read_errors: __u64,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct btrfs_ioctl_dev_replace_args {
pub cmd: __u64,
pub result: __u64,
pub __bindgen_anon_1: btrfs_ioctl_dev_replace_args__bindgen_ty_1,
pub spare: [__u64; 64usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_dev_info_args {
pub devid: __u64,
pub uuid: [__u8; 16usize],
pub bytes_used: __u64,
pub total_bytes: __u64,
pub fsid: [__u8; 16usize],
pub unused: [__u64; 377usize],
pub path: [__u8; 1024usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_fs_info_args {
pub max_id: __u64,
pub num_devices: __u64,
pub fsid: [__u8; 16usize],
pub nodesize: __u32,
pub sectorsize: __u32,
pub clone_alignment: __u32,
pub csum_type: __u16,
pub csum_size: __u16,
pub flags: __u64,
pub generation: __u64,
pub metadata_uuid: [__u8; 16usize],
pub reserved: [__u8; 944usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_feature_flags {
pub compat_flags: __u64,
pub compat_ro_flags: __u64,
pub incompat_flags: __u64,
}
#[repr(C, packed)]
#[derive(Copy, Clone)]
pub struct btrfs_balance_args {
pub profiles: __u64,
pub __bindgen_anon_1: btrfs_balance_args__bindgen_ty_1,
pub devid: __u64,
pub pstart: __u64,
pub pend: __u64,
pub vstart: __u64,
pub vend: __u64,
pub target: __u64,
pub flags: __u64,
pub __bindgen_anon_2: btrfs_balance_args__bindgen_ty_2,
pub stripes_min: __u32,
pub stripes_max: __u32,
pub unused: [__u64; 6usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_balance_args__bindgen_ty_1__bindgen_ty_1 {
pub usage_min: __u32,
pub usage_max: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_balance_args__bindgen_ty_2__bindgen_ty_1 {
pub limit_min: __u32,
pub limit_max: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_balance_progress {
pub expected: __u64,
pub considered: __u64,
pub completed: __u64,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct btrfs_ioctl_balance_args {
pub flags: __u64,
pub state: __u64,
pub data: btrfs_balance_args,
pub meta: btrfs_balance_args,
pub sys: btrfs_balance_args,
pub stat: btrfs_balance_progress,
pub unused: [__u64; 72usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_ino_lookup_args {
pub treeid: __u64,
pub objectid: __u64,
pub name: [crate::ctypes::c_char; 4080usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_ino_lookup_user_args {
pub dirid: __u64,
pub treeid: __u64,
pub name: [crate::ctypes::c_char; 256usize],
pub path: [crate::ctypes::c_char; 3824usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_search_key {
pub tree_id: __u64,
pub min_objectid: __u64,
pub max_objectid: __u64,
pub min_offset: __u64,
pub max_offset: __u64,
pub min_transid: __u64,
pub max_transid: __u64,
pub min_type: __u32,
pub max_type: __u32,
pub nr_items: __u32,
pub unused: __u32,
pub unused1: __u64,
pub unused2: __u64,
pub unused3: __u64,
pub unused4: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_search_header {
pub transid: __u64,
pub objectid: __u64,
pub offset: __u64,
pub type_: __u32,
pub len: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_search_args {
pub key: btrfs_ioctl_search_key,
pub buf: [crate::ctypes::c_char; 3992usize],
}
#[repr(C)]
#[derive(Debug)]
pub struct btrfs_ioctl_search_args_v2 {
pub key: btrfs_ioctl_search_key,
pub buf_size: __u64,
pub buf: __IncompleteArrayField<__u64>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_clone_range_args {
pub src_fd: __s64,
pub src_offset: __u64,
pub src_length: __u64,
pub dest_offset: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_defrag_range_args {
pub start: __u64,
pub len: __u64,
pub flags: __u64,
pub extent_thresh: __u32,
pub compress_type: __u32,
pub unused: [__u32; 4usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_same_extent_info {
pub fd: __s64,
pub logical_offset: __u64,
pub bytes_deduped: __u64,
pub status: __s32,
pub reserved: __u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct btrfs_ioctl_same_args {
pub logical_offset: __u64,
pub length: __u64,
pub dest_count: __u16,
pub reserved1: __u16,
pub reserved2: __u32,
pub info: __IncompleteArrayField<btrfs_ioctl_same_extent_info>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_space_info {
pub flags: __u64,
pub total_bytes: __u64,
pub used_bytes: __u64,
}
#[repr(C)]
#[derive(Debug)]
pub struct btrfs_ioctl_space_args {
pub space_slots: __u64,
pub total_spaces: __u64,
pub spaces: __IncompleteArrayField<btrfs_ioctl_space_info>,
}
#[repr(C)]
#[derive(Debug)]
pub struct btrfs_data_container {
pub bytes_left: __u32,
pub bytes_missing: __u32,
pub elem_cnt: __u32,
pub elem_missed: __u32,
pub val: __IncompleteArrayField<__u64>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_ino_path_args {
pub inum: __u64,
pub size: __u64,
pub reserved: [__u64; 4usize],
pub fspath: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_logical_ino_args {
pub logical: __u64,
pub size: __u64,
pub reserved: [__u64; 3usize],
pub flags: __u64,
pub inodes: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_get_dev_stats {
pub devid: __u64,
pub nr_items: __u64,
pub flags: __u64,
pub values: [__u64; 5usize],
pub unused: [__u64; 121usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_quota_ctl_args {
pub cmd: __u64,
pub status: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_quota_rescan_args {
pub flags: __u64,
pub progress: __u64,
pub reserved: [__u64; 6usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_qgroup_assign_args {
pub assign: __u64,
pub src: __u64,
pub dst: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_qgroup_create_args {
pub create: __u64,
pub qgroupid: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_timespec {
pub sec: __u64,
pub nsec: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_received_subvol_args {
pub uuid: [crate::ctypes::c_char; 16usize],
pub stransid: __u64,
pub rtransid: __u64,
pub stime: btrfs_ioctl_timespec,
pub rtime: btrfs_ioctl_timespec,
pub flags: __u64,
pub reserved: [__u64; 16usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_send_args {
pub send_fd: __s64,
pub clone_sources_count: __u64,
pub clone_sources: *mut __u64,
pub parent_root: __u64,
pub flags: __u64,
pub version: __u32,
pub reserved: [__u8; 28usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_get_subvol_info_args {
pub treeid: __u64,
pub name: [crate::ctypes::c_char; 256usize],
pub parent_id: __u64,
pub dirid: __u64,
pub generation: __u64,
pub flags: __u64,
pub uuid: [__u8; 16usize],
pub parent_uuid: [__u8; 16usize],
pub received_uuid: [__u8; 16usize],
pub ctransid: __u64,
pub otransid: __u64,
pub stransid: __u64,
pub rtransid: __u64,
pub ctime: btrfs_ioctl_timespec,
pub otime: btrfs_ioctl_timespec,
pub stime: btrfs_ioctl_timespec,
pub rtime: btrfs_ioctl_timespec,
pub reserved: [__u64; 8usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_get_subvol_rootref_args {
pub min_treeid: __u64,
pub rootref: [btrfs_ioctl_get_subvol_rootref_args__bindgen_ty_1; 255usize],
pub num_items: __u8,
pub align: [__u8; 7usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_get_subvol_rootref_args__bindgen_ty_1 {
pub treeid: __u64,
pub dirid: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_encoded_io_args {
pub iov: *mut iovec,
pub iovcnt: crate::ctypes::c_ulong,
pub offset: __s64,
pub flags: __u64,
pub len: __u64,
pub unencoded_len: __u64,
pub unencoded_offset: __u64,
pub compression: __u32,
pub encryption: __u32,
pub reserved: [__u8; 64usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_ioctl_subvol_wait {
pub subvolid: __u64,
pub mode: __u32,
pub count: __u32,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_disk_key {
pub objectid: __le64,
pub type_: __u8,
pub offset: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_key {
pub objectid: __u64,
pub type_: __u8,
pub offset: __u64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_header {
pub csum: [__u8; 32usize],
pub fsid: [__u8; 16usize],
pub bytenr: __le64,
pub flags: __le64,
pub chunk_tree_uuid: [__u8; 16usize],
pub generation: __le64,
pub owner: __le64,
pub nritems: __le32,
pub level: __u8,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_root_backup {
pub tree_root: __le64,
pub tree_root_gen: __le64,
pub chunk_root: __le64,
pub chunk_root_gen: __le64,
pub extent_root: __le64,
pub extent_root_gen: __le64,
pub fs_root: __le64,
pub fs_root_gen: __le64,
pub dev_root: __le64,
pub dev_root_gen: __le64,
pub csum_root: __le64,
pub csum_root_gen: __le64,
pub total_bytes: __le64,
pub bytes_used: __le64,
pub num_devices: __le64,
pub unused_64: [__le64; 4usize],
pub tree_root_level: __u8,
pub chunk_root_level: __u8,
pub extent_root_level: __u8,
pub fs_root_level: __u8,
pub dev_root_level: __u8,
pub csum_root_level: __u8,
pub unused_8: [__u8; 10usize],
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_item {
pub key: btrfs_disk_key,
pub offset: __le32,
pub size: __le32,
}
#[repr(C, packed)]
pub struct btrfs_leaf {
pub header: btrfs_header,
pub items: __IncompleteArrayField<btrfs_item>,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_key_ptr {
pub key: btrfs_disk_key,
pub blockptr: __le64,
pub generation: __le64,
}
#[repr(C, packed)]
pub struct btrfs_node {
pub header: btrfs_header,
pub ptrs: __IncompleteArrayField<btrfs_key_ptr>,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_dev_item {
pub devid: __le64,
pub total_bytes: __le64,
pub bytes_used: __le64,
pub io_align: __le32,
pub io_width: __le32,
pub sector_size: __le32,
pub type_: __le64,
pub generation: __le64,
pub start_offset: __le64,
pub dev_group: __le32,
pub seek_speed: __u8,
pub bandwidth: __u8,
pub uuid: [__u8; 16usize],
pub fsid: [__u8; 16usize],
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_stripe {
pub devid: __le64,
pub offset: __le64,
pub dev_uuid: [__u8; 16usize],
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_chunk {
pub length: __le64,
pub owner: __le64,
pub stripe_len: __le64,
pub type_: __le64,
pub io_align: __le32,
pub io_width: __le32,
pub sector_size: __le32,
pub num_stripes: __le16,
pub sub_stripes: __le16,
pub stripe: btrfs_stripe,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_super_block {
pub csum: [__u8; 32usize],
pub fsid: [__u8; 16usize],
pub bytenr: __le64,
pub flags: __le64,
pub magic: __le64,
pub generation: __le64,
pub root: __le64,
pub chunk_root: __le64,
pub log_root: __le64,
pub __unused_log_root_transid: __le64,
pub total_bytes: __le64,
pub bytes_used: __le64,
pub root_dir_objectid: __le64,
pub num_devices: __le64,
pub sectorsize: __le32,
pub nodesize: __le32,
pub __unused_leafsize: __le32,
pub stripesize: __le32,
pub sys_chunk_array_size: __le32,
pub chunk_root_generation: __le64,
pub compat_flags: __le64,
pub compat_ro_flags: __le64,
pub incompat_flags: __le64,
pub csum_type: __le16,
pub root_level: __u8,
pub chunk_root_level: __u8,
pub log_root_level: __u8,
pub dev_item: btrfs_dev_item,
pub label: [crate::ctypes::c_char; 256usize],
pub cache_generation: __le64,
pub uuid_tree_generation: __le64,
pub metadata_uuid: [__u8; 16usize],
pub nr_global_roots: __u64,
pub reserved: [__le64; 27usize],
pub sys_chunk_array: [__u8; 2048usize],
pub super_roots: [btrfs_root_backup; 4usize],
pub padding: [__u8; 565usize],
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_free_space_entry {
pub offset: __le64,
pub bytes: __le64,
pub type_: __u8,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_free_space_header {
pub location: btrfs_disk_key,
pub generation: __le64,
pub num_entries: __le64,
pub num_bitmaps: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_raid_stride {
pub devid: __le64,
pub physical: __le64,
}
#[repr(C, packed)]
pub struct btrfs_stripe_extent {
pub __bindgen_anon_1: btrfs_stripe_extent__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug)]
pub struct btrfs_stripe_extent__bindgen_ty_1 {
pub __empty_strides: btrfs_stripe_extent__bindgen_ty_1__bindgen_ty_1,
pub strides: __IncompleteArrayField<btrfs_raid_stride>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_stripe_extent__bindgen_ty_1__bindgen_ty_1 {}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_extent_item {
pub refs: __le64,
pub generation: __le64,
pub flags: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_extent_item_v0 {
pub refs: __le32,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_tree_block_info {
pub key: btrfs_disk_key,
pub level: __u8,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_extent_data_ref {
pub root: __le64,
pub objectid: __le64,
pub offset: __le64,
pub count: __le32,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_shared_data_ref {
pub count: __le32,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_extent_owner_ref {
pub root_id: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_extent_inline_ref {
pub type_: __u8,
pub offset: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_dev_extent {
pub chunk_tree: __le64,
pub chunk_objectid: __le64,
pub chunk_offset: __le64,
pub length: __le64,
pub chunk_tree_uuid: [__u8; 16usize],
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_inode_ref {
pub index: __le64,
pub name_len: __le16,
}
#[repr(C, packed)]
pub struct btrfs_inode_extref {
pub parent_objectid: __le64,
pub index: __le64,
pub name_len: __le16,
pub name: __IncompleteArrayField<__u8>,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_timespec {
pub sec: __le64,
pub nsec: __le32,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_inode_item {
pub generation: __le64,
pub transid: __le64,
pub size: __le64,
pub nbytes: __le64,
pub block_group: __le64,
pub nlink: __le32,
pub uid: __le32,
pub gid: __le32,
pub mode: __le32,
pub rdev: __le64,
pub flags: __le64,
pub sequence: __le64,
pub reserved: [__le64; 4usize],
pub atime: btrfs_timespec,
pub ctime: btrfs_timespec,
pub mtime: btrfs_timespec,
pub otime: btrfs_timespec,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_dir_log_item {
pub end: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_dir_item {
pub location: btrfs_disk_key,
pub transid: __le64,
pub data_len: __le16,
pub name_len: __le16,
pub type_: __u8,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_root_item {
pub inode: btrfs_inode_item,
pub generation: __le64,
pub root_dirid: __le64,
pub bytenr: __le64,
pub byte_limit: __le64,
pub bytes_used: __le64,
pub last_snapshot: __le64,
pub flags: __le64,
pub refs: __le32,
pub drop_progress: btrfs_disk_key,
pub drop_level: __u8,
pub level: __u8,
pub generation_v2: __le64,
pub uuid: [__u8; 16usize],
pub parent_uuid: [__u8; 16usize],
pub received_uuid: [__u8; 16usize],
pub ctransid: __le64,
pub otransid: __le64,
pub stransid: __le64,
pub rtransid: __le64,
pub ctime: btrfs_timespec,
pub otime: btrfs_timespec,
pub stime: btrfs_timespec,
pub rtime: btrfs_timespec,
pub reserved: [__le64; 8usize],
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_root_ref {
pub dirid: __le64,
pub sequence: __le64,
pub name_len: __le16,
}
#[repr(C, packed)]
#[derive(Copy, Clone)]
pub struct btrfs_disk_balance_args {
pub profiles: __le64,
pub __bindgen_anon_1: btrfs_disk_balance_args__bindgen_ty_1,
pub devid: __le64,
pub pstart: __le64,
pub pend: __le64,
pub vstart: __le64,
pub vend: __le64,
pub target: __le64,
pub flags: __le64,
pub __bindgen_anon_2: btrfs_disk_balance_args__bindgen_ty_2,
pub stripes_min: __le32,
pub stripes_max: __le32,
pub unused: [__le64; 6usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_disk_balance_args__bindgen_ty_1__bindgen_ty_1 {
pub usage_min: __le32,
pub usage_max: __le32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_disk_balance_args__bindgen_ty_2__bindgen_ty_1 {
pub limit_min: __le32,
pub limit_max: __le32,
}
#[repr(C, packed)]
#[derive(Copy, Clone)]
pub struct btrfs_balance_item {
pub flags: __le64,
pub data: btrfs_disk_balance_args,
pub meta: btrfs_disk_balance_args,
pub sys: btrfs_disk_balance_args,
pub unused: [__le64; 4usize],
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_file_extent_item {
pub generation: __le64,
pub ram_bytes: __le64,
pub compression: __u8,
pub encryption: __u8,
pub other_encoding: __le16,
pub type_: __u8,
pub disk_bytenr: __le64,
pub disk_num_bytes: __le64,
pub offset: __le64,
pub num_bytes: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_csum_item {
pub csum: __u8,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_dev_stats_item {
pub values: [__le64; 5usize],
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_dev_replace_item {
pub src_devid: __le64,
pub cursor_left: __le64,
pub cursor_right: __le64,
pub cont_reading_from_srcdev_mode: __le64,
pub replace_state: __le64,
pub time_started: __le64,
pub time_stopped: __le64,
pub num_write_errors: __le64,
pub num_uncorrectable_read_errors: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_block_group_item {
pub used: __le64,
pub chunk_objectid: __le64,
pub flags: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_free_space_info {
pub extent_count: __le32,
pub flags: __le32,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_qgroup_status_item {
pub version: __le64,
pub generation: __le64,
pub flags: __le64,
pub rescan: __le64,
pub enable_gen: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_qgroup_info_item {
pub generation: __le64,
pub rfer: __le64,
pub rfer_cmpr: __le64,
pub excl: __le64,
pub excl_cmpr: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_qgroup_limit_item {
pub flags: __le64,
pub max_rfer: __le64,
pub max_excl: __le64,
pub rsv_rfer: __le64,
pub rsv_excl: __le64,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct btrfs_verity_descriptor_item {
pub size: __le64,
pub reserved: [__le64; 2usize],
pub encryption: __u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct iovec {
pub _address: u8,
}
pub const __BITS_PER_LONG_LONG: u32 = 64;
pub const _MIPS_ISA_MIPS1: u32 = 1;
pub const _MIPS_ISA_MIPS2: u32 = 2;
pub const _MIPS_ISA_MIPS3: u32 = 3;
pub const _MIPS_ISA_MIPS4: u32 = 4;
pub const _MIPS_ISA_MIPS5: u32 = 5;
pub const _MIPS_ISA_MIPS32: u32 = 6;
pub const _MIPS_ISA_MIPS64: u32 = 7;
pub const _MIPS_SIM_ABI32: u32 = 1;
pub const _MIPS_SIM_NABI32: u32 = 2;
pub const _MIPS_SIM_ABI64: u32 = 3;
pub const _IOC_SIZEBITS: u32 = 13;
pub const _IOC_DIRBITS: u32 = 3;
pub const _IOC_NONE: u32 = 1;
pub const _IOC_READ: u32 = 2;
pub const _IOC_WRITE: u32 = 4;
pub const _IOC_NRBITS: u32 = 8;
pub const _IOC_TYPEBITS: u32 = 8;
pub const _IOC_NRMASK: u32 = 255;
pub const _IOC_TYPEMASK: u32 = 255;
pub const _IOC_SIZEMASK: u32 = 8191;
pub const _IOC_DIRMASK: u32 = 7;
pub const _IOC_NRSHIFT: u32 = 0;
pub const _IOC_TYPESHIFT: u32 = 8;
pub const _IOC_SIZESHIFT: u32 = 16;
pub const _IOC_DIRSHIFT: u32 = 29;
pub const IOC_IN: u32 = 2147483648;
pub const IOC_OUT: u32 = 1073741824;
pub const IOC_INOUT: u32 = 3221225472;
pub const IOCSIZE_MASK: u32 = 536805376;
pub const IOCSIZE_SHIFT: u32 = 16;
pub const NR_OPEN: u32 = 1024;
pub const NGROUPS_MAX: u32 = 65536;
pub const ARG_MAX: u32 = 131072;
pub const LINK_MAX: u32 = 127;
pub const MAX_CANON: u32 = 255;
pub const MAX_INPUT: u32 = 255;
pub const NAME_MAX: u32 = 255;
pub const PATH_MAX: u32 = 4096;
pub const PIPE_BUF: u32 = 4096;
pub const XATTR_NAME_MAX: u32 = 255;
pub const XATTR_SIZE_MAX: u32 = 65536;
pub const XATTR_LIST_MAX: u32 = 65536;
pub const RTSIG_MAX: u32 = 32;
pub const FSCRYPT_POLICY_FLAGS_PAD_4: u32 = 0;
pub const FSCRYPT_POLICY_FLAGS_PAD_8: u32 = 1;
pub const FSCRYPT_POLICY_FLAGS_PAD_16: u32 = 2;
pub const FSCRYPT_POLICY_FLAGS_PAD_32: u32 = 3;
pub const FSCRYPT_POLICY_FLAGS_PAD_MASK: u32 = 3;
pub const FSCRYPT_POLICY_FLAG_DIRECT_KEY: u32 = 4;
pub const FSCRYPT_POLICY_FLAG_IV_INO_LBLK_64: u32 = 8;
pub const FSCRYPT_POLICY_FLAG_IV_INO_LBLK_32: u32 = 16;
pub const FSCRYPT_MODE_AES_256_XTS: u32 = 1;
pub const FSCRYPT_MODE_AES_256_CTS: u32 = 4;
pub const FSCRYPT_MODE_AES_128_CBC: u32 = 5;
pub const FSCRYPT_MODE_AES_128_CTS: u32 = 6;
pub const FSCRYPT_MODE_SM4_XTS: u32 = 7;
pub const FSCRYPT_MODE_SM4_CTS: u32 = 8;
pub const FSCRYPT_MODE_ADIANTUM: u32 = 9;
pub const FSCRYPT_MODE_AES_256_HCTR2: u32 = 10;
pub const FSCRYPT_POLICY_V1: u32 = 0;
pub const FSCRYPT_KEY_DESCRIPTOR_SIZE: u32 = 8;
pub const FSCRYPT_KEY_DESC_PREFIX: &[u8; 9] = b"fscrypt:\0";
pub const FSCRYPT_KEY_DESC_PREFIX_SIZE: u32 = 8;
pub const FSCRYPT_MAX_KEY_SIZE: u32 = 64;
pub const FSCRYPT_POLICY_V2: u32 = 2;
pub const FSCRYPT_KEY_IDENTIFIER_SIZE: u32 = 16;
pub const FSCRYPT_KEY_SPEC_TYPE_DESCRIPTOR: u32 = 1;
pub const FSCRYPT_KEY_SPEC_TYPE_IDENTIFIER: u32 = 2;
pub const FSCRYPT_KEY_REMOVAL_STATUS_FLAG_FILES_BUSY: u32 = 1;
pub const FSCRYPT_KEY_REMOVAL_STATUS_FLAG_OTHER_USERS: u32 = 2;
pub const FSCRYPT_KEY_STATUS_ABSENT: u32 = 1;
pub const FSCRYPT_KEY_STATUS_PRESENT: u32 = 2;
pub const FSCRYPT_KEY_STATUS_INCOMPLETELY_REMOVED: u32 = 3;
pub const FSCRYPT_KEY_STATUS_FLAG_ADDED_BY_SELF: u32 = 1;
pub const FS_KEY_DESCRIPTOR_SIZE: u32 = 8;
pub const FS_POLICY_FLAGS_PAD_4: u32 = 0;
pub const FS_POLICY_FLAGS_PAD_8: u32 = 1;
pub const FS_POLICY_FLAGS_PAD_16: u32 = 2;
pub const FS_POLICY_FLAGS_PAD_32: u32 = 3;
pub const FS_POLICY_FLAGS_PAD_MASK: u32 = 3;
pub const FS_POLICY_FLAG_DIRECT_KEY: u32 = 4;
pub const FS_POLICY_FLAGS_VALID: u32 = 7;
pub const FS_ENCRYPTION_MODE_INVALID: u32 = 0;
pub const FS_ENCRYPTION_MODE_AES_256_XTS: u32 = 1;
pub const FS_ENCRYPTION_MODE_AES_256_GCM: u32 = 2;
pub const FS_ENCRYPTION_MODE_AES_256_CBC: u32 = 3;
pub const FS_ENCRYPTION_MODE_AES_256_CTS: u32 = 4;
pub const FS_ENCRYPTION_MODE_AES_128_CBC: u32 = 5;
pub const FS_ENCRYPTION_MODE_AES_128_CTS: u32 = 6;
pub const FS_ENCRYPTION_MODE_ADIANTUM: u32 = 9;
pub const FS_KEY_DESC_PREFIX: &[u8; 9] = b"fscrypt:\0";
pub const FS_KEY_DESC_PREFIX_SIZE: u32 = 8;
pub const FS_MAX_KEY_SIZE: u32 = 64;
pub const MS_RDONLY: u32 = 1;
pub const MS_NOSUID: u32 = 2;
pub const MS_NODEV: u32 = 4;
pub const MS_NOEXEC: u32 = 8;
pub const MS_SYNCHRONOUS: u32 = 16;
pub const MS_REMOUNT: u32 = 32;
pub const MS_MANDLOCK: u32 = 64;
pub const MS_DIRSYNC: u32 = 128;
pub const MS_NOSYMFOLLOW: u32 = 256;
pub const MS_NOATIME: u32 = 1024;
pub const MS_NODIRATIME: u32 = 2048;
pub const MS_BIND: u32 = 4096;
pub const MS_MOVE: u32 = 8192;
pub const MS_REC: u32 = 16384;
pub const MS_VERBOSE: u32 = 32768;
pub const MS_SILENT: u32 = 32768;
pub const MS_POSIXACL: u32 = 65536;
pub const MS_UNBINDABLE: u32 = 131072;
pub const MS_PRIVATE: u32 = 262144;
pub const MS_SLAVE: u32 = 524288;
pub const MS_SHARED: u32 = 1048576;
pub const MS_RELATIME: u32 = 2097152;
pub const MS_KERNMOUNT: u32 = 4194304;
pub const MS_I_VERSION: u32 = 8388608;
pub const MS_STRICTATIME: u32 = 16777216;
pub const MS_LAZYTIME: u32 = 33554432;
pub const MS_SUBMOUNT: u32 = 67108864;
pub const MS_NOREMOTELOCK: u32 = 134217728;
pub const MS_NOSEC: u32 = 268435456;
pub const MS_BORN: u32 = 536870912;
pub const MS_ACTIVE: u32 = 1073741824;
pub const MS_NOUSER: u32 = 2147483648;
pub const MS_RMT_MASK: u32 = 41943121;
pub const MS_MGC_VAL: u32 = 3236757504;
pub const MS_MGC_MSK: u32 = 4294901760;
pub const OPEN_TREE_CLONE: u32 = 1;
pub const MOVE_MOUNT_F_SYMLINKS: u32 = 1;
pub const MOVE_MOUNT_F_AUTOMOUNTS: u32 = 2;
pub const MOVE_MOUNT_F_EMPTY_PATH: u32 = 4;
pub const MOVE_MOUNT_T_SYMLINKS: u32 = 16;
pub const MOVE_MOUNT_T_AUTOMOUNTS: u32 = 32;
pub const MOVE_MOUNT_T_EMPTY_PATH: u32 = 64;
pub const MOVE_MOUNT_SET_GROUP: u32 = 256;
pub const MOVE_MOUNT_BENEATH: u32 = 512;
pub const MOVE_MOUNT__MASK: u32 = 887;
pub const FSOPEN_CLOEXEC: u32 = 1;
pub const FSPICK_CLOEXEC: u32 = 1;
pub const FSPICK_SYMLINK_NOFOLLOW: u32 = 2;
pub const FSPICK_NO_AUTOMOUNT: u32 = 4;
pub const FSPICK_EMPTY_PATH: u32 = 8;
pub const FSMOUNT_CLOEXEC: u32 = 1;
pub const MOUNT_ATTR_RDONLY: u32 = 1;
pub const MOUNT_ATTR_NOSUID: u32 = 2;
pub const MOUNT_ATTR_NODEV: u32 = 4;
pub const MOUNT_ATTR_NOEXEC: u32 = 8;
pub const MOUNT_ATTR__ATIME: u32 = 112;
pub const MOUNT_ATTR_RELATIME: u32 = 0;
pub const MOUNT_ATTR_NOATIME: u32 = 16;
pub const MOUNT_ATTR_STRICTATIME: u32 = 32;
pub const MOUNT_ATTR_NODIRATIME: u32 = 128;
pub const MOUNT_ATTR_IDMAP: u32 = 1048576;
pub const MOUNT_ATTR_NOSYMFOLLOW: u32 = 2097152;
pub const MOUNT_ATTR_SIZE_VER0: u32 = 32;
pub const MNT_ID_REQ_SIZE_VER0: u32 = 24;
pub const MNT_ID_REQ_SIZE_VER1: u32 = 32;
pub const STATMOUNT_SB_BASIC: u32 = 1;
pub const STATMOUNT_MNT_BASIC: u32 = 2;
pub const STATMOUNT_PROPAGATE_FROM: u32 = 4;
pub const STATMOUNT_MNT_ROOT: u32 = 8;
pub const STATMOUNT_MNT_POINT: u32 = 16;
pub const STATMOUNT_FS_TYPE: u32 = 32;
pub const STATMOUNT_MNT_NS_ID: u32 = 64;
pub const STATMOUNT_MNT_OPTS: u32 = 128;
pub const STATMOUNT_FS_SUBTYPE: u32 = 256;
pub const STATMOUNT_SB_SOURCE: u32 = 512;
pub const STATMOUNT_OPT_ARRAY: u32 = 1024;
pub const STATMOUNT_OPT_SEC_ARRAY: u32 = 2048;
pub const LSMT_ROOT: i32 = -1;
pub const LISTMOUNT_REVERSE: u32 = 1;
pub const INR_OPEN_CUR: u32 = 1024;
pub const INR_OPEN_MAX: u32 = 4096;
pub const BLOCK_SIZE_BITS: u32 = 10;
pub const BLOCK_SIZE: u32 = 1024;
pub const SEEK_SET: u32 = 0;
pub const SEEK_CUR: u32 = 1;
pub const SEEK_END: u32 = 2;
pub const SEEK_DATA: u32 = 3;
pub const SEEK_HOLE: u32 = 4;
pub const SEEK_MAX: u32 = 4;
pub const RENAME_NOREPLACE: u32 = 1;
pub const RENAME_EXCHANGE: u32 = 2;
pub const RENAME_WHITEOUT: u32 = 4;
pub const FILE_DEDUPE_RANGE_SAME: u32 = 0;
pub const FILE_DEDUPE_RANGE_DIFFERS: u32 = 1;
pub const NR_FILE: u32 = 8192;
pub const FS_XFLAG_REALTIME: u32 = 1;
pub const FS_XFLAG_PREALLOC: u32 = 2;
pub const FS_XFLAG_IMMUTABLE: u32 = 8;
pub const FS_XFLAG_APPEND: u32 = 16;
pub const FS_XFLAG_SYNC: u32 = 32;
pub const FS_XFLAG_NOATIME: u32 = 64;
pub const FS_XFLAG_NODUMP: u32 = 128;
pub const FS_XFLAG_RTINHERIT: u32 = 256;
pub const FS_XFLAG_PROJINHERIT: u32 = 512;
pub const FS_XFLAG_NOSYMLINKS: u32 = 1024;
pub const FS_XFLAG_EXTSIZE: u32 = 2048;
pub const FS_XFLAG_EXTSZINHERIT: u32 = 4096;
pub const FS_XFLAG_NODEFRAG: u32 = 8192;
pub const FS_XFLAG_FILESTREAM: u32 = 16384;
pub const FS_XFLAG_DAX: u32 = 32768;
pub const FS_XFLAG_COWEXTSIZE: u32 = 65536;
pub const FS_XFLAG_HASATTR: u32 = 2147483648;
pub const BMAP_IOCTL: u32 = 1;
pub const FSLABEL_MAX: u32 = 256;
pub const FS_SECRM_FL: u32 = 1;
pub const FS_UNRM_FL: u32 = 2;
pub const FS_COMPR_FL: u32 = 4;
pub const FS_SYNC_FL: u32 = 8;
pub const FS_IMMUTABLE_FL: u32 = 16;
pub const FS_APPEND_FL: u32 = 32;
pub const FS_NODUMP_FL: u32 = 64;
pub const FS_NOATIME_FL: u32 = 128;
pub const FS_DIRTY_FL: u32 = 256;
pub const FS_COMPRBLK_FL: u32 = 512;
pub const FS_NOCOMP_FL: u32 = 1024;
pub const FS_ENCRYPT_FL: u32 = 2048;
pub const FS_BTREE_FL: u32 = 4096;
pub const FS_INDEX_FL: u32 = 4096;
pub const FS_IMAGIC_FL: u32 = 8192;
pub const FS_JOURNAL_DATA_FL: u32 = 16384;
pub const FS_NOTAIL_FL: u32 = 32768;
pub const FS_DIRSYNC_FL: u32 = 65536;
pub const FS_TOPDIR_FL: u32 = 131072;
pub const FS_HUGE_FILE_FL: u32 = 262144;
pub const FS_EXTENT_FL: u32 = 524288;
pub const FS_VERITY_FL: u32 = 1048576;
pub const FS_EA_INODE_FL: u32 = 2097152;
pub const FS_EOFBLOCKS_FL: u32 = 4194304;
pub const FS_NOCOW_FL: u32 = 8388608;
pub const FS_DAX_FL: u32 = 33554432;
pub const FS_INLINE_DATA_FL: u32 = 268435456;
pub const FS_PROJINHERIT_FL: u32 = 536870912;
pub const FS_CASEFOLD_FL: u32 = 1073741824;
pub const FS_RESERVED_FL: u32 = 2147483648;
pub const FS_FL_USER_VISIBLE: u32 = 253951;
pub const FS_FL_USER_MODIFIABLE: u32 = 229631;
pub const SYNC_FILE_RANGE_WAIT_BEFORE: u32 = 1;
pub const SYNC_FILE_RANGE_WRITE: u32 = 2;
pub const SYNC_FILE_RANGE_WAIT_AFTER: u32 = 4;
pub const SYNC_FILE_RANGE_WRITE_AND_WAIT: u32 = 7;
pub const PROCFS_IOCTL_MAGIC: u8 = 102u8;
pub const PAGE_IS_WPALLOWED: u32 = 1;
pub const PAGE_IS_WRITTEN: u32 = 2;
pub const PAGE_IS_FILE: u32 = 4;
pub const PAGE_IS_PRESENT: u32 = 8;
pub const PAGE_IS_SWAPPED: u32 = 16;
pub const PAGE_IS_PFNZERO: u32 = 32;
pub const PAGE_IS_HUGE: u32 = 64;
pub const PAGE_IS_SOFT_DIRTY: u32 = 128;
pub const PM_SCAN_WP_MATCHING: u32 = 1;
pub const PM_SCAN_CHECK_WPASYNC: u32 = 2;
pub const BTRFS_IOCTL_MAGIC: u32 = 148;
pub const BTRFS_VOL_NAME_MAX: u32 = 255;
pub const BTRFS_LABEL_SIZE: u32 = 256;
pub const BTRFS_PATH_NAME_MAX: u32 = 4087;
pub const BTRFS_DEVICE_PATH_NAME_MAX: u32 = 1024;
pub const BTRFS_SUBVOL_NAME_MAX: u32 = 4039;
pub const BTRFS_SUBVOL_CREATE_ASYNC: u32 = 1;
pub const BTRFS_SUBVOL_RDONLY: u32 = 2;
pub const BTRFS_SUBVOL_QGROUP_INHERIT: u32 = 4;
pub const BTRFS_DEVICE_SPEC_BY_ID: u32 = 8;
pub const BTRFS_SUBVOL_SPEC_BY_ID: u32 = 16;
pub const BTRFS_VOL_ARG_V2_FLAGS_SUPPORTED: u32 = 30;
pub const BTRFS_FSID_SIZE: u32 = 16;
pub const BTRFS_UUID_SIZE: u32 = 16;
pub const BTRFS_UUID_UNPARSED_SIZE: u32 = 37;
pub const BTRFS_QGROUP_LIMIT_MAX_RFER: u32 = 1;
pub const BTRFS_QGROUP_LIMIT_MAX_EXCL: u32 = 2;
pub const BTRFS_QGROUP_LIMIT_RSV_RFER: u32 = 4;
pub const BTRFS_QGROUP_LIMIT_RSV_EXCL: u32 = 8;
pub const BTRFS_QGROUP_LIMIT_RFER_CMPR: u32 = 16;
pub const BTRFS_QGROUP_LIMIT_EXCL_CMPR: u32 = 32;
pub const BTRFS_QGROUP_INHERIT_SET_LIMITS: u32 = 1;
pub const BTRFS_QGROUP_INHERIT_FLAGS_SUPP: u32 = 1;
pub const BTRFS_DEVICE_REMOVE_ARGS_MASK: u32 = 8;
pub const BTRFS_SUBVOL_CREATE_ARGS_MASK: u32 = 6;
pub const BTRFS_SUBVOL_DELETE_ARGS_MASK: u32 = 16;
pub const BTRFS_SCRUB_READONLY: u32 = 1;
pub const BTRFS_SCRUB_SUPPORTED_FLAGS: u32 = 1;
pub const BTRFS_IOCTL_DEV_REPLACE_CONT_READING_FROM_SRCDEV_MODE_ALWAYS: u32 = 0;
pub const BTRFS_IOCTL_DEV_REPLACE_CONT_READING_FROM_SRCDEV_MODE_AVOID: u32 = 1;
pub const BTRFS_IOCTL_DEV_REPLACE_STATE_NEVER_STARTED: u32 = 0;
pub const BTRFS_IOCTL_DEV_REPLACE_STATE_STARTED: u32 = 1;
pub const BTRFS_IOCTL_DEV_REPLACE_STATE_FINISHED: u32 = 2;
pub const BTRFS_IOCTL_DEV_REPLACE_STATE_CANCELED: u32 = 3;
pub const BTRFS_IOCTL_DEV_REPLACE_STATE_SUSPENDED: u32 = 4;
pub const BTRFS_IOCTL_DEV_REPLACE_CMD_START: u32 = 0;
pub const BTRFS_IOCTL_DEV_REPLACE_CMD_STATUS: u32 = 1;
pub const BTRFS_IOCTL_DEV_REPLACE_CMD_CANCEL: u32 = 2;
pub const BTRFS_IOCTL_DEV_REPLACE_RESULT_NO_ERROR: u32 = 0;
pub const BTRFS_IOCTL_DEV_REPLACE_RESULT_NOT_STARTED: u32 = 1;
pub const BTRFS_IOCTL_DEV_REPLACE_RESULT_ALREADY_STARTED: u32 = 2;
pub const BTRFS_IOCTL_DEV_REPLACE_RESULT_SCRUB_INPROGRESS: u32 = 3;
pub const BTRFS_FS_INFO_FLAG_CSUM_INFO: u32 = 1;
pub const BTRFS_FS_INFO_FLAG_GENERATION: u32 = 2;
pub const BTRFS_FS_INFO_FLAG_METADATA_UUID: u32 = 4;
pub const BTRFS_FEATURE_COMPAT_RO_FREE_SPACE_TREE: u32 = 1;
pub const BTRFS_FEATURE_COMPAT_RO_FREE_SPACE_TREE_VALID: u32 = 2;
pub const BTRFS_FEATURE_COMPAT_RO_VERITY: u32 = 4;
pub const BTRFS_FEATURE_COMPAT_RO_BLOCK_GROUP_TREE: u32 = 8;
pub const BTRFS_FEATURE_INCOMPAT_MIXED_BACKREF: u32 = 1;
pub const BTRFS_FEATURE_INCOMPAT_DEFAULT_SUBVOL: u32 = 2;
pub const BTRFS_FEATURE_INCOMPAT_MIXED_GROUPS: u32 = 4;
pub const BTRFS_FEATURE_INCOMPAT_COMPRESS_LZO: u32 = 8;
pub const BTRFS_FEATURE_INCOMPAT_COMPRESS_ZSTD: u32 = 16;
pub const BTRFS_FEATURE_INCOMPAT_BIG_METADATA: u32 = 32;
pub const BTRFS_FEATURE_INCOMPAT_EXTENDED_IREF: u32 = 64;
pub const BTRFS_FEATURE_INCOMPAT_RAID56: u32 = 128;
pub const BTRFS_FEATURE_INCOMPAT_SKINNY_METADATA: u32 = 256;
pub const BTRFS_FEATURE_INCOMPAT_NO_HOLES: u32 = 512;
pub const BTRFS_FEATURE_INCOMPAT_METADATA_UUID: u32 = 1024;
pub const BTRFS_FEATURE_INCOMPAT_RAID1C34: u32 = 2048;
pub const BTRFS_FEATURE_INCOMPAT_ZONED: u32 = 4096;
pub const BTRFS_FEATURE_INCOMPAT_EXTENT_TREE_V2: u32 = 8192;
pub const BTRFS_FEATURE_INCOMPAT_RAID_STRIPE_TREE: u32 = 16384;
pub const BTRFS_FEATURE_INCOMPAT_SIMPLE_QUOTA: u32 = 65536;
pub const BTRFS_BALANCE_CTL_PAUSE: u32 = 1;
pub const BTRFS_BALANCE_CTL_CANCEL: u32 = 2;
pub const BTRFS_BALANCE_DATA: u32 = 1;
pub const BTRFS_BALANCE_SYSTEM: u32 = 2;
pub const BTRFS_BALANCE_METADATA: u32 = 4;
pub const BTRFS_BALANCE_TYPE_MASK: u32 = 7;
pub const BTRFS_BALANCE_FORCE: u32 = 8;
pub const BTRFS_BALANCE_RESUME: u32 = 16;
pub const BTRFS_BALANCE_ARGS_PROFILES: u32 = 1;
pub const BTRFS_BALANCE_ARGS_USAGE: u32 = 2;
pub const BTRFS_BALANCE_ARGS_DEVID: u32 = 4;
pub const BTRFS_BALANCE_ARGS_DRANGE: u32 = 8;
pub const BTRFS_BALANCE_ARGS_VRANGE: u32 = 16;
pub const BTRFS_BALANCE_ARGS_LIMIT: u32 = 32;
pub const BTRFS_BALANCE_ARGS_LIMIT_RANGE: u32 = 64;
pub const BTRFS_BALANCE_ARGS_STRIPES_RANGE: u32 = 128;
pub const BTRFS_BALANCE_ARGS_USAGE_RANGE: u32 = 1024;
pub const BTRFS_BALANCE_ARGS_MASK: u32 = 1279;
pub const BTRFS_BALANCE_ARGS_CONVERT: u32 = 256;
pub const BTRFS_BALANCE_ARGS_SOFT: u32 = 512;
pub const BTRFS_BALANCE_STATE_RUNNING: u32 = 1;
pub const BTRFS_BALANCE_STATE_PAUSE_REQ: u32 = 2;
pub const BTRFS_BALANCE_STATE_CANCEL_REQ: u32 = 4;
pub const BTRFS_INO_LOOKUP_PATH_MAX: u32 = 4080;
pub const BTRFS_INO_LOOKUP_USER_PATH_MAX: u32 = 3824;
pub const BTRFS_DEFRAG_RANGE_COMPRESS: u32 = 1;
pub const BTRFS_DEFRAG_RANGE_START_IO: u32 = 2;
pub const BTRFS_DEFRAG_RANGE_FLAGS_SUPP: u32 = 3;
pub const BTRFS_SAME_DATA_DIFFERS: u32 = 1;
pub const BTRFS_LOGICAL_INO_ARGS_IGNORE_OFFSET: u32 = 1;
pub const BTRFS_DEV_STATS_RESET: u32 = 1;
pub const BTRFS_QUOTA_CTL_ENABLE: u32 = 1;
pub const BTRFS_QUOTA_CTL_DISABLE: u32 = 2;
pub const BTRFS_QUOTA_CTL_RESCAN__NOTUSED: u32 = 3;
pub const BTRFS_QUOTA_CTL_ENABLE_SIMPLE_QUOTA: u32 = 4;
pub const BTRFS_SEND_FLAG_NO_FILE_DATA: u32 = 1;
pub const BTRFS_SEND_FLAG_OMIT_STREAM_HEADER: u32 = 2;
pub const BTRFS_SEND_FLAG_OMIT_END_CMD: u32 = 4;
pub const BTRFS_SEND_FLAG_VERSION: u32 = 8;
pub const BTRFS_SEND_FLAG_COMPRESSED: u32 = 16;
pub const BTRFS_SEND_FLAG_MASK: u32 = 31;
pub const BTRFS_MAX_ROOTREF_BUFFER_NUM: u32 = 255;
pub const BTRFS_ENCODED_IO_COMPRESSION_NONE: u32 = 0;
pub const BTRFS_ENCODED_IO_COMPRESSION_ZLIB: u32 = 1;
pub const BTRFS_ENCODED_IO_COMPRESSION_ZSTD: u32 = 2;
pub const BTRFS_ENCODED_IO_COMPRESSION_LZO_4K: u32 = 3;
pub const BTRFS_ENCODED_IO_COMPRESSION_LZO_8K: u32 = 4;
pub const BTRFS_ENCODED_IO_COMPRESSION_LZO_16K: u32 = 5;
pub const BTRFS_ENCODED_IO_COMPRESSION_LZO_32K: u32 = 6;
pub const BTRFS_ENCODED_IO_COMPRESSION_LZO_64K: u32 = 7;
pub const BTRFS_ENCODED_IO_COMPRESSION_TYPES: u32 = 8;
pub const BTRFS_ENCODED_IO_ENCRYPTION_NONE: u32 = 0;
pub const BTRFS_ENCODED_IO_ENCRYPTION_TYPES: u32 = 1;
pub const BTRFS_SUBVOL_SYNC_WAIT_FOR_ONE: u32 = 0;
pub const BTRFS_SUBVOL_SYNC_WAIT_FOR_QUEUED: u32 = 1;
pub const BTRFS_SUBVOL_SYNC_COUNT: u32 = 2;
pub const BTRFS_SUBVOL_SYNC_PEEK_FIRST: u32 = 3;
pub const BTRFS_SUBVOL_SYNC_PEEK_LAST: u32 = 4;
pub const BTRFS_MAGIC: u64 = 5575266562640200287;
pub const BTRFS_MAX_LEVEL: u32 = 8;
pub const BTRFS_NAME_LEN: u32 = 255;
pub const BTRFS_LINK_MAX: u32 = 65535;
pub const BTRFS_ROOT_TREE_OBJECTID: u32 = 1;
pub const BTRFS_EXTENT_TREE_OBJECTID: u32 = 2;
pub const BTRFS_CHUNK_TREE_OBJECTID: u32 = 3;
pub const BTRFS_DEV_TREE_OBJECTID: u32 = 4;
pub const BTRFS_FS_TREE_OBJECTID: u32 = 5;
pub const BTRFS_ROOT_TREE_DIR_OBJECTID: u32 = 6;
pub const BTRFS_CSUM_TREE_OBJECTID: u32 = 7;
pub const BTRFS_QUOTA_TREE_OBJECTID: u32 = 8;
pub const BTRFS_UUID_TREE_OBJECTID: u32 = 9;
pub const BTRFS_FREE_SPACE_TREE_OBJECTID: u32 = 10;
pub const BTRFS_BLOCK_GROUP_TREE_OBJECTID: u32 = 11;
pub const BTRFS_RAID_STRIPE_TREE_OBJECTID: u32 = 12;
pub const BTRFS_DEV_STATS_OBJECTID: u32 = 0;
pub const BTRFS_BALANCE_OBJECTID: i32 = -4;
pub const BTRFS_ORPHAN_OBJECTID: i32 = -5;
pub const BTRFS_TREE_LOG_OBJECTID: i32 = -6;
pub const BTRFS_TREE_LOG_FIXUP_OBJECTID: i32 = -7;
pub const BTRFS_TREE_RELOC_OBJECTID: i32 = -8;
pub const BTRFS_DATA_RELOC_TREE_OBJECTID: i32 = -9;
pub const BTRFS_EXTENT_CSUM_OBJECTID: i32 = -10;
pub const BTRFS_FREE_SPACE_OBJECTID: i32 = -11;
pub const BTRFS_FREE_INO_OBJECTID: i32 = -12;
pub const BTRFS_MULTIPLE_OBJECTIDS: i32 = -255;
pub const BTRFS_FIRST_FREE_OBJECTID: u32 = 256;
pub const BTRFS_LAST_FREE_OBJECTID: i32 = -256;
pub const BTRFS_FIRST_CHUNK_TREE_OBJECTID: u32 = 256;
pub const BTRFS_DEV_ITEMS_OBJECTID: u32 = 1;
pub const BTRFS_BTREE_INODE_OBJECTID: u32 = 1;
pub const BTRFS_EMPTY_SUBVOL_DIR_OBJECTID: u32 = 2;
pub const BTRFS_DEV_REPLACE_DEVID: u32 = 0;
pub const BTRFS_INODE_ITEM_KEY: u32 = 1;
pub const BTRFS_INODE_REF_KEY: u32 = 12;
pub const BTRFS_INODE_EXTREF_KEY: u32 = 13;
pub const BTRFS_XATTR_ITEM_KEY: u32 = 24;
pub const BTRFS_VERITY_DESC_ITEM_KEY: u32 = 36;
pub const BTRFS_VERITY_MERKLE_ITEM_KEY: u32 = 37;
pub const BTRFS_ORPHAN_ITEM_KEY: u32 = 48;
pub const BTRFS_DIR_LOG_ITEM_KEY: u32 = 60;
pub const BTRFS_DIR_LOG_INDEX_KEY: u32 = 72;
pub const BTRFS_DIR_ITEM_KEY: u32 = 84;
pub const BTRFS_DIR_INDEX_KEY: u32 = 96;
pub const BTRFS_EXTENT_DATA_KEY: u32 = 108;
pub const BTRFS_EXTENT_CSUM_KEY: u32 = 128;
pub const BTRFS_ROOT_ITEM_KEY: u32 = 132;
pub const BTRFS_ROOT_BACKREF_KEY: u32 = 144;
pub const BTRFS_ROOT_REF_KEY: u32 = 156;
pub const BTRFS_EXTENT_ITEM_KEY: u32 = 168;
pub const BTRFS_METADATA_ITEM_KEY: u32 = 169;
pub const BTRFS_EXTENT_OWNER_REF_KEY: u32 = 172;
pub const BTRFS_TREE_BLOCK_REF_KEY: u32 = 176;
pub const BTRFS_EXTENT_DATA_REF_KEY: u32 = 178;
pub const BTRFS_SHARED_BLOCK_REF_KEY: u32 = 182;
pub const BTRFS_SHARED_DATA_REF_KEY: u32 = 184;
pub const BTRFS_BLOCK_GROUP_ITEM_KEY: u32 = 192;
pub const BTRFS_FREE_SPACE_INFO_KEY: u32 = 198;
pub const BTRFS_FREE_SPACE_EXTENT_KEY: u32 = 199;
pub const BTRFS_FREE_SPACE_BITMAP_KEY: u32 = 200;
pub const BTRFS_DEV_EXTENT_KEY: u32 = 204;
pub const BTRFS_DEV_ITEM_KEY: u32 = 216;
pub const BTRFS_CHUNK_ITEM_KEY: u32 = 228;
pub const BTRFS_RAID_STRIPE_KEY: u32 = 230;
pub const BTRFS_QGROUP_STATUS_KEY: u32 = 240;
pub const BTRFS_QGROUP_INFO_KEY: u32 = 242;
pub const BTRFS_QGROUP_LIMIT_KEY: u32 = 244;
pub const BTRFS_QGROUP_RELATION_KEY: u32 = 246;
pub const BTRFS_BALANCE_ITEM_KEY: u32 = 248;
pub const BTRFS_TEMPORARY_ITEM_KEY: u32 = 248;
pub const BTRFS_DEV_STATS_KEY: u32 = 249;
pub const BTRFS_PERSISTENT_ITEM_KEY: u32 = 249;
pub const BTRFS_DEV_REPLACE_KEY: u32 = 250;
pub const BTRFS_UUID_KEY_SUBVOL: u32 = 251;
pub const BTRFS_UUID_KEY_RECEIVED_SUBVOL: u32 = 252;
pub const BTRFS_STRING_ITEM_KEY: u32 = 253;
pub const BTRFS_MAX_METADATA_BLOCKSIZE: u32 = 65536;
pub const BTRFS_CSUM_SIZE: u32 = 32;
pub const BTRFS_FT_UNKNOWN: u32 = 0;
pub const BTRFS_FT_REG_FILE: u32 = 1;
pub const BTRFS_FT_DIR: u32 = 2;
pub const BTRFS_FT_CHRDEV: u32 = 3;
pub const BTRFS_FT_BLKDEV: u32 = 4;
pub const BTRFS_FT_FIFO: u32 = 5;
pub const BTRFS_FT_SOCK: u32 = 6;
pub const BTRFS_FT_SYMLINK: u32 = 7;
pub const BTRFS_FT_XATTR: u32 = 8;
pub const BTRFS_FT_MAX: u32 = 9;
pub const BTRFS_FT_ENCRYPTED: u32 = 128;
pub const BTRFS_INODE_NODATASUM: u32 = 1;
pub const BTRFS_INODE_NODATACOW: u32 = 2;
pub const BTRFS_INODE_READONLY: u32 = 4;
pub const BTRFS_INODE_NOCOMPRESS: u32 = 8;
pub const BTRFS_INODE_PREALLOC: u32 = 16;
pub const BTRFS_INODE_SYNC: u32 = 32;
pub const BTRFS_INODE_IMMUTABLE: u32 = 64;
pub const BTRFS_INODE_APPEND: u32 = 128;
pub const BTRFS_INODE_NODUMP: u32 = 256;
pub const BTRFS_INODE_NOATIME: u32 = 512;
pub const BTRFS_INODE_DIRSYNC: u32 = 1024;
pub const BTRFS_INODE_COMPRESS: u32 = 2048;
pub const BTRFS_INODE_ROOT_ITEM_INIT: u32 = 2147483648;
pub const BTRFS_INODE_FLAG_MASK: u32 = 2147487743;
pub const BTRFS_INODE_RO_VERITY: u32 = 1;
pub const BTRFS_INODE_RO_FLAG_MASK: u32 = 1;
pub const BTRFS_SYSTEM_CHUNK_ARRAY_SIZE: u32 = 2048;
pub const BTRFS_NUM_BACKUP_ROOTS: u32 = 4;
pub const BTRFS_FREE_SPACE_EXTENT: u32 = 1;
pub const BTRFS_FREE_SPACE_BITMAP: u32 = 2;
pub const BTRFS_HEADER_FLAG_WRITTEN: u32 = 1;
pub const BTRFS_HEADER_FLAG_RELOC: u32 = 2;
pub const BTRFS_SUPER_FLAG_ERROR: u32 = 4;
pub const BTRFS_SUPER_FLAG_SEEDING: u64 = 4294967296;
pub const BTRFS_SUPER_FLAG_METADUMP: u64 = 8589934592;
pub const BTRFS_SUPER_FLAG_METADUMP_V2: u64 = 17179869184;
pub const BTRFS_SUPER_FLAG_CHANGING_FSID: u64 = 34359738368;
pub const BTRFS_SUPER_FLAG_CHANGING_FSID_V2: u64 = 68719476736;
pub const BTRFS_SUPER_FLAG_CHANGING_BG_TREE: u64 = 274877906944;
pub const BTRFS_SUPER_FLAG_CHANGING_DATA_CSUM: u64 = 549755813888;
pub const BTRFS_SUPER_FLAG_CHANGING_META_CSUM: u64 = 1099511627776;
pub const BTRFS_EXTENT_FLAG_DATA: u32 = 1;
pub const BTRFS_EXTENT_FLAG_TREE_BLOCK: u32 = 2;
pub const BTRFS_BLOCK_FLAG_FULL_BACKREF: u32 = 256;
pub const BTRFS_BACKREF_REV_MAX: u32 = 256;
pub const BTRFS_BACKREF_REV_SHIFT: u32 = 56;
pub const BTRFS_OLD_BACKREF_REV: u32 = 0;
pub const BTRFS_MIXED_BACKREF_REV: u32 = 1;
pub const BTRFS_EXTENT_FLAG_SUPER: u64 = 281474976710656;
pub const BTRFS_ROOT_SUBVOL_RDONLY: u32 = 1;
pub const BTRFS_ROOT_SUBVOL_DEAD: u64 = 281474976710656;
pub const BTRFS_DEV_REPLACE_ITEM_CONT_READING_FROM_SRCDEV_MODE_ALWAYS: u32 = 0;
pub const BTRFS_DEV_REPLACE_ITEM_CONT_READING_FROM_SRCDEV_MODE_AVOID: u32 = 1;
pub const BTRFS_BLOCK_GROUP_DATA: u32 = 1;
pub const BTRFS_BLOCK_GROUP_SYSTEM: u32 = 2;
pub const BTRFS_BLOCK_GROUP_METADATA: u32 = 4;
pub const BTRFS_BLOCK_GROUP_RAID0: u32 = 8;
pub const BTRFS_BLOCK_GROUP_RAID1: u32 = 16;
pub const BTRFS_BLOCK_GROUP_DUP: u32 = 32;
pub const BTRFS_BLOCK_GROUP_RAID10: u32 = 64;
pub const BTRFS_BLOCK_GROUP_RAID5: u32 = 128;
pub const BTRFS_BLOCK_GROUP_RAID6: u32 = 256;
pub const BTRFS_BLOCK_GROUP_RAID1C3: u32 = 512;
pub const BTRFS_BLOCK_GROUP_RAID1C4: u32 = 1024;
pub const BTRFS_BLOCK_GROUP_TYPE_MASK: u32 = 7;
pub const BTRFS_BLOCK_GROUP_PROFILE_MASK: u32 = 2040;
pub const BTRFS_BLOCK_GROUP_RAID56_MASK: u32 = 384;
pub const BTRFS_BLOCK_GROUP_RAID1_MASK: u32 = 1552;
pub const BTRFS_AVAIL_ALLOC_BIT_SINGLE: u64 = 281474976710656;
pub const BTRFS_SPACE_INFO_GLOBAL_RSV: u64 = 562949953421312;
pub const BTRFS_EXTENDED_PROFILE_MASK: u64 = 281474976712696;
pub const BTRFS_FREE_SPACE_USING_BITMAPS: u32 = 1;
pub const BTRFS_QGROUP_LEVEL_SHIFT: u32 = 48;
pub const BTRFS_QGROUP_STATUS_FLAG_ON: u32 = 1;
pub const BTRFS_QGROUP_STATUS_FLAG_RESCAN: u32 = 2;
pub const BTRFS_QGROUP_STATUS_FLAG_INCONSISTENT: u32 = 4;
pub const BTRFS_QGROUP_STATUS_FLAG_SIMPLE_MODE: u32 = 8;
pub const BTRFS_QGROUP_STATUS_FLAGS_MASK: u32 = 15;
pub const BTRFS_QGROUP_STATUS_VERSION: u32 = 1;
pub const BTRFS_FILE_EXTENT_INLINE: _bindgen_ty_1 = _bindgen_ty_1::BTRFS_FILE_EXTENT_INLINE;
pub const BTRFS_FILE_EXTENT_REG: _bindgen_ty_1 = _bindgen_ty_1::BTRFS_FILE_EXTENT_REG;
pub const BTRFS_FILE_EXTENT_PREALLOC: _bindgen_ty_1 = _bindgen_ty_1::BTRFS_FILE_EXTENT_PREALLOC;
pub const BTRFS_NR_FILE_EXTENT_TYPES: _bindgen_ty_1 = _bindgen_ty_1::BTRFS_NR_FILE_EXTENT_TYPES;
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum fsconfig_command {
FSCONFIG_SET_FLAG = 0,
FSCONFIG_SET_STRING = 1,
FSCONFIG_SET_BINARY = 2,
FSCONFIG_SET_PATH = 3,
FSCONFIG_SET_PATH_EMPTY = 4,
FSCONFIG_SET_FD = 5,
FSCONFIG_CMD_CREATE = 6,
FSCONFIG_CMD_RECONFIGURE = 7,
FSCONFIG_CMD_CREATE_EXCL = 8,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum procmap_query_flags {
PROCMAP_QUERY_VMA_READABLE = 1,
PROCMAP_QUERY_VMA_WRITABLE = 2,
PROCMAP_QUERY_VMA_EXECUTABLE = 4,
PROCMAP_QUERY_VMA_SHARED = 8,
PROCMAP_QUERY_COVERING_OR_NEXT_VMA = 16,
PROCMAP_QUERY_FILE_BACKED_VMA = 32,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum btrfs_dev_stat_values {
BTRFS_DEV_STAT_WRITE_ERRS = 0,
BTRFS_DEV_STAT_READ_ERRS = 1,
BTRFS_DEV_STAT_FLUSH_ERRS = 2,
BTRFS_DEV_STAT_CORRUPTION_ERRS = 3,
BTRFS_DEV_STAT_GENERATION_ERRS = 4,
BTRFS_DEV_STAT_VALUES_MAX = 5,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum btrfs_err_code {
BTRFS_ERROR_DEV_RAID1_MIN_NOT_MET = 1,
BTRFS_ERROR_DEV_RAID10_MIN_NOT_MET = 2,
BTRFS_ERROR_DEV_RAID5_MIN_NOT_MET = 3,
BTRFS_ERROR_DEV_RAID6_MIN_NOT_MET = 4,
BTRFS_ERROR_DEV_TGT_REPLACE = 5,
BTRFS_ERROR_DEV_MISSING_NOT_FOUND = 6,
BTRFS_ERROR_DEV_ONLY_WRITABLE = 7,
BTRFS_ERROR_DEV_EXCL_RUN_IN_PROGRESS = 8,
BTRFS_ERROR_DEV_RAID1C3_MIN_NOT_MET = 9,
BTRFS_ERROR_DEV_RAID1C4_MIN_NOT_MET = 10,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum btrfs_csum_type {
BTRFS_CSUM_TYPE_CRC32 = 0,
BTRFS_CSUM_TYPE_XXHASH = 1,
BTRFS_CSUM_TYPE_SHA256 = 2,
BTRFS_CSUM_TYPE_BLAKE2 = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_1 {
BTRFS_FILE_EXTENT_INLINE = 0,
BTRFS_FILE_EXTENT_REG = 1,
BTRFS_FILE_EXTENT_PREALLOC = 2,
BTRFS_NR_FILE_EXTENT_TYPES = 3,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union fscrypt_get_policy_ex_arg__bindgen_ty_1 {
pub version: __u8,
pub v1: fscrypt_policy_v1,
pub v2: fscrypt_policy_v2,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union fscrypt_key_specifier__bindgen_ty_1 {
pub __reserved: [__u8; 32usize],
pub descriptor: [__u8; 8usize],
pub identifier: [__u8; 16usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union btrfs_ioctl_vol_args_v2__bindgen_ty_1 {
pub __bindgen_anon_1: btrfs_ioctl_vol_args_v2__bindgen_ty_1__bindgen_ty_1,
pub unused: [__u64; 4usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union btrfs_ioctl_vol_args_v2__bindgen_ty_2 {
pub name: [crate::ctypes::c_char; 4040usize],
pub devid: __u64,
pub subvolid: __u64,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union btrfs_ioctl_dev_replace_args__bindgen_ty_1 {
pub start: btrfs_ioctl_dev_replace_start_params,
pub status: btrfs_ioctl_dev_replace_status_params,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union btrfs_balance_args__bindgen_ty_1 {
pub usage: __u64,
pub __bindgen_anon_1: btrfs_balance_args__bindgen_ty_1__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union btrfs_balance_args__bindgen_ty_2 {
pub limit: __u64,
pub __bindgen_anon_1: btrfs_balance_args__bindgen_ty_2__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union btrfs_disk_balance_args__bindgen_ty_1 {
pub usage: __le64,
pub __bindgen_anon_1: btrfs_disk_balance_args__bindgen_ty_1__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union btrfs_disk_balance_args__bindgen_ty_2 {
pub limit: __le64,
pub __bindgen_anon_1: btrfs_disk_balance_args__bindgen_ty_2__bindgen_ty_1,
}
impl<T> __IncompleteArrayField<T> {
#[inline]
pub const fn new() -> Self {
__IncompleteArrayField(::core::marker::PhantomData, [])
}
#[inline]
pub fn as_ptr(&self) -> *const T {
self as *const _ as *const T
}
#[inline]
pub fn as_mut_ptr(&mut self) -> *mut T {
self as *mut _ as *mut T
}
#[inline]
pub unsafe fn as_slice(&self, len: usize) -> &[T] {
::core::slice::from_raw_parts(self.as_ptr(), len)
}
#[inline]
pub unsafe fn as_mut_slice(&mut self, len: usize) -> &mut [T] {
::core::slice::from_raw_parts_mut(self.as_mut_ptr(), len)
}
}
impl<T> ::core::fmt::Debug for __IncompleteArrayField<T> {
fn fmt(&self, fmt: &mut ::core::fmt::Formatter<'_>) -> ::core::fmt::Result {
fmt.write_str("__IncompleteArrayField")
}
}
