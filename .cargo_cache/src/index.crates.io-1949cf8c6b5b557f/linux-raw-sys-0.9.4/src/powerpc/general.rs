/* automatically generated by rust-bindgen 0.71.1 */

pub type __s8 = crate::ctypes::c_schar;
pub type __u8 = crate::ctypes::c_uchar;
pub type __s16 = crate::ctypes::c_short;
pub type __u16 = crate::ctypes::c_ushort;
pub type __s32 = crate::ctypes::c_int;
pub type __u32 = crate::ctypes::c_uint;
pub type __s64 = crate::ctypes::c_longlong;
pub type __u64 = crate::ctypes::c_ulonglong;
pub type __kernel_sighandler_t = ::core::option::Option<unsafe extern "C" fn(arg1: crate::ctypes::c_int)>;
pub type __kernel_key_t = crate::ctypes::c_int;
pub type __kernel_mqd_t = crate::ctypes::c_int;
pub type __kernel_ipc_pid_t = crate::ctypes::c_short;
pub type __kernel_long_t = crate::ctypes::c_long;
pub type __kernel_ulong_t = crate::ctypes::c_ulong;
pub type __kernel_ino_t = __kernel_ulong_t;
pub type __kernel_mode_t = crate::ctypes::c_uint;
pub type __kernel_pid_t = crate::ctypes::c_int;
pub type __kernel_uid_t = crate::ctypes::c_uint;
pub type __kernel_gid_t = crate::ctypes::c_uint;
pub type __kernel_suseconds_t = __kernel_long_t;
pub type __kernel_daddr_t = crate::ctypes::c_int;
pub type __kernel_uid32_t = crate::ctypes::c_uint;
pub type __kernel_gid32_t = crate::ctypes::c_uint;
pub type __kernel_old_uid_t = __kernel_uid_t;
pub type __kernel_old_gid_t = __kernel_gid_t;
pub type __kernel_old_dev_t = crate::ctypes::c_uint;
pub type __kernel_size_t = crate::ctypes::c_uint;
pub type __kernel_ssize_t = crate::ctypes::c_int;
pub type __kernel_ptrdiff_t = crate::ctypes::c_int;
pub type __kernel_off_t = __kernel_long_t;
pub type __kernel_loff_t = crate::ctypes::c_longlong;
pub type __kernel_old_time_t = __kernel_long_t;
pub type __kernel_time_t = __kernel_long_t;
pub type __kernel_time64_t = crate::ctypes::c_longlong;
pub type __kernel_clock_t = __kernel_long_t;
pub type __kernel_timer_t = crate::ctypes::c_int;
pub type __kernel_clockid_t = crate::ctypes::c_int;
pub type __kernel_caddr_t = *mut crate::ctypes::c_char;
pub type __kernel_uid16_t = crate::ctypes::c_ushort;
pub type __kernel_gid16_t = crate::ctypes::c_ushort;
pub type __le16 = __u16;
pub type __be16 = __u16;
pub type __le32 = __u32;
pub type __be32 = __u32;
pub type __le64 = __u64;
pub type __be64 = __u64;
pub type __sum16 = __u16;
pub type __wsum = __u32;
pub type __poll_t = crate::ctypes::c_uint;
pub type cap_user_header_t = *mut __user_cap_header_struct;
pub type cap_user_data_t = *mut __user_cap_data_struct;
pub type __kernel_rwf_t = crate::ctypes::c_int;
pub type old_sigset_t = crate::ctypes::c_ulong;
pub type __signalfn_t = ::core::option::Option<unsafe extern "C" fn(arg1: crate::ctypes::c_int)>;
pub type __sighandler_t = __signalfn_t;
pub type __restorefn_t = ::core::option::Option<unsafe extern "C" fn()>;
pub type __sigrestore_t = __restorefn_t;
pub type stack_t = sigaltstack;
pub type sigval_t = sigval;
pub type siginfo_t = siginfo;
pub type sigevent_t = sigevent;
pub type cc_t = crate::ctypes::c_uchar;
pub type speed_t = crate::ctypes::c_uint;
pub type tcflag_t = crate::ctypes::c_uint;
pub type __fsword_t = __u32;
pub type termios2 = termios;
#[repr(C)]
#[derive(Copy, Clone, Debug, Default, Eq, Hash, Ord, PartialEq, PartialOrd)]
pub struct __BindgenBitfieldUnit<Storage> {
storage: Storage,
}
#[repr(C)]
#[derive(Default)]
pub struct __IncompleteArrayField<T>(::core::marker::PhantomData<T>, [T; 0]);
#[repr(C)]
#[repr(align(16))]
#[derive(Debug, Copy, Clone)]
pub struct __vector128 {
pub u: [__u32; 4usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_fd_set {
pub fds_bits: [crate::ctypes::c_ulong; 32usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_fsid_t {
pub val: [crate::ctypes::c_int; 2usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __user_cap_header_struct {
pub version: __u32,
pub pid: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __user_cap_data_struct {
pub effective: __u32,
pub permitted: __u32,
pub inheritable: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct vfs_cap_data {
pub magic_etc: __le32,
pub data: [vfs_cap_data__bindgen_ty_1; 2usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct vfs_cap_data__bindgen_ty_1 {
pub permitted: __le32,
pub inheritable: __le32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct vfs_ns_cap_data {
pub magic_etc: __le32,
pub data: [vfs_ns_cap_data__bindgen_ty_1; 2usize],
pub rootid: __le32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct vfs_ns_cap_data__bindgen_ty_1 {
pub permitted: __le32,
pub inheritable: __le32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct f_owner_ex {
pub type_: crate::ctypes::c_int,
pub pid: __kernel_pid_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct flock {
pub l_type: crate::ctypes::c_short,
pub l_whence: crate::ctypes::c_short,
pub l_start: __kernel_off_t,
pub l_len: __kernel_off_t,
pub l_pid: __kernel_pid_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct flock64 {
pub l_type: crate::ctypes::c_short,
pub l_whence: crate::ctypes::c_short,
pub l_start: __kernel_loff_t,
pub l_len: __kernel_loff_t,
pub l_pid: __kernel_pid_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct open_how {
pub flags: __u64,
pub mode: __u64,
pub resolve: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct epoll_event {
pub events: __poll_t,
pub data: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct epoll_params {
pub busy_poll_usecs: __u32,
pub busy_poll_budget: __u16,
pub prefer_busy_poll: __u8,
pub __pad: __u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fscrypt_policy_v1 {
pub version: __u8,
pub contents_encryption_mode: __u8,
pub filenames_encryption_mode: __u8,
pub flags: __u8,
pub master_key_descriptor: [__u8; 8usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fscrypt_key {
pub mode: __u32,
pub raw: [__u8; 64usize],
pub size: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fscrypt_policy_v2 {
pub version: __u8,
pub contents_encryption_mode: __u8,
pub filenames_encryption_mode: __u8,
pub flags: __u8,
pub log2_data_unit_size: __u8,
pub __reserved: [__u8; 3usize],
pub master_key_identifier: [__u8; 16usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct fscrypt_get_policy_ex_arg {
pub policy_size: __u64,
pub policy: fscrypt_get_policy_ex_arg__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct fscrypt_key_specifier {
pub type_: __u32,
pub __reserved: __u32,
pub u: fscrypt_key_specifier__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug)]
pub struct fscrypt_provisioning_key_payload {
pub type_: __u32,
pub __reserved: __u32,
pub raw: __IncompleteArrayField<__u8>,
}
#[repr(C)]
pub struct fscrypt_add_key_arg {
pub key_spec: fscrypt_key_specifier,
pub raw_size: __u32,
pub key_id: __u32,
pub __reserved: [__u32; 8usize],
pub raw: __IncompleteArrayField<__u8>,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct fscrypt_remove_key_arg {
pub key_spec: fscrypt_key_specifier,
pub removal_status_flags: __u32,
pub __reserved: [__u32; 5usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct fscrypt_get_key_status_arg {
pub key_spec: fscrypt_key_specifier,
pub __reserved: [__u32; 6usize],
pub status: __u32,
pub status_flags: __u32,
pub user_count: __u32,
pub __out_reserved: [__u32; 13usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mount_attr {
pub attr_set: __u64,
pub attr_clr: __u64,
pub propagation: __u64,
pub userns_fd: __u64,
}
#[repr(C)]
#[derive(Debug)]
pub struct statmount {
pub size: __u32,
pub mnt_opts: __u32,
pub mask: __u64,
pub sb_dev_major: __u32,
pub sb_dev_minor: __u32,
pub sb_magic: __u64,
pub sb_flags: __u32,
pub fs_type: __u32,
pub mnt_id: __u64,
pub mnt_parent_id: __u64,
pub mnt_id_old: __u32,
pub mnt_parent_id_old: __u32,
pub mnt_attr: __u64,
pub mnt_propagation: __u64,
pub mnt_peer_group: __u64,
pub mnt_master: __u64,
pub propagate_from: __u64,
pub mnt_root: __u32,
pub mnt_point: __u32,
pub mnt_ns_id: __u64,
pub fs_subtype: __u32,
pub sb_source: __u32,
pub opt_num: __u32,
pub opt_array: __u32,
pub opt_sec_num: __u32,
pub opt_sec_array: __u32,
pub __spare2: [__u64; 46usize],
pub str_: __IncompleteArrayField<crate::ctypes::c_char>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mnt_id_req {
pub size: __u32,
pub spare: __u32,
pub mnt_id: __u64,
pub param: __u64,
pub mnt_ns_id: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct file_clone_range {
pub src_fd: __s64,
pub src_offset: __u64,
pub src_length: __u64,
pub dest_offset: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fstrim_range {
pub start: __u64,
pub len: __u64,
pub minlen: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fsuuid2 {
pub len: __u8,
pub uuid: [__u8; 16usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fs_sysfs_path {
pub len: __u8,
pub name: [__u8; 128usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct file_dedupe_range_info {
pub dest_fd: __s64,
pub dest_offset: __u64,
pub bytes_deduped: __u64,
pub status: __s32,
pub reserved: __u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct file_dedupe_range {
pub src_offset: __u64,
pub src_length: __u64,
pub dest_count: __u16,
pub reserved1: __u16,
pub reserved2: __u32,
pub info: __IncompleteArrayField<file_dedupe_range_info>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct files_stat_struct {
pub nr_files: crate::ctypes::c_ulong,
pub nr_free_files: crate::ctypes::c_ulong,
pub max_files: crate::ctypes::c_ulong,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct inodes_stat_t {
pub nr_inodes: crate::ctypes::c_long,
pub nr_unused: crate::ctypes::c_long,
pub dummy: [crate::ctypes::c_long; 5usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fsxattr {
pub fsx_xflags: __u32,
pub fsx_extsize: __u32,
pub fsx_nextents: __u32,
pub fsx_projid: __u32,
pub fsx_cowextsize: __u32,
pub fsx_pad: [crate::ctypes::c_uchar; 8usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct page_region {
pub start: __u64,
pub end: __u64,
pub categories: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct pm_scan_arg {
pub size: __u64,
pub flags: __u64,
pub start: __u64,
pub end: __u64,
pub walk_end: __u64,
pub vec: __u64,
pub vec_len: __u64,
pub max_pages: __u64,
pub category_inverted: __u64,
pub category_mask: __u64,
pub category_anyof_mask: __u64,
pub return_mask: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct procmap_query {
pub size: __u64,
pub query_flags: __u64,
pub query_addr: __u64,
pub vma_start: __u64,
pub vma_end: __u64,
pub vma_flags: __u64,
pub vma_page_size: __u64,
pub vma_offset: __u64,
pub inode: __u64,
pub dev_major: __u32,
pub dev_minor: __u32,
pub vma_name_size: __u32,
pub build_id_size: __u32,
pub vma_name_addr: __u64,
pub build_id_addr: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct futex_waitv {
pub val: __u64,
pub uaddr: __u64,
pub flags: __u32,
pub __reserved: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct robust_list {
pub next: *mut robust_list,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct robust_list_head {
pub list: robust_list,
pub futex_offset: crate::ctypes::c_long,
pub list_op_pending: *mut robust_list,
}
#[repr(C)]
#[derive(Debug)]
pub struct inotify_event {
pub wd: __s32,
pub mask: __u32,
pub cookie: __u32,
pub len: __u32,
pub name: __IncompleteArrayField<crate::ctypes::c_char>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct cachestat_range {
pub off: __u64,
pub len: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct cachestat {
pub nr_cache: __u64,
pub nr_dirty: __u64,
pub nr_writeback: __u64,
pub nr_evicted: __u64,
pub nr_recently_evicted: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct pollfd {
pub fd: crate::ctypes::c_int,
pub events: crate::ctypes::c_short,
pub revents: crate::ctypes::c_short,
}
#[repr(C)]
#[derive(Debug)]
pub struct rand_pool_info {
pub entropy_count: crate::ctypes::c_int,
pub buf_size: crate::ctypes::c_int,
pub buf: __IncompleteArrayField<__u32>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct vgetrandom_opaque_params {
pub size_of_opaque_state: __u32,
pub mmap_prot: __u32,
pub mmap_flags: __u32,
pub reserved: [__u32; 13usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_timespec {
pub tv_sec: __kernel_time64_t,
pub tv_nsec: crate::ctypes::c_longlong,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_itimerspec {
pub it_interval: __kernel_timespec,
pub it_value: __kernel_timespec,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_old_timeval {
pub tv_sec: __kernel_long_t,
pub tv_usec: __kernel_long_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_old_timespec {
pub tv_sec: __kernel_old_time_t,
pub tv_nsec: crate::ctypes::c_long,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_old_itimerval {
pub it_interval: __kernel_old_timeval,
pub it_value: __kernel_old_timeval,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_sock_timeval {
pub tv_sec: __s64,
pub tv_usec: __s64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rusage {
pub ru_utime: __kernel_old_timeval,
pub ru_stime: __kernel_old_timeval,
pub ru_maxrss: __kernel_long_t,
pub ru_ixrss: __kernel_long_t,
pub ru_idrss: __kernel_long_t,
pub ru_isrss: __kernel_long_t,
pub ru_minflt: __kernel_long_t,
pub ru_majflt: __kernel_long_t,
pub ru_nswap: __kernel_long_t,
pub ru_inblock: __kernel_long_t,
pub ru_oublock: __kernel_long_t,
pub ru_msgsnd: __kernel_long_t,
pub ru_msgrcv: __kernel_long_t,
pub ru_nsignals: __kernel_long_t,
pub ru_nvcsw: __kernel_long_t,
pub ru_nivcsw: __kernel_long_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rlimit {
pub rlim_cur: __kernel_ulong_t,
pub rlim_max: __kernel_ulong_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rlimit64 {
pub rlim_cur: __u64,
pub rlim_max: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct clone_args {
pub flags: __u64,
pub pidfd: __u64,
pub child_tid: __u64,
pub parent_tid: __u64,
pub exit_signal: __u64,
pub stack: __u64,
pub stack_size: __u64,
pub tls: __u64,
pub set_tid: __u64,
pub set_tid_size: __u64,
pub cgroup: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sigset_t {
pub sig: [crate::ctypes::c_ulong; 2usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct old_sigaction {
pub sa_handler: __sighandler_t,
pub sa_mask: old_sigset_t,
pub sa_flags: crate::ctypes::c_ulong,
pub sa_restorer: __sigrestore_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sigaction {
pub sa_handler: __sighandler_t,
pub sa_flags: crate::ctypes::c_ulong,
pub sa_restorer: __sigrestore_t,
pub sa_mask: sigset_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sigaltstack {
pub ss_sp: *mut crate::ctypes::c_void,
pub ss_flags: crate::ctypes::c_int,
pub ss_size: __kernel_size_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sig_dbg_op {
pub dbg_type: crate::ctypes::c_int,
pub dbg_value: crate::ctypes::c_ulong,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __sifields__bindgen_ty_1 {
pub _pid: __kernel_pid_t,
pub _uid: __kernel_uid32_t,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct __sifields__bindgen_ty_2 {
pub _tid: __kernel_timer_t,
pub _overrun: crate::ctypes::c_int,
pub _sigval: sigval_t,
pub _sys_private: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct __sifields__bindgen_ty_3 {
pub _pid: __kernel_pid_t,
pub _uid: __kernel_uid32_t,
pub _sigval: sigval_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __sifields__bindgen_ty_4 {
pub _pid: __kernel_pid_t,
pub _uid: __kernel_uid32_t,
pub _status: crate::ctypes::c_int,
pub _utime: __kernel_clock_t,
pub _stime: __kernel_clock_t,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct __sifields__bindgen_ty_5 {
pub _addr: *mut crate::ctypes::c_void,
pub __bindgen_anon_1: __sifields__bindgen_ty_5__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __sifields__bindgen_ty_5__bindgen_ty_1__bindgen_ty_1 {
pub _dummy_bnd: [crate::ctypes::c_char; 4usize],
pub _lower: *mut crate::ctypes::c_void,
pub _upper: *mut crate::ctypes::c_void,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __sifields__bindgen_ty_5__bindgen_ty_1__bindgen_ty_2 {
pub _dummy_pkey: [crate::ctypes::c_char; 4usize],
pub _pkey: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __sifields__bindgen_ty_5__bindgen_ty_1__bindgen_ty_3 {
pub _data: crate::ctypes::c_ulong,
pub _type: __u32,
pub _flags: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __sifields__bindgen_ty_6 {
pub _band: crate::ctypes::c_long,
pub _fd: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __sifields__bindgen_ty_7 {
pub _call_addr: *mut crate::ctypes::c_void,
pub _syscall: crate::ctypes::c_int,
pub _arch: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct siginfo {
pub __bindgen_anon_1: siginfo__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct siginfo__bindgen_ty_1__bindgen_ty_1 {
pub si_signo: crate::ctypes::c_int,
pub si_errno: crate::ctypes::c_int,
pub si_code: crate::ctypes::c_int,
pub _sifields: __sifields,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct sigevent {
pub sigev_value: sigval_t,
pub sigev_signo: crate::ctypes::c_int,
pub sigev_notify: crate::ctypes::c_int,
pub _sigev_un: sigevent__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sigevent__bindgen_ty_1__bindgen_ty_1 {
pub _function: ::core::option::Option<unsafe extern "C" fn(arg1: sigval_t)>,
pub _attribute: *mut crate::ctypes::c_void,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct statx_timestamp {
pub tv_sec: __s64,
pub tv_nsec: __u32,
pub __reserved: __s32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct statx {
pub stx_mask: __u32,
pub stx_blksize: __u32,
pub stx_attributes: __u64,
pub stx_nlink: __u32,
pub stx_uid: __u32,
pub stx_gid: __u32,
pub stx_mode: __u16,
pub __spare0: [__u16; 1usize],
pub stx_ino: __u64,
pub stx_size: __u64,
pub stx_blocks: __u64,
pub stx_attributes_mask: __u64,
pub stx_atime: statx_timestamp,
pub stx_btime: statx_timestamp,
pub stx_ctime: statx_timestamp,
pub stx_mtime: statx_timestamp,
pub stx_rdev_major: __u32,
pub stx_rdev_minor: __u32,
pub stx_dev_major: __u32,
pub stx_dev_minor: __u32,
pub stx_mnt_id: __u64,
pub stx_dio_mem_align: __u32,
pub stx_dio_offset_align: __u32,
pub stx_subvol: __u64,
pub stx_atomic_write_unit_min: __u32,
pub stx_atomic_write_unit_max: __u32,
pub stx_atomic_write_segments_max: __u32,
pub __spare1: [__u32; 1usize],
pub __spare3: [__u64; 9usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct termios {
pub c_iflag: tcflag_t,
pub c_oflag: tcflag_t,
pub c_cflag: tcflag_t,
pub c_lflag: tcflag_t,
pub c_cc: [cc_t; 19usize],
pub c_line: cc_t,
pub c_ispeed: speed_t,
pub c_ospeed: speed_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ktermios {
pub c_iflag: tcflag_t,
pub c_oflag: tcflag_t,
pub c_cflag: tcflag_t,
pub c_lflag: tcflag_t,
pub c_cc: [cc_t; 19usize],
pub c_line: cc_t,
pub c_ispeed: speed_t,
pub c_ospeed: speed_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sgttyb {
pub sg_ispeed: crate::ctypes::c_char,
pub sg_ospeed: crate::ctypes::c_char,
pub sg_erase: crate::ctypes::c_char,
pub sg_kill: crate::ctypes::c_char,
pub sg_flags: crate::ctypes::c_short,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tchars {
pub t_intrc: crate::ctypes::c_char,
pub t_quitc: crate::ctypes::c_char,
pub t_startc: crate::ctypes::c_char,
pub t_stopc: crate::ctypes::c_char,
pub t_eofc: crate::ctypes::c_char,
pub t_brkc: crate::ctypes::c_char,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ltchars {
pub t_suspc: crate::ctypes::c_char,
pub t_dsuspc: crate::ctypes::c_char,
pub t_rprntc: crate::ctypes::c_char,
pub t_flushc: crate::ctypes::c_char,
pub t_werasc: crate::ctypes::c_char,
pub t_lnextc: crate::ctypes::c_char,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct winsize {
pub ws_row: crate::ctypes::c_ushort,
pub ws_col: crate::ctypes::c_ushort,
pub ws_xpixel: crate::ctypes::c_ushort,
pub ws_ypixel: crate::ctypes::c_ushort,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct termio {
pub c_iflag: crate::ctypes::c_ushort,
pub c_oflag: crate::ctypes::c_ushort,
pub c_cflag: crate::ctypes::c_ushort,
pub c_lflag: crate::ctypes::c_ushort,
pub c_line: crate::ctypes::c_uchar,
pub c_cc: [crate::ctypes::c_uchar; 10usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct timespec {
pub tv_sec: __kernel_old_time_t,
pub tv_nsec: crate::ctypes::c_long,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct timeval {
pub tv_sec: __kernel_old_time_t,
pub tv_usec: __kernel_suseconds_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct itimerspec {
pub it_interval: timespec,
pub it_value: timespec,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct itimerval {
pub it_interval: timeval,
pub it_value: timeval,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct timezone {
pub tz_minuteswest: crate::ctypes::c_int,
pub tz_dsttime: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct iovec {
pub iov_base: *mut crate::ctypes::c_void,
pub iov_len: __kernel_size_t,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct dmabuf_cmsg {
pub frag_offset: __u64,
pub frag_size: __u32,
pub frag_token: __u32,
pub dmabuf_id: __u32,
pub flags: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct dmabuf_token {
pub token_start: __u32,
pub token_count: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct xattr_args {
pub value: __u64,
pub size: __u32,
pub flags: __u32,
}
#[repr(C, packed)]
#[derive(Copy, Clone)]
pub struct uffd_msg {
pub event: __u8,
pub reserved1: __u8,
pub reserved2: __u16,
pub reserved3: __u32,
pub arg: uffd_msg__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct uffd_msg__bindgen_ty_1__bindgen_ty_1 {
pub flags: __u64,
pub address: __u64,
pub feat: uffd_msg__bindgen_ty_1__bindgen_ty_1__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffd_msg__bindgen_ty_1__bindgen_ty_2 {
pub ufd: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffd_msg__bindgen_ty_1__bindgen_ty_3 {
pub from: __u64,
pub to: __u64,
pub len: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffd_msg__bindgen_ty_1__bindgen_ty_4 {
pub start: __u64,
pub end: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffd_msg__bindgen_ty_1__bindgen_ty_5 {
pub reserved1: __u64,
pub reserved2: __u64,
pub reserved3: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffdio_api {
pub api: __u64,
pub features: __u64,
pub ioctls: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffdio_range {
pub start: __u64,
pub len: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffdio_register {
pub range: uffdio_range,
pub mode: __u64,
pub ioctls: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffdio_copy {
pub dst: __u64,
pub src: __u64,
pub len: __u64,
pub mode: __u64,
pub copy: __s64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffdio_zeropage {
pub range: uffdio_range,
pub mode: __u64,
pub zeropage: __s64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffdio_writeprotect {
pub range: uffdio_range,
pub mode: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffdio_continue {
pub range: uffdio_range,
pub mode: __u64,
pub mapped: __s64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffdio_poison {
pub range: uffdio_range,
pub mode: __u64,
pub updated: __s64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct uffdio_move {
pub dst: __u64,
pub src: __u64,
pub len: __u64,
pub mode: __u64,
pub move_: __s64,
}
#[repr(C)]
#[derive(Debug)]
pub struct linux_dirent64 {
pub d_ino: crate::ctypes::c_ulonglong,
pub d_off: crate::ctypes::c_longlong,
pub d_reclen: __u16,
pub d_type: __u8,
pub d_name: __IncompleteArrayField<crate::ctypes::c_char>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __old_kernel_stat {
pub st_dev: crate::ctypes::c_ushort,
pub st_ino: crate::ctypes::c_ushort,
pub st_mode: crate::ctypes::c_ushort,
pub st_nlink: crate::ctypes::c_ushort,
pub st_uid: crate::ctypes::c_ushort,
pub st_gid: crate::ctypes::c_ushort,
pub st_rdev: crate::ctypes::c_ushort,
pub st_size: crate::ctypes::c_ulong,
pub st_atime: crate::ctypes::c_ulong,
pub st_mtime: crate::ctypes::c_ulong,
pub st_ctime: crate::ctypes::c_ulong,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct stat {
pub st_dev: crate::ctypes::c_ulong,
pub st_ino: __kernel_ino_t,
pub st_mode: __kernel_mode_t,
pub st_nlink: crate::ctypes::c_ushort,
pub st_uid: __kernel_uid32_t,
pub st_gid: __kernel_gid32_t,
pub st_rdev: crate::ctypes::c_ulong,
pub st_size: crate::ctypes::c_long,
pub st_blksize: crate::ctypes::c_ulong,
pub st_blocks: crate::ctypes::c_ulong,
pub st_atime: crate::ctypes::c_ulong,
pub st_atime_nsec: crate::ctypes::c_ulong,
pub st_mtime: crate::ctypes::c_ulong,
pub st_mtime_nsec: crate::ctypes::c_ulong,
pub st_ctime: crate::ctypes::c_ulong,
pub st_ctime_nsec: crate::ctypes::c_ulong,
pub __unused4: crate::ctypes::c_ulong,
pub __unused5: crate::ctypes::c_ulong,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct stat64 {
pub st_dev: crate::ctypes::c_ulonglong,
pub st_ino: crate::ctypes::c_ulonglong,
pub st_mode: crate::ctypes::c_uint,
pub st_nlink: crate::ctypes::c_uint,
pub st_uid: crate::ctypes::c_uint,
pub st_gid: crate::ctypes::c_uint,
pub st_rdev: crate::ctypes::c_ulonglong,
pub __pad2: crate::ctypes::c_ushort,
pub st_size: crate::ctypes::c_longlong,
pub st_blksize: crate::ctypes::c_int,
pub st_blocks: crate::ctypes::c_longlong,
pub st_atime: crate::ctypes::c_int,
pub st_atime_nsec: crate::ctypes::c_uint,
pub st_mtime: crate::ctypes::c_int,
pub st_mtime_nsec: crate::ctypes::c_uint,
pub st_ctime: crate::ctypes::c_int,
pub st_ctime_nsec: crate::ctypes::c_uint,
pub __unused4: crate::ctypes::c_uint,
pub __unused5: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct statfs {
pub f_type: __u32,
pub f_bsize: __u32,
pub f_blocks: __u32,
pub f_bfree: __u32,
pub f_bavail: __u32,
pub f_files: __u32,
pub f_ffree: __u32,
pub f_fsid: __kernel_fsid_t,
pub f_namelen: __u32,
pub f_frsize: __u32,
pub f_flags: __u32,
pub f_spare: [__u32; 4usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct statfs64 {
pub f_type: __u32,
pub f_bsize: __u32,
pub f_blocks: __u64,
pub f_bfree: __u64,
pub f_bavail: __u64,
pub f_files: __u64,
pub f_ffree: __u64,
pub f_fsid: __kernel_fsid_t,
pub f_namelen: __u32,
pub f_frsize: __u32,
pub f_flags: __u32,
pub f_spare: [__u32; 4usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct compat_statfs64 {
pub f_type: __u32,
pub f_bsize: __u32,
pub f_blocks: __u64,
pub f_bfree: __u64,
pub f_bavail: __u64,
pub f_files: __u64,
pub f_ffree: __u64,
pub f_fsid: __kernel_fsid_t,
pub f_namelen: __u32,
pub f_frsize: __u32,
pub f_flags: __u32,
pub f_spare: [__u32; 4usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct user_desc {
pub entry_number: crate::ctypes::c_uint,
pub base_addr: crate::ctypes::c_uint,
pub limit: crate::ctypes::c_uint,
pub _bitfield_align_1: [u8; 0],
pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
pub __bindgen_padding_0: [u8; 3usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct kernel_sigset_t {
pub sig: [crate::ctypes::c_ulong; 2usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct kernel_sigaction {
pub sa_handler_kernel: __kernel_sighandler_t,
pub sa_flags: crate::ctypes::c_ulong,
pub sa_restorer: __sigrestore_t,
pub sa_mask: kernel_sigset_t,
}
pub const LINUX_VERSION_CODE: u32 = 396544;
pub const LINUX_VERSION_MAJOR: u32 = 6;
pub const LINUX_VERSION_PATCHLEVEL: u32 = 13;
pub const LINUX_VERSION_SUBLEVEL: u32 = 0;
pub const AT_DCACHEBSIZE: u32 = 19;
pub const AT_ICACHEBSIZE: u32 = 20;
pub const AT_UCACHEBSIZE: u32 = 21;
pub const AT_IGNOREPPC: u32 = 22;
pub const AT_SYSINFO_EHDR: u32 = 33;
pub const AT_L1I_CACHESIZE: u32 = 40;
pub const AT_L1I_CACHEGEOMETRY: u32 = 41;
pub const AT_L1D_CACHESIZE: u32 = 42;
pub const AT_L1D_CACHEGEOMETRY: u32 = 43;
pub const AT_L2_CACHESIZE: u32 = 44;
pub const AT_L2_CACHEGEOMETRY: u32 = 45;
pub const AT_L3_CACHESIZE: u32 = 46;
pub const AT_L3_CACHEGEOMETRY: u32 = 47;
pub const AT_MINSIGSTKSZ: u32 = 51;
pub const AT_VECTOR_SIZE_ARCH: u32 = 15;
pub const AT_NULL: u32 = 0;
pub const AT_IGNORE: u32 = 1;
pub const AT_EXECFD: u32 = 2;
pub const AT_PHDR: u32 = 3;
pub const AT_PHENT: u32 = 4;
pub const AT_PHNUM: u32 = 5;
pub const AT_PAGESZ: u32 = 6;
pub const AT_BASE: u32 = 7;
pub const AT_FLAGS: u32 = 8;
pub const AT_ENTRY: u32 = 9;
pub const AT_NOTELF: u32 = 10;
pub const AT_UID: u32 = 11;
pub const AT_EUID: u32 = 12;
pub const AT_GID: u32 = 13;
pub const AT_EGID: u32 = 14;
pub const AT_PLATFORM: u32 = 15;
pub const AT_HWCAP: u32 = 16;
pub const AT_CLKTCK: u32 = 17;
pub const AT_SECURE: u32 = 23;
pub const AT_BASE_PLATFORM: u32 = 24;
pub const AT_RANDOM: u32 = 25;
pub const AT_HWCAP2: u32 = 26;
pub const AT_RSEQ_FEATURE_SIZE: u32 = 27;
pub const AT_RSEQ_ALIGN: u32 = 28;
pub const AT_HWCAP3: u32 = 29;
pub const AT_HWCAP4: u32 = 30;
pub const AT_EXECFN: u32 = 31;
pub const __BITS_PER_LONG_LONG: u32 = 64;
pub const __FD_SETSIZE: u32 = 1024;
pub const _LINUX_CAPABILITY_VERSION_1: u32 = 429392688;
pub const _LINUX_CAPABILITY_U32S_1: u32 = 1;
pub const _LINUX_CAPABILITY_VERSION_2: u32 = 537333798;
pub const _LINUX_CAPABILITY_U32S_2: u32 = 2;
pub const _LINUX_CAPABILITY_VERSION_3: u32 = 537396514;
pub const _LINUX_CAPABILITY_U32S_3: u32 = 2;
pub const VFS_CAP_REVISION_MASK: u32 = 4278190080;
pub const VFS_CAP_REVISION_SHIFT: u32 = 24;
pub const VFS_CAP_FLAGS_MASK: i64 = -4278190081;
pub const VFS_CAP_FLAGS_EFFECTIVE: u32 = 1;
pub const VFS_CAP_REVISION_1: u32 = 16777216;
pub const VFS_CAP_U32_1: u32 = 1;
pub const VFS_CAP_REVISION_2: u32 = 33554432;
pub const VFS_CAP_U32_2: u32 = 2;
pub const VFS_CAP_REVISION_3: u32 = 50331648;
pub const VFS_CAP_U32_3: u32 = 2;
pub const VFS_CAP_U32: u32 = 2;
pub const VFS_CAP_REVISION: u32 = 50331648;
pub const _LINUX_CAPABILITY_VERSION: u32 = 429392688;
pub const _LINUX_CAPABILITY_U32S: u32 = 1;
pub const CAP_CHOWN: u32 = 0;
pub const CAP_DAC_OVERRIDE: u32 = 1;
pub const CAP_DAC_READ_SEARCH: u32 = 2;
pub const CAP_FOWNER: u32 = 3;
pub const CAP_FSETID: u32 = 4;
pub const CAP_KILL: u32 = 5;
pub const CAP_SETGID: u32 = 6;
pub const CAP_SETUID: u32 = 7;
pub const CAP_SETPCAP: u32 = 8;
pub const CAP_LINUX_IMMUTABLE: u32 = 9;
pub const CAP_NET_BIND_SERVICE: u32 = 10;
pub const CAP_NET_BROADCAST: u32 = 11;
pub const CAP_NET_ADMIN: u32 = 12;
pub const CAP_NET_RAW: u32 = 13;
pub const CAP_IPC_LOCK: u32 = 14;
pub const CAP_IPC_OWNER: u32 = 15;
pub const CAP_SYS_MODULE: u32 = 16;
pub const CAP_SYS_RAWIO: u32 = 17;
pub const CAP_SYS_CHROOT: u32 = 18;
pub const CAP_SYS_PTRACE: u32 = 19;
pub const CAP_SYS_PACCT: u32 = 20;
pub const CAP_SYS_ADMIN: u32 = 21;
pub const CAP_SYS_BOOT: u32 = 22;
pub const CAP_SYS_NICE: u32 = 23;
pub const CAP_SYS_RESOURCE: u32 = 24;
pub const CAP_SYS_TIME: u32 = 25;
pub const CAP_SYS_TTY_CONFIG: u32 = 26;
pub const CAP_MKNOD: u32 = 27;
pub const CAP_LEASE: u32 = 28;
pub const CAP_AUDIT_WRITE: u32 = 29;
pub const CAP_AUDIT_CONTROL: u32 = 30;
pub const CAP_SETFCAP: u32 = 31;
pub const CAP_MAC_OVERRIDE: u32 = 32;
pub const CAP_MAC_ADMIN: u32 = 33;
pub const CAP_SYSLOG: u32 = 34;
pub const CAP_WAKE_ALARM: u32 = 35;
pub const CAP_BLOCK_SUSPEND: u32 = 36;
pub const CAP_AUDIT_READ: u32 = 37;
pub const CAP_PERFMON: u32 = 38;
pub const CAP_BPF: u32 = 39;
pub const CAP_CHECKPOINT_RESTORE: u32 = 40;
pub const CAP_LAST_CAP: u32 = 40;
pub const O_DIRECTORY: u32 = 16384;
pub const O_NOFOLLOW: u32 = 32768;
pub const O_LARGEFILE: u32 = 65536;
pub const O_DIRECT: u32 = 131072;
pub const O_ACCMODE: u32 = 3;
pub const O_RDONLY: u32 = 0;
pub const O_WRONLY: u32 = 1;
pub const O_RDWR: u32 = 2;
pub const O_CREAT: u32 = 64;
pub const O_EXCL: u32 = 128;
pub const O_NOCTTY: u32 = 256;
pub const O_TRUNC: u32 = 512;
pub const O_APPEND: u32 = 1024;
pub const O_NONBLOCK: u32 = 2048;
pub const O_DSYNC: u32 = 4096;
pub const FASYNC: u32 = 8192;
pub const O_NOATIME: u32 = 262144;
pub const O_CLOEXEC: u32 = 524288;
pub const __O_SYNC: u32 = 1048576;
pub const O_SYNC: u32 = 1052672;
pub const O_PATH: u32 = 2097152;
pub const __O_TMPFILE: u32 = 4194304;
pub const O_TMPFILE: u32 = 4210688;
pub const O_NDELAY: u32 = 2048;
pub const F_DUPFD: u32 = 0;
pub const F_GETFD: u32 = 1;
pub const F_SETFD: u32 = 2;
pub const F_GETFL: u32 = 3;
pub const F_SETFL: u32 = 4;
pub const F_GETLK: u32 = 5;
pub const F_SETLK: u32 = 6;
pub const F_SETLKW: u32 = 7;
pub const F_SETOWN: u32 = 8;
pub const F_GETOWN: u32 = 9;
pub const F_SETSIG: u32 = 10;
pub const F_GETSIG: u32 = 11;
pub const F_GETLK64: u32 = 12;
pub const F_SETLK64: u32 = 13;
pub const F_SETLKW64: u32 = 14;
pub const F_SETOWN_EX: u32 = 15;
pub const F_GETOWN_EX: u32 = 16;
pub const F_GETOWNER_UIDS: u32 = 17;
pub const F_OFD_GETLK: u32 = 36;
pub const F_OFD_SETLK: u32 = 37;
pub const F_OFD_SETLKW: u32 = 38;
pub const F_OWNER_TID: u32 = 0;
pub const F_OWNER_PID: u32 = 1;
pub const F_OWNER_PGRP: u32 = 2;
pub const FD_CLOEXEC: u32 = 1;
pub const F_RDLCK: u32 = 0;
pub const F_WRLCK: u32 = 1;
pub const F_UNLCK: u32 = 2;
pub const F_EXLCK: u32 = 4;
pub const F_SHLCK: u32 = 8;
pub const LOCK_SH: u32 = 1;
pub const LOCK_EX: u32 = 2;
pub const LOCK_NB: u32 = 4;
pub const LOCK_UN: u32 = 8;
pub const LOCK_MAND: u32 = 32;
pub const LOCK_READ: u32 = 64;
pub const LOCK_WRITE: u32 = 128;
pub const LOCK_RW: u32 = 192;
pub const F_LINUX_SPECIFIC_BASE: u32 = 1024;
pub const RESOLVE_NO_XDEV: u32 = 1;
pub const RESOLVE_NO_MAGICLINKS: u32 = 2;
pub const RESOLVE_NO_SYMLINKS: u32 = 4;
pub const RESOLVE_BENEATH: u32 = 8;
pub const RESOLVE_IN_ROOT: u32 = 16;
pub const RESOLVE_CACHED: u32 = 32;
pub const F_SETLEASE: u32 = 1024;
pub const F_GETLEASE: u32 = 1025;
pub const F_NOTIFY: u32 = 1026;
pub const F_DUPFD_QUERY: u32 = 1027;
pub const F_CREATED_QUERY: u32 = 1028;
pub const F_CANCELLK: u32 = 1029;
pub const F_DUPFD_CLOEXEC: u32 = 1030;
pub const F_SETPIPE_SZ: u32 = 1031;
pub const F_GETPIPE_SZ: u32 = 1032;
pub const F_ADD_SEALS: u32 = 1033;
pub const F_GET_SEALS: u32 = 1034;
pub const F_SEAL_SEAL: u32 = 1;
pub const F_SEAL_SHRINK: u32 = 2;
pub const F_SEAL_GROW: u32 = 4;
pub const F_SEAL_WRITE: u32 = 8;
pub const F_SEAL_FUTURE_WRITE: u32 = 16;
pub const F_SEAL_EXEC: u32 = 32;
pub const F_GET_RW_HINT: u32 = 1035;
pub const F_SET_RW_HINT: u32 = 1036;
pub const F_GET_FILE_RW_HINT: u32 = 1037;
pub const F_SET_FILE_RW_HINT: u32 = 1038;
pub const RWH_WRITE_LIFE_NOT_SET: u32 = 0;
pub const RWH_WRITE_LIFE_NONE: u32 = 1;
pub const RWH_WRITE_LIFE_SHORT: u32 = 2;
pub const RWH_WRITE_LIFE_MEDIUM: u32 = 3;
pub const RWH_WRITE_LIFE_LONG: u32 = 4;
pub const RWH_WRITE_LIFE_EXTREME: u32 = 5;
pub const RWF_WRITE_LIFE_NOT_SET: u32 = 0;
pub const DN_ACCESS: u32 = 1;
pub const DN_MODIFY: u32 = 2;
pub const DN_CREATE: u32 = 4;
pub const DN_DELETE: u32 = 8;
pub const DN_RENAME: u32 = 16;
pub const DN_ATTRIB: u32 = 32;
pub const DN_MULTISHOT: u32 = 2147483648;
pub const AT_FDCWD: i32 = -100;
pub const AT_SYMLINK_NOFOLLOW: u32 = 256;
pub const AT_SYMLINK_FOLLOW: u32 = 1024;
pub const AT_NO_AUTOMOUNT: u32 = 2048;
pub const AT_EMPTY_PATH: u32 = 4096;
pub const AT_STATX_SYNC_TYPE: u32 = 24576;
pub const AT_STATX_SYNC_AS_STAT: u32 = 0;
pub const AT_STATX_FORCE_SYNC: u32 = 8192;
pub const AT_STATX_DONT_SYNC: u32 = 16384;
pub const AT_RECURSIVE: u32 = 32768;
pub const AT_RENAME_NOREPLACE: u32 = 1;
pub const AT_RENAME_EXCHANGE: u32 = 2;
pub const AT_RENAME_WHITEOUT: u32 = 4;
pub const AT_EACCESS: u32 = 512;
pub const AT_REMOVEDIR: u32 = 512;
pub const AT_HANDLE_FID: u32 = 512;
pub const AT_HANDLE_MNT_ID_UNIQUE: u32 = 1;
pub const AT_HANDLE_CONNECTABLE: u32 = 2;
pub const EPOLL_CLOEXEC: u32 = 524288;
pub const EPOLL_CTL_ADD: u32 = 1;
pub const EPOLL_CTL_DEL: u32 = 2;
pub const EPOLL_CTL_MOD: u32 = 3;
pub const EPOLL_IOC_TYPE: u32 = 138;
pub const POSIX_FADV_NORMAL: u32 = 0;
pub const POSIX_FADV_RANDOM: u32 = 1;
pub const POSIX_FADV_SEQUENTIAL: u32 = 2;
pub const POSIX_FADV_WILLNEED: u32 = 3;
pub const POSIX_FADV_DONTNEED: u32 = 4;
pub const POSIX_FADV_NOREUSE: u32 = 5;
pub const FALLOC_FL_ALLOCATE_RANGE: u32 = 0;
pub const FALLOC_FL_KEEP_SIZE: u32 = 1;
pub const FALLOC_FL_PUNCH_HOLE: u32 = 2;
pub const FALLOC_FL_NO_HIDE_STALE: u32 = 4;
pub const FALLOC_FL_COLLAPSE_RANGE: u32 = 8;
pub const FALLOC_FL_ZERO_RANGE: u32 = 16;
pub const FALLOC_FL_INSERT_RANGE: u32 = 32;
pub const FALLOC_FL_UNSHARE_RANGE: u32 = 64;
pub const NR_OPEN: u32 = 1024;
pub const NGROUPS_MAX: u32 = 65536;
pub const ARG_MAX: u32 = 131072;
pub const LINK_MAX: u32 = 127;
pub const MAX_CANON: u32 = 255;
pub const MAX_INPUT: u32 = 255;
pub const NAME_MAX: u32 = 255;
pub const PATH_MAX: u32 = 4096;
pub const PIPE_BUF: u32 = 4096;
pub const XATTR_NAME_MAX: u32 = 255;
pub const XATTR_SIZE_MAX: u32 = 65536;
pub const XATTR_LIST_MAX: u32 = 65536;
pub const RTSIG_MAX: u32 = 32;
pub const _IOC_SIZEBITS: u32 = 13;
pub const _IOC_DIRBITS: u32 = 3;
pub const _IOC_NONE: u32 = 1;
pub const _IOC_READ: u32 = 2;
pub const _IOC_WRITE: u32 = 4;
pub const _IOC_NRBITS: u32 = 8;
pub const _IOC_TYPEBITS: u32 = 8;
pub const _IOC_NRMASK: u32 = 255;
pub const _IOC_TYPEMASK: u32 = 255;
pub const _IOC_SIZEMASK: u32 = 8191;
pub const _IOC_DIRMASK: u32 = 7;
pub const _IOC_NRSHIFT: u32 = 0;
pub const _IOC_TYPESHIFT: u32 = 8;
pub const _IOC_SIZESHIFT: u32 = 16;
pub const _IOC_DIRSHIFT: u32 = 29;
pub const IOC_IN: u32 = 2147483648;
pub const IOC_OUT: u32 = 1073741824;
pub const IOC_INOUT: u32 = 3221225472;
pub const IOCSIZE_MASK: u32 = 536805376;
pub const IOCSIZE_SHIFT: u32 = 16;
pub const FSCRYPT_POLICY_FLAGS_PAD_4: u32 = 0;
pub const FSCRYPT_POLICY_FLAGS_PAD_8: u32 = 1;
pub const FSCRYPT_POLICY_FLAGS_PAD_16: u32 = 2;
pub const FSCRYPT_POLICY_FLAGS_PAD_32: u32 = 3;
pub const FSCRYPT_POLICY_FLAGS_PAD_MASK: u32 = 3;
pub const FSCRYPT_POLICY_FLAG_DIRECT_KEY: u32 = 4;
pub const FSCRYPT_POLICY_FLAG_IV_INO_LBLK_64: u32 = 8;
pub const FSCRYPT_POLICY_FLAG_IV_INO_LBLK_32: u32 = 16;
pub const FSCRYPT_MODE_AES_256_XTS: u32 = 1;
pub const FSCRYPT_MODE_AES_256_CTS: u32 = 4;
pub const FSCRYPT_MODE_AES_128_CBC: u32 = 5;
pub const FSCRYPT_MODE_AES_128_CTS: u32 = 6;
pub const FSCRYPT_MODE_SM4_XTS: u32 = 7;
pub const FSCRYPT_MODE_SM4_CTS: u32 = 8;
pub const FSCRYPT_MODE_ADIANTUM: u32 = 9;
pub const FSCRYPT_MODE_AES_256_HCTR2: u32 = 10;
pub const FSCRYPT_POLICY_V1: u32 = 0;
pub const FSCRYPT_KEY_DESCRIPTOR_SIZE: u32 = 8;
pub const FSCRYPT_KEY_DESC_PREFIX: &[u8; 9] = b"fscrypt:\0";
pub const FSCRYPT_KEY_DESC_PREFIX_SIZE: u32 = 8;
pub const FSCRYPT_MAX_KEY_SIZE: u32 = 64;
pub const FSCRYPT_POLICY_V2: u32 = 2;
pub const FSCRYPT_KEY_IDENTIFIER_SIZE: u32 = 16;
pub const FSCRYPT_KEY_SPEC_TYPE_DESCRIPTOR: u32 = 1;
pub const FSCRYPT_KEY_SPEC_TYPE_IDENTIFIER: u32 = 2;
pub const FSCRYPT_KEY_REMOVAL_STATUS_FLAG_FILES_BUSY: u32 = 1;
pub const FSCRYPT_KEY_REMOVAL_STATUS_FLAG_OTHER_USERS: u32 = 2;
pub const FSCRYPT_KEY_STATUS_ABSENT: u32 = 1;
pub const FSCRYPT_KEY_STATUS_PRESENT: u32 = 2;
pub const FSCRYPT_KEY_STATUS_INCOMPLETELY_REMOVED: u32 = 3;
pub const FSCRYPT_KEY_STATUS_FLAG_ADDED_BY_SELF: u32 = 1;
pub const FS_KEY_DESCRIPTOR_SIZE: u32 = 8;
pub const FS_POLICY_FLAGS_PAD_4: u32 = 0;
pub const FS_POLICY_FLAGS_PAD_8: u32 = 1;
pub const FS_POLICY_FLAGS_PAD_16: u32 = 2;
pub const FS_POLICY_FLAGS_PAD_32: u32 = 3;
pub const FS_POLICY_FLAGS_PAD_MASK: u32 = 3;
pub const FS_POLICY_FLAG_DIRECT_KEY: u32 = 4;
pub const FS_POLICY_FLAGS_VALID: u32 = 7;
pub const FS_ENCRYPTION_MODE_INVALID: u32 = 0;
pub const FS_ENCRYPTION_MODE_AES_256_XTS: u32 = 1;
pub const FS_ENCRYPTION_MODE_AES_256_GCM: u32 = 2;
pub const FS_ENCRYPTION_MODE_AES_256_CBC: u32 = 3;
pub const FS_ENCRYPTION_MODE_AES_256_CTS: u32 = 4;
pub const FS_ENCRYPTION_MODE_AES_128_CBC: u32 = 5;
pub const FS_ENCRYPTION_MODE_AES_128_CTS: u32 = 6;
pub const FS_ENCRYPTION_MODE_ADIANTUM: u32 = 9;
pub const FS_KEY_DESC_PREFIX: &[u8; 9] = b"fscrypt:\0";
pub const FS_KEY_DESC_PREFIX_SIZE: u32 = 8;
pub const FS_MAX_KEY_SIZE: u32 = 64;
pub const MS_RDONLY: u32 = 1;
pub const MS_NOSUID: u32 = 2;
pub const MS_NODEV: u32 = 4;
pub const MS_NOEXEC: u32 = 8;
pub const MS_SYNCHRONOUS: u32 = 16;
pub const MS_REMOUNT: u32 = 32;
pub const MS_MANDLOCK: u32 = 64;
pub const MS_DIRSYNC: u32 = 128;
pub const MS_NOSYMFOLLOW: u32 = 256;
pub const MS_NOATIME: u32 = 1024;
pub const MS_NODIRATIME: u32 = 2048;
pub const MS_BIND: u32 = 4096;
pub const MS_MOVE: u32 = 8192;
pub const MS_REC: u32 = 16384;
pub const MS_VERBOSE: u32 = 32768;
pub const MS_SILENT: u32 = 32768;
pub const MS_POSIXACL: u32 = 65536;
pub const MS_UNBINDABLE: u32 = 131072;
pub const MS_PRIVATE: u32 = 262144;
pub const MS_SLAVE: u32 = 524288;
pub const MS_SHARED: u32 = 1048576;
pub const MS_RELATIME: u32 = 2097152;
pub const MS_KERNMOUNT: u32 = 4194304;
pub const MS_I_VERSION: u32 = 8388608;
pub const MS_STRICTATIME: u32 = 16777216;
pub const MS_LAZYTIME: u32 = 33554432;
pub const MS_SUBMOUNT: u32 = 67108864;
pub const MS_NOREMOTELOCK: u32 = 134217728;
pub const MS_NOSEC: u32 = 268435456;
pub const MS_BORN: u32 = 536870912;
pub const MS_ACTIVE: u32 = 1073741824;
pub const MS_NOUSER: u32 = 2147483648;
pub const MS_RMT_MASK: u32 = 41943121;
pub const MS_MGC_VAL: u32 = 3236757504;
pub const MS_MGC_MSK: u32 = 4294901760;
pub const OPEN_TREE_CLONE: u32 = 1;
pub const OPEN_TREE_CLOEXEC: u32 = 524288;
pub const MOVE_MOUNT_F_SYMLINKS: u32 = 1;
pub const MOVE_MOUNT_F_AUTOMOUNTS: u32 = 2;
pub const MOVE_MOUNT_F_EMPTY_PATH: u32 = 4;
pub const MOVE_MOUNT_T_SYMLINKS: u32 = 16;
pub const MOVE_MOUNT_T_AUTOMOUNTS: u32 = 32;
pub const MOVE_MOUNT_T_EMPTY_PATH: u32 = 64;
pub const MOVE_MOUNT_SET_GROUP: u32 = 256;
pub const MOVE_MOUNT_BENEATH: u32 = 512;
pub const MOVE_MOUNT__MASK: u32 = 887;
pub const FSOPEN_CLOEXEC: u32 = 1;
pub const FSPICK_CLOEXEC: u32 = 1;
pub const FSPICK_SYMLINK_NOFOLLOW: u32 = 2;
pub const FSPICK_NO_AUTOMOUNT: u32 = 4;
pub const FSPICK_EMPTY_PATH: u32 = 8;
pub const FSMOUNT_CLOEXEC: u32 = 1;
pub const MOUNT_ATTR_RDONLY: u32 = 1;
pub const MOUNT_ATTR_NOSUID: u32 = 2;
pub const MOUNT_ATTR_NODEV: u32 = 4;
pub const MOUNT_ATTR_NOEXEC: u32 = 8;
pub const MOUNT_ATTR__ATIME: u32 = 112;
pub const MOUNT_ATTR_RELATIME: u32 = 0;
pub const MOUNT_ATTR_NOATIME: u32 = 16;
pub const MOUNT_ATTR_STRICTATIME: u32 = 32;
pub const MOUNT_ATTR_NODIRATIME: u32 = 128;
pub const MOUNT_ATTR_IDMAP: u32 = 1048576;
pub const MOUNT_ATTR_NOSYMFOLLOW: u32 = 2097152;
pub const MOUNT_ATTR_SIZE_VER0: u32 = 32;
pub const MNT_ID_REQ_SIZE_VER0: u32 = 24;
pub const MNT_ID_REQ_SIZE_VER1: u32 = 32;
pub const STATMOUNT_SB_BASIC: u32 = 1;
pub const STATMOUNT_MNT_BASIC: u32 = 2;
pub const STATMOUNT_PROPAGATE_FROM: u32 = 4;
pub const STATMOUNT_MNT_ROOT: u32 = 8;
pub const STATMOUNT_MNT_POINT: u32 = 16;
pub const STATMOUNT_FS_TYPE: u32 = 32;
pub const STATMOUNT_MNT_NS_ID: u32 = 64;
pub const STATMOUNT_MNT_OPTS: u32 = 128;
pub const STATMOUNT_FS_SUBTYPE: u32 = 256;
pub const STATMOUNT_SB_SOURCE: u32 = 512;
pub const STATMOUNT_OPT_ARRAY: u32 = 1024;
pub const STATMOUNT_OPT_SEC_ARRAY: u32 = 2048;
pub const LSMT_ROOT: i32 = -1;
pub const LISTMOUNT_REVERSE: u32 = 1;
pub const INR_OPEN_CUR: u32 = 1024;
pub const INR_OPEN_MAX: u32 = 4096;
pub const BLOCK_SIZE_BITS: u32 = 10;
pub const BLOCK_SIZE: u32 = 1024;
pub const SEEK_SET: u32 = 0;
pub const SEEK_CUR: u32 = 1;
pub const SEEK_END: u32 = 2;
pub const SEEK_DATA: u32 = 3;
pub const SEEK_HOLE: u32 = 4;
pub const SEEK_MAX: u32 = 4;
pub const RENAME_NOREPLACE: u32 = 1;
pub const RENAME_EXCHANGE: u32 = 2;
pub const RENAME_WHITEOUT: u32 = 4;
pub const FILE_DEDUPE_RANGE_SAME: u32 = 0;
pub const FILE_DEDUPE_RANGE_DIFFERS: u32 = 1;
pub const NR_FILE: u32 = 8192;
pub const FS_XFLAG_REALTIME: u32 = 1;
pub const FS_XFLAG_PREALLOC: u32 = 2;
pub const FS_XFLAG_IMMUTABLE: u32 = 8;
pub const FS_XFLAG_APPEND: u32 = 16;
pub const FS_XFLAG_SYNC: u32 = 32;
pub const FS_XFLAG_NOATIME: u32 = 64;
pub const FS_XFLAG_NODUMP: u32 = 128;
pub const FS_XFLAG_RTINHERIT: u32 = 256;
pub const FS_XFLAG_PROJINHERIT: u32 = 512;
pub const FS_XFLAG_NOSYMLINKS: u32 = 1024;
pub const FS_XFLAG_EXTSIZE: u32 = 2048;
pub const FS_XFLAG_EXTSZINHERIT: u32 = 4096;
pub const FS_XFLAG_NODEFRAG: u32 = 8192;
pub const FS_XFLAG_FILESTREAM: u32 = 16384;
pub const FS_XFLAG_DAX: u32 = 32768;
pub const FS_XFLAG_COWEXTSIZE: u32 = 65536;
pub const FS_XFLAG_HASATTR: u32 = 2147483648;
pub const BMAP_IOCTL: u32 = 1;
pub const FSLABEL_MAX: u32 = 256;
pub const FS_SECRM_FL: u32 = 1;
pub const FS_UNRM_FL: u32 = 2;
pub const FS_COMPR_FL: u32 = 4;
pub const FS_SYNC_FL: u32 = 8;
pub const FS_IMMUTABLE_FL: u32 = 16;
pub const FS_APPEND_FL: u32 = 32;
pub const FS_NODUMP_FL: u32 = 64;
pub const FS_NOATIME_FL: u32 = 128;
pub const FS_DIRTY_FL: u32 = 256;
pub const FS_COMPRBLK_FL: u32 = 512;
pub const FS_NOCOMP_FL: u32 = 1024;
pub const FS_ENCRYPT_FL: u32 = 2048;
pub const FS_BTREE_FL: u32 = 4096;
pub const FS_INDEX_FL: u32 = 4096;
pub const FS_IMAGIC_FL: u32 = 8192;
pub const FS_JOURNAL_DATA_FL: u32 = 16384;
pub const FS_NOTAIL_FL: u32 = 32768;
pub const FS_DIRSYNC_FL: u32 = 65536;
pub const FS_TOPDIR_FL: u32 = 131072;
pub const FS_HUGE_FILE_FL: u32 = 262144;
pub const FS_EXTENT_FL: u32 = 524288;
pub const FS_VERITY_FL: u32 = 1048576;
pub const FS_EA_INODE_FL: u32 = 2097152;
pub const FS_EOFBLOCKS_FL: u32 = 4194304;
pub const FS_NOCOW_FL: u32 = 8388608;
pub const FS_DAX_FL: u32 = 33554432;
pub const FS_INLINE_DATA_FL: u32 = 268435456;
pub const FS_PROJINHERIT_FL: u32 = 536870912;
pub const FS_CASEFOLD_FL: u32 = 1073741824;
pub const FS_RESERVED_FL: u32 = 2147483648;
pub const FS_FL_USER_VISIBLE: u32 = 253951;
pub const FS_FL_USER_MODIFIABLE: u32 = 229631;
pub const SYNC_FILE_RANGE_WAIT_BEFORE: u32 = 1;
pub const SYNC_FILE_RANGE_WRITE: u32 = 2;
pub const SYNC_FILE_RANGE_WAIT_AFTER: u32 = 4;
pub const SYNC_FILE_RANGE_WRITE_AND_WAIT: u32 = 7;
pub const PROCFS_IOCTL_MAGIC: u8 = 102u8;
pub const PAGE_IS_WPALLOWED: u32 = 1;
pub const PAGE_IS_WRITTEN: u32 = 2;
pub const PAGE_IS_FILE: u32 = 4;
pub const PAGE_IS_PRESENT: u32 = 8;
pub const PAGE_IS_SWAPPED: u32 = 16;
pub const PAGE_IS_PFNZERO: u32 = 32;
pub const PAGE_IS_HUGE: u32 = 64;
pub const PAGE_IS_SOFT_DIRTY: u32 = 128;
pub const PM_SCAN_WP_MATCHING: u32 = 1;
pub const PM_SCAN_CHECK_WPASYNC: u32 = 2;
pub const FUTEX_WAIT: u32 = 0;
pub const FUTEX_WAKE: u32 = 1;
pub const FUTEX_FD: u32 = 2;
pub const FUTEX_REQUEUE: u32 = 3;
pub const FUTEX_CMP_REQUEUE: u32 = 4;
pub const FUTEX_WAKE_OP: u32 = 5;
pub const FUTEX_LOCK_PI: u32 = 6;
pub const FUTEX_UNLOCK_PI: u32 = 7;
pub const FUTEX_TRYLOCK_PI: u32 = 8;
pub const FUTEX_WAIT_BITSET: u32 = 9;
pub const FUTEX_WAKE_BITSET: u32 = 10;
pub const FUTEX_WAIT_REQUEUE_PI: u32 = 11;
pub const FUTEX_CMP_REQUEUE_PI: u32 = 12;
pub const FUTEX_LOCK_PI2: u32 = 13;
pub const FUTEX_PRIVATE_FLAG: u32 = 128;
pub const FUTEX_CLOCK_REALTIME: u32 = 256;
pub const FUTEX_CMD_MASK: i32 = -385;
pub const FUTEX_WAIT_PRIVATE: u32 = 128;
pub const FUTEX_WAKE_PRIVATE: u32 = 129;
pub const FUTEX_REQUEUE_PRIVATE: u32 = 131;
pub const FUTEX_CMP_REQUEUE_PRIVATE: u32 = 132;
pub const FUTEX_WAKE_OP_PRIVATE: u32 = 133;
pub const FUTEX_LOCK_PI_PRIVATE: u32 = 134;
pub const FUTEX_LOCK_PI2_PRIVATE: u32 = 141;
pub const FUTEX_UNLOCK_PI_PRIVATE: u32 = 135;
pub const FUTEX_TRYLOCK_PI_PRIVATE: u32 = 136;
pub const FUTEX_WAIT_BITSET_PRIVATE: u32 = 137;
pub const FUTEX_WAKE_BITSET_PRIVATE: u32 = 138;
pub const FUTEX_WAIT_REQUEUE_PI_PRIVATE: u32 = 139;
pub const FUTEX_CMP_REQUEUE_PI_PRIVATE: u32 = 140;
pub const FUTEX2_SIZE_U8: u32 = 0;
pub const FUTEX2_SIZE_U16: u32 = 1;
pub const FUTEX2_SIZE_U32: u32 = 2;
pub const FUTEX2_SIZE_U64: u32 = 3;
pub const FUTEX2_NUMA: u32 = 4;
pub const FUTEX2_PRIVATE: u32 = 128;
pub const FUTEX2_SIZE_MASK: u32 = 3;
pub const FUTEX_32: u32 = 2;
pub const FUTEX_WAITV_MAX: u32 = 128;
pub const FUTEX_WAITERS: u32 = 2147483648;
pub const FUTEX_OWNER_DIED: u32 = 1073741824;
pub const FUTEX_TID_MASK: u32 = 1073741823;
pub const ROBUST_LIST_LIMIT: u32 = 2048;
pub const FUTEX_BITSET_MATCH_ANY: u32 = 4294967295;
pub const FUTEX_OP_SET: u32 = 0;
pub const FUTEX_OP_ADD: u32 = 1;
pub const FUTEX_OP_OR: u32 = 2;
pub const FUTEX_OP_ANDN: u32 = 3;
pub const FUTEX_OP_XOR: u32 = 4;
pub const FUTEX_OP_OPARG_SHIFT: u32 = 8;
pub const FUTEX_OP_CMP_EQ: u32 = 0;
pub const FUTEX_OP_CMP_NE: u32 = 1;
pub const FUTEX_OP_CMP_LT: u32 = 2;
pub const FUTEX_OP_CMP_LE: u32 = 3;
pub const FUTEX_OP_CMP_GT: u32 = 4;
pub const FUTEX_OP_CMP_GE: u32 = 5;
pub const IN_ACCESS: u32 = 1;
pub const IN_MODIFY: u32 = 2;
pub const IN_ATTRIB: u32 = 4;
pub const IN_CLOSE_WRITE: u32 = 8;
pub const IN_CLOSE_NOWRITE: u32 = 16;
pub const IN_OPEN: u32 = 32;
pub const IN_MOVED_FROM: u32 = 64;
pub const IN_MOVED_TO: u32 = 128;
pub const IN_CREATE: u32 = 256;
pub const IN_DELETE: u32 = 512;
pub const IN_DELETE_SELF: u32 = 1024;
pub const IN_MOVE_SELF: u32 = 2048;
pub const IN_UNMOUNT: u32 = 8192;
pub const IN_Q_OVERFLOW: u32 = 16384;
pub const IN_IGNORED: u32 = 32768;
pub const IN_CLOSE: u32 = 24;
pub const IN_MOVE: u32 = 192;
pub const IN_ONLYDIR: u32 = 16777216;
pub const IN_DONT_FOLLOW: u32 = 33554432;
pub const IN_EXCL_UNLINK: u32 = 67108864;
pub const IN_MASK_CREATE: u32 = 268435456;
pub const IN_MASK_ADD: u32 = 536870912;
pub const IN_ISDIR: u32 = 1073741824;
pub const IN_ONESHOT: u32 = 2147483648;
pub const IN_ALL_EVENTS: u32 = 4095;
pub const IN_CLOEXEC: u32 = 524288;
pub const IN_NONBLOCK: u32 = 2048;
pub const ADFS_SUPER_MAGIC: u32 = 44533;
pub const AFFS_SUPER_MAGIC: u32 = 44543;
pub const AFS_SUPER_MAGIC: u32 = 1397113167;
pub const AUTOFS_SUPER_MAGIC: u32 = 391;
pub const CEPH_SUPER_MAGIC: u32 = 12805120;
pub const CODA_SUPER_MAGIC: u32 = 1937076805;
pub const CRAMFS_MAGIC: u32 = 684539205;
pub const CRAMFS_MAGIC_WEND: u32 = 1161678120;
pub const DEBUGFS_MAGIC: u32 = 1684170528;
pub const SECURITYFS_MAGIC: u32 = 1935894131;
pub const SELINUX_MAGIC: u32 = 4185718668;
pub const SMACK_MAGIC: u32 = 1128357203;
pub const RAMFS_MAGIC: u32 = 2240043254;
pub const TMPFS_MAGIC: u32 = 16914836;
pub const HUGETLBFS_MAGIC: u32 = 2508478710;
pub const SQUASHFS_MAGIC: u32 = 1936814952;
pub const ECRYPTFS_SUPER_MAGIC: u32 = 61791;
pub const EFS_SUPER_MAGIC: u32 = 4278867;
pub const EROFS_SUPER_MAGIC_V1: u32 = 3774210530;
pub const EXT2_SUPER_MAGIC: u32 = 61267;
pub const EXT3_SUPER_MAGIC: u32 = 61267;
pub const XENFS_SUPER_MAGIC: u32 = 2881100148;
pub const EXT4_SUPER_MAGIC: u32 = 61267;
pub const BTRFS_SUPER_MAGIC: u32 = 2435016766;
pub const NILFS_SUPER_MAGIC: u32 = 13364;
pub const F2FS_SUPER_MAGIC: u32 = 4076150800;
pub const HPFS_SUPER_MAGIC: u32 = 4187351113;
pub const ISOFS_SUPER_MAGIC: u32 = 38496;
pub const JFFS2_SUPER_MAGIC: u32 = 29366;
pub const XFS_SUPER_MAGIC: u32 = 1481003842;
pub const PSTOREFS_MAGIC: u32 = 1634035564;
pub const EFIVARFS_MAGIC: u32 = 3730735588;
pub const HOSTFS_SUPER_MAGIC: u32 = 12648430;
pub const OVERLAYFS_SUPER_MAGIC: u32 = 2035054128;
pub const FUSE_SUPER_MAGIC: u32 = 1702057286;
pub const BCACHEFS_SUPER_MAGIC: u32 = 3393526350;
pub const MINIX_SUPER_MAGIC: u32 = 4991;
pub const MINIX_SUPER_MAGIC2: u32 = 5007;
pub const MINIX2_SUPER_MAGIC: u32 = 9320;
pub const MINIX2_SUPER_MAGIC2: u32 = 9336;
pub const MINIX3_SUPER_MAGIC: u32 = 19802;
pub const MSDOS_SUPER_MAGIC: u32 = 19780;
pub const EXFAT_SUPER_MAGIC: u32 = 538032816;
pub const NCP_SUPER_MAGIC: u32 = 22092;
pub const NFS_SUPER_MAGIC: u32 = 26985;
pub const OCFS2_SUPER_MAGIC: u32 = 1952539503;
pub const OPENPROM_SUPER_MAGIC: u32 = 40865;
pub const QNX4_SUPER_MAGIC: u32 = 47;
pub const QNX6_SUPER_MAGIC: u32 = 1746473250;
pub const AFS_FS_MAGIC: u32 = 1799439955;
pub const REISERFS_SUPER_MAGIC: u32 = 1382369651;
pub const REISERFS_SUPER_MAGIC_STRING: &[u8; 9] = b"ReIsErFs\0";
pub const REISER2FS_SUPER_MAGIC_STRING: &[u8; 10] = b"ReIsEr2Fs\0";
pub const REISER2FS_JR_SUPER_MAGIC_STRING: &[u8; 10] = b"ReIsEr3Fs\0";
pub const SMB_SUPER_MAGIC: u32 = 20859;
pub const CIFS_SUPER_MAGIC: u32 = 4283649346;
pub const SMB2_SUPER_MAGIC: u32 = 4266872130;
pub const CGROUP_SUPER_MAGIC: u32 = 2613483;
pub const CGROUP2_SUPER_MAGIC: u32 = 1667723888;
pub const RDTGROUP_SUPER_MAGIC: u32 = 124082209;
pub const STACK_END_MAGIC: u32 = 1470918301;
pub const TRACEFS_MAGIC: u32 = 1953653091;
pub const V9FS_MAGIC: u32 = 16914839;
pub const BDEVFS_MAGIC: u32 = 1650746742;
pub const DAXFS_MAGIC: u32 = 1684300152;
pub const BINFMTFS_MAGIC: u32 = 1112100429;
pub const DEVPTS_SUPER_MAGIC: u32 = 7377;
pub const BINDERFS_SUPER_MAGIC: u32 = 1819242352;
pub const FUTEXFS_SUPER_MAGIC: u32 = 195894762;
pub const PIPEFS_MAGIC: u32 = 1346981957;
pub const PROC_SUPER_MAGIC: u32 = 40864;
pub const SOCKFS_MAGIC: u32 = 1397703499;
pub const SYSFS_MAGIC: u32 = 1650812274;
pub const USBDEVICE_SUPER_MAGIC: u32 = 40866;
pub const MTD_INODE_FS_MAGIC: u32 = 288389204;
pub const ANON_INODE_FS_MAGIC: u32 = 151263540;
pub const BTRFS_TEST_MAGIC: u32 = 1936880249;
pub const NSFS_MAGIC: u32 = 1853056627;
pub const BPF_FS_MAGIC: u32 = 3405662737;
pub const AAFS_MAGIC: u32 = 1513908720;
pub const ZONEFS_MAGIC: u32 = 1515144787;
pub const UDF_SUPER_MAGIC: u32 = 352400198;
pub const DMA_BUF_MAGIC: u32 = 1145913666;
pub const DEVMEM_MAGIC: u32 = 1162691661;
pub const SECRETMEM_MAGIC: u32 = 1397048141;
pub const PID_FS_MAGIC: u32 = 1346978886;
pub const PROT_READ: u32 = 1;
pub const PROT_WRITE: u32 = 2;
pub const PROT_EXEC: u32 = 4;
pub const PROT_SEM: u32 = 8;
pub const PROT_NONE: u32 = 0;
pub const PROT_GROWSDOWN: u32 = 16777216;
pub const PROT_GROWSUP: u32 = 33554432;
pub const MAP_TYPE: u32 = 15;
pub const MAP_FIXED: u32 = 16;
pub const MAP_ANONYMOUS: u32 = 32;
pub const MAP_POPULATE: u32 = 32768;
pub const MAP_NONBLOCK: u32 = 65536;
pub const MAP_STACK: u32 = 131072;
pub const MAP_HUGETLB: u32 = 262144;
pub const MAP_SYNC: u32 = 524288;
pub const MAP_FIXED_NOREPLACE: u32 = 1048576;
pub const MAP_UNINITIALIZED: u32 = 67108864;
pub const MLOCK_ONFAULT: u32 = 1;
pub const MS_ASYNC: u32 = 1;
pub const MS_INVALIDATE: u32 = 2;
pub const MS_SYNC: u32 = 4;
pub const MADV_NORMAL: u32 = 0;
pub const MADV_RANDOM: u32 = 1;
pub const MADV_SEQUENTIAL: u32 = 2;
pub const MADV_WILLNEED: u32 = 3;
pub const MADV_DONTNEED: u32 = 4;
pub const MADV_FREE: u32 = 8;
pub const MADV_REMOVE: u32 = 9;
pub const MADV_DONTFORK: u32 = 10;
pub const MADV_DOFORK: u32 = 11;
pub const MADV_HWPOISON: u32 = 100;
pub const MADV_SOFT_OFFLINE: u32 = 101;
pub const MADV_MERGEABLE: u32 = 12;
pub const MADV_UNMERGEABLE: u32 = 13;
pub const MADV_HUGEPAGE: u32 = 14;
pub const MADV_NOHUGEPAGE: u32 = 15;
pub const MADV_DONTDUMP: u32 = 16;
pub const MADV_DODUMP: u32 = 17;
pub const MADV_WIPEONFORK: u32 = 18;
pub const MADV_KEEPONFORK: u32 = 19;
pub const MADV_COLD: u32 = 20;
pub const MADV_PAGEOUT: u32 = 21;
pub const MADV_POPULATE_READ: u32 = 22;
pub const MADV_POPULATE_WRITE: u32 = 23;
pub const MADV_DONTNEED_LOCKED: u32 = 24;
pub const MADV_COLLAPSE: u32 = 25;
pub const MADV_GUARD_INSTALL: u32 = 102;
pub const MADV_GUARD_REMOVE: u32 = 103;
pub const MAP_FILE: u32 = 0;
pub const PKEY_DISABLE_ACCESS: u32 = 1;
pub const PKEY_DISABLE_WRITE: u32 = 2;
pub const PKEY_ACCESS_MASK: u32 = 3;
pub const PROT_SAO: u32 = 16;
pub const MAP_RENAME: u32 = 32;
pub const MAP_NORESERVE: u32 = 64;
pub const MAP_LOCKED: u32 = 128;
pub const MAP_GROWSDOWN: u32 = 256;
pub const MAP_DENYWRITE: u32 = 2048;
pub const MAP_EXECUTABLE: u32 = 4096;
pub const MCL_CURRENT: u32 = 8192;
pub const MCL_FUTURE: u32 = 16384;
pub const MCL_ONFAULT: u32 = 32768;
pub const PKEY_DISABLE_EXECUTE: u32 = 4;
pub const HUGETLB_FLAG_ENCODE_SHIFT: u32 = 26;
pub const HUGETLB_FLAG_ENCODE_MASK: u32 = 63;
pub const HUGETLB_FLAG_ENCODE_16KB: u32 = 939524096;
pub const HUGETLB_FLAG_ENCODE_64KB: u32 = 1073741824;
pub const HUGETLB_FLAG_ENCODE_512KB: u32 = 1275068416;
pub const HUGETLB_FLAG_ENCODE_1MB: u32 = 1342177280;
pub const HUGETLB_FLAG_ENCODE_2MB: u32 = 1409286144;
pub const HUGETLB_FLAG_ENCODE_8MB: u32 = 1543503872;
pub const HUGETLB_FLAG_ENCODE_16MB: u32 = 1610612736;
pub const HUGETLB_FLAG_ENCODE_32MB: u32 = 1677721600;
pub const HUGETLB_FLAG_ENCODE_256MB: u32 = 1879048192;
pub const HUGETLB_FLAG_ENCODE_512MB: u32 = 1946157056;
pub const HUGETLB_FLAG_ENCODE_1GB: u32 = 2013265920;
pub const HUGETLB_FLAG_ENCODE_2GB: u32 = 2080374784;
pub const HUGETLB_FLAG_ENCODE_16GB: u32 = 2281701376;
pub const MREMAP_MAYMOVE: u32 = 1;
pub const MREMAP_FIXED: u32 = 2;
pub const MREMAP_DONTUNMAP: u32 = 4;
pub const OVERCOMMIT_GUESS: u32 = 0;
pub const OVERCOMMIT_ALWAYS: u32 = 1;
pub const OVERCOMMIT_NEVER: u32 = 2;
pub const MAP_SHARED: u32 = 1;
pub const MAP_PRIVATE: u32 = 2;
pub const MAP_SHARED_VALIDATE: u32 = 3;
pub const MAP_DROPPABLE: u32 = 8;
pub const MAP_HUGE_SHIFT: u32 = 26;
pub const MAP_HUGE_MASK: u32 = 63;
pub const MAP_HUGE_16KB: u32 = 939524096;
pub const MAP_HUGE_64KB: u32 = 1073741824;
pub const MAP_HUGE_512KB: u32 = 1275068416;
pub const MAP_HUGE_1MB: u32 = 1342177280;
pub const MAP_HUGE_2MB: u32 = 1409286144;
pub const MAP_HUGE_8MB: u32 = 1543503872;
pub const MAP_HUGE_16MB: u32 = 1610612736;
pub const MAP_HUGE_32MB: u32 = 1677721600;
pub const MAP_HUGE_256MB: u32 = 1879048192;
pub const MAP_HUGE_512MB: u32 = 1946157056;
pub const MAP_HUGE_1GB: u32 = 2013265920;
pub const MAP_HUGE_2GB: u32 = 2080374784;
pub const MAP_HUGE_16GB: u32 = 2281701376;
pub const POLLIN: u32 = 1;
pub const POLLPRI: u32 = 2;
pub const POLLOUT: u32 = 4;
pub const POLLERR: u32 = 8;
pub const POLLHUP: u32 = 16;
pub const POLLNVAL: u32 = 32;
pub const POLLRDNORM: u32 = 64;
pub const POLLRDBAND: u32 = 128;
pub const POLLWRNORM: u32 = 256;
pub const POLLWRBAND: u32 = 512;
pub const POLLMSG: u32 = 1024;
pub const POLLREMOVE: u32 = 4096;
pub const POLLRDHUP: u32 = 8192;
pub const GRND_NONBLOCK: u32 = 1;
pub const GRND_RANDOM: u32 = 2;
pub const GRND_INSECURE: u32 = 4;
pub const LINUX_REBOOT_MAGIC1: u32 = 4276215469;
pub const LINUX_REBOOT_MAGIC2: u32 = 672274793;
pub const LINUX_REBOOT_MAGIC2A: u32 = 85072278;
pub const LINUX_REBOOT_MAGIC2B: u32 = 369367448;
pub const LINUX_REBOOT_MAGIC2C: u32 = 537993216;
pub const LINUX_REBOOT_CMD_RESTART: u32 = 19088743;
pub const LINUX_REBOOT_CMD_HALT: u32 = 3454992675;
pub const LINUX_REBOOT_CMD_CAD_ON: u32 = 2309737967;
pub const LINUX_REBOOT_CMD_CAD_OFF: u32 = 0;
pub const LINUX_REBOOT_CMD_POWER_OFF: u32 = 1126301404;
pub const LINUX_REBOOT_CMD_RESTART2: u32 = 2712847316;
pub const LINUX_REBOOT_CMD_SW_SUSPEND: u32 = 3489725666;
pub const LINUX_REBOOT_CMD_KEXEC: u32 = 1163412803;
pub const RUSAGE_SELF: u32 = 0;
pub const RUSAGE_CHILDREN: i32 = -1;
pub const RUSAGE_BOTH: i32 = -2;
pub const RUSAGE_THREAD: u32 = 1;
pub const RLIM64_INFINITY: i32 = -1;
pub const PRIO_MIN: i32 = -20;
pub const PRIO_MAX: u32 = 20;
pub const PRIO_PROCESS: u32 = 0;
pub const PRIO_PGRP: u32 = 1;
pub const PRIO_USER: u32 = 2;
pub const _STK_LIM: u32 = 8388608;
pub const MLOCK_LIMIT: u32 = 8388608;
pub const RLIMIT_CPU: u32 = 0;
pub const RLIMIT_FSIZE: u32 = 1;
pub const RLIMIT_DATA: u32 = 2;
pub const RLIMIT_STACK: u32 = 3;
pub const RLIMIT_CORE: u32 = 4;
pub const RLIMIT_RSS: u32 = 5;
pub const RLIMIT_NPROC: u32 = 6;
pub const RLIMIT_NOFILE: u32 = 7;
pub const RLIMIT_MEMLOCK: u32 = 8;
pub const RLIMIT_AS: u32 = 9;
pub const RLIMIT_LOCKS: u32 = 10;
pub const RLIMIT_SIGPENDING: u32 = 11;
pub const RLIMIT_MSGQUEUE: u32 = 12;
pub const RLIMIT_NICE: u32 = 13;
pub const RLIMIT_RTPRIO: u32 = 14;
pub const RLIMIT_RTTIME: u32 = 15;
pub const RLIM_NLIMITS: u32 = 16;
pub const RLIM_INFINITY: i32 = -1;
pub const CSIGNAL: u32 = 255;
pub const CLONE_VM: u32 = 256;
pub const CLONE_FS: u32 = 512;
pub const CLONE_FILES: u32 = 1024;
pub const CLONE_SIGHAND: u32 = 2048;
pub const CLONE_PIDFD: u32 = 4096;
pub const CLONE_PTRACE: u32 = 8192;
pub const CLONE_VFORK: u32 = 16384;
pub const CLONE_PARENT: u32 = 32768;
pub const CLONE_THREAD: u32 = 65536;
pub const CLONE_NEWNS: u32 = 131072;
pub const CLONE_SYSVSEM: u32 = 262144;
pub const CLONE_SETTLS: u32 = 524288;
pub const CLONE_PARENT_SETTID: u32 = 1048576;
pub const CLONE_CHILD_CLEARTID: u32 = 2097152;
pub const CLONE_DETACHED: u32 = 4194304;
pub const CLONE_UNTRACED: u32 = 8388608;
pub const CLONE_CHILD_SETTID: u32 = 16777216;
pub const CLONE_NEWCGROUP: u32 = 33554432;
pub const CLONE_NEWUTS: u32 = 67108864;
pub const CLONE_NEWIPC: u32 = 134217728;
pub const CLONE_NEWUSER: u32 = 268435456;
pub const CLONE_NEWPID: u32 = 536870912;
pub const CLONE_NEWNET: u32 = 1073741824;
pub const CLONE_IO: u32 = 2147483648;
pub const CLONE_CLEAR_SIGHAND: u64 = 4294967296;
pub const CLONE_INTO_CGROUP: u64 = 8589934592;
pub const CLONE_NEWTIME: u32 = 128;
pub const CLONE_ARGS_SIZE_VER0: u32 = 64;
pub const CLONE_ARGS_SIZE_VER1: u32 = 80;
pub const CLONE_ARGS_SIZE_VER2: u32 = 88;
pub const SCHED_NORMAL: u32 = 0;
pub const SCHED_FIFO: u32 = 1;
pub const SCHED_RR: u32 = 2;
pub const SCHED_BATCH: u32 = 3;
pub const SCHED_IDLE: u32 = 5;
pub const SCHED_DEADLINE: u32 = 6;
pub const SCHED_EXT: u32 = 7;
pub const SCHED_RESET_ON_FORK: u32 = 1073741824;
pub const SCHED_FLAG_RESET_ON_FORK: u32 = 1;
pub const SCHED_FLAG_RECLAIM: u32 = 2;
pub const SCHED_FLAG_DL_OVERRUN: u32 = 4;
pub const SCHED_FLAG_KEEP_POLICY: u32 = 8;
pub const SCHED_FLAG_KEEP_PARAMS: u32 = 16;
pub const SCHED_FLAG_UTIL_CLAMP_MIN: u32 = 32;
pub const SCHED_FLAG_UTIL_CLAMP_MAX: u32 = 64;
pub const SCHED_FLAG_KEEP_ALL: u32 = 24;
pub const SCHED_FLAG_UTIL_CLAMP: u32 = 96;
pub const SCHED_FLAG_ALL: u32 = 127;
pub const _NSIG: u32 = 64;
pub const _NSIG_BPW: u32 = 32;
pub const _NSIG_WORDS: u32 = 2;
pub const SIGHUP: u32 = 1;
pub const SIGINT: u32 = 2;
pub const SIGQUIT: u32 = 3;
pub const SIGILL: u32 = 4;
pub const SIGTRAP: u32 = 5;
pub const SIGABRT: u32 = 6;
pub const SIGIOT: u32 = 6;
pub const SIGBUS: u32 = 7;
pub const SIGFPE: u32 = 8;
pub const SIGKILL: u32 = 9;
pub const SIGUSR1: u32 = 10;
pub const SIGSEGV: u32 = 11;
pub const SIGUSR2: u32 = 12;
pub const SIGPIPE: u32 = 13;
pub const SIGALRM: u32 = 14;
pub const SIGTERM: u32 = 15;
pub const SIGSTKFLT: u32 = 16;
pub const SIGCHLD: u32 = 17;
pub const SIGCONT: u32 = 18;
pub const SIGSTOP: u32 = 19;
pub const SIGTSTP: u32 = 20;
pub const SIGTTIN: u32 = 21;
pub const SIGTTOU: u32 = 22;
pub const SIGURG: u32 = 23;
pub const SIGXCPU: u32 = 24;
pub const SIGXFSZ: u32 = 25;
pub const SIGVTALRM: u32 = 26;
pub const SIGPROF: u32 = 27;
pub const SIGWINCH: u32 = 28;
pub const SIGIO: u32 = 29;
pub const SIGPOLL: u32 = 29;
pub const SIGPWR: u32 = 30;
pub const SIGSYS: u32 = 31;
pub const SIGUNUSED: u32 = 31;
pub const SIGRTMIN: u32 = 32;
pub const SIGRTMAX: u32 = 64;
pub const SA_RESTORER: u32 = 67108864;
pub const MINSIGSTKSZ: u32 = 2048;
pub const SIGSTKSZ: u32 = 8192;
pub const SA_NOCLDSTOP: u32 = 1;
pub const SA_NOCLDWAIT: u32 = 2;
pub const SA_SIGINFO: u32 = 4;
pub const SA_UNSUPPORTED: u32 = 1024;
pub const SA_EXPOSE_TAGBITS: u32 = 2048;
pub const SA_ONSTACK: u32 = 134217728;
pub const SA_RESTART: u32 = 268435456;
pub const SA_NODEFER: u32 = 1073741824;
pub const SA_RESETHAND: u32 = 2147483648;
pub const SA_NOMASK: u32 = 1073741824;
pub const SA_ONESHOT: u32 = 2147483648;
pub const SIG_BLOCK: u32 = 0;
pub const SIG_UNBLOCK: u32 = 1;
pub const SIG_SETMASK: u32 = 2;
pub const SIG_DBG_SINGLE_STEPPING: u32 = 1;
pub const SIG_DBG_BRANCH_TRACING: u32 = 2;
pub const SI_MAX_SIZE: u32 = 128;
pub const SI_USER: u32 = 0;
pub const SI_KERNEL: u32 = 128;
pub const SI_QUEUE: i32 = -1;
pub const SI_TIMER: i32 = -2;
pub const SI_MESGQ: i32 = -3;
pub const SI_ASYNCIO: i32 = -4;
pub const SI_SIGIO: i32 = -5;
pub const SI_TKILL: i32 = -6;
pub const SI_DETHREAD: i32 = -7;
pub const SI_ASYNCNL: i32 = -60;
pub const ILL_ILLOPC: u32 = 1;
pub const ILL_ILLOPN: u32 = 2;
pub const ILL_ILLADR: u32 = 3;
pub const ILL_ILLTRP: u32 = 4;
pub const ILL_PRVOPC: u32 = 5;
pub const ILL_PRVREG: u32 = 6;
pub const ILL_COPROC: u32 = 7;
pub const ILL_BADSTK: u32 = 8;
pub const ILL_BADIADDR: u32 = 9;
pub const __ILL_BREAK: u32 = 10;
pub const __ILL_BNDMOD: u32 = 11;
pub const NSIGILL: u32 = 11;
pub const FPE_INTDIV: u32 = 1;
pub const FPE_INTOVF: u32 = 2;
pub const FPE_FLTDIV: u32 = 3;
pub const FPE_FLTOVF: u32 = 4;
pub const FPE_FLTUND: u32 = 5;
pub const FPE_FLTRES: u32 = 6;
pub const FPE_FLTINV: u32 = 7;
pub const FPE_FLTSUB: u32 = 8;
pub const __FPE_DECOVF: u32 = 9;
pub const __FPE_DECDIV: u32 = 10;
pub const __FPE_DECERR: u32 = 11;
pub const __FPE_INVASC: u32 = 12;
pub const __FPE_INVDEC: u32 = 13;
pub const FPE_FLTUNK: u32 = 14;
pub const FPE_CONDTRAP: u32 = 15;
pub const NSIGFPE: u32 = 15;
pub const SEGV_MAPERR: u32 = 1;
pub const SEGV_ACCERR: u32 = 2;
pub const SEGV_BNDERR: u32 = 3;
pub const SEGV_PKUERR: u32 = 4;
pub const SEGV_ACCADI: u32 = 5;
pub const SEGV_ADIDERR: u32 = 6;
pub const SEGV_ADIPERR: u32 = 7;
pub const SEGV_MTEAERR: u32 = 8;
pub const SEGV_MTESERR: u32 = 9;
pub const SEGV_CPERR: u32 = 10;
pub const NSIGSEGV: u32 = 10;
pub const BUS_ADRALN: u32 = 1;
pub const BUS_ADRERR: u32 = 2;
pub const BUS_OBJERR: u32 = 3;
pub const BUS_MCEERR_AR: u32 = 4;
pub const BUS_MCEERR_AO: u32 = 5;
pub const NSIGBUS: u32 = 5;
pub const TRAP_BRKPT: u32 = 1;
pub const TRAP_TRACE: u32 = 2;
pub const TRAP_BRANCH: u32 = 3;
pub const TRAP_HWBKPT: u32 = 4;
pub const TRAP_UNK: u32 = 5;
pub const TRAP_PERF: u32 = 6;
pub const NSIGTRAP: u32 = 6;
pub const TRAP_PERF_FLAG_ASYNC: u32 = 1;
pub const CLD_EXITED: u32 = 1;
pub const CLD_KILLED: u32 = 2;
pub const CLD_DUMPED: u32 = 3;
pub const CLD_TRAPPED: u32 = 4;
pub const CLD_STOPPED: u32 = 5;
pub const CLD_CONTINUED: u32 = 6;
pub const NSIGCHLD: u32 = 6;
pub const POLL_IN: u32 = 1;
pub const POLL_OUT: u32 = 2;
pub const POLL_MSG: u32 = 3;
pub const POLL_ERR: u32 = 4;
pub const POLL_PRI: u32 = 5;
pub const POLL_HUP: u32 = 6;
pub const NSIGPOLL: u32 = 6;
pub const SYS_SECCOMP: u32 = 1;
pub const SYS_USER_DISPATCH: u32 = 2;
pub const NSIGSYS: u32 = 2;
pub const EMT_TAGOVF: u32 = 1;
pub const NSIGEMT: u32 = 1;
pub const SIGEV_SIGNAL: u32 = 0;
pub const SIGEV_NONE: u32 = 1;
pub const SIGEV_THREAD: u32 = 2;
pub const SIGEV_THREAD_ID: u32 = 4;
pub const SIGEV_MAX_SIZE: u32 = 64;
pub const SS_ONSTACK: u32 = 1;
pub const SS_DISABLE: u32 = 2;
pub const SS_AUTODISARM: u32 = 2147483648;
pub const SS_FLAG_BITS: u32 = 2147483648;
pub const S_IFMT: u32 = 61440;
pub const S_IFSOCK: u32 = 49152;
pub const S_IFLNK: u32 = 40960;
pub const S_IFREG: u32 = 32768;
pub const S_IFBLK: u32 = 24576;
pub const S_IFDIR: u32 = 16384;
pub const S_IFCHR: u32 = 8192;
pub const S_IFIFO: u32 = 4096;
pub const S_ISUID: u32 = 2048;
pub const S_ISGID: u32 = 1024;
pub const S_ISVTX: u32 = 512;
pub const S_IRWXU: u32 = 448;
pub const S_IRUSR: u32 = 256;
pub const S_IWUSR: u32 = 128;
pub const S_IXUSR: u32 = 64;
pub const S_IRWXG: u32 = 56;
pub const S_IRGRP: u32 = 32;
pub const S_IWGRP: u32 = 16;
pub const S_IXGRP: u32 = 8;
pub const S_IRWXO: u32 = 7;
pub const S_IROTH: u32 = 4;
pub const S_IWOTH: u32 = 2;
pub const S_IXOTH: u32 = 1;
pub const STATX_TYPE: u32 = 1;
pub const STATX_MODE: u32 = 2;
pub const STATX_NLINK: u32 = 4;
pub const STATX_UID: u32 = 8;
pub const STATX_GID: u32 = 16;
pub const STATX_ATIME: u32 = 32;
pub const STATX_MTIME: u32 = 64;
pub const STATX_CTIME: u32 = 128;
pub const STATX_INO: u32 = 256;
pub const STATX_SIZE: u32 = 512;
pub const STATX_BLOCKS: u32 = 1024;
pub const STATX_BASIC_STATS: u32 = 2047;
pub const STATX_BTIME: u32 = 2048;
pub const STATX_MNT_ID: u32 = 4096;
pub const STATX_DIOALIGN: u32 = 8192;
pub const STATX_MNT_ID_UNIQUE: u32 = 16384;
pub const STATX_SUBVOL: u32 = 32768;
pub const STATX_WRITE_ATOMIC: u32 = 65536;
pub const STATX__RESERVED: u32 = 2147483648;
pub const STATX_ALL: u32 = 4095;
pub const STATX_ATTR_COMPRESSED: u32 = 4;
pub const STATX_ATTR_IMMUTABLE: u32 = 16;
pub const STATX_ATTR_APPEND: u32 = 32;
pub const STATX_ATTR_NODUMP: u32 = 64;
pub const STATX_ATTR_ENCRYPTED: u32 = 2048;
pub const STATX_ATTR_AUTOMOUNT: u32 = 4096;
pub const STATX_ATTR_MOUNT_ROOT: u32 = 8192;
pub const STATX_ATTR_VERITY: u32 = 1048576;
pub const STATX_ATTR_DAX: u32 = 2097152;
pub const STATX_ATTR_WRITE_ATOMIC: u32 = 4194304;
pub const TIOCM_LE: u32 = 1;
pub const TIOCM_DTR: u32 = 2;
pub const TIOCM_RTS: u32 = 4;
pub const TIOCM_ST: u32 = 8;
pub const TIOCM_SR: u32 = 16;
pub const TIOCM_CTS: u32 = 32;
pub const TIOCM_CAR: u32 = 64;
pub const TIOCM_RNG: u32 = 128;
pub const TIOCM_DSR: u32 = 256;
pub const TIOCM_CD: u32 = 64;
pub const TIOCM_RI: u32 = 128;
pub const TIOCM_OUT1: u32 = 8192;
pub const TIOCM_OUT2: u32 = 16384;
pub const TIOCM_LOOP: u32 = 32768;
pub const TIOCPKT_DATA: u32 = 0;
pub const TIOCPKT_FLUSHREAD: u32 = 1;
pub const TIOCPKT_FLUSHWRITE: u32 = 2;
pub const TIOCPKT_STOP: u32 = 4;
pub const TIOCPKT_START: u32 = 8;
pub const TIOCPKT_NOSTOP: u32 = 16;
pub const TIOCPKT_DOSTOP: u32 = 32;
pub const TIOCPKT_IOCTL: u32 = 64;
pub const TIOCSER_TEMT: u32 = 1;
pub const IGNBRK: u32 = 1;
pub const BRKINT: u32 = 2;
pub const IGNPAR: u32 = 4;
pub const PARMRK: u32 = 8;
pub const INPCK: u32 = 16;
pub const ISTRIP: u32 = 32;
pub const INLCR: u32 = 64;
pub const IGNCR: u32 = 128;
pub const ICRNL: u32 = 256;
pub const IXANY: u32 = 2048;
pub const OPOST: u32 = 1;
pub const OCRNL: u32 = 8;
pub const ONOCR: u32 = 16;
pub const ONLRET: u32 = 32;
pub const OFILL: u32 = 64;
pub const OFDEL: u32 = 128;
pub const B0: u32 = 0;
pub const B50: u32 = 1;
pub const B75: u32 = 2;
pub const B110: u32 = 3;
pub const B134: u32 = 4;
pub const B150: u32 = 5;
pub const B200: u32 = 6;
pub const B300: u32 = 7;
pub const B600: u32 = 8;
pub const B1200: u32 = 9;
pub const B1800: u32 = 10;
pub const B2400: u32 = 11;
pub const B4800: u32 = 12;
pub const B9600: u32 = 13;
pub const B19200: u32 = 14;
pub const B38400: u32 = 15;
pub const EXTA: u32 = 14;
pub const EXTB: u32 = 15;
pub const ADDRB: u32 = 536870912;
pub const CMSPAR: u32 = 1073741824;
pub const CRTSCTS: u32 = 2147483648;
pub const IBSHIFT: u32 = 16;
pub const TCOOFF: u32 = 0;
pub const TCOON: u32 = 1;
pub const TCIOFF: u32 = 2;
pub const TCION: u32 = 3;
pub const TCIFLUSH: u32 = 0;
pub const TCOFLUSH: u32 = 1;
pub const TCIOFLUSH: u32 = 2;
pub const NCCS: u32 = 19;
pub const VINTR: u32 = 0;
pub const VQUIT: u32 = 1;
pub const VERASE: u32 = 2;
pub const VKILL: u32 = 3;
pub const VEOF: u32 = 4;
pub const VMIN: u32 = 5;
pub const VEOL: u32 = 6;
pub const VTIME: u32 = 7;
pub const VEOL2: u32 = 8;
pub const VSWTC: u32 = 9;
pub const VWERASE: u32 = 10;
pub const VREPRINT: u32 = 11;
pub const VSUSP: u32 = 12;
pub const VSTART: u32 = 13;
pub const VSTOP: u32 = 14;
pub const VLNEXT: u32 = 15;
pub const VDISCARD: u32 = 16;
pub const IXON: u32 = 512;
pub const IXOFF: u32 = 1024;
pub const IUCLC: u32 = 4096;
pub const IMAXBEL: u32 = 8192;
pub const IUTF8: u32 = 16384;
pub const ONLCR: u32 = 2;
pub const OLCUC: u32 = 4;
pub const NLDLY: u32 = 768;
pub const NL0: u32 = 0;
pub const NL1: u32 = 256;
pub const NL2: u32 = 512;
pub const NL3: u32 = 768;
pub const TABDLY: u32 = 3072;
pub const TAB0: u32 = 0;
pub const TAB1: u32 = 1024;
pub const TAB2: u32 = 2048;
pub const TAB3: u32 = 3072;
pub const XTABS: u32 = 3072;
pub const CRDLY: u32 = 12288;
pub const CR0: u32 = 0;
pub const CR1: u32 = 4096;
pub const CR2: u32 = 8192;
pub const CR3: u32 = 12288;
pub const FFDLY: u32 = 16384;
pub const FF0: u32 = 0;
pub const FF1: u32 = 16384;
pub const BSDLY: u32 = 32768;
pub const BS0: u32 = 0;
pub const BS1: u32 = 32768;
pub const VTDLY: u32 = 65536;
pub const VT0: u32 = 0;
pub const VT1: u32 = 65536;
pub const CBAUD: u32 = 255;
pub const CBAUDEX: u32 = 0;
pub const BOTHER: u32 = 31;
pub const B57600: u32 = 16;
pub const B115200: u32 = 17;
pub const B230400: u32 = 18;
pub const B460800: u32 = 19;
pub const B500000: u32 = 20;
pub const B576000: u32 = 21;
pub const B921600: u32 = 22;
pub const ********: u32 = 23;
pub const ********: u32 = 24;
pub const ********: u32 = 25;
pub const ********: u32 = 26;
pub const ********: u32 = 27;
pub const ********: u32 = 28;
pub const ********: u32 = 29;
pub const ********: u32 = 30;
pub const CSIZE: u32 = 768;
pub const CS5: u32 = 0;
pub const CS6: u32 = 256;
pub const CS7: u32 = 512;
pub const CS8: u32 = 768;
pub const CSTOPB: u32 = 1024;
pub const CREAD: u32 = 2048;
pub const PARENB: u32 = 4096;
pub const PARODD: u32 = 8192;
pub const HUPCL: u32 = 16384;
pub const CLOCAL: u32 = 32768;
pub const CIBAUD: u32 = 16711680;
pub const ISIG: u32 = 128;
pub const ICANON: u32 = 256;
pub const XCASE: u32 = 16384;
pub const ECHO: u32 = 8;
pub const ECHOE: u32 = 2;
pub const ECHOK: u32 = 4;
pub const ECHONL: u32 = 16;
pub const NOFLSH: u32 = 2147483648;
pub const TOSTOP: u32 = 4194304;
pub const ECHOCTL: u32 = 64;
pub const ECHOPRT: u32 = 32;
pub const ECHOKE: u32 = 1;
pub const FLUSHO: u32 = 8388608;
pub const PENDIN: u32 = 536870912;
pub const IEXTEN: u32 = 1024;
pub const EXTPROC: u32 = 268435456;
pub const TCSANOW: u32 = 0;
pub const TCSADRAIN: u32 = 1;
pub const TCSAFLUSH: u32 = 2;
pub const NCC: u32 = 10;
pub const _VINTR: u32 = 0;
pub const _VQUIT: u32 = 1;
pub const _VERASE: u32 = 2;
pub const _VKILL: u32 = 3;
pub const _VEOF: u32 = 4;
pub const _VMIN: u32 = 5;
pub const _VEOL: u32 = 6;
pub const _VTIME: u32 = 7;
pub const _VEOL2: u32 = 8;
pub const _VSWTC: u32 = 9;
pub const ITIMER_REAL: u32 = 0;
pub const ITIMER_VIRTUAL: u32 = 1;
pub const ITIMER_PROF: u32 = 2;
pub const CLOCK_REALTIME: u32 = 0;
pub const CLOCK_MONOTONIC: u32 = 1;
pub const CLOCK_PROCESS_CPUTIME_ID: u32 = 2;
pub const CLOCK_THREAD_CPUTIME_ID: u32 = 3;
pub const CLOCK_MONOTONIC_RAW: u32 = 4;
pub const CLOCK_REALTIME_COARSE: u32 = 5;
pub const CLOCK_MONOTONIC_COARSE: u32 = 6;
pub const CLOCK_BOOTTIME: u32 = 7;
pub const CLOCK_REALTIME_ALARM: u32 = 8;
pub const CLOCK_BOOTTIME_ALARM: u32 = 9;
pub const CLOCK_SGI_CYCLE: u32 = 10;
pub const CLOCK_TAI: u32 = 11;
pub const MAX_CLOCKS: u32 = 16;
pub const CLOCKS_MASK: u32 = 1;
pub const CLOCKS_MONO: u32 = 1;
pub const TIMER_ABSTIME: u32 = 1;
pub const UIO_FASTIOV: u32 = 8;
pub const UIO_MAXIOV: u32 = 1024;
pub const __NR_restart_syscall: u32 = 0;
pub const __NR_exit: u32 = 1;
pub const __NR_fork: u32 = 2;
pub const __NR_read: u32 = 3;
pub const __NR_write: u32 = 4;
pub const __NR_open: u32 = 5;
pub const __NR_close: u32 = 6;
pub const __NR_waitpid: u32 = 7;
pub const __NR_creat: u32 = 8;
pub const __NR_link: u32 = 9;
pub const __NR_unlink: u32 = 10;
pub const __NR_execve: u32 = 11;
pub const __NR_chdir: u32 = 12;
pub const __NR_time: u32 = 13;
pub const __NR_mknod: u32 = 14;
pub const __NR_chmod: u32 = 15;
pub const __NR_lchown: u32 = 16;
pub const __NR_break: u32 = 17;
pub const __NR_oldstat: u32 = 18;
pub const __NR_lseek: u32 = 19;
pub const __NR_getpid: u32 = 20;
pub const __NR_mount: u32 = 21;
pub const __NR_umount: u32 = 22;
pub const __NR_setuid: u32 = 23;
pub const __NR_getuid: u32 = 24;
pub const __NR_stime: u32 = 25;
pub const __NR_ptrace: u32 = 26;
pub const __NR_alarm: u32 = 27;
pub const __NR_oldfstat: u32 = 28;
pub const __NR_pause: u32 = 29;
pub const __NR_utime: u32 = 30;
pub const __NR_stty: u32 = 31;
pub const __NR_gtty: u32 = 32;
pub const __NR_access: u32 = 33;
pub const __NR_nice: u32 = 34;
pub const __NR_ftime: u32 = 35;
pub const __NR_sync: u32 = 36;
pub const __NR_kill: u32 = 37;
pub const __NR_rename: u32 = 38;
pub const __NR_mkdir: u32 = 39;
pub const __NR_rmdir: u32 = 40;
pub const __NR_dup: u32 = 41;
pub const __NR_pipe: u32 = 42;
pub const __NR_times: u32 = 43;
pub const __NR_prof: u32 = 44;
pub const __NR_brk: u32 = 45;
pub const __NR_setgid: u32 = 46;
pub const __NR_getgid: u32 = 47;
pub const __NR_signal: u32 = 48;
pub const __NR_geteuid: u32 = 49;
pub const __NR_getegid: u32 = 50;
pub const __NR_acct: u32 = 51;
pub const __NR_umount2: u32 = 52;
pub const __NR_lock: u32 = 53;
pub const __NR_ioctl: u32 = 54;
pub const __NR_fcntl: u32 = 55;
pub const __NR_mpx: u32 = 56;
pub const __NR_setpgid: u32 = 57;
pub const __NR_ulimit: u32 = 58;
pub const __NR_oldolduname: u32 = 59;
pub const __NR_umask: u32 = 60;
pub const __NR_chroot: u32 = 61;
pub const __NR_ustat: u32 = 62;
pub const __NR_dup2: u32 = 63;
pub const __NR_getppid: u32 = 64;
pub const __NR_getpgrp: u32 = 65;
pub const __NR_setsid: u32 = 66;
pub const __NR_sigaction: u32 = 67;
pub const __NR_sgetmask: u32 = 68;
pub const __NR_ssetmask: u32 = 69;
pub const __NR_setreuid: u32 = 70;
pub const __NR_setregid: u32 = 71;
pub const __NR_sigsuspend: u32 = 72;
pub const __NR_sigpending: u32 = 73;
pub const __NR_sethostname: u32 = 74;
pub const __NR_setrlimit: u32 = 75;
pub const __NR_getrlimit: u32 = 76;
pub const __NR_getrusage: u32 = 77;
pub const __NR_gettimeofday: u32 = 78;
pub const __NR_settimeofday: u32 = 79;
pub const __NR_getgroups: u32 = 80;
pub const __NR_setgroups: u32 = 81;
pub const __NR_select: u32 = 82;
pub const __NR_symlink: u32 = 83;
pub const __NR_oldlstat: u32 = 84;
pub const __NR_readlink: u32 = 85;
pub const __NR_uselib: u32 = 86;
pub const __NR_swapon: u32 = 87;
pub const __NR_reboot: u32 = 88;
pub const __NR_readdir: u32 = 89;
pub const __NR_mmap: u32 = 90;
pub const __NR_munmap: u32 = 91;
pub const __NR_truncate: u32 = 92;
pub const __NR_ftruncate: u32 = 93;
pub const __NR_fchmod: u32 = 94;
pub const __NR_fchown: u32 = 95;
pub const __NR_getpriority: u32 = 96;
pub const __NR_setpriority: u32 = 97;
pub const __NR_profil: u32 = 98;
pub const __NR_statfs: u32 = 99;
pub const __NR_fstatfs: u32 = 100;
pub const __NR_ioperm: u32 = 101;
pub const __NR_socketcall: u32 = 102;
pub const __NR_syslog: u32 = 103;
pub const __NR_setitimer: u32 = 104;
pub const __NR_getitimer: u32 = 105;
pub const __NR_stat: u32 = 106;
pub const __NR_lstat: u32 = 107;
pub const __NR_fstat: u32 = 108;
pub const __NR_olduname: u32 = 109;
pub const __NR_iopl: u32 = 110;
pub const __NR_vhangup: u32 = 111;
pub const __NR_idle: u32 = 112;
pub const __NR_vm86: u32 = 113;
pub const __NR_wait4: u32 = 114;
pub const __NR_swapoff: u32 = 115;
pub const __NR_sysinfo: u32 = 116;
pub const __NR_ipc: u32 = 117;
pub const __NR_fsync: u32 = 118;
pub const __NR_sigreturn: u32 = 119;
pub const __NR_clone: u32 = 120;
pub const __NR_setdomainname: u32 = 121;
pub const __NR_uname: u32 = 122;
pub const __NR_modify_ldt: u32 = 123;
pub const __NR_adjtimex: u32 = 124;
pub const __NR_mprotect: u32 = 125;
pub const __NR_sigprocmask: u32 = 126;
pub const __NR_create_module: u32 = 127;
pub const __NR_init_module: u32 = 128;
pub const __NR_delete_module: u32 = 129;
pub const __NR_get_kernel_syms: u32 = 130;
pub const __NR_quotactl: u32 = 131;
pub const __NR_getpgid: u32 = 132;
pub const __NR_fchdir: u32 = 133;
pub const __NR_bdflush: u32 = 134;
pub const __NR_sysfs: u32 = 135;
pub const __NR_personality: u32 = 136;
pub const __NR_afs_syscall: u32 = 137;
pub const __NR_setfsuid: u32 = 138;
pub const __NR_setfsgid: u32 = 139;
pub const __NR__llseek: u32 = 140;
pub const __NR_getdents: u32 = 141;
pub const __NR__newselect: u32 = 142;
pub const __NR_flock: u32 = 143;
pub const __NR_msync: u32 = 144;
pub const __NR_readv: u32 = 145;
pub const __NR_writev: u32 = 146;
pub const __NR_getsid: u32 = 147;
pub const __NR_fdatasync: u32 = 148;
pub const __NR__sysctl: u32 = 149;
pub const __NR_mlock: u32 = 150;
pub const __NR_munlock: u32 = 151;
pub const __NR_mlockall: u32 = 152;
pub const __NR_munlockall: u32 = 153;
pub const __NR_sched_setparam: u32 = 154;
pub const __NR_sched_getparam: u32 = 155;
pub const __NR_sched_setscheduler: u32 = 156;
pub const __NR_sched_getscheduler: u32 = 157;
pub const __NR_sched_yield: u32 = 158;
pub const __NR_sched_get_priority_max: u32 = 159;
pub const __NR_sched_get_priority_min: u32 = 160;
pub const __NR_sched_rr_get_interval: u32 = 161;
pub const __NR_nanosleep: u32 = 162;
pub const __NR_mremap: u32 = 163;
pub const __NR_setresuid: u32 = 164;
pub const __NR_getresuid: u32 = 165;
pub const __NR_query_module: u32 = 166;
pub const __NR_poll: u32 = 167;
pub const __NR_nfsservctl: u32 = 168;
pub const __NR_setresgid: u32 = 169;
pub const __NR_getresgid: u32 = 170;
pub const __NR_prctl: u32 = 171;
pub const __NR_rt_sigreturn: u32 = 172;
pub const __NR_rt_sigaction: u32 = 173;
pub const __NR_rt_sigprocmask: u32 = 174;
pub const __NR_rt_sigpending: u32 = 175;
pub const __NR_rt_sigtimedwait: u32 = 176;
pub const __NR_rt_sigqueueinfo: u32 = 177;
pub const __NR_rt_sigsuspend: u32 = 178;
pub const __NR_pread64: u32 = 179;
pub const __NR_pwrite64: u32 = 180;
pub const __NR_chown: u32 = 181;
pub const __NR_getcwd: u32 = 182;
pub const __NR_capget: u32 = 183;
pub const __NR_capset: u32 = 184;
pub const __NR_sigaltstack: u32 = 185;
pub const __NR_sendfile: u32 = 186;
pub const __NR_getpmsg: u32 = 187;
pub const __NR_putpmsg: u32 = 188;
pub const __NR_vfork: u32 = 189;
pub const __NR_ugetrlimit: u32 = 190;
pub const __NR_readahead: u32 = 191;
pub const __NR_mmap2: u32 = 192;
pub const __NR_truncate64: u32 = 193;
pub const __NR_ftruncate64: u32 = 194;
pub const __NR_stat64: u32 = 195;
pub const __NR_lstat64: u32 = 196;
pub const __NR_fstat64: u32 = 197;
pub const __NR_pciconfig_read: u32 = 198;
pub const __NR_pciconfig_write: u32 = 199;
pub const __NR_pciconfig_iobase: u32 = 200;
pub const __NR_multiplexer: u32 = 201;
pub const __NR_getdents64: u32 = 202;
pub const __NR_pivot_root: u32 = 203;
pub const __NR_fcntl64: u32 = 204;
pub const __NR_madvise: u32 = 205;
pub const __NR_mincore: u32 = 206;
pub const __NR_gettid: u32 = 207;
pub const __NR_tkill: u32 = 208;
pub const __NR_setxattr: u32 = 209;
pub const __NR_lsetxattr: u32 = 210;
pub const __NR_fsetxattr: u32 = 211;
pub const __NR_getxattr: u32 = 212;
pub const __NR_lgetxattr: u32 = 213;
pub const __NR_fgetxattr: u32 = 214;
pub const __NR_listxattr: u32 = 215;
pub const __NR_llistxattr: u32 = 216;
pub const __NR_flistxattr: u32 = 217;
pub const __NR_removexattr: u32 = 218;
pub const __NR_lremovexattr: u32 = 219;
pub const __NR_fremovexattr: u32 = 220;
pub const __NR_futex: u32 = 221;
pub const __NR_sched_setaffinity: u32 = 222;
pub const __NR_sched_getaffinity: u32 = 223;
pub const __NR_tuxcall: u32 = 225;
pub const __NR_sendfile64: u32 = 226;
pub const __NR_io_setup: u32 = 227;
pub const __NR_io_destroy: u32 = 228;
pub const __NR_io_getevents: u32 = 229;
pub const __NR_io_submit: u32 = 230;
pub const __NR_io_cancel: u32 = 231;
pub const __NR_set_tid_address: u32 = 232;
pub const __NR_fadvise64: u32 = 233;
pub const __NR_exit_group: u32 = 234;
pub const __NR_lookup_dcookie: u32 = 235;
pub const __NR_epoll_create: u32 = 236;
pub const __NR_epoll_ctl: u32 = 237;
pub const __NR_epoll_wait: u32 = 238;
pub const __NR_remap_file_pages: u32 = 239;
pub const __NR_timer_create: u32 = 240;
pub const __NR_timer_settime: u32 = 241;
pub const __NR_timer_gettime: u32 = 242;
pub const __NR_timer_getoverrun: u32 = 243;
pub const __NR_timer_delete: u32 = 244;
pub const __NR_clock_settime: u32 = 245;
pub const __NR_clock_gettime: u32 = 246;
pub const __NR_clock_getres: u32 = 247;
pub const __NR_clock_nanosleep: u32 = 248;
pub const __NR_swapcontext: u32 = 249;
pub const __NR_tgkill: u32 = 250;
pub const __NR_utimes: u32 = 251;
pub const __NR_statfs64: u32 = 252;
pub const __NR_fstatfs64: u32 = 253;
pub const __NR_fadvise64_64: u32 = 254;
pub const __NR_rtas: u32 = 255;
pub const __NR_sys_debug_setcontext: u32 = 256;
pub const __NR_migrate_pages: u32 = 258;
pub const __NR_mbind: u32 = 259;
pub const __NR_get_mempolicy: u32 = 260;
pub const __NR_set_mempolicy: u32 = 261;
pub const __NR_mq_open: u32 = 262;
pub const __NR_mq_unlink: u32 = 263;
pub const __NR_mq_timedsend: u32 = 264;
pub const __NR_mq_timedreceive: u32 = 265;
pub const __NR_mq_notify: u32 = 266;
pub const __NR_mq_getsetattr: u32 = 267;
pub const __NR_kexec_load: u32 = 268;
pub const __NR_add_key: u32 = 269;
pub const __NR_request_key: u32 = 270;
pub const __NR_keyctl: u32 = 271;
pub const __NR_waitid: u32 = 272;
pub const __NR_ioprio_set: u32 = 273;
pub const __NR_ioprio_get: u32 = 274;
pub const __NR_inotify_init: u32 = 275;
pub const __NR_inotify_add_watch: u32 = 276;
pub const __NR_inotify_rm_watch: u32 = 277;
pub const __NR_spu_run: u32 = 278;
pub const __NR_spu_create: u32 = 279;
pub const __NR_pselect6: u32 = 280;
pub const __NR_ppoll: u32 = 281;
pub const __NR_unshare: u32 = 282;
pub const __NR_splice: u32 = 283;
pub const __NR_tee: u32 = 284;
pub const __NR_vmsplice: u32 = 285;
pub const __NR_openat: u32 = 286;
pub const __NR_mkdirat: u32 = 287;
pub const __NR_mknodat: u32 = 288;
pub const __NR_fchownat: u32 = 289;
pub const __NR_futimesat: u32 = 290;
pub const __NR_fstatat64: u32 = 291;
pub const __NR_unlinkat: u32 = 292;
pub const __NR_renameat: u32 = 293;
pub const __NR_linkat: u32 = 294;
pub const __NR_symlinkat: u32 = 295;
pub const __NR_readlinkat: u32 = 296;
pub const __NR_fchmodat: u32 = 297;
pub const __NR_faccessat: u32 = 298;
pub const __NR_get_robust_list: u32 = 299;
pub const __NR_set_robust_list: u32 = 300;
pub const __NR_move_pages: u32 = 301;
pub const __NR_getcpu: u32 = 302;
pub const __NR_epoll_pwait: u32 = 303;
pub const __NR_utimensat: u32 = 304;
pub const __NR_signalfd: u32 = 305;
pub const __NR_timerfd_create: u32 = 306;
pub const __NR_eventfd: u32 = 307;
pub const __NR_sync_file_range2: u32 = 308;
pub const __NR_fallocate: u32 = 309;
pub const __NR_subpage_prot: u32 = 310;
pub const __NR_timerfd_settime: u32 = 311;
pub const __NR_timerfd_gettime: u32 = 312;
pub const __NR_signalfd4: u32 = 313;
pub const __NR_eventfd2: u32 = 314;
pub const __NR_epoll_create1: u32 = 315;
pub const __NR_dup3: u32 = 316;
pub const __NR_pipe2: u32 = 317;
pub const __NR_inotify_init1: u32 = 318;
pub const __NR_perf_event_open: u32 = 319;
pub const __NR_preadv: u32 = 320;
pub const __NR_pwritev: u32 = 321;
pub const __NR_rt_tgsigqueueinfo: u32 = 322;
pub const __NR_fanotify_init: u32 = 323;
pub const __NR_fanotify_mark: u32 = 324;
pub const __NR_prlimit64: u32 = 325;
pub const __NR_socket: u32 = 326;
pub const __NR_bind: u32 = 327;
pub const __NR_connect: u32 = 328;
pub const __NR_listen: u32 = 329;
pub const __NR_accept: u32 = 330;
pub const __NR_getsockname: u32 = 331;
pub const __NR_getpeername: u32 = 332;
pub const __NR_socketpair: u32 = 333;
pub const __NR_send: u32 = 334;
pub const __NR_sendto: u32 = 335;
pub const __NR_recv: u32 = 336;
pub const __NR_recvfrom: u32 = 337;
pub const __NR_shutdown: u32 = 338;
pub const __NR_setsockopt: u32 = 339;
pub const __NR_getsockopt: u32 = 340;
pub const __NR_sendmsg: u32 = 341;
pub const __NR_recvmsg: u32 = 342;
pub const __NR_recvmmsg: u32 = 343;
pub const __NR_accept4: u32 = 344;
pub const __NR_name_to_handle_at: u32 = 345;
pub const __NR_open_by_handle_at: u32 = 346;
pub const __NR_clock_adjtime: u32 = 347;
pub const __NR_syncfs: u32 = 348;
pub const __NR_sendmmsg: u32 = 349;
pub const __NR_setns: u32 = 350;
pub const __NR_process_vm_readv: u32 = 351;
pub const __NR_process_vm_writev: u32 = 352;
pub const __NR_finit_module: u32 = 353;
pub const __NR_kcmp: u32 = 354;
pub const __NR_sched_setattr: u32 = 355;
pub const __NR_sched_getattr: u32 = 356;
pub const __NR_renameat2: u32 = 357;
pub const __NR_seccomp: u32 = 358;
pub const __NR_getrandom: u32 = 359;
pub const __NR_memfd_create: u32 = 360;
pub const __NR_bpf: u32 = 361;
pub const __NR_execveat: u32 = 362;
pub const __NR_switch_endian: u32 = 363;
pub const __NR_userfaultfd: u32 = 364;
pub const __NR_membarrier: u32 = 365;
pub const __NR_mlock2: u32 = 378;
pub const __NR_copy_file_range: u32 = 379;
pub const __NR_preadv2: u32 = 380;
pub const __NR_pwritev2: u32 = 381;
pub const __NR_kexec_file_load: u32 = 382;
pub const __NR_statx: u32 = 383;
pub const __NR_pkey_alloc: u32 = 384;
pub const __NR_pkey_free: u32 = 385;
pub const __NR_pkey_mprotect: u32 = 386;
pub const __NR_rseq: u32 = 387;
pub const __NR_io_pgetevents: u32 = 388;
pub const __NR_semget: u32 = 393;
pub const __NR_semctl: u32 = 394;
pub const __NR_shmget: u32 = 395;
pub const __NR_shmctl: u32 = 396;
pub const __NR_shmat: u32 = 397;
pub const __NR_shmdt: u32 = 398;
pub const __NR_msgget: u32 = 399;
pub const __NR_msgsnd: u32 = 400;
pub const __NR_msgrcv: u32 = 401;
pub const __NR_msgctl: u32 = 402;
pub const __NR_clock_gettime64: u32 = 403;
pub const __NR_clock_settime64: u32 = 404;
pub const __NR_clock_adjtime64: u32 = 405;
pub const __NR_clock_getres_time64: u32 = 406;
pub const __NR_clock_nanosleep_time64: u32 = 407;
pub const __NR_timer_gettime64: u32 = 408;
pub const __NR_timer_settime64: u32 = 409;
pub const __NR_timerfd_gettime64: u32 = 410;
pub const __NR_timerfd_settime64: u32 = 411;
pub const __NR_utimensat_time64: u32 = 412;
pub const __NR_pselect6_time64: u32 = 413;
pub const __NR_ppoll_time64: u32 = 414;
pub const __NR_io_pgetevents_time64: u32 = 416;
pub const __NR_recvmmsg_time64: u32 = 417;
pub const __NR_mq_timedsend_time64: u32 = 418;
pub const __NR_mq_timedreceive_time64: u32 = 419;
pub const __NR_semtimedop_time64: u32 = 420;
pub const __NR_rt_sigtimedwait_time64: u32 = 421;
pub const __NR_futex_time64: u32 = 422;
pub const __NR_sched_rr_get_interval_time64: u32 = 423;
pub const __NR_pidfd_send_signal: u32 = 424;
pub const __NR_io_uring_setup: u32 = 425;
pub const __NR_io_uring_enter: u32 = 426;
pub const __NR_io_uring_register: u32 = 427;
pub const __NR_open_tree: u32 = 428;
pub const __NR_move_mount: u32 = 429;
pub const __NR_fsopen: u32 = 430;
pub const __NR_fsconfig: u32 = 431;
pub const __NR_fsmount: u32 = 432;
pub const __NR_fspick: u32 = 433;
pub const __NR_pidfd_open: u32 = 434;
pub const __NR_clone3: u32 = 435;
pub const __NR_close_range: u32 = 436;
pub const __NR_openat2: u32 = 437;
pub const __NR_pidfd_getfd: u32 = 438;
pub const __NR_faccessat2: u32 = 439;
pub const __NR_process_madvise: u32 = 440;
pub const __NR_epoll_pwait2: u32 = 441;
pub const __NR_mount_setattr: u32 = 442;
pub const __NR_quotactl_fd: u32 = 443;
pub const __NR_landlock_create_ruleset: u32 = 444;
pub const __NR_landlock_add_rule: u32 = 445;
pub const __NR_landlock_restrict_self: u32 = 446;
pub const __NR_process_mrelease: u32 = 448;
pub const __NR_futex_waitv: u32 = 449;
pub const __NR_set_mempolicy_home_node: u32 = 450;
pub const __NR_cachestat: u32 = 451;
pub const __NR_fchmodat2: u32 = 452;
pub const __NR_map_shadow_stack: u32 = 453;
pub const __NR_futex_wake: u32 = 454;
pub const __NR_futex_wait: u32 = 455;
pub const __NR_futex_requeue: u32 = 456;
pub const __NR_statmount: u32 = 457;
pub const __NR_listmount: u32 = 458;
pub const __NR_lsm_get_self_attr: u32 = 459;
pub const __NR_lsm_set_self_attr: u32 = 460;
pub const __NR_lsm_list_modules: u32 = 461;
pub const __NR_mseal: u32 = 462;
pub const __NR_setxattrat: u32 = 463;
pub const __NR_getxattrat: u32 = 464;
pub const __NR_listxattrat: u32 = 465;
pub const __NR_removexattrat: u32 = 466;
pub const WNOHANG: u32 = 1;
pub const WUNTRACED: u32 = 2;
pub const WSTOPPED: u32 = 2;
pub const WEXITED: u32 = 4;
pub const WCONTINUED: u32 = 8;
pub const WNOWAIT: u32 = 16777216;
pub const __WNOTHREAD: u32 = 536870912;
pub const __WALL: u32 = 1073741824;
pub const __WCLONE: u32 = 2147483648;
pub const P_ALL: u32 = 0;
pub const P_PID: u32 = 1;
pub const P_PGID: u32 = 2;
pub const P_PIDFD: u32 = 3;
pub const XATTR_CREATE: u32 = 1;
pub const XATTR_REPLACE: u32 = 2;
pub const XATTR_OS2_PREFIX: &[u8; 5] = b"os2.\0";
pub const XATTR_MAC_OSX_PREFIX: &[u8; 5] = b"osx.\0";
pub const XATTR_BTRFS_PREFIX: &[u8; 7] = b"btrfs.\0";
pub const XATTR_HURD_PREFIX: &[u8; 5] = b"gnu.\0";
pub const XATTR_SECURITY_PREFIX: &[u8; 10] = b"security.\0";
pub const XATTR_SYSTEM_PREFIX: &[u8; 8] = b"system.\0";
pub const XATTR_TRUSTED_PREFIX: &[u8; 9] = b"trusted.\0";
pub const XATTR_USER_PREFIX: &[u8; 6] = b"user.\0";
pub const XATTR_EVM_SUFFIX: &[u8; 4] = b"evm\0";
pub const XATTR_NAME_EVM: &[u8; 13] = b"security.evm\0";
pub const XATTR_IMA_SUFFIX: &[u8; 4] = b"ima\0";
pub const XATTR_NAME_IMA: &[u8; 13] = b"security.ima\0";
pub const XATTR_SELINUX_SUFFIX: &[u8; 8] = b"selinux\0";
pub const XATTR_NAME_SELINUX: &[u8; 17] = b"security.selinux\0";
pub const XATTR_SMACK_SUFFIX: &[u8; 8] = b"SMACK64\0";
pub const XATTR_SMACK_IPIN: &[u8; 12] = b"SMACK64IPIN\0";
pub const XATTR_SMACK_IPOUT: &[u8; 13] = b"SMACK64IPOUT\0";
pub const XATTR_SMACK_EXEC: &[u8; 12] = b"SMACK64EXEC\0";
pub const XATTR_SMACK_TRANSMUTE: &[u8; 17] = b"SMACK64TRANSMUTE\0";
pub const XATTR_SMACK_MMAP: &[u8; 12] = b"SMACK64MMAP\0";
pub const XATTR_NAME_SMACK: &[u8; 17] = b"security.SMACK64\0";
pub const XATTR_NAME_SMACKIPIN: &[u8; 21] = b"security.SMACK64IPIN\0";
pub const XATTR_NAME_SMACKIPOUT: &[u8; 22] = b"security.SMACK64IPOUT\0";
pub const XATTR_NAME_SMACKEXEC: &[u8; 21] = b"security.SMACK64EXEC\0";
pub const XATTR_NAME_SMACKTRANSMUTE: &[u8; 26] = b"security.SMACK64TRANSMUTE\0";
pub const XATTR_NAME_SMACKMMAP: &[u8; 21] = b"security.SMACK64MMAP\0";
pub const XATTR_APPARMOR_SUFFIX: &[u8; 9] = b"apparmor\0";
pub const XATTR_NAME_APPARMOR: &[u8; 18] = b"security.apparmor\0";
pub const XATTR_CAPS_SUFFIX: &[u8; 11] = b"capability\0";
pub const XATTR_NAME_CAPS: &[u8; 20] = b"security.capability\0";
pub const XATTR_POSIX_ACL_ACCESS: &[u8; 17] = b"posix_acl_access\0";
pub const XATTR_NAME_POSIX_ACL_ACCESS: &[u8; 24] = b"system.posix_acl_access\0";
pub const XATTR_POSIX_ACL_DEFAULT: &[u8; 18] = b"posix_acl_default\0";
pub const XATTR_NAME_POSIX_ACL_DEFAULT: &[u8; 25] = b"system.posix_acl_default\0";
pub const MFD_CLOEXEC: u32 = 1;
pub const MFD_ALLOW_SEALING: u32 = 2;
pub const MFD_HUGETLB: u32 = 4;
pub const MFD_NOEXEC_SEAL: u32 = 8;
pub const MFD_EXEC: u32 = 16;
pub const MFD_HUGE_SHIFT: u32 = 26;
pub const MFD_HUGE_MASK: u32 = 63;
pub const MFD_HUGE_64KB: u32 = 1073741824;
pub const MFD_HUGE_512KB: u32 = 1275068416;
pub const MFD_HUGE_1MB: u32 = 1342177280;
pub const MFD_HUGE_2MB: u32 = 1409286144;
pub const MFD_HUGE_8MB: u32 = 1543503872;
pub const MFD_HUGE_16MB: u32 = 1610612736;
pub const MFD_HUGE_32MB: u32 = 1677721600;
pub const MFD_HUGE_256MB: u32 = 1879048192;
pub const MFD_HUGE_512MB: u32 = 1946157056;
pub const MFD_HUGE_1GB: u32 = 2013265920;
pub const MFD_HUGE_2GB: u32 = 2080374784;
pub const MFD_HUGE_16GB: u32 = 2281701376;
pub const TFD_TIMER_ABSTIME: u32 = 1;
pub const TFD_TIMER_CANCEL_ON_SET: u32 = 2;
pub const TFD_CLOEXEC: u32 = 524288;
pub const TFD_NONBLOCK: u32 = 2048;
pub const USERFAULTFD_IOC: u32 = 170;
pub const _UFFDIO_REGISTER: u32 = 0;
pub const _UFFDIO_UNREGISTER: u32 = 1;
pub const _UFFDIO_WAKE: u32 = 2;
pub const _UFFDIO_COPY: u32 = 3;
pub const _UFFDIO_ZEROPAGE: u32 = 4;
pub const _UFFDIO_MOVE: u32 = 5;
pub const _UFFDIO_WRITEPROTECT: u32 = 6;
pub const _UFFDIO_CONTINUE: u32 = 7;
pub const _UFFDIO_POISON: u32 = 8;
pub const _UFFDIO_API: u32 = 63;
pub const UFFDIO: u32 = 170;
pub const UFFD_EVENT_PAGEFAULT: u32 = 18;
pub const UFFD_EVENT_FORK: u32 = 19;
pub const UFFD_EVENT_REMAP: u32 = 20;
pub const UFFD_EVENT_REMOVE: u32 = 21;
pub const UFFD_EVENT_UNMAP: u32 = 22;
pub const UFFD_PAGEFAULT_FLAG_WRITE: u32 = 1;
pub const UFFD_PAGEFAULT_FLAG_WP: u32 = 2;
pub const UFFD_PAGEFAULT_FLAG_MINOR: u32 = 4;
pub const UFFD_FEATURE_PAGEFAULT_FLAG_WP: u32 = 1;
pub const UFFD_FEATURE_EVENT_FORK: u32 = 2;
pub const UFFD_FEATURE_EVENT_REMAP: u32 = 4;
pub const UFFD_FEATURE_EVENT_REMOVE: u32 = 8;
pub const UFFD_FEATURE_MISSING_HUGETLBFS: u32 = 16;
pub const UFFD_FEATURE_MISSING_SHMEM: u32 = 32;
pub const UFFD_FEATURE_EVENT_UNMAP: u32 = 64;
pub const UFFD_FEATURE_SIGBUS: u32 = 128;
pub const UFFD_FEATURE_THREAD_ID: u32 = 256;
pub const UFFD_FEATURE_MINOR_HUGETLBFS: u32 = 512;
pub const UFFD_FEATURE_MINOR_SHMEM: u32 = 1024;
pub const UFFD_FEATURE_EXACT_ADDRESS: u32 = 2048;
pub const UFFD_FEATURE_WP_HUGETLBFS_SHMEM: u32 = 4096;
pub const UFFD_FEATURE_WP_UNPOPULATED: u32 = 8192;
pub const UFFD_FEATURE_POISON: u32 = 16384;
pub const UFFD_FEATURE_WP_ASYNC: u32 = 32768;
pub const UFFD_FEATURE_MOVE: u32 = 65536;
pub const UFFD_USER_MODE_ONLY: u32 = 1;
pub const DT_UNKNOWN: u32 = 0;
pub const DT_FIFO: u32 = 1;
pub const DT_CHR: u32 = 2;
pub const DT_DIR: u32 = 4;
pub const DT_BLK: u32 = 6;
pub const DT_REG: u32 = 8;
pub const DT_LNK: u32 = 10;
pub const DT_SOCK: u32 = 12;
pub const STAT_HAVE_NSEC: u32 = 1;
pub const F_OK: u32 = 0;
pub const R_OK: u32 = 4;
pub const W_OK: u32 = 2;
pub const X_OK: u32 = 1;
pub const UTIME_NOW: u32 = 1073741823;
pub const UTIME_OMIT: u32 = 1073741822;
pub const MNT_FORCE: u32 = 1;
pub const MNT_DETACH: u32 = 2;
pub const MNT_EXPIRE: u32 = 4;
pub const UMOUNT_NOFOLLOW: u32 = 8;
pub const UMOUNT_UNUSED: u32 = 2147483648;
pub const STDIN_FILENO: u32 = 0;
pub const STDOUT_FILENO: u32 = 1;
pub const STDERR_FILENO: u32 = 2;
pub const RWF_HIPRI: u32 = 1;
pub const RWF_DSYNC: u32 = 2;
pub const RWF_SYNC: u32 = 4;
pub const RWF_NOWAIT: u32 = 8;
pub const RWF_APPEND: u32 = 16;
pub const EFD_SEMAPHORE: u32 = 1;
pub const EFD_CLOEXEC: u32 = 524288;
pub const EFD_NONBLOCK: u32 = 2048;
pub const EPOLLIN: u32 = 1;
pub const EPOLLPRI: u32 = 2;
pub const EPOLLOUT: u32 = 4;
pub const EPOLLERR: u32 = 8;
pub const EPOLLHUP: u32 = 16;
pub const EPOLLNVAL: u32 = 32;
pub const EPOLLRDNORM: u32 = 64;
pub const EPOLLRDBAND: u32 = 128;
pub const EPOLLWRNORM: u32 = 256;
pub const EPOLLWRBAND: u32 = 512;
pub const EPOLLMSG: u32 = 1024;
pub const EPOLLRDHUP: u32 = 8192;
pub const EPOLLEXCLUSIVE: u32 = 268435456;
pub const EPOLLWAKEUP: u32 = 536870912;
pub const EPOLLONESHOT: u32 = 1073741824;
pub const EPOLLET: u32 = 2147483648;
pub const TFD_SHARED_FCNTL_FLAGS: u32 = 526336;
pub const TFD_CREATE_FLAGS: u32 = 526336;
pub const TFD_SETTIME_FLAGS: u32 = 1;
pub const UFFD_API: u32 = 170;
pub const UFFDIO_REGISTER_MODE_MISSING: u32 = 1;
pub const UFFDIO_REGISTER_MODE_WP: u32 = 2;
pub const UFFDIO_REGISTER_MODE_MINOR: u32 = 4;
pub const UFFDIO_COPY_MODE_DONTWAKE: u32 = 1;
pub const UFFDIO_COPY_MODE_WP: u32 = 2;
pub const UFFDIO_ZEROPAGE_MODE_DONTWAKE: u32 = 1;
pub const SPLICE_F_MOVE: u32 = 1;
pub const SPLICE_F_NONBLOCK: u32 = 2;
pub const SPLICE_F_MORE: u32 = 4;
pub const SPLICE_F_GIFT: u32 = 8;
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum fsconfig_command {
FSCONFIG_SET_FLAG = 0,
FSCONFIG_SET_STRING = 1,
FSCONFIG_SET_BINARY = 2,
FSCONFIG_SET_PATH = 3,
FSCONFIG_SET_PATH_EMPTY = 4,
FSCONFIG_SET_FD = 5,
FSCONFIG_CMD_CREATE = 6,
FSCONFIG_CMD_RECONFIGURE = 7,
FSCONFIG_CMD_CREATE_EXCL = 8,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum procmap_query_flags {
PROCMAP_QUERY_VMA_READABLE = 1,
PROCMAP_QUERY_VMA_WRITABLE = 2,
PROCMAP_QUERY_VMA_EXECUTABLE = 4,
PROCMAP_QUERY_VMA_SHARED = 8,
PROCMAP_QUERY_COVERING_OR_NEXT_VMA = 16,
PROCMAP_QUERY_FILE_BACKED_VMA = 32,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum membarrier_cmd {
MEMBARRIER_CMD_QUERY = 0,
MEMBARRIER_CMD_GLOBAL = 1,
MEMBARRIER_CMD_GLOBAL_EXPEDITED = 2,
MEMBARRIER_CMD_REGISTER_GLOBAL_EXPEDITED = 4,
MEMBARRIER_CMD_PRIVATE_EXPEDITED = 8,
MEMBARRIER_CMD_REGISTER_PRIVATE_EXPEDITED = 16,
MEMBARRIER_CMD_PRIVATE_EXPEDITED_SYNC_CORE = 32,
MEMBARRIER_CMD_REGISTER_PRIVATE_EXPEDITED_SYNC_CORE = 64,
MEMBARRIER_CMD_PRIVATE_EXPEDITED_RSEQ = 128,
MEMBARRIER_CMD_REGISTER_PRIVATE_EXPEDITED_RSEQ = 256,
MEMBARRIER_CMD_GET_REGISTRATIONS = 512,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum membarrier_cmd_flag {
MEMBARRIER_CMD_FLAG_CPU = 1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union fscrypt_get_policy_ex_arg__bindgen_ty_1 {
pub version: __u8,
pub v1: fscrypt_policy_v1,
pub v2: fscrypt_policy_v2,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union fscrypt_key_specifier__bindgen_ty_1 {
pub __reserved: [__u8; 32usize],
pub descriptor: [__u8; 8usize],
pub identifier: [__u8; 16usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union sigval {
pub sival_int: crate::ctypes::c_int,
pub sival_ptr: *mut crate::ctypes::c_void,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union __sifields {
pub _kill: __sifields__bindgen_ty_1,
pub _timer: __sifields__bindgen_ty_2,
pub _rt: __sifields__bindgen_ty_3,
pub _sigchld: __sifields__bindgen_ty_4,
pub _sigfault: __sifields__bindgen_ty_5,
pub _sigpoll: __sifields__bindgen_ty_6,
pub _sigsys: __sifields__bindgen_ty_7,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union __sifields__bindgen_ty_5__bindgen_ty_1 {
pub _trapno: crate::ctypes::c_int,
pub _addr_lsb: crate::ctypes::c_short,
pub _addr_bnd: __sifields__bindgen_ty_5__bindgen_ty_1__bindgen_ty_1,
pub _addr_pkey: __sifields__bindgen_ty_5__bindgen_ty_1__bindgen_ty_2,
pub _perf: __sifields__bindgen_ty_5__bindgen_ty_1__bindgen_ty_3,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union siginfo__bindgen_ty_1 {
pub __bindgen_anon_1: siginfo__bindgen_ty_1__bindgen_ty_1,
pub _si_pad: [crate::ctypes::c_int; 32usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union sigevent__bindgen_ty_1 {
pub _pad: [crate::ctypes::c_int; 13usize],
pub _tid: crate::ctypes::c_int,
pub _sigev_thread: sigevent__bindgen_ty_1__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union uffd_msg__bindgen_ty_1 {
pub pagefault: uffd_msg__bindgen_ty_1__bindgen_ty_1,
pub fork: uffd_msg__bindgen_ty_1__bindgen_ty_2,
pub remap: uffd_msg__bindgen_ty_1__bindgen_ty_3,
pub remove: uffd_msg__bindgen_ty_1__bindgen_ty_4,
pub reserved: uffd_msg__bindgen_ty_1__bindgen_ty_5,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union uffd_msg__bindgen_ty_1__bindgen_ty_1__bindgen_ty_1 {
pub ptid: __u32,
}
impl<Storage> __BindgenBitfieldUnit<Storage> {
#[inline]
pub const fn new(storage: Storage) -> Self {
Self { storage }
}
}
impl<Storage> __BindgenBitfieldUnit<Storage>
where
Storage: AsRef<[u8]> + AsMut<[u8]>,
{
#[inline]
fn extract_bit(byte: u8, index: usize) -> bool {
let bit_index = if cfg!(target_endian = "big") { 7 - (index % 8) } else { index % 8 };
let mask = 1 << bit_index;
byte & mask == mask
}
#[inline]
pub fn get_bit(&self, index: usize) -> bool {
debug_assert!(index / 8 < self.storage.as_ref().len());
let byte_index = index / 8;
let byte = self.storage.as_ref()[byte_index];
Self::extract_bit(byte, index)
}
#[inline]
pub unsafe fn raw_get_bit(this: *const Self, index: usize) -> bool {
debug_assert!(index / 8 < core::mem::size_of::<Storage>());
let byte_index = index / 8;
let byte = *(core::ptr::addr_of!((*this).storage) as *const u8).offset(byte_index as isize);
Self::extract_bit(byte, index)
}
#[inline]
fn change_bit(byte: u8, index: usize, val: bool) -> u8 {
let bit_index = if cfg!(target_endian = "big") { 7 - (index % 8) } else { index % 8 };
let mask = 1 << bit_index;
if val {
byte | mask
} else {
byte & !mask
}
}
#[inline]
pub fn set_bit(&mut self, index: usize, val: bool) {
debug_assert!(index / 8 < self.storage.as_ref().len());
let byte_index = index / 8;
let byte = &mut self.storage.as_mut()[byte_index];
*byte = Self::change_bit(*byte, index, val);
}
#[inline]
pub unsafe fn raw_set_bit(this: *mut Self, index: usize, val: bool) {
debug_assert!(index / 8 < core::mem::size_of::<Storage>());
let byte_index = index / 8;
let byte = (core::ptr::addr_of_mut!((*this).storage) as *mut u8).offset(byte_index as isize);
*byte = Self::change_bit(*byte, index, val);
}
#[inline]
pub fn get(&self, bit_offset: usize, bit_width: u8) -> u64 {
debug_assert!(bit_width <= 64);
debug_assert!(bit_offset / 8 < self.storage.as_ref().len());
debug_assert!((bit_offset + (bit_width as usize)) / 8 <= self.storage.as_ref().len());
let mut val = 0;
for i in 0..(bit_width as usize) {
if self.get_bit(i + bit_offset) {
let index = if cfg!(target_endian = "big") { bit_width as usize - 1 - i } else { i };
val |= 1 << index;
}
}
val
}
#[inline]
pub unsafe fn raw_get(this: *const Self, bit_offset: usize, bit_width: u8) -> u64 {
debug_assert!(bit_width <= 64);
debug_assert!(bit_offset / 8 < core::mem::size_of::<Storage>());
debug_assert!((bit_offset + (bit_width as usize)) / 8 <= core::mem::size_of::<Storage>());
let mut val = 0;
for i in 0..(bit_width as usize) {
if Self::raw_get_bit(this, i + bit_offset) {
let index = if cfg!(target_endian = "big") { bit_width as usize - 1 - i } else { i };
val |= 1 << index;
}
}
val
}
#[inline]
pub fn set(&mut self, bit_offset: usize, bit_width: u8, val: u64) {
debug_assert!(bit_width <= 64);
debug_assert!(bit_offset / 8 < self.storage.as_ref().len());
debug_assert!((bit_offset + (bit_width as usize)) / 8 <= self.storage.as_ref().len());
for i in 0..(bit_width as usize) {
let mask = 1 << i;
let val_bit_is_set = val & mask == mask;
let index = if cfg!(target_endian = "big") { bit_width as usize - 1 - i } else { i };
self.set_bit(index + bit_offset, val_bit_is_set);
}
}
#[inline]
pub unsafe fn raw_set(this: *mut Self, bit_offset: usize, bit_width: u8, val: u64) {
debug_assert!(bit_width <= 64);
debug_assert!(bit_offset / 8 < core::mem::size_of::<Storage>());
debug_assert!((bit_offset + (bit_width as usize)) / 8 <= core::mem::size_of::<Storage>());
for i in 0..(bit_width as usize) {
let mask = 1 << i;
let val_bit_is_set = val & mask == mask;
let index = if cfg!(target_endian = "big") { bit_width as usize - 1 - i } else { i };
Self::raw_set_bit(this, index + bit_offset, val_bit_is_set);
}
}
}
impl<T> __IncompleteArrayField<T> {
#[inline]
pub const fn new() -> Self {
__IncompleteArrayField(::core::marker::PhantomData, [])
}
#[inline]
pub fn as_ptr(&self) -> *const T {
self as *const _ as *const T
}
#[inline]
pub fn as_mut_ptr(&mut self) -> *mut T {
self as *mut _ as *mut T
}
#[inline]
pub unsafe fn as_slice(&self, len: usize) -> &[T] {
::core::slice::from_raw_parts(self.as_ptr(), len)
}
#[inline]
pub unsafe fn as_mut_slice(&mut self, len: usize) -> &mut [T] {
::core::slice::from_raw_parts_mut(self.as_mut_ptr(), len)
}
}
impl<T> ::core::fmt::Debug for __IncompleteArrayField<T> {
fn fmt(&self, fmt: &mut ::core::fmt::Formatter<'_>) -> ::core::fmt::Result {
fmt.write_str("__IncompleteArrayField")
}
}
impl membarrier_cmd {
pub const MEMBARRIER_CMD_SHARED: membarrier_cmd = membarrier_cmd::MEMBARRIER_CMD_GLOBAL;
}
impl user_desc {
#[inline]
pub fn seg_32bit(&self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u32) }
}
#[inline]
pub fn set_seg_32bit(&mut self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(0usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn seg_32bit_raw(this: *const Self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 0usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_seg_32bit_raw(this: *mut Self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 0usize, 1u8, val as u64)
}
}
#[inline]
pub fn contents(&self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(self._bitfield_1.get(1usize, 2u8) as u32) }
}
#[inline]
pub fn set_contents(&mut self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(1usize, 2u8, val as u64)
}
}
#[inline]
pub unsafe fn contents_raw(this: *const Self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 1usize, 2u8) as u32) }
}
#[inline]
pub unsafe fn set_contents_raw(this: *mut Self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 1usize, 2u8, val as u64)
}
}
#[inline]
pub fn read_exec_only(&self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(self._bitfield_1.get(3usize, 1u8) as u32) }
}
#[inline]
pub fn set_read_exec_only(&mut self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(3usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn read_exec_only_raw(this: *const Self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 3usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_read_exec_only_raw(this: *mut Self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 3usize, 1u8, val as u64)
}
}
#[inline]
pub fn limit_in_pages(&self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(self._bitfield_1.get(4usize, 1u8) as u32) }
}
#[inline]
pub fn set_limit_in_pages(&mut self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(4usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn limit_in_pages_raw(this: *const Self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 4usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_limit_in_pages_raw(this: *mut Self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 4usize, 1u8, val as u64)
}
}
#[inline]
pub fn seg_not_present(&self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(self._bitfield_1.get(5usize, 1u8) as u32) }
}
#[inline]
pub fn set_seg_not_present(&mut self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(5usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn seg_not_present_raw(this: *const Self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 5usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_seg_not_present_raw(this: *mut Self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 5usize, 1u8, val as u64)
}
}
#[inline]
pub fn useable(&self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(self._bitfield_1.get(6usize, 1u8) as u32) }
}
#[inline]
pub fn set_useable(&mut self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(6usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn useable_raw(this: *const Self) -> crate::ctypes::c_uint {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 6usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_useable_raw(this: *mut Self, val: crate::ctypes::c_uint) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 6usize, 1u8, val as u64)
}
}
#[inline]
pub fn new_bitfield_1(seg_32bit: crate::ctypes::c_uint, contents: crate::ctypes::c_uint, read_exec_only: crate::ctypes::c_uint, limit_in_pages: crate::ctypes::c_uint, seg_not_present: crate::ctypes::c_uint, useable: crate::ctypes::c_uint) -> __BindgenBitfieldUnit<[u8; 1usize]> {
let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
__bindgen_bitfield_unit.set(0usize, 1u8, {
let seg_32bit: u32 = unsafe { ::core::mem::transmute(seg_32bit) };
seg_32bit as u64
});
__bindgen_bitfield_unit.set(1usize, 2u8, {
let contents: u32 = unsafe { ::core::mem::transmute(contents) };
contents as u64
});
__bindgen_bitfield_unit.set(3usize, 1u8, {
let read_exec_only: u32 = unsafe { ::core::mem::transmute(read_exec_only) };
read_exec_only as u64
});
__bindgen_bitfield_unit.set(4usize, 1u8, {
let limit_in_pages: u32 = unsafe { ::core::mem::transmute(limit_in_pages) };
limit_in_pages as u64
});
__bindgen_bitfield_unit.set(5usize, 1u8, {
let seg_not_present: u32 = unsafe { ::core::mem::transmute(seg_not_present) };
seg_not_present as u64
});
__bindgen_bitfield_unit.set(6usize, 1u8, {
let useable: u32 = unsafe { ::core::mem::transmute(useable) };
useable as u64
});
__bindgen_bitfield_unit
}
}
