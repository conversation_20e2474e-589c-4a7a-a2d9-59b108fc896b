/* automatically generated by rust-bindgen 0.71.1 */

pub type __s8 = crate::ctypes::c_schar;
pub type __u8 = crate::ctypes::c_uchar;
pub type __s16 = crate::ctypes::c_short;
pub type __u16 = crate::ctypes::c_ushort;
pub type __s32 = crate::ctypes::c_int;
pub type __u32 = crate::ctypes::c_uint;
pub type __s64 = crate::ctypes::c_longlong;
pub type __u64 = crate::ctypes::c_ulonglong;
pub type __kernel_key_t = crate::ctypes::c_int;
pub type __kernel_mqd_t = crate::ctypes::c_int;
pub type __kernel_old_uid_t = crate::ctypes::c_ushort;
pub type __kernel_old_gid_t = crate::ctypes::c_ushort;
pub type __kernel_suseconds_t = crate::ctypes::c_int;
pub type __kernel_long_t = crate::ctypes::c_long;
pub type __kernel_ulong_t = crate::ctypes::c_ulong;
pub type __kernel_ino_t = __kernel_ulong_t;
pub type __kernel_mode_t = crate::ctypes::c_uint;
pub type __kernel_pid_t = crate::ctypes::c_int;
pub type __kernel_ipc_pid_t = crate::ctypes::c_int;
pub type __kernel_uid_t = crate::ctypes::c_uint;
pub type __kernel_gid_t = crate::ctypes::c_uint;
pub type __kernel_daddr_t = crate::ctypes::c_int;
pub type __kernel_uid32_t = crate::ctypes::c_uint;
pub type __kernel_gid32_t = crate::ctypes::c_uint;
pub type __kernel_old_dev_t = crate::ctypes::c_uint;
pub type __kernel_size_t = __kernel_ulong_t;
pub type __kernel_ssize_t = __kernel_long_t;
pub type __kernel_ptrdiff_t = __kernel_long_t;
pub type __kernel_off_t = __kernel_long_t;
pub type __kernel_loff_t = crate::ctypes::c_longlong;
pub type __kernel_old_time_t = __kernel_long_t;
pub type __kernel_time_t = __kernel_long_t;
pub type __kernel_time64_t = crate::ctypes::c_longlong;
pub type __kernel_clock_t = __kernel_long_t;
pub type __kernel_timer_t = crate::ctypes::c_int;
pub type __kernel_clockid_t = crate::ctypes::c_int;
pub type __kernel_caddr_t = *mut crate::ctypes::c_char;
pub type __kernel_uid16_t = crate::ctypes::c_ushort;
pub type __kernel_gid16_t = crate::ctypes::c_ushort;
pub type __s128 = i128;
pub type __u128 = u128;
pub type __le16 = __u16;
pub type __be16 = __u16;
pub type __le32 = __u32;
pub type __be32 = __u32;
pub type __le64 = __u64;
pub type __be64 = __u64;
pub type __sum16 = __u16;
pub type __wsum = __u32;
pub type __poll_t = crate::ctypes::c_uint;
pub type __kernel_sa_family_t = crate::ctypes::c_ushort;
pub type socklen_t = crate::ctypes::c_uint;
#[repr(C)]
#[derive(Copy, Clone, Debug, Default, Eq, Hash, Ord, PartialEq, PartialOrd)]
pub struct __BindgenBitfieldUnit<Storage> {
storage: Storage,
}
#[repr(C)]
#[derive(Default)]
pub struct __IncompleteArrayField<T>(::core::marker::PhantomData<T>, [T; 0]);
#[repr(C)]
pub struct __BindgenUnionField<T>(::core::marker::PhantomData<T>);
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_old_timeval {
pub tv_sec: __kernel_long_t,
pub tv_usec: __kernel_suseconds_t,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct __kernel_sockaddr_storage {
pub __bindgen_anon_1: __kernel_sockaddr_storage__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_sockaddr_storage__bindgen_ty_1__bindgen_ty_1 {
pub ss_family: __kernel_sa_family_t,
pub __data: [crate::ctypes::c_char; 126usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct in_addr {
pub s_addr: __be32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ip_mreq {
pub imr_multiaddr: in_addr,
pub imr_interface: in_addr,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ip_mreqn {
pub imr_multiaddr: in_addr,
pub imr_address: in_addr,
pub imr_ifindex: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ip_mreq_source {
pub imr_multiaddr: __be32,
pub imr_interface: __be32,
pub imr_sourceaddr: __be32,
}
#[repr(C)]
pub struct ip_msfilter {
pub imsf_multiaddr: __be32,
pub imsf_interface: __be32,
pub imsf_fmode: __u32,
pub imsf_numsrc: __u32,
pub __bindgen_anon_1: ip_msfilter__bindgen_ty_1,
}
#[repr(C)]
pub struct ip_msfilter__bindgen_ty_1 {
pub imsf_slist: __BindgenUnionField<[__be32; 1usize]>,
pub __bindgen_anon_1: __BindgenUnionField<ip_msfilter__bindgen_ty_1__bindgen_ty_1>,
pub bindgen_union_field: u32,
}
#[repr(C)]
#[derive(Debug)]
pub struct ip_msfilter__bindgen_ty_1__bindgen_ty_1 {
pub __empty_imsf_slist_flex: ip_msfilter__bindgen_ty_1__bindgen_ty_1__bindgen_ty_1,
pub imsf_slist_flex: __IncompleteArrayField<__be32>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ip_msfilter__bindgen_ty_1__bindgen_ty_1__bindgen_ty_1 {}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct group_req {
pub gr_interface: __u32,
pub gr_group: __kernel_sockaddr_storage,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct group_source_req {
pub gsr_interface: __u32,
pub gsr_group: __kernel_sockaddr_storage,
pub gsr_source: __kernel_sockaddr_storage,
}
#[repr(C)]
pub struct group_filter {
pub __bindgen_anon_1: group_filter__bindgen_ty_1,
}
#[repr(C)]
pub struct group_filter__bindgen_ty_1 {
pub __bindgen_anon_1: __BindgenUnionField<group_filter__bindgen_ty_1__bindgen_ty_1>,
pub __bindgen_anon_2: __BindgenUnionField<group_filter__bindgen_ty_1__bindgen_ty_2>,
pub bindgen_union_field: [u64; 34usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct group_filter__bindgen_ty_1__bindgen_ty_1 {
pub gf_interface_aux: __u32,
pub gf_group_aux: __kernel_sockaddr_storage,
pub gf_fmode_aux: __u32,
pub gf_numsrc_aux: __u32,
pub gf_slist: [__kernel_sockaddr_storage; 1usize],
}
#[repr(C)]
pub struct group_filter__bindgen_ty_1__bindgen_ty_2 {
pub gf_interface: __u32,
pub gf_group: __kernel_sockaddr_storage,
pub gf_fmode: __u32,
pub gf_numsrc: __u32,
pub gf_slist_flex: __IncompleteArrayField<__kernel_sockaddr_storage>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct in_pktinfo {
pub ipi_ifindex: crate::ctypes::c_int,
pub ipi_spec_dst: in_addr,
pub ipi_addr: in_addr,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sockaddr_in {
pub sin_family: __kernel_sa_family_t,
pub sin_port: __be16,
pub sin_addr: in_addr,
pub __pad: [crate::ctypes::c_uchar; 8usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct iphdr {
pub _bitfield_align_1: [u8; 0],
pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
pub tos: __u8,
pub tot_len: __be16,
pub id: __be16,
pub frag_off: __be16,
pub ttl: __u8,
pub protocol: __u8,
pub check: __sum16,
pub __bindgen_anon_1: iphdr__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct iphdr__bindgen_ty_1__bindgen_ty_1 {
pub saddr: __be32,
pub daddr: __be32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct iphdr__bindgen_ty_1__bindgen_ty_2 {
pub saddr: __be32,
pub daddr: __be32,
}
#[repr(C)]
#[derive(Debug)]
pub struct ip_auth_hdr {
pub nexthdr: __u8,
pub hdrlen: __u8,
pub reserved: __be16,
pub spi: __be32,
pub seq_no: __be32,
pub auth_data: __IncompleteArrayField<__u8>,
}
#[repr(C)]
#[derive(Debug)]
pub struct ip_esp_hdr {
pub spi: __be32,
pub seq_no: __be32,
pub enc_data: __IncompleteArrayField<__u8>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ip_comp_hdr {
pub nexthdr: __u8,
pub flags: __u8,
pub cpi: __be16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ip_beet_phdr {
pub nexthdr: __u8,
pub hdrlen: __u8,
pub padlen: __u8,
pub reserved: __u8,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct in6_addr {
pub in6_u: in6_addr__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct sockaddr_in6 {
pub sin6_family: crate::ctypes::c_ushort,
pub sin6_port: __be16,
pub sin6_flowinfo: __be32,
pub sin6_addr: in6_addr,
pub sin6_scope_id: __u32,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct ipv6_mreq {
pub ipv6mr_multiaddr: in6_addr,
pub ipv6mr_ifindex: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct in6_flowlabel_req {
pub flr_dst: in6_addr,
pub flr_label: __be32,
pub flr_action: __u8,
pub flr_share: __u8,
pub flr_flags: __u16,
pub flr_expires: __u16,
pub flr_linger: __u16,
pub __flr_pad: __u32,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct in6_pktinfo {
pub ipi6_addr: in6_addr,
pub ipi6_ifindex: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct ip6_mtuinfo {
pub ip6m_addr: sockaddr_in6,
pub ip6m_mtu: __u32,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct in6_ifreq {
pub ifr6_addr: in6_addr,
pub ifr6_prefixlen: __u32,
pub ifr6_ifindex: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ipv6_rt_hdr {
pub nexthdr: __u8,
pub hdrlen: __u8,
pub type_: __u8,
pub segments_left: __u8,
}
#[repr(C, packed)]
#[derive(Debug, Copy, Clone)]
pub struct ipv6_opt_hdr {
pub nexthdr: __u8,
pub hdrlen: __u8,
}
#[repr(C)]
pub struct rt0_hdr {
pub rt_hdr: ipv6_rt_hdr,
pub reserved: __u32,
pub addr: __IncompleteArrayField<in6_addr>,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rt2_hdr {
pub rt_hdr: ipv6_rt_hdr,
pub reserved: __u32,
pub addr: in6_addr,
}
#[repr(C, packed)]
#[derive(Copy, Clone)]
pub struct ipv6_destopt_hao {
pub type_: __u8,
pub length: __u8,
pub addr: in6_addr,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct ipv6hdr {
pub _bitfield_align_1: [u8; 0],
pub _bitfield_1: __BindgenBitfieldUnit<[u8; 1usize]>,
pub flow_lbl: [__u8; 3usize],
pub payload_len: __be16,
pub nexthdr: __u8,
pub hop_limit: __u8,
pub __bindgen_anon_1: ipv6hdr__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct ipv6hdr__bindgen_ty_1__bindgen_ty_1 {
pub saddr: in6_addr,
pub daddr: in6_addr,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct ipv6hdr__bindgen_ty_1__bindgen_ty_2 {
pub saddr: in6_addr,
pub daddr: in6_addr,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tcphdr {
pub source: __be16,
pub dest: __be16,
pub seq: __be32,
pub ack_seq: __be32,
pub _bitfield_align_1: [u8; 0],
pub _bitfield_1: __BindgenBitfieldUnit<[u8; 2usize]>,
pub window: __be16,
pub check: __sum16,
pub urg_ptr: __be16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tcp_repair_opt {
pub opt_code: __u32,
pub opt_val: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tcp_repair_window {
pub snd_wl1: __u32,
pub snd_wnd: __u32,
pub max_window: __u32,
pub rcv_wnd: __u32,
pub rcv_wup: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tcp_info {
pub tcpi_state: __u8,
pub tcpi_ca_state: __u8,
pub tcpi_retransmits: __u8,
pub tcpi_probes: __u8,
pub tcpi_backoff: __u8,
pub tcpi_options: __u8,
pub _bitfield_align_1: [u8; 0],
pub _bitfield_1: __BindgenBitfieldUnit<[u8; 2usize]>,
pub tcpi_rto: __u32,
pub tcpi_ato: __u32,
pub tcpi_snd_mss: __u32,
pub tcpi_rcv_mss: __u32,
pub tcpi_unacked: __u32,
pub tcpi_sacked: __u32,
pub tcpi_lost: __u32,
pub tcpi_retrans: __u32,
pub tcpi_fackets: __u32,
pub tcpi_last_data_sent: __u32,
pub tcpi_last_ack_sent: __u32,
pub tcpi_last_data_recv: __u32,
pub tcpi_last_ack_recv: __u32,
pub tcpi_pmtu: __u32,
pub tcpi_rcv_ssthresh: __u32,
pub tcpi_rtt: __u32,
pub tcpi_rttvar: __u32,
pub tcpi_snd_ssthresh: __u32,
pub tcpi_snd_cwnd: __u32,
pub tcpi_advmss: __u32,
pub tcpi_reordering: __u32,
pub tcpi_rcv_rtt: __u32,
pub tcpi_rcv_space: __u32,
pub tcpi_total_retrans: __u32,
pub tcpi_pacing_rate: __u64,
pub tcpi_max_pacing_rate: __u64,
pub tcpi_bytes_acked: __u64,
pub tcpi_bytes_received: __u64,
pub tcpi_segs_out: __u32,
pub tcpi_segs_in: __u32,
pub tcpi_notsent_bytes: __u32,
pub tcpi_min_rtt: __u32,
pub tcpi_data_segs_in: __u32,
pub tcpi_data_segs_out: __u32,
pub tcpi_delivery_rate: __u64,
pub tcpi_busy_time: __u64,
pub tcpi_rwnd_limited: __u64,
pub tcpi_sndbuf_limited: __u64,
pub tcpi_delivered: __u32,
pub tcpi_delivered_ce: __u32,
pub tcpi_bytes_sent: __u64,
pub tcpi_bytes_retrans: __u64,
pub tcpi_dsack_dups: __u32,
pub tcpi_reord_seen: __u32,
pub tcpi_rcv_ooopack: __u32,
pub tcpi_snd_wnd: __u32,
pub tcpi_rcv_wnd: __u32,
pub tcpi_rehash: __u32,
pub tcpi_total_rto: __u16,
pub tcpi_total_rto_recoveries: __u16,
pub tcpi_total_rto_time: __u32,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct tcp_md5sig {
pub tcpm_addr: __kernel_sockaddr_storage,
pub tcpm_flags: __u8,
pub tcpm_prefixlen: __u8,
pub tcpm_keylen: __u16,
pub tcpm_ifindex: crate::ctypes::c_int,
pub tcpm_key: [__u8; 80usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tcp_diag_md5sig {
pub tcpm_family: __u8,
pub tcpm_prefixlen: __u8,
pub tcpm_keylen: __u16,
pub tcpm_addr: [__be32; 4usize],
pub tcpm_key: [__u8; 80usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct tcp_ao_add {
pub addr: __kernel_sockaddr_storage,
pub alg_name: [crate::ctypes::c_char; 64usize],
pub ifindex: __s32,
pub _bitfield_align_1: [u32; 0],
pub _bitfield_1: __BindgenBitfieldUnit<[u8; 4usize]>,
pub reserved2: __u16,
pub prefix: __u8,
pub sndid: __u8,
pub rcvid: __u8,
pub maclen: __u8,
pub keyflags: __u8,
pub keylen: __u8,
pub key: [__u8; 80usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct tcp_ao_del {
pub addr: __kernel_sockaddr_storage,
pub ifindex: __s32,
pub _bitfield_align_1: [u32; 0],
pub _bitfield_1: __BindgenBitfieldUnit<[u8; 4usize]>,
pub reserved2: __u16,
pub prefix: __u8,
pub sndid: __u8,
pub rcvid: __u8,
pub current_key: __u8,
pub rnext: __u8,
pub keyflags: __u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tcp_ao_info_opt {
pub _bitfield_align_1: [u32; 0],
pub _bitfield_1: __BindgenBitfieldUnit<[u8; 4usize]>,
pub reserved2: __u16,
pub current_key: __u8,
pub rnext: __u8,
pub pkt_good: __u64,
pub pkt_bad: __u64,
pub pkt_key_not_found: __u64,
pub pkt_ao_required: __u64,
pub pkt_dropped_icmp: __u64,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct tcp_ao_getsockopt {
pub addr: __kernel_sockaddr_storage,
pub alg_name: [crate::ctypes::c_char; 64usize],
pub key: [__u8; 80usize],
pub nkeys: __u32,
pub _bitfield_align_1: [u16; 0],
pub _bitfield_1: __BindgenBitfieldUnit<[u8; 2usize]>,
pub sndid: __u8,
pub rcvid: __u8,
pub prefix: __u8,
pub maclen: __u8,
pub keyflags: __u8,
pub keylen: __u8,
pub ifindex: __s32,
pub pkt_good: __u64,
pub pkt_bad: __u64,
}
#[repr(C)]
#[repr(align(8))]
#[derive(Debug, Copy, Clone)]
pub struct tcp_ao_repair {
pub snt_isn: __be32,
pub rcv_isn: __be32,
pub snd_sne: __u32,
pub rcv_sne: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tcp_zerocopy_receive {
pub address: __u64,
pub length: __u32,
pub recv_skip_hint: __u32,
pub inq: __u32,
pub err: __s32,
pub copybuf_address: __u64,
pub copybuf_len: __s32,
pub flags: __u32,
pub msg_control: __u64,
pub msg_controllen: __u64,
pub msg_flags: __u32,
pub reserved: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sockaddr_un {
pub sun_family: __kernel_sa_family_t,
pub sun_path: [crate::ctypes::c_char; 108usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct sockaddr {
pub __storage: __kernel_sockaddr_storage,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sync_serial_settings {
pub clock_rate: crate::ctypes::c_uint,
pub clock_type: crate::ctypes::c_uint,
pub loopback: crate::ctypes::c_ushort,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct te1_settings {
pub clock_rate: crate::ctypes::c_uint,
pub clock_type: crate::ctypes::c_uint,
pub loopback: crate::ctypes::c_ushort,
pub slot_map: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct raw_hdlc_proto {
pub encoding: crate::ctypes::c_ushort,
pub parity: crate::ctypes::c_ushort,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fr_proto {
pub t391: crate::ctypes::c_uint,
pub t392: crate::ctypes::c_uint,
pub n391: crate::ctypes::c_uint,
pub n392: crate::ctypes::c_uint,
pub n393: crate::ctypes::c_uint,
pub lmi: crate::ctypes::c_ushort,
pub dce: crate::ctypes::c_ushort,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fr_proto_pvc {
pub dlci: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct fr_proto_pvc_info {
pub dlci: crate::ctypes::c_uint,
pub master: [crate::ctypes::c_char; 16usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct cisco_proto {
pub interval: crate::ctypes::c_uint,
pub timeout: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct x25_hdlc_proto {
pub dce: crate::ctypes::c_ushort,
pub modulo: crate::ctypes::c_uint,
pub window: crate::ctypes::c_uint,
pub t1: crate::ctypes::c_uint,
pub t2: crate::ctypes::c_uint,
pub n2: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifmap {
pub mem_start: crate::ctypes::c_ulong,
pub mem_end: crate::ctypes::c_ulong,
pub base_addr: crate::ctypes::c_ushort,
pub irq: crate::ctypes::c_uchar,
pub dma: crate::ctypes::c_uchar,
pub port: crate::ctypes::c_uchar,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct if_settings {
pub type_: crate::ctypes::c_uint,
pub size: crate::ctypes::c_uint,
pub ifs_ifsu: if_settings__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct ifreq {
pub ifr_ifrn: ifreq__bindgen_ty_1,
pub ifr_ifru: ifreq__bindgen_ty_2,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct ifconf {
pub ifc_len: crate::ctypes::c_int,
pub ifc_ifcu: ifconf__bindgen_ty_1,
}
#[repr(C)]
pub struct xt_entry_match {
pub u: xt_entry_match__bindgen_ty_1,
pub data: __IncompleteArrayField<crate::ctypes::c_uchar>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct xt_entry_match__bindgen_ty_1__bindgen_ty_1 {
pub match_size: __u16,
pub name: [crate::ctypes::c_char; 29usize],
pub revision: __u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct xt_entry_match__bindgen_ty_1__bindgen_ty_2 {
pub match_size: __u16,
pub match_: *mut xt_match,
}
#[repr(C)]
pub struct xt_entry_target {
pub u: xt_entry_target__bindgen_ty_1,
pub data: __IncompleteArrayField<crate::ctypes::c_uchar>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct xt_entry_target__bindgen_ty_1__bindgen_ty_1 {
pub target_size: __u16,
pub name: [crate::ctypes::c_char; 29usize],
pub revision: __u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct xt_entry_target__bindgen_ty_1__bindgen_ty_2 {
pub target_size: __u16,
pub target: *mut xt_target,
}
#[repr(C)]
pub struct xt_standard_target {
pub target: xt_entry_target,
pub verdict: crate::ctypes::c_int,
}
#[repr(C)]
pub struct xt_error_target {
pub target: xt_entry_target,
pub errorname: [crate::ctypes::c_char; 30usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct xt_get_revision {
pub name: [crate::ctypes::c_char; 29usize],
pub revision: __u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _xt_align {
pub u8_: __u8,
pub u16_: __u16,
pub u32_: __u32,
pub u64_: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct xt_counters {
pub pcnt: __u64,
pub bcnt: __u64,
}
#[repr(C)]
#[derive(Debug)]
pub struct xt_counters_info {
pub name: [crate::ctypes::c_char; 32usize],
pub num_counters: crate::ctypes::c_uint,
pub counters: __IncompleteArrayField<xt_counters>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct xt_tcp {
pub spts: [__u16; 2usize],
pub dpts: [__u16; 2usize],
pub option: __u8,
pub flg_mask: __u8,
pub flg_cmp: __u8,
pub invflags: __u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct xt_udp {
pub spts: [__u16; 2usize],
pub dpts: [__u16; 2usize],
pub invflags: __u8,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct ip6t_ip6 {
pub src: in6_addr,
pub dst: in6_addr,
pub smsk: in6_addr,
pub dmsk: in6_addr,
pub iniface: [crate::ctypes::c_char; 16usize],
pub outiface: [crate::ctypes::c_char; 16usize],
pub iniface_mask: [crate::ctypes::c_uchar; 16usize],
pub outiface_mask: [crate::ctypes::c_uchar; 16usize],
pub proto: __u16,
pub tos: __u8,
pub flags: __u8,
pub invflags: __u8,
}
#[repr(C)]
pub struct ip6t_entry {
pub ipv6: ip6t_ip6,
pub nfcache: crate::ctypes::c_uint,
pub target_offset: __u16,
pub next_offset: __u16,
pub comefrom: crate::ctypes::c_uint,
pub counters: xt_counters,
pub elems: __IncompleteArrayField<crate::ctypes::c_uchar>,
}
#[repr(C)]
pub struct ip6t_standard {
pub entry: ip6t_entry,
pub target: xt_standard_target,
}
#[repr(C)]
pub struct ip6t_error {
pub entry: ip6t_entry,
pub target: xt_error_target,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ip6t_icmp {
pub type_: __u8,
pub code: [__u8; 2usize],
pub invflags: __u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ip6t_getinfo {
pub name: [crate::ctypes::c_char; 32usize],
pub valid_hooks: crate::ctypes::c_uint,
pub hook_entry: [crate::ctypes::c_uint; 5usize],
pub underflow: [crate::ctypes::c_uint; 5usize],
pub num_entries: crate::ctypes::c_uint,
pub size: crate::ctypes::c_uint,
}
#[repr(C)]
pub struct ip6t_replace {
pub name: [crate::ctypes::c_char; 32usize],
pub valid_hooks: crate::ctypes::c_uint,
pub num_entries: crate::ctypes::c_uint,
pub size: crate::ctypes::c_uint,
pub hook_entry: [crate::ctypes::c_uint; 5usize],
pub underflow: [crate::ctypes::c_uint; 5usize],
pub num_counters: crate::ctypes::c_uint,
pub counters: *mut xt_counters,
pub entries: __IncompleteArrayField<ip6t_entry>,
}
#[repr(C)]
pub struct ip6t_get_entries {
pub name: [crate::ctypes::c_char; 32usize],
pub size: crate::ctypes::c_uint,
pub entrytable: __IncompleteArrayField<ip6t_entry>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct so_timestamping {
pub flags: crate::ctypes::c_int,
pub bind_phc: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct hwtstamp_config {
pub flags: crate::ctypes::c_int,
pub tx_type: crate::ctypes::c_int,
pub rx_filter: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct scm_ts_pktinfo {
pub if_index: __u32,
pub pkt_length: __u32,
pub reserved: [__u32; 2usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sock_txtime {
pub clockid: __kernel_clockid_t,
pub flags: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct linger {
pub l_onoff: crate::ctypes::c_int,
pub l_linger: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct msghdr {
pub msg_name: *mut crate::ctypes::c_void,
pub msg_namelen: crate::ctypes::c_int,
pub msg_iov: *mut iovec,
pub msg_iovlen: usize,
pub msg_control: *mut crate::ctypes::c_void,
pub msg_controllen: usize,
pub msg_flags: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct cmsghdr {
pub cmsg_len: usize,
pub cmsg_level: crate::ctypes::c_int,
pub cmsg_type: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ucred {
pub pid: __u32,
pub uid: __u32,
pub gid: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct mmsghdr {
pub msg_hdr: msghdr,
pub msg_len: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct xt_match {
pub _address: u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct xt_target {
pub _address: u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct iovec {
pub _address: u8,
}
pub const __BITS_PER_LONG_LONG: u32 = 64;
pub const _K_SS_MAXSIZE: u32 = 128;
pub const SOCK_SNDBUF_LOCK: u32 = 1;
pub const SOCK_RCVBUF_LOCK: u32 = 2;
pub const SOCK_BUF_LOCK_MASK: u32 = 3;
pub const SOCK_TXREHASH_DEFAULT: u32 = 255;
pub const SOCK_TXREHASH_DISABLED: u32 = 0;
pub const SOCK_TXREHASH_ENABLED: u32 = 1;
pub const IP_TOS: u32 = 1;
pub const IP_TTL: u32 = 2;
pub const IP_HDRINCL: u32 = 3;
pub const IP_OPTIONS: u32 = 4;
pub const IP_ROUTER_ALERT: u32 = 5;
pub const IP_RECVOPTS: u32 = 6;
pub const IP_RETOPTS: u32 = 7;
pub const IP_PKTINFO: u32 = 8;
pub const IP_PKTOPTIONS: u32 = 9;
pub const IP_MTU_DISCOVER: u32 = 10;
pub const IP_RECVERR: u32 = 11;
pub const IP_RECVTTL: u32 = 12;
pub const IP_RECVTOS: u32 = 13;
pub const IP_MTU: u32 = 14;
pub const IP_FREEBIND: u32 = 15;
pub const IP_IPSEC_POLICY: u32 = 16;
pub const IP_XFRM_POLICY: u32 = 17;
pub const IP_PASSSEC: u32 = 18;
pub const IP_TRANSPARENT: u32 = 19;
pub const IP_RECVRETOPTS: u32 = 7;
pub const IP_ORIGDSTADDR: u32 = 20;
pub const IP_RECVORIGDSTADDR: u32 = 20;
pub const IP_MINTTL: u32 = 21;
pub const IP_NODEFRAG: u32 = 22;
pub const IP_CHECKSUM: u32 = 23;
pub const IP_BIND_ADDRESS_NO_PORT: u32 = 24;
pub const IP_RECVFRAGSIZE: u32 = 25;
pub const IP_RECVERR_RFC4884: u32 = 26;
pub const IP_PMTUDISC_DONT: u32 = 0;
pub const IP_PMTUDISC_WANT: u32 = 1;
pub const IP_PMTUDISC_DO: u32 = 2;
pub const IP_PMTUDISC_PROBE: u32 = 3;
pub const IP_PMTUDISC_INTERFACE: u32 = 4;
pub const IP_PMTUDISC_OMIT: u32 = 5;
pub const IP_MULTICAST_IF: u32 = 32;
pub const IP_MULTICAST_TTL: u32 = 33;
pub const IP_MULTICAST_LOOP: u32 = 34;
pub const IP_ADD_MEMBERSHIP: u32 = 35;
pub const IP_DROP_MEMBERSHIP: u32 = 36;
pub const IP_UNBLOCK_SOURCE: u32 = 37;
pub const IP_BLOCK_SOURCE: u32 = 38;
pub const IP_ADD_SOURCE_MEMBERSHIP: u32 = 39;
pub const IP_DROP_SOURCE_MEMBERSHIP: u32 = 40;
pub const IP_MSFILTER: u32 = 41;
pub const MCAST_JOIN_GROUP: u32 = 42;
pub const MCAST_BLOCK_SOURCE: u32 = 43;
pub const MCAST_UNBLOCK_SOURCE: u32 = 44;
pub const MCAST_LEAVE_GROUP: u32 = 45;
pub const MCAST_JOIN_SOURCE_GROUP: u32 = 46;
pub const MCAST_LEAVE_SOURCE_GROUP: u32 = 47;
pub const MCAST_MSFILTER: u32 = 48;
pub const IP_MULTICAST_ALL: u32 = 49;
pub const IP_UNICAST_IF: u32 = 50;
pub const IP_LOCAL_PORT_RANGE: u32 = 51;
pub const IP_PROTOCOL: u32 = 52;
pub const MCAST_EXCLUDE: u32 = 0;
pub const MCAST_INCLUDE: u32 = 1;
pub const IP_DEFAULT_MULTICAST_TTL: u32 = 1;
pub const IP_DEFAULT_MULTICAST_LOOP: u32 = 1;
pub const __SOCK_SIZE__: u32 = 16;
pub const IN_CLASSA_NET: u32 = 4278190080;
pub const IN_CLASSA_NSHIFT: u32 = 24;
pub const IN_CLASSA_HOST: u32 = 16777215;
pub const IN_CLASSA_MAX: u32 = 128;
pub const IN_CLASSB_NET: u32 = **********;
pub const IN_CLASSB_NSHIFT: u32 = 16;
pub const IN_CLASSB_HOST: u32 = 65535;
pub const IN_CLASSB_MAX: u32 = 65536;
pub const IN_CLASSC_NET: u32 = 4294967040;
pub const IN_CLASSC_NSHIFT: u32 = 8;
pub const IN_CLASSC_HOST: u32 = 255;
pub const IN_MULTICAST_NET: u32 = 3758096384;
pub const IN_CLASSE_NET: u32 = **********;
pub const IN_CLASSE_NSHIFT: u32 = 0;
pub const IN_LOOPBACKNET: u32 = 127;
pub const INADDR_LOOPBACK: u32 = 2130706433;
pub const INADDR_UNSPEC_GROUP: u32 = 3758096384;
pub const INADDR_ALLHOSTS_GROUP: u32 = 3758096385;
pub const INADDR_ALLRTRS_GROUP: u32 = 3758096386;
pub const INADDR_ALLSNOOPERS_GROUP: u32 = 3758096490;
pub const INADDR_MAX_LOCAL_GROUP: u32 = 3758096639;
pub const __BIG_ENDIAN: u32 = 4321;
pub const ASI_NULL1: u32 = 0;
pub const ASI_NULL2: u32 = 1;
pub const ASI_CONTROL: u32 = 2;
pub const ASI_SEGMAP: u32 = 3;
pub const ASI_PTE: u32 = 4;
pub const ASI_HWFLUSHSEG: u32 = 5;
pub const ASI_HWFLUSHPAGE: u32 = 6;
pub const ASI_REGMAP: u32 = 6;
pub const ASI_HWFLUSHCONTEXT: u32 = 7;
pub const ASI_USERTXT: u32 = 8;
pub const ASI_KERNELTXT: u32 = 9;
pub const ASI_USERDATA: u32 = 10;
pub const ASI_KERNELDATA: u32 = 11;
pub const ASI_FLUSHSEG: u32 = 12;
pub const ASI_FLUSHPG: u32 = 13;
pub const ASI_FLUSHCTX: u32 = 14;
pub const ASI_M_RES00: u32 = 0;
pub const ASI_M_UNA01: u32 = 1;
pub const ASI_M_MXCC: u32 = 2;
pub const ASI_M_FLUSH_PROBE: u32 = 3;
pub const ASI_M_MMUREGS: u32 = 4;
pub const ASI_M_TLBDIAG: u32 = 5;
pub const ASI_M_DIAGS: u32 = 6;
pub const ASI_M_IODIAG: u32 = 7;
pub const ASI_M_USERTXT: u32 = 8;
pub const ASI_M_KERNELTXT: u32 = 9;
pub const ASI_M_USERDATA: u32 = 10;
pub const ASI_M_KERNELDATA: u32 = 11;
pub const ASI_M_TXTC_TAG: u32 = 12;
pub const ASI_M_TXTC_DATA: u32 = 13;
pub const ASI_M_DATAC_TAG: u32 = 14;
pub const ASI_M_DATAC_DATA: u32 = 15;
pub const ASI_M_FLUSH_PAGE: u32 = 16;
pub const ASI_M_FLUSH_SEG: u32 = 17;
pub const ASI_M_FLUSH_REGION: u32 = 18;
pub const ASI_M_FLUSH_CTX: u32 = 19;
pub const ASI_M_FLUSH_USER: u32 = 20;
pub const ASI_M_BCOPY: u32 = 23;
pub const ASI_M_IFLUSH_PAGE: u32 = 24;
pub const ASI_M_IFLUSH_SEG: u32 = 25;
pub const ASI_M_IFLUSH_REGION: u32 = 26;
pub const ASI_M_IFLUSH_CTX: u32 = 27;
pub const ASI_M_IFLUSH_USER: u32 = 28;
pub const ASI_M_BFILL: u32 = 31;
pub const ASI_M_BYPASS: u32 = 32;
pub const ASI_M_FBMEM: u32 = 41;
pub const ASI_M_VMEUS: u32 = 42;
pub const ASI_M_VMEPS: u32 = 43;
pub const ASI_M_VMEUT: u32 = 44;
pub const ASI_M_VMEPT: u32 = 45;
pub const ASI_M_SBUS: u32 = 46;
pub const ASI_M_CTL: u32 = 47;
pub const ASI_M_FLUSH_IWHOLE: u32 = 49;
pub const ASI_M_IC_FLCLEAR: u32 = 54;
pub const ASI_M_DC_FLCLEAR: u32 = 55;
pub const ASI_M_DCDR: u32 = 57;
pub const ASI_M_VIKING_TMP1: u32 = 64;
pub const ASI_M_ACTION: u32 = 76;
pub const ASI_LEON_NOCACHE: u32 = 1;
pub const ASI_LEON_DCACHE_MISS: u32 = 1;
pub const ASI_LEON_CACHEREGS: u32 = 2;
pub const ASI_LEON_IFLUSH: u32 = 16;
pub const ASI_LEON_DFLUSH: u32 = 17;
pub const ASI_LEON_MMUFLUSH: u32 = 24;
pub const ASI_LEON_MMUREGS: u32 = 25;
pub const ASI_LEON_BYPASS: u32 = 28;
pub const ASI_LEON_FLUSH_PAGE: u32 = 16;
pub const ASI_N: u32 = 4;
pub const ASI_NL: u32 = 12;
pub const ASI_AIUP: u32 = 16;
pub const ASI_AIUS: u32 = 17;
pub const ASI_AIUPL: u32 = 24;
pub const ASI_AIUSL: u32 = 25;
pub const ASI_P: u32 = 128;
pub const ASI_S: u32 = 129;
pub const ASI_PNF: u32 = 130;
pub const ASI_SNF: u32 = 131;
pub const ASI_PL: u32 = 136;
pub const ASI_SL: u32 = 137;
pub const ASI_PNFL: u32 = 138;
pub const ASI_SNFL: u32 = 139;
pub const ASI_MCD_PRIV_PRIMARY: u32 = 2;
pub const ASI_MCD_REAL: u32 = 5;
pub const ASI_PHYS_USE_EC: u32 = 20;
pub const ASI_PHYS_BYPASS_EC_E: u32 = 21;
pub const ASI_BLK_AIUP_4V: u32 = 22;
pub const ASI_BLK_AIUS_4V: u32 = 23;
pub const ASI_PHYS_USE_EC_L: u32 = 28;
pub const ASI_PHYS_BYPASS_EC_E_L: u32 = 29;
pub const ASI_BLK_AIUP_L_4V: u32 = 30;
pub const ASI_BLK_AIUS_L_4V: u32 = 31;
pub const ASI_SCRATCHPAD: u32 = 32;
pub const ASI_MMU: u32 = 33;
pub const ASI_BLK_INIT_QUAD_LDD_AIUS: u32 = 35;
pub const ASI_NUCLEUS_QUAD_LDD: u32 = 36;
pub const ASI_QUEUE: u32 = 37;
pub const ASI_QUAD_LDD_PHYS_4V: u32 = 38;
pub const ASI_NUCLEUS_QUAD_LDD_L: u32 = 44;
pub const ASI_QUAD_LDD_PHYS_L_4V: u32 = 46;
pub const ASI_PCACHE_DATA_STATUS: u32 = 48;
pub const ASI_PCACHE_DATA: u32 = 49;
pub const ASI_PCACHE_TAG: u32 = 50;
pub const ASI_PCACHE_SNOOP_TAG: u32 = 51;
pub const ASI_QUAD_LDD_PHYS: u32 = 52;
pub const ASI_WCACHE_VALID_BITS: u32 = 56;
pub const ASI_WCACHE_DATA: u32 = 57;
pub const ASI_WCACHE_TAG: u32 = 58;
pub const ASI_WCACHE_SNOOP_TAG: u32 = 59;
pub const ASI_QUAD_LDD_PHYS_L: u32 = 60;
pub const ASI_SRAM_FAST_INIT: u32 = 64;
pub const ASI_CORE_AVAILABLE: u32 = 65;
pub const ASI_CORE_ENABLE_STAT: u32 = 65;
pub const ASI_CORE_ENABLE: u32 = 65;
pub const ASI_XIR_STEERING: u32 = 65;
pub const ASI_CORE_RUNNING_RW: u32 = 65;
pub const ASI_CORE_RUNNING_W1S: u32 = 65;
pub const ASI_CORE_RUNNING_W1C: u32 = 65;
pub const ASI_CORE_RUNNING_STAT: u32 = 65;
pub const ASI_CMT_ERROR_STEERING: u32 = 65;
pub const ASI_DCACHE_INVALIDATE: u32 = 66;
pub const ASI_DCACHE_UTAG: u32 = 67;
pub const ASI_DCACHE_SNOOP_TAG: u32 = 68;
pub const ASI_LSU_CONTROL: u32 = 69;
pub const ASI_DCU_CONTROL_REG: u32 = 69;
pub const ASI_DCACHE_DATA: u32 = 70;
pub const ASI_DCACHE_TAG: u32 = 71;
pub const ASI_INTR_DISPATCH_STAT: u32 = 72;
pub const ASI_INTR_RECEIVE: u32 = 73;
pub const ASI_UPA_CONFIG: u32 = 74;
pub const ASI_JBUS_CONFIG: u32 = 74;
pub const ASI_SAFARI_CONFIG: u32 = 74;
pub const ASI_SAFARI_ADDRESS: u32 = 74;
pub const ASI_ESTATE_ERROR_EN: u32 = 75;
pub const ASI_AFSR: u32 = 76;
pub const ASI_AFAR: u32 = 77;
pub const ASI_EC_TAG_DATA: u32 = 78;
pub const ASI_IMMU: u32 = 80;
pub const ASI_IMMU_TSB_8KB_PTR: u32 = 81;
pub const ASI_IMMU_TSB_64KB_PTR: u32 = 82;
pub const ASI_ITLB_DATA_IN: u32 = 84;
pub const ASI_ITLB_DATA_ACCESS: u32 = 85;
pub const ASI_ITLB_TAG_READ: u32 = 86;
pub const ASI_IMMU_DEMAP: u32 = 87;
pub const ASI_DMMU: u32 = 88;
pub const ASI_DMMU_TSB_8KB_PTR: u32 = 89;
pub const ASI_DMMU_TSB_64KB_PTR: u32 = 90;
pub const ASI_DMMU_TSB_DIRECT_PTR: u32 = 91;
pub const ASI_DTLB_DATA_IN: u32 = 92;
pub const ASI_DTLB_DATA_ACCESS: u32 = 93;
pub const ASI_DTLB_TAG_READ: u32 = 94;
pub const ASI_DMMU_DEMAP: u32 = 95;
pub const ASI_IIU_INST_TRAP: u32 = 96;
pub const ASI_INTR_ID: u32 = 99;
pub const ASI_CORE_ID: u32 = 99;
pub const ASI_CESR_ID: u32 = 99;
pub const ASI_IC_INSTR: u32 = 102;
pub const ASI_IC_TAG: u32 = 103;
pub const ASI_IC_STAG: u32 = 104;
pub const ASI_IC_PRE_DECODE: u32 = 110;
pub const ASI_IC_NEXT_FIELD: u32 = 111;
pub const ASI_BRPRED_ARRAY: u32 = 111;
pub const ASI_BLK_AIUP: u32 = 112;
pub const ASI_BLK_AIUS: u32 = 113;
pub const ASI_MCU_CTRL_REG: u32 = 114;
pub const ASI_EC_DATA: u32 = 116;
pub const ASI_EC_CTRL: u32 = 117;
pub const ASI_EC_W: u32 = 118;
pub const ASI_UDB_ERROR_W: u32 = 119;
pub const ASI_UDB_CONTROL_W: u32 = 119;
pub const ASI_INTR_W: u32 = 119;
pub const ASI_INTR_DATAN_W: u32 = 119;
pub const ASI_INTR_DISPATCH_W: u32 = 119;
pub const ASI_BLK_AIUPL: u32 = 120;
pub const ASI_BLK_AIUSL: u32 = 121;
pub const ASI_EC_R: u32 = 126;
pub const ASI_UDBH_ERROR_R: u32 = 127;
pub const ASI_UDBL_ERROR_R: u32 = 127;
pub const ASI_UDBH_CONTROL_R: u32 = 127;
pub const ASI_UDBL_CONTROL_R: u32 = 127;
pub const ASI_INTR_R: u32 = 127;
pub const ASI_INTR_DATAN_R: u32 = 127;
pub const ASI_MCD_PRIMARY: u32 = 144;
pub const ASI_MCD_ST_BLKINIT_PRIMARY: u32 = 146;
pub const ASI_PIC: u32 = 176;
pub const ASI_PST8_P: u32 = 192;
pub const ASI_PST8_S: u32 = 193;
pub const ASI_PST16_P: u32 = 194;
pub const ASI_PST16_S: u32 = 195;
pub const ASI_PST32_P: u32 = 196;
pub const ASI_PST32_S: u32 = 197;
pub const ASI_PST8_PL: u32 = 200;
pub const ASI_PST8_SL: u32 = 201;
pub const ASI_PST16_PL: u32 = 202;
pub const ASI_PST16_SL: u32 = 203;
pub const ASI_PST32_PL: u32 = 204;
pub const ASI_PST32_SL: u32 = 205;
pub const ASI_FL8_P: u32 = 208;
pub const ASI_FL8_S: u32 = 209;
pub const ASI_FL16_P: u32 = 210;
pub const ASI_FL16_S: u32 = 211;
pub const ASI_FL8_PL: u32 = 216;
pub const ASI_FL8_SL: u32 = 217;
pub const ASI_FL16_PL: u32 = 218;
pub const ASI_FL16_SL: u32 = 219;
pub const ASI_BLK_COMMIT_P: u32 = 224;
pub const ASI_BLK_COMMIT_S: u32 = 225;
pub const ASI_BLK_INIT_QUAD_LDD_P: u32 = 226;
pub const ASI_BLK_INIT_QUAD_LDD_S: u32 = 227;
pub const ASI_BLK_P: u32 = 240;
pub const ASI_BLK_S: u32 = 241;
pub const ASI_ST_BLKINIT_MRU_P: u32 = 242;
pub const ASI_ST_BLKINIT_MRU_S: u32 = 243;
pub const ASI_BLK_PL: u32 = 248;
pub const ASI_BLK_SL: u32 = 249;
pub const ASI_ST_BLKINIT_MRU_PL: u32 = 250;
pub const ASI_ST_BLKINIT_MRU_SL: u32 = 251;
pub const IPTOS_TOS_MASK: u32 = 30;
pub const IPTOS_LOWDELAY: u32 = 16;
pub const IPTOS_THROUGHPUT: u32 = 8;
pub const IPTOS_RELIABILITY: u32 = 4;
pub const IPTOS_MINCOST: u32 = 2;
pub const IPTOS_PREC_MASK: u32 = 224;
pub const IPTOS_PREC_NETCONTROL: u32 = 224;
pub const IPTOS_PREC_INTERNETCONTROL: u32 = 192;
pub const IPTOS_PREC_CRITIC_ECP: u32 = 160;
pub const IPTOS_PREC_FLASHOVERRIDE: u32 = 128;
pub const IPTOS_PREC_FLASH: u32 = 96;
pub const IPTOS_PREC_IMMEDIATE: u32 = 64;
pub const IPTOS_PREC_PRIORITY: u32 = 32;
pub const IPTOS_PREC_ROUTINE: u32 = 0;
pub const IPOPT_COPY: u32 = 128;
pub const IPOPT_CLASS_MASK: u32 = 96;
pub const IPOPT_NUMBER_MASK: u32 = 31;
pub const IPOPT_CONTROL: u32 = 0;
pub const IPOPT_RESERVED1: u32 = 32;
pub const IPOPT_MEASUREMENT: u32 = 64;
pub const IPOPT_RESERVED2: u32 = 96;
pub const IPOPT_END: u32 = 0;
pub const IPOPT_NOOP: u32 = 1;
pub const IPOPT_SEC: u32 = 130;
pub const IPOPT_LSRR: u32 = 131;
pub const IPOPT_TIMESTAMP: u32 = 68;
pub const IPOPT_CIPSO: u32 = 134;
pub const IPOPT_RR: u32 = 7;
pub const IPOPT_SID: u32 = 136;
pub const IPOPT_SSRR: u32 = 137;
pub const IPOPT_RA: u32 = 148;
pub const IPVERSION: u32 = 4;
pub const MAXTTL: u32 = 255;
pub const IPDEFTTL: u32 = 64;
pub const IPOPT_OPTVAL: u32 = 0;
pub const IPOPT_OLEN: u32 = 1;
pub const IPOPT_OFFSET: u32 = 2;
pub const IPOPT_MINOFF: u32 = 4;
pub const MAX_IPOPTLEN: u32 = 40;
pub const IPOPT_NOP: u32 = 1;
pub const IPOPT_EOL: u32 = 0;
pub const IPOPT_TS: u32 = 68;
pub const IPOPT_TS_TSONLY: u32 = 0;
pub const IPOPT_TS_TSANDADDR: u32 = 1;
pub const IPOPT_TS_PRESPEC: u32 = 3;
pub const IPV4_BEET_PHMAXLEN: u32 = 8;
pub const IPV6_FL_A_GET: u32 = 0;
pub const IPV6_FL_A_PUT: u32 = 1;
pub const IPV6_FL_A_RENEW: u32 = 2;
pub const IPV6_FL_F_CREATE: u32 = 1;
pub const IPV6_FL_F_EXCL: u32 = 2;
pub const IPV6_FL_F_REFLECT: u32 = 4;
pub const IPV6_FL_F_REMOTE: u32 = 8;
pub const IPV6_FL_S_NONE: u32 = 0;
pub const IPV6_FL_S_EXCL: u32 = 1;
pub const IPV6_FL_S_PROCESS: u32 = 2;
pub const IPV6_FL_S_USER: u32 = 3;
pub const IPV6_FL_S_ANY: u32 = 255;
pub const IPV6_FLOWINFO_FLOWLABEL: u32 = 1048575;
pub const IPV6_FLOWINFO_PRIORITY: u32 = 267386880;
pub const IPV6_PRIORITY_UNCHARACTERIZED: u32 = 0;
pub const IPV6_PRIORITY_FILLER: u32 = 256;
pub const IPV6_PRIORITY_UNATTENDED: u32 = 512;
pub const IPV6_PRIORITY_RESERVED1: u32 = 768;
pub const IPV6_PRIORITY_BULK: u32 = 1024;
pub const IPV6_PRIORITY_RESERVED2: u32 = 1280;
pub const IPV6_PRIORITY_INTERACTIVE: u32 = 1536;
pub const IPV6_PRIORITY_CONTROL: u32 = 1792;
pub const IPV6_PRIORITY_8: u32 = 2048;
pub const IPV6_PRIORITY_9: u32 = 2304;
pub const IPV6_PRIORITY_10: u32 = 2560;
pub const IPV6_PRIORITY_11: u32 = 2816;
pub const IPV6_PRIORITY_12: u32 = 3072;
pub const IPV6_PRIORITY_13: u32 = 3328;
pub const IPV6_PRIORITY_14: u32 = 3584;
pub const IPV6_PRIORITY_15: u32 = 3840;
pub const IPPROTO_HOPOPTS: u32 = 0;
pub const IPPROTO_ROUTING: u32 = 43;
pub const IPPROTO_FRAGMENT: u32 = 44;
pub const IPPROTO_ICMPV6: u32 = 58;
pub const IPPROTO_NONE: u32 = 59;
pub const IPPROTO_DSTOPTS: u32 = 60;
pub const IPPROTO_MH: u32 = 135;
pub const IPV6_TLV_PAD1: u32 = 0;
pub const IPV6_TLV_PADN: u32 = 1;
pub const IPV6_TLV_ROUTERALERT: u32 = 5;
pub const IPV6_TLV_CALIPSO: u32 = 7;
pub const IPV6_TLV_IOAM: u32 = 49;
pub const IPV6_TLV_JUMBO: u32 = 194;
pub const IPV6_TLV_HAO: u32 = 201;
pub const IPV6_ADDRFORM: u32 = 1;
pub const IPV6_2292PKTINFO: u32 = 2;
pub const IPV6_2292HOPOPTS: u32 = 3;
pub const IPV6_2292DSTOPTS: u32 = 4;
pub const IPV6_2292RTHDR: u32 = 5;
pub const IPV6_2292PKTOPTIONS: u32 = 6;
pub const IPV6_CHECKSUM: u32 = 7;
pub const IPV6_2292HOPLIMIT: u32 = 8;
pub const IPV6_NEXTHOP: u32 = 9;
pub const IPV6_AUTHHDR: u32 = 10;
pub const IPV6_FLOWINFO: u32 = 11;
pub const IPV6_UNICAST_HOPS: u32 = 16;
pub const IPV6_MULTICAST_IF: u32 = 17;
pub const IPV6_MULTICAST_HOPS: u32 = 18;
pub const IPV6_MULTICAST_LOOP: u32 = 19;
pub const IPV6_ADD_MEMBERSHIP: u32 = 20;
pub const IPV6_DROP_MEMBERSHIP: u32 = 21;
pub const IPV6_ROUTER_ALERT: u32 = 22;
pub const IPV6_MTU_DISCOVER: u32 = 23;
pub const IPV6_MTU: u32 = 24;
pub const IPV6_RECVERR: u32 = 25;
pub const IPV6_V6ONLY: u32 = 26;
pub const IPV6_JOIN_ANYCAST: u32 = 27;
pub const IPV6_LEAVE_ANYCAST: u32 = 28;
pub const IPV6_MULTICAST_ALL: u32 = 29;
pub const IPV6_ROUTER_ALERT_ISOLATE: u32 = 30;
pub const IPV6_RECVERR_RFC4884: u32 = 31;
pub const IPV6_PMTUDISC_DONT: u32 = 0;
pub const IPV6_PMTUDISC_WANT: u32 = 1;
pub const IPV6_PMTUDISC_DO: u32 = 2;
pub const IPV6_PMTUDISC_PROBE: u32 = 3;
pub const IPV6_PMTUDISC_INTERFACE: u32 = 4;
pub const IPV6_PMTUDISC_OMIT: u32 = 5;
pub const IPV6_FLOWLABEL_MGR: u32 = 32;
pub const IPV6_FLOWINFO_SEND: u32 = 33;
pub const IPV6_IPSEC_POLICY: u32 = 34;
pub const IPV6_XFRM_POLICY: u32 = 35;
pub const IPV6_HDRINCL: u32 = 36;
pub const IPV6_RECVPKTINFO: u32 = 49;
pub const IPV6_PKTINFO: u32 = 50;
pub const IPV6_RECVHOPLIMIT: u32 = 51;
pub const IPV6_HOPLIMIT: u32 = 52;
pub const IPV6_RECVHOPOPTS: u32 = 53;
pub const IPV6_HOPOPTS: u32 = 54;
pub const IPV6_RTHDRDSTOPTS: u32 = 55;
pub const IPV6_RECVRTHDR: u32 = 56;
pub const IPV6_RTHDR: u32 = 57;
pub const IPV6_RECVDSTOPTS: u32 = 58;
pub const IPV6_DSTOPTS: u32 = 59;
pub const IPV6_RECVPATHMTU: u32 = 60;
pub const IPV6_PATHMTU: u32 = 61;
pub const IPV6_DONTFRAG: u32 = 62;
pub const IPV6_RECVTCLASS: u32 = 66;
pub const IPV6_TCLASS: u32 = 67;
pub const IPV6_AUTOFLOWLABEL: u32 = 70;
pub const IPV6_ADDR_PREFERENCES: u32 = 72;
pub const IPV6_PREFER_SRC_TMP: u32 = 1;
pub const IPV6_PREFER_SRC_PUBLIC: u32 = 2;
pub const IPV6_PREFER_SRC_PUBTMP_DEFAULT: u32 = 256;
pub const IPV6_PREFER_SRC_COA: u32 = 4;
pub const IPV6_PREFER_SRC_HOME: u32 = 1024;
pub const IPV6_PREFER_SRC_CGA: u32 = 8;
pub const IPV6_PREFER_SRC_NONCGA: u32 = 2048;
pub const IPV6_MINHOPCOUNT: u32 = 73;
pub const IPV6_ORIGDSTADDR: u32 = 74;
pub const IPV6_RECVORIGDSTADDR: u32 = 74;
pub const IPV6_TRANSPARENT: u32 = 75;
pub const IPV6_UNICAST_IF: u32 = 76;
pub const IPV6_RECVFRAGSIZE: u32 = 77;
pub const IPV6_FREEBIND: u32 = 78;
pub const IPV6_MIN_MTU: u32 = 1280;
pub const IPV6_SRCRT_STRICT: u32 = 1;
pub const IPV6_SRCRT_TYPE_0: u32 = 0;
pub const IPV6_SRCRT_TYPE_2: u32 = 2;
pub const IPV6_SRCRT_TYPE_3: u32 = 3;
pub const IPV6_SRCRT_TYPE_4: u32 = 4;
pub const IPV6_OPT_ROUTERALERT_MLD: u32 = 0;
pub const SIOCGSTAMP_OLD: u32 = 35078;
pub const SIOCGSTAMPNS_OLD: u32 = 35079;
pub const SOL_SOCKET: u32 = 65535;
pub const SO_DEBUG: u32 = 1;
pub const SO_PASSCRED: u32 = 2;
pub const SO_REUSEADDR: u32 = 4;
pub const SO_KEEPALIVE: u32 = 8;
pub const SO_DONTROUTE: u32 = 16;
pub const SO_BROADCAST: u32 = 32;
pub const SO_PEERCRED: u32 = 64;
pub const SO_LINGER: u32 = 128;
pub const SO_OOBINLINE: u32 = 256;
pub const SO_REUSEPORT: u32 = 512;
pub const SO_BSDCOMPAT: u32 = 1024;
pub const SO_RCVLOWAT: u32 = 2048;
pub const SO_SNDLOWAT: u32 = 4096;
pub const SO_RCVTIMEO_OLD: u32 = 8192;
pub const SO_SNDTIMEO_OLD: u32 = 16384;
pub const SO_ACCEPTCONN: u32 = 32768;
pub const SO_SNDBUF: u32 = 4097;
pub const SO_RCVBUF: u32 = 4098;
pub const SO_SNDBUFFORCE: u32 = 4106;
pub const SO_RCVBUFFORCE: u32 = 4107;
pub const SO_ERROR: u32 = 4103;
pub const SO_TYPE: u32 = 4104;
pub const SO_PROTOCOL: u32 = 4136;
pub const SO_DOMAIN: u32 = 4137;
pub const SO_NO_CHECK: u32 = 11;
pub const SO_PRIORITY: u32 = 12;
pub const SO_BINDTODEVICE: u32 = 13;
pub const SO_ATTACH_FILTER: u32 = 26;
pub const SO_DETACH_FILTER: u32 = 27;
pub const SO_GET_FILTER: u32 = 26;
pub const SO_PEERNAME: u32 = 28;
pub const SO_PEERSEC: u32 = 30;
pub const SO_PASSSEC: u32 = 31;
pub const SO_MARK: u32 = 34;
pub const SO_RXQ_OVFL: u32 = 36;
pub const SO_WIFI_STATUS: u32 = 37;
pub const SCM_WIFI_STATUS: u32 = 37;
pub const SO_PEEK_OFF: u32 = 38;
pub const SO_NOFCS: u32 = 39;
pub const SO_LOCK_FILTER: u32 = 40;
pub const SO_SELECT_ERR_QUEUE: u32 = 41;
pub const SO_BUSY_POLL: u32 = 48;
pub const SO_MAX_PACING_RATE: u32 = 49;
pub const SO_BPF_EXTENSIONS: u32 = 50;
pub const SO_INCOMING_CPU: u32 = 51;
pub const SO_ATTACH_BPF: u32 = 52;
pub const SO_DETACH_BPF: u32 = 27;
pub const SO_ATTACH_REUSEPORT_CBPF: u32 = 53;
pub const SO_ATTACH_REUSEPORT_EBPF: u32 = 54;
pub const SO_CNX_ADVICE: u32 = 55;
pub const SCM_TIMESTAMPING_OPT_STATS: u32 = 56;
pub const SO_MEMINFO: u32 = 57;
pub const SO_INCOMING_NAPI_ID: u32 = 58;
pub const SO_COOKIE: u32 = 59;
pub const SCM_TIMESTAMPING_PKTINFO: u32 = 60;
pub const SO_PEERGROUPS: u32 = 61;
pub const SO_ZEROCOPY: u32 = 62;
pub const SO_TXTIME: u32 = 63;
pub const SCM_TXTIME: u32 = 63;
pub const SO_BINDTOIFINDEX: u32 = 65;
pub const SO_SECURITY_AUTHENTICATION: u32 = 20481;
pub const SO_SECURITY_ENCRYPTION_TRANSPORT: u32 = 20482;
pub const SO_SECURITY_ENCRYPTION_NETWORK: u32 = 20484;
pub const SO_TIMESTAMP_OLD: u32 = 29;
pub const SO_TIMESTAMPNS_OLD: u32 = 33;
pub const SO_TIMESTAMPING_OLD: u32 = 35;
pub const SO_TIMESTAMP_NEW: u32 = 70;
pub const SO_TIMESTAMPNS_NEW: u32 = 66;
pub const SO_TIMESTAMPING_NEW: u32 = 67;
pub const SO_RCVTIMEO_NEW: u32 = 68;
pub const SO_SNDTIMEO_NEW: u32 = 69;
pub const SO_DETACH_REUSEPORT_BPF: u32 = 71;
pub const SO_PREFER_BUSY_POLL: u32 = 72;
pub const SO_BUSY_POLL_BUDGET: u32 = 73;
pub const SO_NETNS_COOKIE: u32 = 80;
pub const SO_BUF_LOCK: u32 = 81;
pub const SO_RESERVE_MEM: u32 = 82;
pub const SO_TXREHASH: u32 = 83;
pub const SO_RCVMARK: u32 = 84;
pub const SO_PASSPIDFD: u32 = 85;
pub const SO_PEERPIDFD: u32 = 86;
pub const SO_DEVMEM_LINEAR: u32 = 87;
pub const SCM_DEVMEM_LINEAR: u32 = 87;
pub const SO_DEVMEM_DMABUF: u32 = 88;
pub const SCM_DEVMEM_DMABUF: u32 = 88;
pub const SO_DEVMEM_DONTNEED: u32 = 89;
pub const SCM_TS_OPT_ID: u32 = 90;
pub const SO_TIMESTAMP: u32 = 29;
pub const SO_TIMESTAMPNS: u32 = 33;
pub const SO_TIMESTAMPING: u32 = 35;
pub const SO_RCVTIMEO: u32 = 8192;
pub const SO_SNDTIMEO: u32 = 16384;
pub const SCM_TIMESTAMP: u32 = 29;
pub const SCM_TIMESTAMPNS: u32 = 33;
pub const SCM_TIMESTAMPING: u32 = 35;
pub const SYS_SOCKET: u32 = 1;
pub const SYS_BIND: u32 = 2;
pub const SYS_CONNECT: u32 = 3;
pub const SYS_LISTEN: u32 = 4;
pub const SYS_ACCEPT: u32 = 5;
pub const SYS_GETSOCKNAME: u32 = 6;
pub const SYS_GETPEERNAME: u32 = 7;
pub const SYS_SOCKETPAIR: u32 = 8;
pub const SYS_SEND: u32 = 9;
pub const SYS_RECV: u32 = 10;
pub const SYS_SENDTO: u32 = 11;
pub const SYS_RECVFROM: u32 = 12;
pub const SYS_SHUTDOWN: u32 = 13;
pub const SYS_SETSOCKOPT: u32 = 14;
pub const SYS_GETSOCKOPT: u32 = 15;
pub const SYS_SENDMSG: u32 = 16;
pub const SYS_RECVMSG: u32 = 17;
pub const SYS_ACCEPT4: u32 = 18;
pub const SYS_RECVMMSG: u32 = 19;
pub const SYS_SENDMMSG: u32 = 20;
pub const __SO_ACCEPTCON: u32 = 65536;
pub const TCP_MSS_DEFAULT: u32 = 536;
pub const TCP_MSS_DESIRED: u32 = 1220;
pub const TCP_NODELAY: u32 = 1;
pub const TCP_MAXSEG: u32 = 2;
pub const TCP_CORK: u32 = 3;
pub const TCP_KEEPIDLE: u32 = 4;
pub const TCP_KEEPINTVL: u32 = 5;
pub const TCP_KEEPCNT: u32 = 6;
pub const TCP_SYNCNT: u32 = 7;
pub const TCP_LINGER2: u32 = 8;
pub const TCP_DEFER_ACCEPT: u32 = 9;
pub const TCP_WINDOW_CLAMP: u32 = 10;
pub const TCP_INFO: u32 = 11;
pub const TCP_QUICKACK: u32 = 12;
pub const TCP_CONGESTION: u32 = 13;
pub const TCP_MD5SIG: u32 = 14;
pub const TCP_THIN_LINEAR_TIMEOUTS: u32 = 16;
pub const TCP_THIN_DUPACK: u32 = 17;
pub const TCP_USER_TIMEOUT: u32 = 18;
pub const TCP_REPAIR: u32 = 19;
pub const TCP_REPAIR_QUEUE: u32 = 20;
pub const TCP_QUEUE_SEQ: u32 = 21;
pub const TCP_REPAIR_OPTIONS: u32 = 22;
pub const TCP_FASTOPEN: u32 = 23;
pub const TCP_TIMESTAMP: u32 = 24;
pub const TCP_NOTSENT_LOWAT: u32 = 25;
pub const TCP_CC_INFO: u32 = 26;
pub const TCP_SAVE_SYN: u32 = 27;
pub const TCP_SAVED_SYN: u32 = 28;
pub const TCP_REPAIR_WINDOW: u32 = 29;
pub const TCP_FASTOPEN_CONNECT: u32 = 30;
pub const TCP_ULP: u32 = 31;
pub const TCP_MD5SIG_EXT: u32 = 32;
pub const TCP_FASTOPEN_KEY: u32 = 33;
pub const TCP_FASTOPEN_NO_COOKIE: u32 = 34;
pub const TCP_ZEROCOPY_RECEIVE: u32 = 35;
pub const TCP_INQ: u32 = 36;
pub const TCP_CM_INQ: u32 = 36;
pub const TCP_TX_DELAY: u32 = 37;
pub const TCP_AO_ADD_KEY: u32 = 38;
pub const TCP_AO_DEL_KEY: u32 = 39;
pub const TCP_AO_INFO: u32 = 40;
pub const TCP_AO_GET_KEYS: u32 = 41;
pub const TCP_AO_REPAIR: u32 = 42;
pub const TCP_IS_MPTCP: u32 = 43;
pub const TCP_REPAIR_ON: u32 = 1;
pub const TCP_REPAIR_OFF: u32 = 0;
pub const TCP_REPAIR_OFF_NO_WP: i32 = -1;
pub const TCPI_OPT_TIMESTAMPS: u32 = 1;
pub const TCPI_OPT_SACK: u32 = 2;
pub const TCPI_OPT_WSCALE: u32 = 4;
pub const TCPI_OPT_ECN: u32 = 8;
pub const TCPI_OPT_ECN_SEEN: u32 = 16;
pub const TCPI_OPT_SYN_DATA: u32 = 32;
pub const TCPI_OPT_USEC_TS: u32 = 64;
pub const TCP_MD5SIG_MAXKEYLEN: u32 = 80;
pub const TCP_MD5SIG_FLAG_PREFIX: u32 = 1;
pub const TCP_MD5SIG_FLAG_IFINDEX: u32 = 2;
pub const TCP_AO_MAXKEYLEN: u32 = 80;
pub const TCP_AO_KEYF_IFINDEX: u32 = 1;
pub const TCP_AO_KEYF_EXCLUDE_OPT: u32 = 2;
pub const TCP_RECEIVE_ZEROCOPY_FLAG_TLB_CLEAN_HINT: u32 = 1;
pub const UNIX_PATH_MAX: u32 = 108;
pub const IFNAMSIZ: u32 = 16;
pub const IFALIASZ: u32 = 256;
pub const ALTIFNAMSIZ: u32 = 128;
pub const GENERIC_HDLC_VERSION: u32 = 4;
pub const CLOCK_DEFAULT: u32 = 0;
pub const CLOCK_EXT: u32 = 1;
pub const CLOCK_INT: u32 = 2;
pub const CLOCK_TXINT: u32 = 3;
pub const CLOCK_TXFROMRX: u32 = 4;
pub const ENCODING_DEFAULT: u32 = 0;
pub const ENCODING_NRZ: u32 = 1;
pub const ENCODING_NRZI: u32 = 2;
pub const ENCODING_FM_MARK: u32 = 3;
pub const ENCODING_FM_SPACE: u32 = 4;
pub const ENCODING_MANCHESTER: u32 = 5;
pub const PARITY_DEFAULT: u32 = 0;
pub const PARITY_NONE: u32 = 1;
pub const PARITY_CRC16_PR0: u32 = 2;
pub const PARITY_CRC16_PR1: u32 = 3;
pub const PARITY_CRC16_PR0_CCITT: u32 = 4;
pub const PARITY_CRC16_PR1_CCITT: u32 = 5;
pub const PARITY_CRC32_PR0_CCITT: u32 = 6;
pub const PARITY_CRC32_PR1_CCITT: u32 = 7;
pub const LMI_DEFAULT: u32 = 0;
pub const LMI_NONE: u32 = 1;
pub const LMI_ANSI: u32 = 2;
pub const LMI_CCITT: u32 = 3;
pub const LMI_CISCO: u32 = 4;
pub const IF_GET_IFACE: u32 = 1;
pub const IF_GET_PROTO: u32 = 2;
pub const IF_IFACE_V35: u32 = 4096;
pub const IF_IFACE_V24: u32 = 4097;
pub const IF_IFACE_X21: u32 = 4098;
pub const IF_IFACE_T1: u32 = 4099;
pub const IF_IFACE_E1: u32 = 4100;
pub const IF_IFACE_SYNC_SERIAL: u32 = 4101;
pub const IF_IFACE_X21D: u32 = 4102;
pub const IF_PROTO_HDLC: u32 = 8192;
pub const IF_PROTO_PPP: u32 = 8193;
pub const IF_PROTO_CISCO: u32 = 8194;
pub const IF_PROTO_FR: u32 = 8195;
pub const IF_PROTO_FR_ADD_PVC: u32 = 8196;
pub const IF_PROTO_FR_DEL_PVC: u32 = 8197;
pub const IF_PROTO_X25: u32 = 8198;
pub const IF_PROTO_HDLC_ETH: u32 = 8199;
pub const IF_PROTO_FR_ADD_ETH_PVC: u32 = 8200;
pub const IF_PROTO_FR_DEL_ETH_PVC: u32 = 8201;
pub const IF_PROTO_FR_PVC: u32 = 8202;
pub const IF_PROTO_FR_ETH_PVC: u32 = 8203;
pub const IF_PROTO_RAW: u32 = 8204;
pub const IFHWADDRLEN: u32 = 6;
pub const NF_DROP: u32 = 0;
pub const NF_ACCEPT: u32 = 1;
pub const NF_STOLEN: u32 = 2;
pub const NF_QUEUE: u32 = 3;
pub const NF_REPEAT: u32 = 4;
pub const NF_STOP: u32 = 5;
pub const NF_MAX_VERDICT: u32 = 5;
pub const NF_VERDICT_MASK: u32 = 255;
pub const NF_VERDICT_FLAG_QUEUE_BYPASS: u32 = 32768;
pub const NF_VERDICT_QMASK: u32 = **********;
pub const NF_VERDICT_QBITS: u32 = 16;
pub const NF_VERDICT_BITS: u32 = 16;
pub const NF_IP6_PRE_ROUTING: u32 = 0;
pub const NF_IP6_LOCAL_IN: u32 = 1;
pub const NF_IP6_FORWARD: u32 = 2;
pub const NF_IP6_LOCAL_OUT: u32 = 3;
pub const NF_IP6_POST_ROUTING: u32 = 4;
pub const NF_IP6_NUMHOOKS: u32 = 5;
pub const XT_FUNCTION_MAXNAMELEN: u32 = 30;
pub const XT_EXTENSION_MAXNAMELEN: u32 = 29;
pub const XT_TABLE_MAXNAMELEN: u32 = 32;
pub const XT_CONTINUE: u32 = **********;
pub const XT_RETURN: i32 = -5;
pub const XT_STANDARD_TARGET: &[u8; 1] = b"\0";
pub const XT_ERROR_TARGET: &[u8; 6] = b"ERROR\0";
pub const XT_INV_PROTO: u32 = 64;
pub const IP6T_FUNCTION_MAXNAMELEN: u32 = 30;
pub const IP6T_TABLE_MAXNAMELEN: u32 = 32;
pub const IP6T_CONTINUE: u32 = **********;
pub const IP6T_RETURN: i32 = -5;
pub const XT_TCP_INV_SRCPT: u32 = 1;
pub const XT_TCP_INV_DSTPT: u32 = 2;
pub const XT_TCP_INV_FLAGS: u32 = 4;
pub const XT_TCP_INV_OPTION: u32 = 8;
pub const XT_TCP_INV_MASK: u32 = 15;
pub const XT_UDP_INV_SRCPT: u32 = 1;
pub const XT_UDP_INV_DSTPT: u32 = 2;
pub const XT_UDP_INV_MASK: u32 = 3;
pub const IP6T_TCP_INV_SRCPT: u32 = 1;
pub const IP6T_TCP_INV_DSTPT: u32 = 2;
pub const IP6T_TCP_INV_FLAGS: u32 = 4;
pub const IP6T_TCP_INV_OPTION: u32 = 8;
pub const IP6T_TCP_INV_MASK: u32 = 15;
pub const IP6T_UDP_INV_SRCPT: u32 = 1;
pub const IP6T_UDP_INV_DSTPT: u32 = 2;
pub const IP6T_UDP_INV_MASK: u32 = 3;
pub const IP6T_STANDARD_TARGET: &[u8; 1] = b"\0";
pub const IP6T_ERROR_TARGET: &[u8; 6] = b"ERROR\0";
pub const IP6T_F_PROTO: u32 = 1;
pub const IP6T_F_TOS: u32 = 2;
pub const IP6T_F_GOTO: u32 = 4;
pub const IP6T_F_MASK: u32 = 7;
pub const IP6T_INV_VIA_IN: u32 = 1;
pub const IP6T_INV_VIA_OUT: u32 = 2;
pub const IP6T_INV_TOS: u32 = 4;
pub const IP6T_INV_SRCIP: u32 = 8;
pub const IP6T_INV_DSTIP: u32 = 16;
pub const IP6T_INV_FRAG: u32 = 32;
pub const IP6T_INV_PROTO: u32 = 64;
pub const IP6T_INV_MASK: u32 = 127;
pub const IP6T_BASE_CTL: u32 = 64;
pub const IP6T_SO_SET_REPLACE: u32 = 64;
pub const IP6T_SO_SET_ADD_COUNTERS: u32 = 65;
pub const IP6T_SO_SET_MAX: u32 = 65;
pub const IP6T_SO_GET_INFO: u32 = 64;
pub const IP6T_SO_GET_ENTRIES: u32 = 65;
pub const IP6T_SO_GET_REVISION_MATCH: u32 = 68;
pub const IP6T_SO_GET_REVISION_TARGET: u32 = 69;
pub const IP6T_SO_GET_MAX: u32 = 69;
pub const IP6T_SO_ORIGINAL_DST: u32 = 80;
pub const IP6T_ICMP_INV: u32 = 1;
pub const NF_IP_PRE_ROUTING: u32 = 0;
pub const NF_IP_LOCAL_IN: u32 = 1;
pub const NF_IP_FORWARD: u32 = 2;
pub const NF_IP_LOCAL_OUT: u32 = 3;
pub const NF_IP_POST_ROUTING: u32 = 4;
pub const NF_IP_NUMHOOKS: u32 = 5;
pub const SO_ORIGINAL_DST: u32 = 80;
pub const SHUT_RD: u32 = 0;
pub const SHUT_WR: u32 = 1;
pub const SHUT_RDWR: u32 = 2;
pub const SOCK_STREAM: u32 = 1;
pub const SOCK_DGRAM: u32 = 2;
pub const SOCK_RAW: u32 = 3;
pub const SOCK_RDM: u32 = 4;
pub const SOCK_SEQPACKET: u32 = 5;
pub const MSG_DONTWAIT: u32 = 64;
pub const AF_UNSPEC: u32 = 0;
pub const AF_UNIX: u32 = 1;
pub const AF_INET: u32 = 2;
pub const AF_AX25: u32 = 3;
pub const AF_IPX: u32 = 4;
pub const AF_APPLETALK: u32 = 5;
pub const AF_NETROM: u32 = 6;
pub const AF_BRIDGE: u32 = 7;
pub const AF_ATMPVC: u32 = 8;
pub const AF_X25: u32 = 9;
pub const AF_INET6: u32 = 10;
pub const AF_ROSE: u32 = 11;
pub const AF_DECnet: u32 = 12;
pub const AF_NETBEUI: u32 = 13;
pub const AF_SECURITY: u32 = 14;
pub const AF_KEY: u32 = 15;
pub const AF_NETLINK: u32 = 16;
pub const AF_PACKET: u32 = 17;
pub const AF_ASH: u32 = 18;
pub const AF_ECONET: u32 = 19;
pub const AF_ATMSVC: u32 = 20;
pub const AF_RDS: u32 = 21;
pub const AF_SNA: u32 = 22;
pub const AF_IRDA: u32 = 23;
pub const AF_PPPOX: u32 = 24;
pub const AF_WANPIPE: u32 = 25;
pub const AF_LLC: u32 = 26;
pub const AF_CAN: u32 = 29;
pub const AF_TIPC: u32 = 30;
pub const AF_BLUETOOTH: u32 = 31;
pub const AF_IUCV: u32 = 32;
pub const AF_RXRPC: u32 = 33;
pub const AF_ISDN: u32 = 34;
pub const AF_PHONET: u32 = 35;
pub const AF_IEEE802154: u32 = 36;
pub const AF_CAIF: u32 = 37;
pub const AF_ALG: u32 = 38;
pub const AF_NFC: u32 = 39;
pub const AF_VSOCK: u32 = 40;
pub const AF_KCM: u32 = 41;
pub const AF_QIPCRTR: u32 = 42;
pub const AF_SMC: u32 = 43;
pub const AF_XDP: u32 = 44;
pub const AF_MCTP: u32 = 45;
pub const AF_MAX: u32 = 46;
pub const MSG_OOB: u32 = 1;
pub const MSG_PEEK: u32 = 2;
pub const MSG_DONTROUTE: u32 = 4;
pub const MSG_CTRUNC: u32 = 8;
pub const MSG_PROBE: u32 = 16;
pub const MSG_TRUNC: u32 = 32;
pub const MSG_EOR: u32 = 128;
pub const MSG_WAITALL: u32 = 256;
pub const MSG_FIN: u32 = 512;
pub const MSG_SYN: u32 = 1024;
pub const MSG_CONFIRM: u32 = 2048;
pub const MSG_RST: u32 = 4096;
pub const MSG_ERRQUEUE: u32 = 8192;
pub const MSG_NOSIGNAL: u32 = 16384;
pub const MSG_MORE: u32 = 32768;
pub const MSG_CMSG_CLOEXEC: u32 = 1073741824;
pub const SCM_RIGHTS: u32 = 1;
pub const SCM_CREDENTIALS: u32 = 2;
pub const SCM_SECURITY: u32 = 3;
pub const SOL_IP: u32 = 0;
pub const SOL_TCP: u32 = 6;
pub const SOL_UDP: u32 = 17;
pub const SOL_IPV6: u32 = 41;
pub const SOL_ICMPV6: u32 = 58;
pub const SOL_SCTP: u32 = 132;
pub const SOL_UDPLITE: u32 = 136;
pub const SOL_RAW: u32 = 255;
pub const SOL_IPX: u32 = 256;
pub const SOL_AX25: u32 = 257;
pub const SOL_ATALK: u32 = 258;
pub const SOL_NETROM: u32 = 259;
pub const SOL_ROSE: u32 = 260;
pub const SOL_DECNET: u32 = 261;
pub const SOL_X25: u32 = 262;
pub const SOL_PACKET: u32 = 263;
pub const SOL_ATM: u32 = 264;
pub const SOL_AAL: u32 = 265;
pub const SOL_IRDA: u32 = 266;
pub const SOL_NETBEUI: u32 = 267;
pub const SOL_LLC: u32 = 268;
pub const SOL_DCCP: u32 = 269;
pub const SOL_NETLINK: u32 = 270;
pub const SOL_TIPC: u32 = 271;
pub const SOL_RXRPC: u32 = 272;
pub const SOL_PPPOL2TP: u32 = 273;
pub const SOL_BLUETOOTH: u32 = 274;
pub const SOL_PNPIPE: u32 = 275;
pub const SOL_RDS: u32 = 276;
pub const SOL_IUCV: u32 = 277;
pub const SOL_CAIF: u32 = 278;
pub const SOL_ALG: u32 = 279;
pub const SOL_NFC: u32 = 280;
pub const SOL_KCM: u32 = 281;
pub const SOL_TLS: u32 = 282;
pub const SOL_XDP: u32 = 283;
pub const SOL_MPTCP: u32 = 284;
pub const SOL_MCTP: u32 = 285;
pub const SOL_SMC: u32 = 286;
pub const IPPROTO_IP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_IP;
pub const IPPROTO_ICMP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_ICMP;
pub const IPPROTO_IGMP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_IGMP;
pub const IPPROTO_IPIP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_IPIP;
pub const IPPROTO_TCP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_TCP;
pub const IPPROTO_EGP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_EGP;
pub const IPPROTO_PUP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_PUP;
pub const IPPROTO_UDP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_UDP;
pub const IPPROTO_IDP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_IDP;
pub const IPPROTO_TP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_TP;
pub const IPPROTO_DCCP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_DCCP;
pub const IPPROTO_IPV6: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_IPV6;
pub const IPPROTO_RSVP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_RSVP;
pub const IPPROTO_GRE: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_GRE;
pub const IPPROTO_ESP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_ESP;
pub const IPPROTO_AH: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_AH;
pub const IPPROTO_MTP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_MTP;
pub const IPPROTO_BEETPH: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_BEETPH;
pub const IPPROTO_ENCAP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_ENCAP;
pub const IPPROTO_PIM: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_PIM;
pub const IPPROTO_COMP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_COMP;
pub const IPPROTO_L2TP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_L2TP;
pub const IPPROTO_SCTP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_SCTP;
pub const IPPROTO_UDPLITE: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_UDPLITE;
pub const IPPROTO_MPLS: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_MPLS;
pub const IPPROTO_ETHERNET: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_ETHERNET;
pub const IPPROTO_RAW: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_RAW;
pub const IPPROTO_SMC: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_SMC;
pub const IPPROTO_MPTCP: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_MPTCP;
pub const IPPROTO_MAX: _bindgen_ty_1 = _bindgen_ty_1::IPPROTO_MAX;
pub const IPV4_DEVCONF_FORWARDING: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_FORWARDING;
pub const IPV4_DEVCONF_MC_FORWARDING: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_MC_FORWARDING;
pub const IPV4_DEVCONF_PROXY_ARP: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_PROXY_ARP;
pub const IPV4_DEVCONF_ACCEPT_REDIRECTS: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_ACCEPT_REDIRECTS;
pub const IPV4_DEVCONF_SECURE_REDIRECTS: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_SECURE_REDIRECTS;
pub const IPV4_DEVCONF_SEND_REDIRECTS: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_SEND_REDIRECTS;
pub const IPV4_DEVCONF_SHARED_MEDIA: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_SHARED_MEDIA;
pub const IPV4_DEVCONF_RP_FILTER: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_RP_FILTER;
pub const IPV4_DEVCONF_ACCEPT_SOURCE_ROUTE: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_ACCEPT_SOURCE_ROUTE;
pub const IPV4_DEVCONF_BOOTP_RELAY: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_BOOTP_RELAY;
pub const IPV4_DEVCONF_LOG_MARTIANS: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_LOG_MARTIANS;
pub const IPV4_DEVCONF_TAG: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_TAG;
pub const IPV4_DEVCONF_ARPFILTER: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_ARPFILTER;
pub const IPV4_DEVCONF_MEDIUM_ID: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_MEDIUM_ID;
pub const IPV4_DEVCONF_NOXFRM: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_NOXFRM;
pub const IPV4_DEVCONF_NOPOLICY: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_NOPOLICY;
pub const IPV4_DEVCONF_FORCE_IGMP_VERSION: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_FORCE_IGMP_VERSION;
pub const IPV4_DEVCONF_ARP_ANNOUNCE: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_ARP_ANNOUNCE;
pub const IPV4_DEVCONF_ARP_IGNORE: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_ARP_IGNORE;
pub const IPV4_DEVCONF_PROMOTE_SECONDARIES: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_PROMOTE_SECONDARIES;
pub const IPV4_DEVCONF_ARP_ACCEPT: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_ARP_ACCEPT;
pub const IPV4_DEVCONF_ARP_NOTIFY: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_ARP_NOTIFY;
pub const IPV4_DEVCONF_ACCEPT_LOCAL: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_ACCEPT_LOCAL;
pub const IPV4_DEVCONF_SRC_VMARK: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_SRC_VMARK;
pub const IPV4_DEVCONF_PROXY_ARP_PVLAN: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_PROXY_ARP_PVLAN;
pub const IPV4_DEVCONF_ROUTE_LOCALNET: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_ROUTE_LOCALNET;
pub const IPV4_DEVCONF_IGMPV2_UNSOLICITED_REPORT_INTERVAL: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_IGMPV2_UNSOLICITED_REPORT_INTERVAL;
pub const IPV4_DEVCONF_IGMPV3_UNSOLICITED_REPORT_INTERVAL: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_IGMPV3_UNSOLICITED_REPORT_INTERVAL;
pub const IPV4_DEVCONF_IGNORE_ROUTES_WITH_LINKDOWN: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_IGNORE_ROUTES_WITH_LINKDOWN;
pub const IPV4_DEVCONF_DROP_UNICAST_IN_L2_MULTICAST: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_DROP_UNICAST_IN_L2_MULTICAST;
pub const IPV4_DEVCONF_DROP_GRATUITOUS_ARP: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_DROP_GRATUITOUS_ARP;
pub const IPV4_DEVCONF_BC_FORWARDING: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_BC_FORWARDING;
pub const IPV4_DEVCONF_ARP_EVICT_NOCARRIER: _bindgen_ty_2 = _bindgen_ty_2::IPV4_DEVCONF_ARP_EVICT_NOCARRIER;
pub const __IPV4_DEVCONF_MAX: _bindgen_ty_2 = _bindgen_ty_2::__IPV4_DEVCONF_MAX;
pub const DEVCONF_FORWARDING: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_FORWARDING;
pub const DEVCONF_HOPLIMIT: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_HOPLIMIT;
pub const DEVCONF_MTU6: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_MTU6;
pub const DEVCONF_ACCEPT_RA: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_RA;
pub const DEVCONF_ACCEPT_REDIRECTS: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_REDIRECTS;
pub const DEVCONF_AUTOCONF: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_AUTOCONF;
pub const DEVCONF_DAD_TRANSMITS: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_DAD_TRANSMITS;
pub const DEVCONF_RTR_SOLICITS: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_RTR_SOLICITS;
pub const DEVCONF_RTR_SOLICIT_INTERVAL: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_RTR_SOLICIT_INTERVAL;
pub const DEVCONF_RTR_SOLICIT_DELAY: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_RTR_SOLICIT_DELAY;
pub const DEVCONF_USE_TEMPADDR: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_USE_TEMPADDR;
pub const DEVCONF_TEMP_VALID_LFT: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_TEMP_VALID_LFT;
pub const DEVCONF_TEMP_PREFERED_LFT: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_TEMP_PREFERED_LFT;
pub const DEVCONF_REGEN_MAX_RETRY: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_REGEN_MAX_RETRY;
pub const DEVCONF_MAX_DESYNC_FACTOR: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_MAX_DESYNC_FACTOR;
pub const DEVCONF_MAX_ADDRESSES: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_MAX_ADDRESSES;
pub const DEVCONF_FORCE_MLD_VERSION: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_FORCE_MLD_VERSION;
pub const DEVCONF_ACCEPT_RA_DEFRTR: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_RA_DEFRTR;
pub const DEVCONF_ACCEPT_RA_PINFO: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_RA_PINFO;
pub const DEVCONF_ACCEPT_RA_RTR_PREF: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_RA_RTR_PREF;
pub const DEVCONF_RTR_PROBE_INTERVAL: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_RTR_PROBE_INTERVAL;
pub const DEVCONF_ACCEPT_RA_RT_INFO_MAX_PLEN: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_RA_RT_INFO_MAX_PLEN;
pub const DEVCONF_PROXY_NDP: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_PROXY_NDP;
pub const DEVCONF_OPTIMISTIC_DAD: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_OPTIMISTIC_DAD;
pub const DEVCONF_ACCEPT_SOURCE_ROUTE: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_SOURCE_ROUTE;
pub const DEVCONF_MC_FORWARDING: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_MC_FORWARDING;
pub const DEVCONF_DISABLE_IPV6: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_DISABLE_IPV6;
pub const DEVCONF_ACCEPT_DAD: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_DAD;
pub const DEVCONF_FORCE_TLLAO: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_FORCE_TLLAO;
pub const DEVCONF_NDISC_NOTIFY: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_NDISC_NOTIFY;
pub const DEVCONF_MLDV1_UNSOLICITED_REPORT_INTERVAL: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_MLDV1_UNSOLICITED_REPORT_INTERVAL;
pub const DEVCONF_MLDV2_UNSOLICITED_REPORT_INTERVAL: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_MLDV2_UNSOLICITED_REPORT_INTERVAL;
pub const DEVCONF_SUPPRESS_FRAG_NDISC: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_SUPPRESS_FRAG_NDISC;
pub const DEVCONF_ACCEPT_RA_FROM_LOCAL: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_RA_FROM_LOCAL;
pub const DEVCONF_USE_OPTIMISTIC: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_USE_OPTIMISTIC;
pub const DEVCONF_ACCEPT_RA_MTU: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_RA_MTU;
pub const DEVCONF_STABLE_SECRET: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_STABLE_SECRET;
pub const DEVCONF_USE_OIF_ADDRS_ONLY: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_USE_OIF_ADDRS_ONLY;
pub const DEVCONF_ACCEPT_RA_MIN_HOP_LIMIT: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_RA_MIN_HOP_LIMIT;
pub const DEVCONF_IGNORE_ROUTES_WITH_LINKDOWN: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_IGNORE_ROUTES_WITH_LINKDOWN;
pub const DEVCONF_DROP_UNICAST_IN_L2_MULTICAST: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_DROP_UNICAST_IN_L2_MULTICAST;
pub const DEVCONF_DROP_UNSOLICITED_NA: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_DROP_UNSOLICITED_NA;
pub const DEVCONF_KEEP_ADDR_ON_DOWN: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_KEEP_ADDR_ON_DOWN;
pub const DEVCONF_RTR_SOLICIT_MAX_INTERVAL: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_RTR_SOLICIT_MAX_INTERVAL;
pub const DEVCONF_SEG6_ENABLED: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_SEG6_ENABLED;
pub const DEVCONF_SEG6_REQUIRE_HMAC: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_SEG6_REQUIRE_HMAC;
pub const DEVCONF_ENHANCED_DAD: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ENHANCED_DAD;
pub const DEVCONF_ADDR_GEN_MODE: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ADDR_GEN_MODE;
pub const DEVCONF_DISABLE_POLICY: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_DISABLE_POLICY;
pub const DEVCONF_ACCEPT_RA_RT_INFO_MIN_PLEN: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_RA_RT_INFO_MIN_PLEN;
pub const DEVCONF_NDISC_TCLASS: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_NDISC_TCLASS;
pub const DEVCONF_RPL_SEG_ENABLED: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_RPL_SEG_ENABLED;
pub const DEVCONF_RA_DEFRTR_METRIC: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_RA_DEFRTR_METRIC;
pub const DEVCONF_IOAM6_ENABLED: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_IOAM6_ENABLED;
pub const DEVCONF_IOAM6_ID: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_IOAM6_ID;
pub const DEVCONF_IOAM6_ID_WIDE: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_IOAM6_ID_WIDE;
pub const DEVCONF_NDISC_EVICT_NOCARRIER: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_NDISC_EVICT_NOCARRIER;
pub const DEVCONF_ACCEPT_UNTRACKED_NA: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_UNTRACKED_NA;
pub const DEVCONF_ACCEPT_RA_MIN_LFT: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_ACCEPT_RA_MIN_LFT;
pub const DEVCONF_MAX: _bindgen_ty_3 = _bindgen_ty_3::DEVCONF_MAX;
pub const TCP_FLAG_CWR: _bindgen_ty_4 = _bindgen_ty_4::TCP_FLAG_CWR;
pub const TCP_FLAG_ECE: _bindgen_ty_4 = _bindgen_ty_4::TCP_FLAG_ECE;
pub const TCP_FLAG_URG: _bindgen_ty_4 = _bindgen_ty_4::TCP_FLAG_URG;
pub const TCP_FLAG_ACK: _bindgen_ty_4 = _bindgen_ty_4::TCP_FLAG_ACK;
pub const TCP_FLAG_PSH: _bindgen_ty_4 = _bindgen_ty_4::TCP_FLAG_PSH;
pub const TCP_FLAG_RST: _bindgen_ty_4 = _bindgen_ty_4::TCP_FLAG_RST;
pub const TCP_FLAG_SYN: _bindgen_ty_4 = _bindgen_ty_4::TCP_FLAG_SYN;
pub const TCP_FLAG_FIN: _bindgen_ty_4 = _bindgen_ty_4::TCP_FLAG_FIN;
pub const TCP_RESERVED_BITS: _bindgen_ty_4 = _bindgen_ty_4::TCP_RESERVED_BITS;
pub const TCP_DATA_OFFSET: _bindgen_ty_4 = _bindgen_ty_4::TCP_DATA_OFFSET;
pub const TCP_NO_QUEUE: _bindgen_ty_5 = _bindgen_ty_5::TCP_NO_QUEUE;
pub const TCP_RECV_QUEUE: _bindgen_ty_5 = _bindgen_ty_5::TCP_RECV_QUEUE;
pub const TCP_SEND_QUEUE: _bindgen_ty_5 = _bindgen_ty_5::TCP_SEND_QUEUE;
pub const TCP_QUEUES_NR: _bindgen_ty_5 = _bindgen_ty_5::TCP_QUEUES_NR;
pub const TCP_NLA_PAD: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_PAD;
pub const TCP_NLA_BUSY: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_BUSY;
pub const TCP_NLA_RWND_LIMITED: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_RWND_LIMITED;
pub const TCP_NLA_SNDBUF_LIMITED: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_SNDBUF_LIMITED;
pub const TCP_NLA_DATA_SEGS_OUT: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_DATA_SEGS_OUT;
pub const TCP_NLA_TOTAL_RETRANS: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_TOTAL_RETRANS;
pub const TCP_NLA_PACING_RATE: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_PACING_RATE;
pub const TCP_NLA_DELIVERY_RATE: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_DELIVERY_RATE;
pub const TCP_NLA_SND_CWND: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_SND_CWND;
pub const TCP_NLA_REORDERING: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_REORDERING;
pub const TCP_NLA_MIN_RTT: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_MIN_RTT;
pub const TCP_NLA_RECUR_RETRANS: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_RECUR_RETRANS;
pub const TCP_NLA_DELIVERY_RATE_APP_LMT: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_DELIVERY_RATE_APP_LMT;
pub const TCP_NLA_SNDQ_SIZE: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_SNDQ_SIZE;
pub const TCP_NLA_CA_STATE: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_CA_STATE;
pub const TCP_NLA_SND_SSTHRESH: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_SND_SSTHRESH;
pub const TCP_NLA_DELIVERED: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_DELIVERED;
pub const TCP_NLA_DELIVERED_CE: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_DELIVERED_CE;
pub const TCP_NLA_BYTES_SENT: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_BYTES_SENT;
pub const TCP_NLA_BYTES_RETRANS: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_BYTES_RETRANS;
pub const TCP_NLA_DSACK_DUPS: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_DSACK_DUPS;
pub const TCP_NLA_REORD_SEEN: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_REORD_SEEN;
pub const TCP_NLA_SRTT: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_SRTT;
pub const TCP_NLA_TIMEOUT_REHASH: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_TIMEOUT_REHASH;
pub const TCP_NLA_BYTES_NOTSENT: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_BYTES_NOTSENT;
pub const TCP_NLA_EDT: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_EDT;
pub const TCP_NLA_TTL: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_TTL;
pub const TCP_NLA_REHASH: _bindgen_ty_6 = _bindgen_ty_6::TCP_NLA_REHASH;
pub const IF_OPER_UNKNOWN: _bindgen_ty_7 = _bindgen_ty_7::IF_OPER_UNKNOWN;
pub const IF_OPER_NOTPRESENT: _bindgen_ty_7 = _bindgen_ty_7::IF_OPER_NOTPRESENT;
pub const IF_OPER_DOWN: _bindgen_ty_7 = _bindgen_ty_7::IF_OPER_DOWN;
pub const IF_OPER_LOWERLAYERDOWN: _bindgen_ty_7 = _bindgen_ty_7::IF_OPER_LOWERLAYERDOWN;
pub const IF_OPER_TESTING: _bindgen_ty_7 = _bindgen_ty_7::IF_OPER_TESTING;
pub const IF_OPER_DORMANT: _bindgen_ty_7 = _bindgen_ty_7::IF_OPER_DORMANT;
pub const IF_OPER_UP: _bindgen_ty_7 = _bindgen_ty_7::IF_OPER_UP;
pub const IF_LINK_MODE_DEFAULT: _bindgen_ty_8 = _bindgen_ty_8::IF_LINK_MODE_DEFAULT;
pub const IF_LINK_MODE_DORMANT: _bindgen_ty_8 = _bindgen_ty_8::IF_LINK_MODE_DORMANT;
pub const IF_LINK_MODE_TESTING: _bindgen_ty_8 = _bindgen_ty_8::IF_LINK_MODE_TESTING;
pub const NFPROTO_UNSPEC: _bindgen_ty_9 = _bindgen_ty_9::NFPROTO_UNSPEC;
pub const NFPROTO_INET: _bindgen_ty_9 = _bindgen_ty_9::NFPROTO_INET;
pub const NFPROTO_IPV4: _bindgen_ty_9 = _bindgen_ty_9::NFPROTO_IPV4;
pub const NFPROTO_ARP: _bindgen_ty_9 = _bindgen_ty_9::NFPROTO_ARP;
pub const NFPROTO_NETDEV: _bindgen_ty_9 = _bindgen_ty_9::NFPROTO_NETDEV;
pub const NFPROTO_BRIDGE: _bindgen_ty_9 = _bindgen_ty_9::NFPROTO_BRIDGE;
pub const NFPROTO_IPV6: _bindgen_ty_9 = _bindgen_ty_9::NFPROTO_IPV6;
pub const NFPROTO_DECNET: _bindgen_ty_9 = _bindgen_ty_9::NFPROTO_DECNET;
pub const NFPROTO_NUMPROTO: _bindgen_ty_9 = _bindgen_ty_9::NFPROTO_NUMPROTO;
pub const SOF_TIMESTAMPING_TX_HARDWARE: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_TX_HARDWARE;
pub const SOF_TIMESTAMPING_TX_SOFTWARE: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_TX_SOFTWARE;
pub const SOF_TIMESTAMPING_RX_HARDWARE: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_RX_HARDWARE;
pub const SOF_TIMESTAMPING_RX_SOFTWARE: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_RX_SOFTWARE;
pub const SOF_TIMESTAMPING_SOFTWARE: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_SOFTWARE;
pub const SOF_TIMESTAMPING_SYS_HARDWARE: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_SYS_HARDWARE;
pub const SOF_TIMESTAMPING_RAW_HARDWARE: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_RAW_HARDWARE;
pub const SOF_TIMESTAMPING_OPT_ID: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_OPT_ID;
pub const SOF_TIMESTAMPING_TX_SCHED: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_TX_SCHED;
pub const SOF_TIMESTAMPING_TX_ACK: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_TX_ACK;
pub const SOF_TIMESTAMPING_OPT_CMSG: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_OPT_CMSG;
pub const SOF_TIMESTAMPING_OPT_TSONLY: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_OPT_TSONLY;
pub const SOF_TIMESTAMPING_OPT_STATS: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_OPT_STATS;
pub const SOF_TIMESTAMPING_OPT_PKTINFO: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_OPT_PKTINFO;
pub const SOF_TIMESTAMPING_OPT_TX_SWHW: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_OPT_TX_SWHW;
pub const SOF_TIMESTAMPING_BIND_PHC: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_BIND_PHC;
pub const SOF_TIMESTAMPING_OPT_ID_TCP: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_OPT_ID_TCP;
pub const SOF_TIMESTAMPING_OPT_RX_FILTER: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_OPT_RX_FILTER;
pub const SOF_TIMESTAMPING_LAST: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_OPT_RX_FILTER;
pub const SOF_TIMESTAMPING_MASK: _bindgen_ty_10 = _bindgen_ty_10::SOF_TIMESTAMPING_MASK;
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_1 {
IPPROTO_IP = 0,
IPPROTO_ICMP = 1,
IPPROTO_IGMP = 2,
IPPROTO_IPIP = 4,
IPPROTO_TCP = 6,
IPPROTO_EGP = 8,
IPPROTO_PUP = 12,
IPPROTO_UDP = 17,
IPPROTO_IDP = 22,
IPPROTO_TP = 29,
IPPROTO_DCCP = 33,
IPPROTO_IPV6 = 41,
IPPROTO_RSVP = 46,
IPPROTO_GRE = 47,
IPPROTO_ESP = 50,
IPPROTO_AH = 51,
IPPROTO_MTP = 92,
IPPROTO_BEETPH = 94,
IPPROTO_ENCAP = 98,
IPPROTO_PIM = 103,
IPPROTO_COMP = 108,
IPPROTO_L2TP = 115,
IPPROTO_SCTP = 132,
IPPROTO_UDPLITE = 136,
IPPROTO_MPLS = 137,
IPPROTO_ETHERNET = 143,
IPPROTO_RAW = 255,
IPPROTO_SMC = 256,
IPPROTO_MPTCP = 262,
IPPROTO_MAX = 263,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_2 {
IPV4_DEVCONF_FORWARDING = 1,
IPV4_DEVCONF_MC_FORWARDING = 2,
IPV4_DEVCONF_PROXY_ARP = 3,
IPV4_DEVCONF_ACCEPT_REDIRECTS = 4,
IPV4_DEVCONF_SECURE_REDIRECTS = 5,
IPV4_DEVCONF_SEND_REDIRECTS = 6,
IPV4_DEVCONF_SHARED_MEDIA = 7,
IPV4_DEVCONF_RP_FILTER = 8,
IPV4_DEVCONF_ACCEPT_SOURCE_ROUTE = 9,
IPV4_DEVCONF_BOOTP_RELAY = 10,
IPV4_DEVCONF_LOG_MARTIANS = 11,
IPV4_DEVCONF_TAG = 12,
IPV4_DEVCONF_ARPFILTER = 13,
IPV4_DEVCONF_MEDIUM_ID = 14,
IPV4_DEVCONF_NOXFRM = 15,
IPV4_DEVCONF_NOPOLICY = 16,
IPV4_DEVCONF_FORCE_IGMP_VERSION = 17,
IPV4_DEVCONF_ARP_ANNOUNCE = 18,
IPV4_DEVCONF_ARP_IGNORE = 19,
IPV4_DEVCONF_PROMOTE_SECONDARIES = 20,
IPV4_DEVCONF_ARP_ACCEPT = 21,
IPV4_DEVCONF_ARP_NOTIFY = 22,
IPV4_DEVCONF_ACCEPT_LOCAL = 23,
IPV4_DEVCONF_SRC_VMARK = 24,
IPV4_DEVCONF_PROXY_ARP_PVLAN = 25,
IPV4_DEVCONF_ROUTE_LOCALNET = 26,
IPV4_DEVCONF_IGMPV2_UNSOLICITED_REPORT_INTERVAL = 27,
IPV4_DEVCONF_IGMPV3_UNSOLICITED_REPORT_INTERVAL = 28,
IPV4_DEVCONF_IGNORE_ROUTES_WITH_LINKDOWN = 29,
IPV4_DEVCONF_DROP_UNICAST_IN_L2_MULTICAST = 30,
IPV4_DEVCONF_DROP_GRATUITOUS_ARP = 31,
IPV4_DEVCONF_BC_FORWARDING = 32,
IPV4_DEVCONF_ARP_EVICT_NOCARRIER = 33,
__IPV4_DEVCONF_MAX = 34,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_3 {
DEVCONF_FORWARDING = 0,
DEVCONF_HOPLIMIT = 1,
DEVCONF_MTU6 = 2,
DEVCONF_ACCEPT_RA = 3,
DEVCONF_ACCEPT_REDIRECTS = 4,
DEVCONF_AUTOCONF = 5,
DEVCONF_DAD_TRANSMITS = 6,
DEVCONF_RTR_SOLICITS = 7,
DEVCONF_RTR_SOLICIT_INTERVAL = 8,
DEVCONF_RTR_SOLICIT_DELAY = 9,
DEVCONF_USE_TEMPADDR = 10,
DEVCONF_TEMP_VALID_LFT = 11,
DEVCONF_TEMP_PREFERED_LFT = 12,
DEVCONF_REGEN_MAX_RETRY = 13,
DEVCONF_MAX_DESYNC_FACTOR = 14,
DEVCONF_MAX_ADDRESSES = 15,
DEVCONF_FORCE_MLD_VERSION = 16,
DEVCONF_ACCEPT_RA_DEFRTR = 17,
DEVCONF_ACCEPT_RA_PINFO = 18,
DEVCONF_ACCEPT_RA_RTR_PREF = 19,
DEVCONF_RTR_PROBE_INTERVAL = 20,
DEVCONF_ACCEPT_RA_RT_INFO_MAX_PLEN = 21,
DEVCONF_PROXY_NDP = 22,
DEVCONF_OPTIMISTIC_DAD = 23,
DEVCONF_ACCEPT_SOURCE_ROUTE = 24,
DEVCONF_MC_FORWARDING = 25,
DEVCONF_DISABLE_IPV6 = 26,
DEVCONF_ACCEPT_DAD = 27,
DEVCONF_FORCE_TLLAO = 28,
DEVCONF_NDISC_NOTIFY = 29,
DEVCONF_MLDV1_UNSOLICITED_REPORT_INTERVAL = 30,
DEVCONF_MLDV2_UNSOLICITED_REPORT_INTERVAL = 31,
DEVCONF_SUPPRESS_FRAG_NDISC = 32,
DEVCONF_ACCEPT_RA_FROM_LOCAL = 33,
DEVCONF_USE_OPTIMISTIC = 34,
DEVCONF_ACCEPT_RA_MTU = 35,
DEVCONF_STABLE_SECRET = 36,
DEVCONF_USE_OIF_ADDRS_ONLY = 37,
DEVCONF_ACCEPT_RA_MIN_HOP_LIMIT = 38,
DEVCONF_IGNORE_ROUTES_WITH_LINKDOWN = 39,
DEVCONF_DROP_UNICAST_IN_L2_MULTICAST = 40,
DEVCONF_DROP_UNSOLICITED_NA = 41,
DEVCONF_KEEP_ADDR_ON_DOWN = 42,
DEVCONF_RTR_SOLICIT_MAX_INTERVAL = 43,
DEVCONF_SEG6_ENABLED = 44,
DEVCONF_SEG6_REQUIRE_HMAC = 45,
DEVCONF_ENHANCED_DAD = 46,
DEVCONF_ADDR_GEN_MODE = 47,
DEVCONF_DISABLE_POLICY = 48,
DEVCONF_ACCEPT_RA_RT_INFO_MIN_PLEN = 49,
DEVCONF_NDISC_TCLASS = 50,
DEVCONF_RPL_SEG_ENABLED = 51,
DEVCONF_RA_DEFRTR_METRIC = 52,
DEVCONF_IOAM6_ENABLED = 53,
DEVCONF_IOAM6_ID = 54,
DEVCONF_IOAM6_ID_WIDE = 55,
DEVCONF_NDISC_EVICT_NOCARRIER = 56,
DEVCONF_ACCEPT_UNTRACKED_NA = 57,
DEVCONF_ACCEPT_RA_MIN_LFT = 58,
DEVCONF_MAX = 59,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum socket_state {
SS_FREE = 0,
SS_UNCONNECTED = 1,
SS_CONNECTING = 2,
SS_CONNECTED = 3,
SS_DISCONNECTING = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_4 {
TCP_FLAG_CWR = 8388608,
TCP_FLAG_ECE = 4194304,
TCP_FLAG_URG = 2097152,
TCP_FLAG_ACK = 1048576,
TCP_FLAG_PSH = 524288,
TCP_FLAG_RST = 262144,
TCP_FLAG_SYN = 131072,
TCP_FLAG_FIN = 65536,
TCP_RESERVED_BITS = 251658240,
TCP_DATA_OFFSET = 4026531840,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_5 {
TCP_NO_QUEUE = 0,
TCP_RECV_QUEUE = 1,
TCP_SEND_QUEUE = 2,
TCP_QUEUES_NR = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum tcp_fastopen_client_fail {
TFO_STATUS_UNSPEC = 0,
TFO_COOKIE_UNAVAILABLE = 1,
TFO_DATA_NOT_ACKED = 2,
TFO_SYN_RETRANSMITTED = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum tcp_ca_state {
TCP_CA_Open = 0,
TCP_CA_Disorder = 1,
TCP_CA_CWR = 2,
TCP_CA_Recovery = 3,
TCP_CA_Loss = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_6 {
TCP_NLA_PAD = 0,
TCP_NLA_BUSY = 1,
TCP_NLA_RWND_LIMITED = 2,
TCP_NLA_SNDBUF_LIMITED = 3,
TCP_NLA_DATA_SEGS_OUT = 4,
TCP_NLA_TOTAL_RETRANS = 5,
TCP_NLA_PACING_RATE = 6,
TCP_NLA_DELIVERY_RATE = 7,
TCP_NLA_SND_CWND = 8,
TCP_NLA_REORDERING = 9,
TCP_NLA_MIN_RTT = 10,
TCP_NLA_RECUR_RETRANS = 11,
TCP_NLA_DELIVERY_RATE_APP_LMT = 12,
TCP_NLA_SNDQ_SIZE = 13,
TCP_NLA_CA_STATE = 14,
TCP_NLA_SND_SSTHRESH = 15,
TCP_NLA_DELIVERED = 16,
TCP_NLA_DELIVERED_CE = 17,
TCP_NLA_BYTES_SENT = 18,
TCP_NLA_BYTES_RETRANS = 19,
TCP_NLA_DSACK_DUPS = 20,
TCP_NLA_REORD_SEEN = 21,
TCP_NLA_SRTT = 22,
TCP_NLA_TIMEOUT_REHASH = 23,
TCP_NLA_BYTES_NOTSENT = 24,
TCP_NLA_EDT = 25,
TCP_NLA_TTL = 26,
TCP_NLA_REHASH = 27,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum net_device_flags {
IFF_UP = 1,
IFF_BROADCAST = 2,
IFF_DEBUG = 4,
IFF_LOOPBACK = 8,
IFF_POINTOPOINT = 16,
IFF_NOTRAILERS = 32,
IFF_RUNNING = 64,
IFF_NOARP = 128,
IFF_PROMISC = 256,
IFF_ALLMULTI = 512,
IFF_MASTER = 1024,
IFF_SLAVE = 2048,
IFF_MULTICAST = 4096,
IFF_PORTSEL = 8192,
IFF_AUTOMEDIA = 16384,
IFF_DYNAMIC = 32768,
IFF_LOWER_UP = 65536,
IFF_DORMANT = 131072,
IFF_ECHO = 262144,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_7 {
IF_OPER_UNKNOWN = 0,
IF_OPER_NOTPRESENT = 1,
IF_OPER_DOWN = 2,
IF_OPER_LOWERLAYERDOWN = 3,
IF_OPER_TESTING = 4,
IF_OPER_DORMANT = 5,
IF_OPER_UP = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_8 {
IF_LINK_MODE_DEFAULT = 0,
IF_LINK_MODE_DORMANT = 1,
IF_LINK_MODE_TESTING = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum nf_inet_hooks {
NF_INET_PRE_ROUTING = 0,
NF_INET_LOCAL_IN = 1,
NF_INET_FORWARD = 2,
NF_INET_LOCAL_OUT = 3,
NF_INET_POST_ROUTING = 4,
NF_INET_NUMHOOKS = 5,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum nf_dev_hooks {
NF_NETDEV_INGRESS = 0,
NF_NETDEV_EGRESS = 1,
NF_NETDEV_NUMHOOKS = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_9 {
NFPROTO_UNSPEC = 0,
NFPROTO_INET = 1,
NFPROTO_IPV4 = 2,
NFPROTO_ARP = 3,
NFPROTO_NETDEV = 5,
NFPROTO_BRIDGE = 7,
NFPROTO_IPV6 = 10,
NFPROTO_DECNET = 12,
NFPROTO_NUMPROTO = 13,
}
#[repr(i32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum nf_ip6_hook_priorities {
NF_IP6_PRI_FIRST = -**********,
NF_IP6_PRI_RAW_BEFORE_DEFRAG = -450,
NF_IP6_PRI_CONNTRACK_DEFRAG = -400,
NF_IP6_PRI_RAW = -300,
NF_IP6_PRI_SELINUX_FIRST = -225,
NF_IP6_PRI_CONNTRACK = -200,
NF_IP6_PRI_MANGLE = -150,
NF_IP6_PRI_NAT_DST = -100,
NF_IP6_PRI_FILTER = 0,
NF_IP6_PRI_SECURITY = 50,
NF_IP6_PRI_NAT_SRC = 100,
NF_IP6_PRI_SELINUX_LAST = 225,
NF_IP6_PRI_CONNTRACK_HELPER = 300,
NF_IP6_PRI_LAST = 2147483647,
}
#[repr(i32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum nf_ip_hook_priorities {
NF_IP_PRI_FIRST = -**********,
NF_IP_PRI_RAW_BEFORE_DEFRAG = -450,
NF_IP_PRI_CONNTRACK_DEFRAG = -400,
NF_IP_PRI_RAW = -300,
NF_IP_PRI_SELINUX_FIRST = -225,
NF_IP_PRI_CONNTRACK = -200,
NF_IP_PRI_MANGLE = -150,
NF_IP_PRI_NAT_DST = -100,
NF_IP_PRI_FILTER = 0,
NF_IP_PRI_SECURITY = 50,
NF_IP_PRI_NAT_SRC = 100,
NF_IP_PRI_SELINUX_LAST = 225,
NF_IP_PRI_CONNTRACK_HELPER = 300,
NF_IP_PRI_CONNTRACK_CONFIRM = 2147483647,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_10 {
SOF_TIMESTAMPING_TX_HARDWARE = 1,
SOF_TIMESTAMPING_TX_SOFTWARE = 2,
SOF_TIMESTAMPING_RX_HARDWARE = 4,
SOF_TIMESTAMPING_RX_SOFTWARE = 8,
SOF_TIMESTAMPING_SOFTWARE = 16,
SOF_TIMESTAMPING_SYS_HARDWARE = 32,
SOF_TIMESTAMPING_RAW_HARDWARE = 64,
SOF_TIMESTAMPING_OPT_ID = 128,
SOF_TIMESTAMPING_TX_SCHED = 256,
SOF_TIMESTAMPING_TX_ACK = 512,
SOF_TIMESTAMPING_OPT_CMSG = 1024,
SOF_TIMESTAMPING_OPT_TSONLY = 2048,
SOF_TIMESTAMPING_OPT_STATS = 4096,
SOF_TIMESTAMPING_OPT_PKTINFO = 8192,
SOF_TIMESTAMPING_OPT_TX_SWHW = 16384,
SOF_TIMESTAMPING_BIND_PHC = 32768,
SOF_TIMESTAMPING_OPT_ID_TCP = 65536,
SOF_TIMESTAMPING_OPT_RX_FILTER = 131072,
SOF_TIMESTAMPING_MASK = 262143,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum hwtstamp_flags {
HWTSTAMP_FLAG_BONDED_PHC_INDEX = 1,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum hwtstamp_tx_types {
HWTSTAMP_TX_OFF = 0,
HWTSTAMP_TX_ON = 1,
HWTSTAMP_TX_ONESTEP_SYNC = 2,
HWTSTAMP_TX_ONESTEP_P2P = 3,
__HWTSTAMP_TX_CNT = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum hwtstamp_rx_filters {
HWTSTAMP_FILTER_NONE = 0,
HWTSTAMP_FILTER_ALL = 1,
HWTSTAMP_FILTER_SOME = 2,
HWTSTAMP_FILTER_PTP_V1_L4_EVENT = 3,
HWTSTAMP_FILTER_PTP_V1_L4_SYNC = 4,
HWTSTAMP_FILTER_PTP_V1_L4_DELAY_REQ = 5,
HWTSTAMP_FILTER_PTP_V2_L4_EVENT = 6,
HWTSTAMP_FILTER_PTP_V2_L4_SYNC = 7,
HWTSTAMP_FILTER_PTP_V2_L4_DELAY_REQ = 8,
HWTSTAMP_FILTER_PTP_V2_L2_EVENT = 9,
HWTSTAMP_FILTER_PTP_V2_L2_SYNC = 10,
HWTSTAMP_FILTER_PTP_V2_L2_DELAY_REQ = 11,
HWTSTAMP_FILTER_PTP_V2_EVENT = 12,
HWTSTAMP_FILTER_PTP_V2_SYNC = 13,
HWTSTAMP_FILTER_PTP_V2_DELAY_REQ = 14,
HWTSTAMP_FILTER_NTP_ALL = 15,
__HWTSTAMP_FILTER_CNT = 16,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum txtime_flags {
SOF_TXTIME_DEADLINE_MODE = 1,
SOF_TXTIME_REPORT_ERRORS = 2,
SOF_TXTIME_FLAGS_MASK = 3,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union __kernel_sockaddr_storage__bindgen_ty_1 {
pub __bindgen_anon_1: __kernel_sockaddr_storage__bindgen_ty_1__bindgen_ty_1,
pub __align: *mut crate::ctypes::c_void,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union iphdr__bindgen_ty_1 {
pub __bindgen_anon_1: iphdr__bindgen_ty_1__bindgen_ty_1,
pub addrs: iphdr__bindgen_ty_1__bindgen_ty_2,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union in6_addr__bindgen_ty_1 {
pub u6_addr8: [__u8; 16usize],
pub u6_addr16: [__be16; 8usize],
pub u6_addr32: [__be32; 4usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union ipv6hdr__bindgen_ty_1 {
pub __bindgen_anon_1: ipv6hdr__bindgen_ty_1__bindgen_ty_1,
pub addrs: ipv6hdr__bindgen_ty_1__bindgen_ty_2,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union tcp_word_hdr {
pub hdr: tcphdr,
pub words: [__be32; 5usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union if_settings__bindgen_ty_1 {
pub raw_hdlc: *mut raw_hdlc_proto,
pub cisco: *mut cisco_proto,
pub fr: *mut fr_proto,
pub fr_pvc: *mut fr_proto_pvc,
pub fr_pvc_info: *mut fr_proto_pvc_info,
pub x25: *mut x25_hdlc_proto,
pub sync: *mut sync_serial_settings,
pub te1: *mut te1_settings,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union ifreq__bindgen_ty_1 {
pub ifrn_name: [crate::ctypes::c_char; 16usize],
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union ifreq__bindgen_ty_2 {
pub ifru_addr: sockaddr,
pub ifru_dstaddr: sockaddr,
pub ifru_broadaddr: sockaddr,
pub ifru_netmask: sockaddr,
pub ifru_hwaddr: sockaddr,
pub ifru_flags: crate::ctypes::c_short,
pub ifru_ivalue: crate::ctypes::c_int,
pub ifru_mtu: crate::ctypes::c_int,
pub ifru_map: ifmap,
pub ifru_slave: [crate::ctypes::c_char; 16usize],
pub ifru_newname: [crate::ctypes::c_char; 16usize],
pub ifru_data: *mut crate::ctypes::c_void,
pub ifru_settings: if_settings,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union ifconf__bindgen_ty_1 {
pub ifcu_buf: *mut crate::ctypes::c_char,
pub ifcu_req: *mut ifreq,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union nf_inet_addr {
pub all: [__u32; 4usize],
pub ip: __be32,
pub ip6: [__be32; 4usize],
pub in_: in_addr,
pub in6: in6_addr,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union xt_entry_match__bindgen_ty_1 {
pub user: xt_entry_match__bindgen_ty_1__bindgen_ty_1,
pub kernel: xt_entry_match__bindgen_ty_1__bindgen_ty_2,
pub match_size: __u16,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union xt_entry_target__bindgen_ty_1 {
pub user: xt_entry_target__bindgen_ty_1__bindgen_ty_1,
pub kernel: xt_entry_target__bindgen_ty_1__bindgen_ty_2,
pub target_size: __u16,
}
impl<Storage> __BindgenBitfieldUnit<Storage> {
#[inline]
pub const fn new(storage: Storage) -> Self {
Self { storage }
}
}
impl<Storage> __BindgenBitfieldUnit<Storage>
where
Storage: AsRef<[u8]> + AsMut<[u8]>,
{
#[inline]
fn extract_bit(byte: u8, index: usize) -> bool {
let bit_index = if cfg!(target_endian = "big") { 7 - (index % 8) } else { index % 8 };
let mask = 1 << bit_index;
byte & mask == mask
}
#[inline]
pub fn get_bit(&self, index: usize) -> bool {
debug_assert!(index / 8 < self.storage.as_ref().len());
let byte_index = index / 8;
let byte = self.storage.as_ref()[byte_index];
Self::extract_bit(byte, index)
}
#[inline]
pub unsafe fn raw_get_bit(this: *const Self, index: usize) -> bool {
debug_assert!(index / 8 < core::mem::size_of::<Storage>());
let byte_index = index / 8;
let byte = *(core::ptr::addr_of!((*this).storage) as *const u8).offset(byte_index as isize);
Self::extract_bit(byte, index)
}
#[inline]
fn change_bit(byte: u8, index: usize, val: bool) -> u8 {
let bit_index = if cfg!(target_endian = "big") { 7 - (index % 8) } else { index % 8 };
let mask = 1 << bit_index;
if val {
byte | mask
} else {
byte & !mask
}
}
#[inline]
pub fn set_bit(&mut self, index: usize, val: bool) {
debug_assert!(index / 8 < self.storage.as_ref().len());
let byte_index = index / 8;
let byte = &mut self.storage.as_mut()[byte_index];
*byte = Self::change_bit(*byte, index, val);
}
#[inline]
pub unsafe fn raw_set_bit(this: *mut Self, index: usize, val: bool) {
debug_assert!(index / 8 < core::mem::size_of::<Storage>());
let byte_index = index / 8;
let byte = (core::ptr::addr_of_mut!((*this).storage) as *mut u8).offset(byte_index as isize);
*byte = Self::change_bit(*byte, index, val);
}
#[inline]
pub fn get(&self, bit_offset: usize, bit_width: u8) -> u64 {
debug_assert!(bit_width <= 64);
debug_assert!(bit_offset / 8 < self.storage.as_ref().len());
debug_assert!((bit_offset + (bit_width as usize)) / 8 <= self.storage.as_ref().len());
let mut val = 0;
for i in 0..(bit_width as usize) {
if self.get_bit(i + bit_offset) {
let index = if cfg!(target_endian = "big") { bit_width as usize - 1 - i } else { i };
val |= 1 << index;
}
}
val
}
#[inline]
pub unsafe fn raw_get(this: *const Self, bit_offset: usize, bit_width: u8) -> u64 {
debug_assert!(bit_width <= 64);
debug_assert!(bit_offset / 8 < core::mem::size_of::<Storage>());
debug_assert!((bit_offset + (bit_width as usize)) / 8 <= core::mem::size_of::<Storage>());
let mut val = 0;
for i in 0..(bit_width as usize) {
if Self::raw_get_bit(this, i + bit_offset) {
let index = if cfg!(target_endian = "big") { bit_width as usize - 1 - i } else { i };
val |= 1 << index;
}
}
val
}
#[inline]
pub fn set(&mut self, bit_offset: usize, bit_width: u8, val: u64) {
debug_assert!(bit_width <= 64);
debug_assert!(bit_offset / 8 < self.storage.as_ref().len());
debug_assert!((bit_offset + (bit_width as usize)) / 8 <= self.storage.as_ref().len());
for i in 0..(bit_width as usize) {
let mask = 1 << i;
let val_bit_is_set = val & mask == mask;
let index = if cfg!(target_endian = "big") { bit_width as usize - 1 - i } else { i };
self.set_bit(index + bit_offset, val_bit_is_set);
}
}
#[inline]
pub unsafe fn raw_set(this: *mut Self, bit_offset: usize, bit_width: u8, val: u64) {
debug_assert!(bit_width <= 64);
debug_assert!(bit_offset / 8 < core::mem::size_of::<Storage>());
debug_assert!((bit_offset + (bit_width as usize)) / 8 <= core::mem::size_of::<Storage>());
for i in 0..(bit_width as usize) {
let mask = 1 << i;
let val_bit_is_set = val & mask == mask;
let index = if cfg!(target_endian = "big") { bit_width as usize - 1 - i } else { i };
Self::raw_set_bit(this, index + bit_offset, val_bit_is_set);
}
}
}
impl<T> __IncompleteArrayField<T> {
#[inline]
pub const fn new() -> Self {
__IncompleteArrayField(::core::marker::PhantomData, [])
}
#[inline]
pub fn as_ptr(&self) -> *const T {
self as *const _ as *const T
}
#[inline]
pub fn as_mut_ptr(&mut self) -> *mut T {
self as *mut _ as *mut T
}
#[inline]
pub unsafe fn as_slice(&self, len: usize) -> &[T] {
::core::slice::from_raw_parts(self.as_ptr(), len)
}
#[inline]
pub unsafe fn as_mut_slice(&mut self, len: usize) -> &mut [T] {
::core::slice::from_raw_parts_mut(self.as_mut_ptr(), len)
}
}
impl<T> ::core::fmt::Debug for __IncompleteArrayField<T> {
fn fmt(&self, fmt: &mut ::core::fmt::Formatter<'_>) -> ::core::fmt::Result {
fmt.write_str("__IncompleteArrayField")
}
}
impl<T> __BindgenUnionField<T> {
#[inline]
pub const fn new() -> Self {
__BindgenUnionField(::core::marker::PhantomData)
}
#[inline]
pub unsafe fn as_ref(&self) -> &T {
::core::mem::transmute(self)
}
#[inline]
pub unsafe fn as_mut(&mut self) -> &mut T {
::core::mem::transmute(self)
}
}
impl<T> ::core::default::Default for __BindgenUnionField<T> {
#[inline]
fn default() -> Self {
Self::new()
}
}
impl<T> ::core::clone::Clone for __BindgenUnionField<T> {
#[inline]
fn clone(&self) -> Self {
*self
}
}
impl<T> ::core::marker::Copy for __BindgenUnionField<T> {}
impl<T> ::core::fmt::Debug for __BindgenUnionField<T> {
fn fmt(&self, fmt: &mut ::core::fmt::Formatter<'_>) -> ::core::fmt::Result {
fmt.write_str("__BindgenUnionField")
}
}
impl<T> ::core::hash::Hash for __BindgenUnionField<T> {
fn hash<H: ::core::hash::Hasher>(&self, _state: &mut H) {}
}
impl<T> ::core::cmp::PartialEq for __BindgenUnionField<T> {
fn eq(&self, _other: &__BindgenUnionField<T>) -> bool {
true
}
}
impl<T> ::core::cmp::Eq for __BindgenUnionField<T> {}
impl iphdr {
#[inline]
pub fn version(&self) -> __u8 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(0usize, 4u8) as u8) }
}
#[inline]
pub fn set_version(&mut self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
self._bitfield_1.set(0usize, 4u8, val as u64)
}
}
#[inline]
pub unsafe fn version_raw(this: *const Self) -> __u8 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 0usize, 4u8) as u8) }
}
#[inline]
pub unsafe fn set_version_raw(this: *mut Self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 0usize, 4u8, val as u64)
}
}
#[inline]
pub fn ihl(&self) -> __u8 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(4usize, 4u8) as u8) }
}
#[inline]
pub fn set_ihl(&mut self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
self._bitfield_1.set(4usize, 4u8, val as u64)
}
}
#[inline]
pub unsafe fn ihl_raw(this: *const Self) -> __u8 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 4usize, 4u8) as u8) }
}
#[inline]
pub unsafe fn set_ihl_raw(this: *mut Self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 4usize, 4u8, val as u64)
}
}
#[inline]
pub fn new_bitfield_1(version: __u8, ihl: __u8) -> __BindgenBitfieldUnit<[u8; 1usize]> {
let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
__bindgen_bitfield_unit.set(0usize, 4u8, {
let version: u8 = unsafe { ::core::mem::transmute(version) };
version as u64
});
__bindgen_bitfield_unit.set(4usize, 4u8, {
let ihl: u8 = unsafe { ::core::mem::transmute(ihl) };
ihl as u64
});
__bindgen_bitfield_unit
}
}
impl ipv6hdr {
#[inline]
pub fn version(&self) -> __u8 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(0usize, 4u8) as u8) }
}
#[inline]
pub fn set_version(&mut self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
self._bitfield_1.set(0usize, 4u8, val as u64)
}
}
#[inline]
pub unsafe fn version_raw(this: *const Self) -> __u8 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 0usize, 4u8) as u8) }
}
#[inline]
pub unsafe fn set_version_raw(this: *mut Self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 0usize, 4u8, val as u64)
}
}
#[inline]
pub fn priority(&self) -> __u8 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(4usize, 4u8) as u8) }
}
#[inline]
pub fn set_priority(&mut self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
self._bitfield_1.set(4usize, 4u8, val as u64)
}
}
#[inline]
pub unsafe fn priority_raw(this: *const Self) -> __u8 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 4usize, 4u8) as u8) }
}
#[inline]
pub unsafe fn set_priority_raw(this: *mut Self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 1usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 4usize, 4u8, val as u64)
}
}
#[inline]
pub fn new_bitfield_1(version: __u8, priority: __u8) -> __BindgenBitfieldUnit<[u8; 1usize]> {
let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 1usize]> = Default::default();
__bindgen_bitfield_unit.set(0usize, 4u8, {
let version: u8 = unsafe { ::core::mem::transmute(version) };
version as u64
});
__bindgen_bitfield_unit.set(4usize, 4u8, {
let priority: u8 = unsafe { ::core::mem::transmute(priority) };
priority as u64
});
__bindgen_bitfield_unit
}
}
impl tcphdr {
#[inline]
pub fn doff(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(0usize, 4u8) as u16) }
}
#[inline]
pub fn set_doff(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(0usize, 4u8, val as u64)
}
}
#[inline]
pub unsafe fn doff_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 0usize, 4u8) as u16) }
}
#[inline]
pub unsafe fn set_doff_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 0usize, 4u8, val as u64)
}
}
#[inline]
pub fn res1(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(4usize, 4u8) as u16) }
}
#[inline]
pub fn set_res1(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(4usize, 4u8, val as u64)
}
}
#[inline]
pub unsafe fn res1_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 4usize, 4u8) as u16) }
}
#[inline]
pub unsafe fn set_res1_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 4usize, 4u8, val as u64)
}
}
#[inline]
pub fn cwr(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(8usize, 1u8) as u16) }
}
#[inline]
pub fn set_cwr(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(8usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn cwr_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 8usize, 1u8) as u16) }
}
#[inline]
pub unsafe fn set_cwr_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 8usize, 1u8, val as u64)
}
}
#[inline]
pub fn ece(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(9usize, 1u8) as u16) }
}
#[inline]
pub fn set_ece(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(9usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn ece_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 9usize, 1u8) as u16) }
}
#[inline]
pub unsafe fn set_ece_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 9usize, 1u8, val as u64)
}
}
#[inline]
pub fn urg(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(10usize, 1u8) as u16) }
}
#[inline]
pub fn set_urg(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(10usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn urg_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 10usize, 1u8) as u16) }
}
#[inline]
pub unsafe fn set_urg_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 10usize, 1u8, val as u64)
}
}
#[inline]
pub fn ack(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(11usize, 1u8) as u16) }
}
#[inline]
pub fn set_ack(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(11usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn ack_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 11usize, 1u8) as u16) }
}
#[inline]
pub unsafe fn set_ack_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 11usize, 1u8, val as u64)
}
}
#[inline]
pub fn psh(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(12usize, 1u8) as u16) }
}
#[inline]
pub fn set_psh(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(12usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn psh_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 12usize, 1u8) as u16) }
}
#[inline]
pub unsafe fn set_psh_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 12usize, 1u8, val as u64)
}
}
#[inline]
pub fn rst(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(13usize, 1u8) as u16) }
}
#[inline]
pub fn set_rst(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(13usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn rst_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 13usize, 1u8) as u16) }
}
#[inline]
pub unsafe fn set_rst_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 13usize, 1u8, val as u64)
}
}
#[inline]
pub fn syn(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(14usize, 1u8) as u16) }
}
#[inline]
pub fn set_syn(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(14usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn syn_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 14usize, 1u8) as u16) }
}
#[inline]
pub unsafe fn set_syn_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 14usize, 1u8, val as u64)
}
}
#[inline]
pub fn fin(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(15usize, 1u8) as u16) }
}
#[inline]
pub fn set_fin(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(15usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn fin_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 15usize, 1u8) as u16) }
}
#[inline]
pub unsafe fn set_fin_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 15usize, 1u8, val as u64)
}
}
#[inline]
pub fn new_bitfield_1(doff: __u16, res1: __u16, cwr: __u16, ece: __u16, urg: __u16, ack: __u16, psh: __u16, rst: __u16, syn: __u16, fin: __u16) -> __BindgenBitfieldUnit<[u8; 2usize]> {
let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 2usize]> = Default::default();
__bindgen_bitfield_unit.set(0usize, 4u8, {
let doff: u16 = unsafe { ::core::mem::transmute(doff) };
doff as u64
});
__bindgen_bitfield_unit.set(4usize, 4u8, {
let res1: u16 = unsafe { ::core::mem::transmute(res1) };
res1 as u64
});
__bindgen_bitfield_unit.set(8usize, 1u8, {
let cwr: u16 = unsafe { ::core::mem::transmute(cwr) };
cwr as u64
});
__bindgen_bitfield_unit.set(9usize, 1u8, {
let ece: u16 = unsafe { ::core::mem::transmute(ece) };
ece as u64
});
__bindgen_bitfield_unit.set(10usize, 1u8, {
let urg: u16 = unsafe { ::core::mem::transmute(urg) };
urg as u64
});
__bindgen_bitfield_unit.set(11usize, 1u8, {
let ack: u16 = unsafe { ::core::mem::transmute(ack) };
ack as u64
});
__bindgen_bitfield_unit.set(12usize, 1u8, {
let psh: u16 = unsafe { ::core::mem::transmute(psh) };
psh as u64
});
__bindgen_bitfield_unit.set(13usize, 1u8, {
let rst: u16 = unsafe { ::core::mem::transmute(rst) };
rst as u64
});
__bindgen_bitfield_unit.set(14usize, 1u8, {
let syn: u16 = unsafe { ::core::mem::transmute(syn) };
syn as u64
});
__bindgen_bitfield_unit.set(15usize, 1u8, {
let fin: u16 = unsafe { ::core::mem::transmute(fin) };
fin as u64
});
__bindgen_bitfield_unit
}
}
impl tcp_info {
#[inline]
pub fn tcpi_snd_wscale(&self) -> __u8 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(0usize, 4u8) as u8) }
}
#[inline]
pub fn set_tcpi_snd_wscale(&mut self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
self._bitfield_1.set(0usize, 4u8, val as u64)
}
}
#[inline]
pub unsafe fn tcpi_snd_wscale_raw(this: *const Self) -> __u8 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 0usize, 4u8) as u8) }
}
#[inline]
pub unsafe fn set_tcpi_snd_wscale_raw(this: *mut Self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 0usize, 4u8, val as u64)
}
}
#[inline]
pub fn tcpi_rcv_wscale(&self) -> __u8 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(4usize, 4u8) as u8) }
}
#[inline]
pub fn set_tcpi_rcv_wscale(&mut self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
self._bitfield_1.set(4usize, 4u8, val as u64)
}
}
#[inline]
pub unsafe fn tcpi_rcv_wscale_raw(this: *const Self) -> __u8 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 4usize, 4u8) as u8) }
}
#[inline]
pub unsafe fn set_tcpi_rcv_wscale_raw(this: *mut Self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 4usize, 4u8, val as u64)
}
}
#[inline]
pub fn tcpi_delivery_rate_app_limited(&self) -> __u8 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(8usize, 1u8) as u8) }
}
#[inline]
pub fn set_tcpi_delivery_rate_app_limited(&mut self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
self._bitfield_1.set(8usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn tcpi_delivery_rate_app_limited_raw(this: *const Self) -> __u8 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 8usize, 1u8) as u8) }
}
#[inline]
pub unsafe fn set_tcpi_delivery_rate_app_limited_raw(this: *mut Self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 8usize, 1u8, val as u64)
}
}
#[inline]
pub fn tcpi_fastopen_client_fail(&self) -> __u8 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(9usize, 2u8) as u8) }
}
#[inline]
pub fn set_tcpi_fastopen_client_fail(&mut self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
self._bitfield_1.set(9usize, 2u8, val as u64)
}
}
#[inline]
pub unsafe fn tcpi_fastopen_client_fail_raw(this: *const Self) -> __u8 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 9usize, 2u8) as u8) }
}
#[inline]
pub unsafe fn set_tcpi_fastopen_client_fail_raw(this: *mut Self, val: __u8) {
unsafe {
let val: u8 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 9usize, 2u8, val as u64)
}
}
#[inline]
pub fn new_bitfield_1(tcpi_snd_wscale: __u8, tcpi_rcv_wscale: __u8, tcpi_delivery_rate_app_limited: __u8, tcpi_fastopen_client_fail: __u8) -> __BindgenBitfieldUnit<[u8; 2usize]> {
let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 2usize]> = Default::default();
__bindgen_bitfield_unit.set(0usize, 4u8, {
let tcpi_snd_wscale: u8 = unsafe { ::core::mem::transmute(tcpi_snd_wscale) };
tcpi_snd_wscale as u64
});
__bindgen_bitfield_unit.set(4usize, 4u8, {
let tcpi_rcv_wscale: u8 = unsafe { ::core::mem::transmute(tcpi_rcv_wscale) };
tcpi_rcv_wscale as u64
});
__bindgen_bitfield_unit.set(8usize, 1u8, {
let tcpi_delivery_rate_app_limited: u8 = unsafe { ::core::mem::transmute(tcpi_delivery_rate_app_limited) };
tcpi_delivery_rate_app_limited as u64
});
__bindgen_bitfield_unit.set(9usize, 2u8, {
let tcpi_fastopen_client_fail: u8 = unsafe { ::core::mem::transmute(tcpi_fastopen_client_fail) };
tcpi_fastopen_client_fail as u64
});
__bindgen_bitfield_unit
}
}
impl tcp_ao_add {
#[inline]
pub fn set_current(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u32) }
}
#[inline]
pub fn set_set_current(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(0usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn set_current_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 0usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_set_current_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 0usize, 1u8, val as u64)
}
}
#[inline]
pub fn set_rnext(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u32) }
}
#[inline]
pub fn set_set_rnext(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(1usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn set_rnext_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 1usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_set_rnext_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 1usize, 1u8, val as u64)
}
}
#[inline]
pub fn reserved(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(2usize, 30u8) as u32) }
}
#[inline]
pub fn set_reserved(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(2usize, 30u8, val as u64)
}
}
#[inline]
pub unsafe fn reserved_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 2usize, 30u8) as u32) }
}
#[inline]
pub unsafe fn set_reserved_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 2usize, 30u8, val as u64)
}
}
#[inline]
pub fn new_bitfield_1(set_current: __u32, set_rnext: __u32, reserved: __u32) -> __BindgenBitfieldUnit<[u8; 4usize]> {
let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 4usize]> = Default::default();
__bindgen_bitfield_unit.set(0usize, 1u8, {
let set_current: u32 = unsafe { ::core::mem::transmute(set_current) };
set_current as u64
});
__bindgen_bitfield_unit.set(1usize, 1u8, {
let set_rnext: u32 = unsafe { ::core::mem::transmute(set_rnext) };
set_rnext as u64
});
__bindgen_bitfield_unit.set(2usize, 30u8, {
let reserved: u32 = unsafe { ::core::mem::transmute(reserved) };
reserved as u64
});
__bindgen_bitfield_unit
}
}
impl tcp_ao_del {
#[inline]
pub fn set_current(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u32) }
}
#[inline]
pub fn set_set_current(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(0usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn set_current_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 0usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_set_current_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 0usize, 1u8, val as u64)
}
}
#[inline]
pub fn set_rnext(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u32) }
}
#[inline]
pub fn set_set_rnext(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(1usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn set_rnext_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 1usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_set_rnext_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 1usize, 1u8, val as u64)
}
}
#[inline]
pub fn del_async(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u32) }
}
#[inline]
pub fn set_del_async(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(2usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn del_async_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 2usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_del_async_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 2usize, 1u8, val as u64)
}
}
#[inline]
pub fn reserved(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(3usize, 29u8) as u32) }
}
#[inline]
pub fn set_reserved(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(3usize, 29u8, val as u64)
}
}
#[inline]
pub unsafe fn reserved_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 3usize, 29u8) as u32) }
}
#[inline]
pub unsafe fn set_reserved_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 3usize, 29u8, val as u64)
}
}
#[inline]
pub fn new_bitfield_1(set_current: __u32, set_rnext: __u32, del_async: __u32, reserved: __u32) -> __BindgenBitfieldUnit<[u8; 4usize]> {
let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 4usize]> = Default::default();
__bindgen_bitfield_unit.set(0usize, 1u8, {
let set_current: u32 = unsafe { ::core::mem::transmute(set_current) };
set_current as u64
});
__bindgen_bitfield_unit.set(1usize, 1u8, {
let set_rnext: u32 = unsafe { ::core::mem::transmute(set_rnext) };
set_rnext as u64
});
__bindgen_bitfield_unit.set(2usize, 1u8, {
let del_async: u32 = unsafe { ::core::mem::transmute(del_async) };
del_async as u64
});
__bindgen_bitfield_unit.set(3usize, 29u8, {
let reserved: u32 = unsafe { ::core::mem::transmute(reserved) };
reserved as u64
});
__bindgen_bitfield_unit
}
}
impl tcp_ao_info_opt {
#[inline]
pub fn set_current(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u32) }
}
#[inline]
pub fn set_set_current(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(0usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn set_current_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 0usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_set_current_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 0usize, 1u8, val as u64)
}
}
#[inline]
pub fn set_rnext(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u32) }
}
#[inline]
pub fn set_set_rnext(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(1usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn set_rnext_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 1usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_set_rnext_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 1usize, 1u8, val as u64)
}
}
#[inline]
pub fn ao_required(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u32) }
}
#[inline]
pub fn set_ao_required(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(2usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn ao_required_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 2usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_ao_required_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 2usize, 1u8, val as u64)
}
}
#[inline]
pub fn set_counters(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(3usize, 1u8) as u32) }
}
#[inline]
pub fn set_set_counters(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(3usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn set_counters_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 3usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_set_counters_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 3usize, 1u8, val as u64)
}
}
#[inline]
pub fn accept_icmps(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(4usize, 1u8) as u32) }
}
#[inline]
pub fn set_accept_icmps(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(4usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn accept_icmps_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 4usize, 1u8) as u32) }
}
#[inline]
pub unsafe fn set_accept_icmps_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 4usize, 1u8, val as u64)
}
}
#[inline]
pub fn reserved(&self) -> __u32 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(5usize, 27u8) as u32) }
}
#[inline]
pub fn set_reserved(&mut self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
self._bitfield_1.set(5usize, 27u8, val as u64)
}
}
#[inline]
pub unsafe fn reserved_raw(this: *const Self) -> __u32 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 5usize, 27u8) as u32) }
}
#[inline]
pub unsafe fn set_reserved_raw(this: *mut Self, val: __u32) {
unsafe {
let val: u32 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 4usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 5usize, 27u8, val as u64)
}
}
#[inline]
pub fn new_bitfield_1(set_current: __u32, set_rnext: __u32, ao_required: __u32, set_counters: __u32, accept_icmps: __u32, reserved: __u32) -> __BindgenBitfieldUnit<[u8; 4usize]> {
let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 4usize]> = Default::default();
__bindgen_bitfield_unit.set(0usize, 1u8, {
let set_current: u32 = unsafe { ::core::mem::transmute(set_current) };
set_current as u64
});
__bindgen_bitfield_unit.set(1usize, 1u8, {
let set_rnext: u32 = unsafe { ::core::mem::transmute(set_rnext) };
set_rnext as u64
});
__bindgen_bitfield_unit.set(2usize, 1u8, {
let ao_required: u32 = unsafe { ::core::mem::transmute(ao_required) };
ao_required as u64
});
__bindgen_bitfield_unit.set(3usize, 1u8, {
let set_counters: u32 = unsafe { ::core::mem::transmute(set_counters) };
set_counters as u64
});
__bindgen_bitfield_unit.set(4usize, 1u8, {
let accept_icmps: u32 = unsafe { ::core::mem::transmute(accept_icmps) };
accept_icmps as u64
});
__bindgen_bitfield_unit.set(5usize, 27u8, {
let reserved: u32 = unsafe { ::core::mem::transmute(reserved) };
reserved as u64
});
__bindgen_bitfield_unit
}
}
impl tcp_ao_getsockopt {
#[inline]
pub fn is_current(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(0usize, 1u8) as u16) }
}
#[inline]
pub fn set_is_current(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(0usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn is_current_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 0usize, 1u8) as u16) }
}
#[inline]
pub unsafe fn set_is_current_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 0usize, 1u8, val as u64)
}
}
#[inline]
pub fn is_rnext(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(1usize, 1u8) as u16) }
}
#[inline]
pub fn set_is_rnext(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(1usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn is_rnext_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 1usize, 1u8) as u16) }
}
#[inline]
pub unsafe fn set_is_rnext_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 1usize, 1u8, val as u64)
}
}
#[inline]
pub fn get_all(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(2usize, 1u8) as u16) }
}
#[inline]
pub fn set_get_all(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(2usize, 1u8, val as u64)
}
}
#[inline]
pub unsafe fn get_all_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 2usize, 1u8) as u16) }
}
#[inline]
pub unsafe fn set_get_all_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 2usize, 1u8, val as u64)
}
}
#[inline]
pub fn reserved(&self) -> __u16 {
unsafe { ::core::mem::transmute(self._bitfield_1.get(3usize, 13u8) as u16) }
}
#[inline]
pub fn set_reserved(&mut self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
self._bitfield_1.set(3usize, 13u8, val as u64)
}
}
#[inline]
pub unsafe fn reserved_raw(this: *const Self) -> __u16 {
unsafe { ::core::mem::transmute(<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_get(::core::ptr::addr_of!((*this)._bitfield_1), 3usize, 13u8) as u16) }
}
#[inline]
pub unsafe fn set_reserved_raw(this: *mut Self, val: __u16) {
unsafe {
let val: u16 = ::core::mem::transmute(val);
<__BindgenBitfieldUnit<[u8; 2usize]>>::raw_set(::core::ptr::addr_of_mut!((*this)._bitfield_1), 3usize, 13u8, val as u64)
}
}
#[inline]
pub fn new_bitfield_1(is_current: __u16, is_rnext: __u16, get_all: __u16, reserved: __u16) -> __BindgenBitfieldUnit<[u8; 2usize]> {
let mut __bindgen_bitfield_unit: __BindgenBitfieldUnit<[u8; 2usize]> = Default::default();
__bindgen_bitfield_unit.set(0usize, 1u8, {
let is_current: u16 = unsafe { ::core::mem::transmute(is_current) };
is_current as u64
});
__bindgen_bitfield_unit.set(1usize, 1u8, {
let is_rnext: u16 = unsafe { ::core::mem::transmute(is_rnext) };
is_rnext as u64
});
__bindgen_bitfield_unit.set(2usize, 1u8, {
let get_all: u16 = unsafe { ::core::mem::transmute(get_all) };
get_all as u64
});
__bindgen_bitfield_unit.set(3usize, 13u8, {
let reserved: u16 = unsafe { ::core::mem::transmute(reserved) };
reserved as u64
});
__bindgen_bitfield_unit
}
}
impl nf_inet_hooks {
pub const NF_INET_INGRESS: nf_inet_hooks = nf_inet_hooks::NF_INET_NUMHOOKS;
}
impl nf_ip_hook_priorities {
pub const NF_IP_PRI_LAST: nf_ip_hook_priorities = nf_ip_hook_priorities::NF_IP_PRI_CONNTRACK_CONFIRM;
}
impl hwtstamp_flags {
pub const HWTSTAMP_FLAG_LAST: hwtstamp_flags = hwtstamp_flags::HWTSTAMP_FLAG_BONDED_PHC_INDEX;
}
impl hwtstamp_flags {
pub const HWTSTAMP_FLAG_MASK: hwtstamp_flags = hwtstamp_flags::HWTSTAMP_FLAG_BONDED_PHC_INDEX;
}
impl txtime_flags {
pub const SOF_TXTIME_FLAGS_LAST: txtime_flags = txtime_flags::SOF_TXTIME_REPORT_ERRORS;
}
