/* automatically generated by rust-bindgen 0.71.1 */

pub type __kernel_sa_family_t = crate::ctypes::c_ushort;
pub type __s8 = crate::ctypes::c_schar;
pub type __u8 = crate::ctypes::c_uchar;
pub type __s16 = crate::ctypes::c_short;
pub type __u16 = crate::ctypes::c_ushort;
pub type __s32 = crate::ctypes::c_int;
pub type __u32 = crate::ctypes::c_uint;
pub type __s64 = crate::ctypes::c_longlong;
pub type __u64 = crate::ctypes::c_ulonglong;
pub type __kernel_key_t = crate::ctypes::c_int;
pub type __kernel_mqd_t = crate::ctypes::c_int;
pub type __kernel_old_uid_t = crate::ctypes::c_ushort;
pub type __kernel_old_gid_t = crate::ctypes::c_ushort;
pub type __kernel_old_dev_t = crate::ctypes::c_ulong;
pub type __kernel_long_t = crate::ctypes::c_long;
pub type __kernel_ulong_t = crate::ctypes::c_ulong;
pub type __kernel_ino_t = __kernel_ulong_t;
pub type __kernel_mode_t = crate::ctypes::c_uint;
pub type __kernel_pid_t = crate::ctypes::c_int;
pub type __kernel_ipc_pid_t = crate::ctypes::c_int;
pub type __kernel_uid_t = crate::ctypes::c_uint;
pub type __kernel_gid_t = crate::ctypes::c_uint;
pub type __kernel_suseconds_t = __kernel_long_t;
pub type __kernel_daddr_t = crate::ctypes::c_int;
pub type __kernel_uid32_t = crate::ctypes::c_uint;
pub type __kernel_gid32_t = crate::ctypes::c_uint;
pub type __kernel_size_t = __kernel_ulong_t;
pub type __kernel_ssize_t = __kernel_long_t;
pub type __kernel_ptrdiff_t = __kernel_long_t;
pub type __kernel_off_t = __kernel_long_t;
pub type __kernel_loff_t = crate::ctypes::c_longlong;
pub type __kernel_old_time_t = __kernel_long_t;
pub type __kernel_time_t = __kernel_long_t;
pub type __kernel_time64_t = crate::ctypes::c_longlong;
pub type __kernel_clock_t = __kernel_long_t;
pub type __kernel_timer_t = crate::ctypes::c_int;
pub type __kernel_clockid_t = crate::ctypes::c_int;
pub type __kernel_caddr_t = *mut crate::ctypes::c_char;
pub type __kernel_uid16_t = crate::ctypes::c_ushort;
pub type __kernel_gid16_t = crate::ctypes::c_ushort;
pub type __s128 = i128;
pub type __u128 = u128;
pub type __le16 = __u16;
pub type __be16 = __u16;
pub type __le32 = __u32;
pub type __be32 = __u32;
pub type __le64 = __u64;
pub type __be64 = __u64;
pub type __sum16 = __u16;
pub type __wsum = __u32;
pub type __poll_t = crate::ctypes::c_uint;
#[repr(C)]
#[derive(Default)]
pub struct __IncompleteArrayField<T>(::core::marker::PhantomData<T>, [T; 0]);
#[repr(C)]
#[derive(Copy, Clone)]
pub struct __kernel_sockaddr_storage {
pub __bindgen_anon_1: __kernel_sockaddr_storage__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __kernel_sockaddr_storage__bindgen_ty_1__bindgen_ty_1 {
pub ss_family: __kernel_sa_family_t,
pub __data: [crate::ctypes::c_char; 126usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct sockaddr_nl {
pub nl_family: __kernel_sa_family_t,
pub nl_pad: crate::ctypes::c_ushort,
pub nl_pid: __u32,
pub nl_groups: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nlmsghdr {
pub nlmsg_len: __u32,
pub nlmsg_type: __u16,
pub nlmsg_flags: __u16,
pub nlmsg_seq: __u32,
pub nlmsg_pid: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nlmsgerr {
pub error: crate::ctypes::c_int,
pub msg: nlmsghdr,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nl_pktinfo {
pub group: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nl_mmap_req {
pub nm_block_size: crate::ctypes::c_uint,
pub nm_block_nr: crate::ctypes::c_uint,
pub nm_frame_size: crate::ctypes::c_uint,
pub nm_frame_nr: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nl_mmap_hdr {
pub nm_status: crate::ctypes::c_uint,
pub nm_len: crate::ctypes::c_uint,
pub nm_group: __u32,
pub nm_pid: __u32,
pub nm_uid: __u32,
pub nm_gid: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nlattr {
pub nla_len: __u16,
pub nla_type: __u16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nla_bitfield32 {
pub value: __u32,
pub selector: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtnl_link_stats {
pub rx_packets: __u32,
pub tx_packets: __u32,
pub rx_bytes: __u32,
pub tx_bytes: __u32,
pub rx_errors: __u32,
pub tx_errors: __u32,
pub rx_dropped: __u32,
pub tx_dropped: __u32,
pub multicast: __u32,
pub collisions: __u32,
pub rx_length_errors: __u32,
pub rx_over_errors: __u32,
pub rx_crc_errors: __u32,
pub rx_frame_errors: __u32,
pub rx_fifo_errors: __u32,
pub rx_missed_errors: __u32,
pub tx_aborted_errors: __u32,
pub tx_carrier_errors: __u32,
pub tx_fifo_errors: __u32,
pub tx_heartbeat_errors: __u32,
pub tx_window_errors: __u32,
pub rx_compressed: __u32,
pub tx_compressed: __u32,
pub rx_nohandler: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtnl_link_stats64 {
pub rx_packets: __u64,
pub tx_packets: __u64,
pub rx_bytes: __u64,
pub tx_bytes: __u64,
pub rx_errors: __u64,
pub tx_errors: __u64,
pub rx_dropped: __u64,
pub tx_dropped: __u64,
pub multicast: __u64,
pub collisions: __u64,
pub rx_length_errors: __u64,
pub rx_over_errors: __u64,
pub rx_crc_errors: __u64,
pub rx_frame_errors: __u64,
pub rx_fifo_errors: __u64,
pub rx_missed_errors: __u64,
pub tx_aborted_errors: __u64,
pub tx_carrier_errors: __u64,
pub tx_fifo_errors: __u64,
pub tx_heartbeat_errors: __u64,
pub tx_window_errors: __u64,
pub rx_compressed: __u64,
pub tx_compressed: __u64,
pub rx_nohandler: __u64,
pub rx_otherhost_dropped: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtnl_hw_stats64 {
pub rx_packets: __u64,
pub tx_packets: __u64,
pub rx_bytes: __u64,
pub tx_bytes: __u64,
pub rx_errors: __u64,
pub tx_errors: __u64,
pub rx_dropped: __u64,
pub tx_dropped: __u64,
pub multicast: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtnl_link_ifmap {
pub mem_start: __u64,
pub mem_end: __u64,
pub base_addr: __u64,
pub irq: __u16,
pub dma: __u8,
pub port: __u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_bridge_id {
pub prio: [__u8; 2usize],
pub addr: [__u8; 6usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_cacheinfo {
pub max_reasm_len: __u32,
pub tstamp: __u32,
pub reachable_time: __u32,
pub retrans_time: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vlan_flags {
pub flags: __u32,
pub mask: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vlan_qos_mapping {
pub from: __u32,
pub to: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tunnel_msg {
pub family: __u8,
pub flags: __u8,
pub reserved2: __u16,
pub ifindex: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vxlan_port_range {
pub low: __be16,
pub high: __be16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_mac {
pub vf: __u32,
pub mac: [__u8; 32usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_broadcast {
pub broadcast: [__u8; 32usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_vlan {
pub vf: __u32,
pub vlan: __u32,
pub qos: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_vlan_info {
pub vf: __u32,
pub vlan: __u32,
pub qos: __u32,
pub vlan_proto: __be16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_tx_rate {
pub vf: __u32,
pub rate: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_rate {
pub vf: __u32,
pub min_tx_rate: __u32,
pub max_tx_rate: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_spoofchk {
pub vf: __u32,
pub setting: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_guid {
pub vf: __u32,
pub guid: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_link_state {
pub vf: __u32,
pub link_state: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_rss_query_en {
pub vf: __u32,
pub setting: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_vf_trust {
pub vf: __u32,
pub setting: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_port_vsi {
pub vsi_mgr_id: __u8,
pub vsi_type_id: [__u8; 3usize],
pub vsi_type_version: __u8,
pub pad: [__u8; 3usize],
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct if_stats_msg {
pub family: __u8,
pub pad1: __u8,
pub pad2: __u16,
pub ifindex: __u32,
pub filter_mask: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifla_rmnet_flags {
pub flags: __u32,
pub mask: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifaddrmsg {
pub ifa_family: __u8,
pub ifa_prefixlen: __u8,
pub ifa_flags: __u8,
pub ifa_scope: __u8,
pub ifa_index: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifa_cacheinfo {
pub ifa_prefered: __u32,
pub ifa_valid: __u32,
pub cstamp: __u32,
pub tstamp: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ndmsg {
pub ndm_family: __u8,
pub ndm_pad1: __u8,
pub ndm_pad2: __u16,
pub ndm_ifindex: __s32,
pub ndm_state: __u16,
pub ndm_flags: __u8,
pub ndm_type: __u8,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nda_cacheinfo {
pub ndm_confirmed: __u32,
pub ndm_used: __u32,
pub ndm_updated: __u32,
pub ndm_refcnt: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ndt_stats {
pub ndts_allocs: __u64,
pub ndts_destroys: __u64,
pub ndts_hash_grows: __u64,
pub ndts_res_failed: __u64,
pub ndts_lookups: __u64,
pub ndts_hits: __u64,
pub ndts_rcv_probes_mcast: __u64,
pub ndts_rcv_probes_ucast: __u64,
pub ndts_periodic_gc_runs: __u64,
pub ndts_forced_gc_runs: __u64,
pub ndts_table_fulls: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ndtmsg {
pub ndtm_family: __u8,
pub ndtm_pad1: __u8,
pub ndtm_pad2: __u16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ndt_config {
pub ndtc_key_len: __u16,
pub ndtc_entry_size: __u16,
pub ndtc_entries: __u32,
pub ndtc_last_flush: __u32,
pub ndtc_last_rand: __u32,
pub ndtc_hash_rnd: __u32,
pub ndtc_hash_mask: __u32,
pub ndtc_hash_chain_gc: __u32,
pub ndtc_proxy_qlen: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtattr {
pub rta_len: crate::ctypes::c_ushort,
pub rta_type: crate::ctypes::c_ushort,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtmsg {
pub rtm_family: crate::ctypes::c_uchar,
pub rtm_dst_len: crate::ctypes::c_uchar,
pub rtm_src_len: crate::ctypes::c_uchar,
pub rtm_tos: crate::ctypes::c_uchar,
pub rtm_table: crate::ctypes::c_uchar,
pub rtm_protocol: crate::ctypes::c_uchar,
pub rtm_scope: crate::ctypes::c_uchar,
pub rtm_type: crate::ctypes::c_uchar,
pub rtm_flags: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtnexthop {
pub rtnh_len: crate::ctypes::c_ushort,
pub rtnh_flags: crate::ctypes::c_uchar,
pub rtnh_hops: crate::ctypes::c_uchar,
pub rtnh_ifindex: crate::ctypes::c_int,
}
#[repr(C)]
#[derive(Debug)]
pub struct rtvia {
pub rtvia_family: __kernel_sa_family_t,
pub rtvia_addr: __IncompleteArrayField<__u8>,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rta_cacheinfo {
pub rta_clntref: __u32,
pub rta_lastuse: __u32,
pub rta_expires: __s32,
pub rta_error: __u32,
pub rta_used: __u32,
pub rta_id: __u32,
pub rta_ts: __u32,
pub rta_tsage: __u32,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub struct rta_session {
pub proto: __u8,
pub pad1: __u8,
pub pad2: __u16,
pub u: rta_session__bindgen_ty_1,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rta_session__bindgen_ty_1__bindgen_ty_1 {
pub sport: __u16,
pub dport: __u16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rta_session__bindgen_ty_1__bindgen_ty_2 {
pub type_: __u8,
pub code: __u8,
pub ident: __u16,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rta_mfc_stats {
pub mfcs_packets: __u64,
pub mfcs_bytes: __u64,
pub mfcs_wrong_if: __u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct rtgenmsg {
pub rtgen_family: crate::ctypes::c_uchar,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct ifinfomsg {
pub ifi_family: crate::ctypes::c_uchar,
pub __ifi_pad: crate::ctypes::c_uchar,
pub ifi_type: crate::ctypes::c_ushort,
pub ifi_index: crate::ctypes::c_int,
pub ifi_flags: crate::ctypes::c_uint,
pub ifi_change: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct prefixmsg {
pub prefix_family: crate::ctypes::c_uchar,
pub prefix_pad1: crate::ctypes::c_uchar,
pub prefix_pad2: crate::ctypes::c_ushort,
pub prefix_ifindex: crate::ctypes::c_int,
pub prefix_type: crate::ctypes::c_uchar,
pub prefix_len: crate::ctypes::c_uchar,
pub prefix_flags: crate::ctypes::c_uchar,
pub prefix_pad3: crate::ctypes::c_uchar,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct prefix_cacheinfo {
pub preferred_time: __u32,
pub valid_time: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tcmsg {
pub tcm_family: crate::ctypes::c_uchar,
pub tcm__pad1: crate::ctypes::c_uchar,
pub tcm__pad2: crate::ctypes::c_ushort,
pub tcm_ifindex: crate::ctypes::c_int,
pub tcm_handle: __u32,
pub tcm_parent: __u32,
pub tcm_info: __u32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct nduseroptmsg {
pub nduseropt_family: crate::ctypes::c_uchar,
pub nduseropt_pad1: crate::ctypes::c_uchar,
pub nduseropt_opts_len: crate::ctypes::c_ushort,
pub nduseropt_ifindex: crate::ctypes::c_int,
pub nduseropt_icmp_type: __u8,
pub nduseropt_icmp_code: __u8,
pub nduseropt_pad2: crate::ctypes::c_ushort,
pub nduseropt_pad3: crate::ctypes::c_uint,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct tcamsg {
pub tca_family: crate::ctypes::c_uchar,
pub tca__pad1: crate::ctypes::c_uchar,
pub tca__pad2: crate::ctypes::c_ushort,
}
pub const _K_SS_MAXSIZE: u32 = 128;
pub const SOCK_SNDBUF_LOCK: u32 = 1;
pub const SOCK_RCVBUF_LOCK: u32 = 2;
pub const SOCK_BUF_LOCK_MASK: u32 = 3;
pub const SOCK_TXREHASH_DEFAULT: u32 = 255;
pub const SOCK_TXREHASH_DISABLED: u32 = 0;
pub const SOCK_TXREHASH_ENABLED: u32 = 1;
pub const __BITS_PER_LONG_LONG: u32 = 64;
pub const NETLINK_ROUTE: u32 = 0;
pub const NETLINK_UNUSED: u32 = 1;
pub const NETLINK_USERSOCK: u32 = 2;
pub const NETLINK_FIREWALL: u32 = 3;
pub const NETLINK_SOCK_DIAG: u32 = 4;
pub const NETLINK_NFLOG: u32 = 5;
pub const NETLINK_XFRM: u32 = 6;
pub const NETLINK_SELINUX: u32 = 7;
pub const NETLINK_ISCSI: u32 = 8;
pub const NETLINK_AUDIT: u32 = 9;
pub const NETLINK_FIB_LOOKUP: u32 = 10;
pub const NETLINK_CONNECTOR: u32 = 11;
pub const NETLINK_NETFILTER: u32 = 12;
pub const NETLINK_IP6_FW: u32 = 13;
pub const NETLINK_DNRTMSG: u32 = 14;
pub const NETLINK_KOBJECT_UEVENT: u32 = 15;
pub const NETLINK_GENERIC: u32 = 16;
pub const NETLINK_SCSITRANSPORT: u32 = 18;
pub const NETLINK_ECRYPTFS: u32 = 19;
pub const NETLINK_RDMA: u32 = 20;
pub const NETLINK_CRYPTO: u32 = 21;
pub const NETLINK_SMC: u32 = 22;
pub const NETLINK_INET_DIAG: u32 = 4;
pub const MAX_LINKS: u32 = 32;
pub const NLM_F_REQUEST: u32 = 1;
pub const NLM_F_MULTI: u32 = 2;
pub const NLM_F_ACK: u32 = 4;
pub const NLM_F_ECHO: u32 = 8;
pub const NLM_F_DUMP_INTR: u32 = 16;
pub const NLM_F_DUMP_FILTERED: u32 = 32;
pub const NLM_F_ROOT: u32 = 256;
pub const NLM_F_MATCH: u32 = 512;
pub const NLM_F_ATOMIC: u32 = 1024;
pub const NLM_F_DUMP: u32 = 768;
pub const NLM_F_REPLACE: u32 = 256;
pub const NLM_F_EXCL: u32 = 512;
pub const NLM_F_CREATE: u32 = 1024;
pub const NLM_F_APPEND: u32 = 2048;
pub const NLM_F_NONREC: u32 = 256;
pub const NLM_F_BULK: u32 = 512;
pub const NLM_F_CAPPED: u32 = 256;
pub const NLM_F_ACK_TLVS: u32 = 512;
pub const NLMSG_ALIGNTO: u32 = 4;
pub const NLMSG_NOOP: u32 = 1;
pub const NLMSG_ERROR: u32 = 2;
pub const NLMSG_DONE: u32 = 3;
pub const NLMSG_OVERRUN: u32 = 4;
pub const NLMSG_MIN_TYPE: u32 = 16;
pub const NETLINK_ADD_MEMBERSHIP: u32 = 1;
pub const NETLINK_DROP_MEMBERSHIP: u32 = 2;
pub const NETLINK_PKTINFO: u32 = 3;
pub const NETLINK_BROADCAST_ERROR: u32 = 4;
pub const NETLINK_NO_ENOBUFS: u32 = 5;
pub const NETLINK_RX_RING: u32 = 6;
pub const NETLINK_TX_RING: u32 = 7;
pub const NETLINK_LISTEN_ALL_NSID: u32 = 8;
pub const NETLINK_LIST_MEMBERSHIPS: u32 = 9;
pub const NETLINK_CAP_ACK: u32 = 10;
pub const NETLINK_EXT_ACK: u32 = 11;
pub const NETLINK_GET_STRICT_CHK: u32 = 12;
pub const NL_MMAP_MSG_ALIGNMENT: u32 = 4;
pub const NET_MAJOR: u32 = 36;
pub const NLA_F_NESTED: u32 = 32768;
pub const NLA_F_NET_BYTEORDER: u32 = 16384;
pub const NLA_TYPE_MASK: i32 = -49153;
pub const NLA_ALIGNTO: u32 = 4;
pub const MACVLAN_FLAG_NOPROMISC: u32 = 1;
pub const MACVLAN_FLAG_NODST: u32 = 2;
pub const IPVLAN_F_PRIVATE: u32 = 1;
pub const IPVLAN_F_VEPA: u32 = 2;
pub const TUNNEL_MSG_FLAG_STATS: u32 = 1;
pub const TUNNEL_MSG_VALID_USER_FLAGS: u32 = 1;
pub const MAX_VLAN_LIST_LEN: u32 = 1;
pub const PORT_PROFILE_MAX: u32 = 40;
pub const PORT_UUID_MAX: u32 = 16;
pub const PORT_SELF_VF: i32 = -1;
pub const XDP_FLAGS_UPDATE_IF_NOEXIST: u32 = 1;
pub const XDP_FLAGS_SKB_MODE: u32 = 2;
pub const XDP_FLAGS_DRV_MODE: u32 = 4;
pub const XDP_FLAGS_HW_MODE: u32 = 8;
pub const XDP_FLAGS_REPLACE: u32 = 16;
pub const XDP_FLAGS_MODES: u32 = 14;
pub const XDP_FLAGS_MASK: u32 = 31;
pub const RMNET_FLAGS_INGRESS_DEAGGREGATION: u32 = 1;
pub const RMNET_FLAGS_INGRESS_MAP_COMMANDS: u32 = 2;
pub const RMNET_FLAGS_INGRESS_MAP_CKSUMV4: u32 = 4;
pub const RMNET_FLAGS_EGRESS_MAP_CKSUMV4: u32 = 8;
pub const RMNET_FLAGS_INGRESS_MAP_CKSUMV5: u32 = 16;
pub const RMNET_FLAGS_EGRESS_MAP_CKSUMV5: u32 = 32;
pub const IFA_F_SECONDARY: u32 = 1;
pub const IFA_F_TEMPORARY: u32 = 1;
pub const IFA_F_NODAD: u32 = 2;
pub const IFA_F_OPTIMISTIC: u32 = 4;
pub const IFA_F_DADFAILED: u32 = 8;
pub const IFA_F_HOMEADDRESS: u32 = 16;
pub const IFA_F_DEPRECATED: u32 = 32;
pub const IFA_F_TENTATIVE: u32 = 64;
pub const IFA_F_PERMANENT: u32 = 128;
pub const IFA_F_MANAGETEMPADDR: u32 = 256;
pub const IFA_F_NOPREFIXROUTE: u32 = 512;
pub const IFA_F_MCAUTOJOIN: u32 = 1024;
pub const IFA_F_STABLE_PRIVACY: u32 = 2048;
pub const IFAPROT_UNSPEC: u32 = 0;
pub const IFAPROT_KERNEL_LO: u32 = 1;
pub const IFAPROT_KERNEL_RA: u32 = 2;
pub const IFAPROT_KERNEL_LL: u32 = 3;
pub const NTF_USE: u32 = 1;
pub const NTF_SELF: u32 = 2;
pub const NTF_MASTER: u32 = 4;
pub const NTF_PROXY: u32 = 8;
pub const NTF_EXT_LEARNED: u32 = 16;
pub const NTF_OFFLOADED: u32 = 32;
pub const NTF_STICKY: u32 = 64;
pub const NTF_ROUTER: u32 = 128;
pub const NTF_EXT_MANAGED: u32 = 1;
pub const NTF_EXT_LOCKED: u32 = 2;
pub const NUD_INCOMPLETE: u32 = 1;
pub const NUD_REACHABLE: u32 = 2;
pub const NUD_STALE: u32 = 4;
pub const NUD_DELAY: u32 = 8;
pub const NUD_PROBE: u32 = 16;
pub const NUD_FAILED: u32 = 32;
pub const NUD_NOARP: u32 = 64;
pub const NUD_PERMANENT: u32 = 128;
pub const NUD_NONE: u32 = 0;
pub const RTNL_FAMILY_IPMR: u32 = 128;
pub const RTNL_FAMILY_IP6MR: u32 = 129;
pub const RTNL_FAMILY_MAX: u32 = 129;
pub const RTA_ALIGNTO: u32 = 4;
pub const RTPROT_UNSPEC: u32 = 0;
pub const RTPROT_REDIRECT: u32 = 1;
pub const RTPROT_KERNEL: u32 = 2;
pub const RTPROT_BOOT: u32 = 3;
pub const RTPROT_STATIC: u32 = 4;
pub const RTPROT_GATED: u32 = 8;
pub const RTPROT_RA: u32 = 9;
pub const RTPROT_MRT: u32 = 10;
pub const RTPROT_ZEBRA: u32 = 11;
pub const RTPROT_BIRD: u32 = 12;
pub const RTPROT_DNROUTED: u32 = 13;
pub const RTPROT_XORP: u32 = 14;
pub const RTPROT_NTK: u32 = 15;
pub const RTPROT_DHCP: u32 = 16;
pub const RTPROT_MROUTED: u32 = 17;
pub const RTPROT_KEEPALIVED: u32 = 18;
pub const RTPROT_BABEL: u32 = 42;
pub const RTPROT_OPENR: u32 = 99;
pub const RTPROT_BGP: u32 = 186;
pub const RTPROT_ISIS: u32 = 187;
pub const RTPROT_OSPF: u32 = 188;
pub const RTPROT_RIP: u32 = 189;
pub const RTPROT_EIGRP: u32 = 192;
pub const RTM_F_NOTIFY: u32 = 256;
pub const RTM_F_CLONED: u32 = 512;
pub const RTM_F_EQUALIZE: u32 = 1024;
pub const RTM_F_PREFIX: u32 = 2048;
pub const RTM_F_LOOKUP_TABLE: u32 = 4096;
pub const RTM_F_FIB_MATCH: u32 = 8192;
pub const RTM_F_OFFLOAD: u32 = 16384;
pub const RTM_F_TRAP: u32 = 32768;
pub const RTM_F_OFFLOAD_FAILED: u32 = *********;
pub const RTNH_F_DEAD: u32 = 1;
pub const RTNH_F_PERVASIVE: u32 = 2;
pub const RTNH_F_ONLINK: u32 = 4;
pub const RTNH_F_OFFLOAD: u32 = 8;
pub const RTNH_F_LINKDOWN: u32 = 16;
pub const RTNH_F_UNRESOLVED: u32 = 32;
pub const RTNH_F_TRAP: u32 = 64;
pub const RTNH_COMPARE_MASK: u32 = 89;
pub const RTNH_ALIGNTO: u32 = 4;
pub const RTNETLINK_HAVE_PEERINFO: u32 = 1;
pub const RTAX_FEATURE_ECN: u32 = 1;
pub const RTAX_FEATURE_SACK: u32 = 2;
pub const RTAX_FEATURE_TIMESTAMP: u32 = 4;
pub const RTAX_FEATURE_ALLFRAG: u32 = 8;
pub const RTAX_FEATURE_TCP_USEC_TS: u32 = 16;
pub const RTAX_FEATURE_MASK: u32 = 31;
pub const TCM_IFINDEX_MAGIC_BLOCK: u32 = 4294967295;
pub const TCA_DUMP_FLAGS_TERSE: u32 = 1;
pub const RTMGRP_LINK: u32 = 1;
pub const RTMGRP_NOTIFY: u32 = 2;
pub const RTMGRP_NEIGH: u32 = 4;
pub const RTMGRP_TC: u32 = 8;
pub const RTMGRP_IPV4_IFADDR: u32 = 16;
pub const RTMGRP_IPV4_MROUTE: u32 = 32;
pub const RTMGRP_IPV4_ROUTE: u32 = 64;
pub const RTMGRP_IPV4_RULE: u32 = 128;
pub const RTMGRP_IPV6_IFADDR: u32 = 256;
pub const RTMGRP_IPV6_MROUTE: u32 = 512;
pub const RTMGRP_IPV6_ROUTE: u32 = 1024;
pub const RTMGRP_IPV6_IFINFO: u32 = 2048;
pub const RTMGRP_DECnet_IFADDR: u32 = 4096;
pub const RTMGRP_DECnet_ROUTE: u32 = 16384;
pub const RTMGRP_IPV6_PREFIX: u32 = 131072;
pub const TCA_FLAG_LARGE_DUMP_ON: u32 = 1;
pub const TCA_ACT_FLAG_LARGE_DUMP_ON: u32 = 1;
pub const TCA_ACT_FLAG_TERSE_DUMP: u32 = 2;
pub const RTEXT_FILTER_VF: u32 = 1;
pub const RTEXT_FILTER_BRVLAN: u32 = 2;
pub const RTEXT_FILTER_BRVLAN_COMPRESSED: u32 = 4;
pub const RTEXT_FILTER_SKIP_STATS: u32 = 8;
pub const RTEXT_FILTER_MRP: u32 = 16;
pub const RTEXT_FILTER_CFM_CONFIG: u32 = 32;
pub const RTEXT_FILTER_CFM_STATUS: u32 = 64;
pub const RTEXT_FILTER_MST: u32 = 128;
pub const NETLINK_UNCONNECTED: _bindgen_ty_1 = _bindgen_ty_1::NETLINK_UNCONNECTED;
pub const NETLINK_CONNECTED: _bindgen_ty_1 = _bindgen_ty_1::NETLINK_CONNECTED;
pub const IFLA_UNSPEC: _bindgen_ty_2 = _bindgen_ty_2::IFLA_UNSPEC;
pub const IFLA_ADDRESS: _bindgen_ty_2 = _bindgen_ty_2::IFLA_ADDRESS;
pub const IFLA_BROADCAST: _bindgen_ty_2 = _bindgen_ty_2::IFLA_BROADCAST;
pub const IFLA_IFNAME: _bindgen_ty_2 = _bindgen_ty_2::IFLA_IFNAME;
pub const IFLA_MTU: _bindgen_ty_2 = _bindgen_ty_2::IFLA_MTU;
pub const IFLA_LINK: _bindgen_ty_2 = _bindgen_ty_2::IFLA_LINK;
pub const IFLA_QDISC: _bindgen_ty_2 = _bindgen_ty_2::IFLA_QDISC;
pub const IFLA_STATS: _bindgen_ty_2 = _bindgen_ty_2::IFLA_STATS;
pub const IFLA_COST: _bindgen_ty_2 = _bindgen_ty_2::IFLA_COST;
pub const IFLA_PRIORITY: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PRIORITY;
pub const IFLA_MASTER: _bindgen_ty_2 = _bindgen_ty_2::IFLA_MASTER;
pub const IFLA_WIRELESS: _bindgen_ty_2 = _bindgen_ty_2::IFLA_WIRELESS;
pub const IFLA_PROTINFO: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PROTINFO;
pub const IFLA_TXQLEN: _bindgen_ty_2 = _bindgen_ty_2::IFLA_TXQLEN;
pub const IFLA_MAP: _bindgen_ty_2 = _bindgen_ty_2::IFLA_MAP;
pub const IFLA_WEIGHT: _bindgen_ty_2 = _bindgen_ty_2::IFLA_WEIGHT;
pub const IFLA_OPERSTATE: _bindgen_ty_2 = _bindgen_ty_2::IFLA_OPERSTATE;
pub const IFLA_LINKMODE: _bindgen_ty_2 = _bindgen_ty_2::IFLA_LINKMODE;
pub const IFLA_LINKINFO: _bindgen_ty_2 = _bindgen_ty_2::IFLA_LINKINFO;
pub const IFLA_NET_NS_PID: _bindgen_ty_2 = _bindgen_ty_2::IFLA_NET_NS_PID;
pub const IFLA_IFALIAS: _bindgen_ty_2 = _bindgen_ty_2::IFLA_IFALIAS;
pub const IFLA_NUM_VF: _bindgen_ty_2 = _bindgen_ty_2::IFLA_NUM_VF;
pub const IFLA_VFINFO_LIST: _bindgen_ty_2 = _bindgen_ty_2::IFLA_VFINFO_LIST;
pub const IFLA_STATS64: _bindgen_ty_2 = _bindgen_ty_2::IFLA_STATS64;
pub const IFLA_VF_PORTS: _bindgen_ty_2 = _bindgen_ty_2::IFLA_VF_PORTS;
pub const IFLA_PORT_SELF: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PORT_SELF;
pub const IFLA_AF_SPEC: _bindgen_ty_2 = _bindgen_ty_2::IFLA_AF_SPEC;
pub const IFLA_GROUP: _bindgen_ty_2 = _bindgen_ty_2::IFLA_GROUP;
pub const IFLA_NET_NS_FD: _bindgen_ty_2 = _bindgen_ty_2::IFLA_NET_NS_FD;
pub const IFLA_EXT_MASK: _bindgen_ty_2 = _bindgen_ty_2::IFLA_EXT_MASK;
pub const IFLA_PROMISCUITY: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PROMISCUITY;
pub const IFLA_NUM_TX_QUEUES: _bindgen_ty_2 = _bindgen_ty_2::IFLA_NUM_TX_QUEUES;
pub const IFLA_NUM_RX_QUEUES: _bindgen_ty_2 = _bindgen_ty_2::IFLA_NUM_RX_QUEUES;
pub const IFLA_CARRIER: _bindgen_ty_2 = _bindgen_ty_2::IFLA_CARRIER;
pub const IFLA_PHYS_PORT_ID: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PHYS_PORT_ID;
pub const IFLA_CARRIER_CHANGES: _bindgen_ty_2 = _bindgen_ty_2::IFLA_CARRIER_CHANGES;
pub const IFLA_PHYS_SWITCH_ID: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PHYS_SWITCH_ID;
pub const IFLA_LINK_NETNSID: _bindgen_ty_2 = _bindgen_ty_2::IFLA_LINK_NETNSID;
pub const IFLA_PHYS_PORT_NAME: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PHYS_PORT_NAME;
pub const IFLA_PROTO_DOWN: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PROTO_DOWN;
pub const IFLA_GSO_MAX_SEGS: _bindgen_ty_2 = _bindgen_ty_2::IFLA_GSO_MAX_SEGS;
pub const IFLA_GSO_MAX_SIZE: _bindgen_ty_2 = _bindgen_ty_2::IFLA_GSO_MAX_SIZE;
pub const IFLA_PAD: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PAD;
pub const IFLA_XDP: _bindgen_ty_2 = _bindgen_ty_2::IFLA_XDP;
pub const IFLA_EVENT: _bindgen_ty_2 = _bindgen_ty_2::IFLA_EVENT;
pub const IFLA_NEW_NETNSID: _bindgen_ty_2 = _bindgen_ty_2::IFLA_NEW_NETNSID;
pub const IFLA_IF_NETNSID: _bindgen_ty_2 = _bindgen_ty_2::IFLA_IF_NETNSID;
pub const IFLA_TARGET_NETNSID: _bindgen_ty_2 = _bindgen_ty_2::IFLA_IF_NETNSID;
pub const IFLA_CARRIER_UP_COUNT: _bindgen_ty_2 = _bindgen_ty_2::IFLA_CARRIER_UP_COUNT;
pub const IFLA_CARRIER_DOWN_COUNT: _bindgen_ty_2 = _bindgen_ty_2::IFLA_CARRIER_DOWN_COUNT;
pub const IFLA_NEW_IFINDEX: _bindgen_ty_2 = _bindgen_ty_2::IFLA_NEW_IFINDEX;
pub const IFLA_MIN_MTU: _bindgen_ty_2 = _bindgen_ty_2::IFLA_MIN_MTU;
pub const IFLA_MAX_MTU: _bindgen_ty_2 = _bindgen_ty_2::IFLA_MAX_MTU;
pub const IFLA_PROP_LIST: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PROP_LIST;
pub const IFLA_ALT_IFNAME: _bindgen_ty_2 = _bindgen_ty_2::IFLA_ALT_IFNAME;
pub const IFLA_PERM_ADDRESS: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PERM_ADDRESS;
pub const IFLA_PROTO_DOWN_REASON: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PROTO_DOWN_REASON;
pub const IFLA_PARENT_DEV_NAME: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PARENT_DEV_NAME;
pub const IFLA_PARENT_DEV_BUS_NAME: _bindgen_ty_2 = _bindgen_ty_2::IFLA_PARENT_DEV_BUS_NAME;
pub const IFLA_GRO_MAX_SIZE: _bindgen_ty_2 = _bindgen_ty_2::IFLA_GRO_MAX_SIZE;
pub const IFLA_TSO_MAX_SIZE: _bindgen_ty_2 = _bindgen_ty_2::IFLA_TSO_MAX_SIZE;
pub const IFLA_TSO_MAX_SEGS: _bindgen_ty_2 = _bindgen_ty_2::IFLA_TSO_MAX_SEGS;
pub const IFLA_ALLMULTI: _bindgen_ty_2 = _bindgen_ty_2::IFLA_ALLMULTI;
pub const IFLA_DEVLINK_PORT: _bindgen_ty_2 = _bindgen_ty_2::IFLA_DEVLINK_PORT;
pub const IFLA_GSO_IPV4_MAX_SIZE: _bindgen_ty_2 = _bindgen_ty_2::IFLA_GSO_IPV4_MAX_SIZE;
pub const IFLA_GRO_IPV4_MAX_SIZE: _bindgen_ty_2 = _bindgen_ty_2::IFLA_GRO_IPV4_MAX_SIZE;
pub const IFLA_DPLL_PIN: _bindgen_ty_2 = _bindgen_ty_2::IFLA_DPLL_PIN;
pub const IFLA_MAX_PACING_OFFLOAD_HORIZON: _bindgen_ty_2 = _bindgen_ty_2::IFLA_MAX_PACING_OFFLOAD_HORIZON;
pub const __IFLA_MAX: _bindgen_ty_2 = _bindgen_ty_2::__IFLA_MAX;
pub const IFLA_PROTO_DOWN_REASON_UNSPEC: _bindgen_ty_3 = _bindgen_ty_3::IFLA_PROTO_DOWN_REASON_UNSPEC;
pub const IFLA_PROTO_DOWN_REASON_MASK: _bindgen_ty_3 = _bindgen_ty_3::IFLA_PROTO_DOWN_REASON_MASK;
pub const IFLA_PROTO_DOWN_REASON_VALUE: _bindgen_ty_3 = _bindgen_ty_3::IFLA_PROTO_DOWN_REASON_VALUE;
pub const __IFLA_PROTO_DOWN_REASON_CNT: _bindgen_ty_3 = _bindgen_ty_3::__IFLA_PROTO_DOWN_REASON_CNT;
pub const IFLA_PROTO_DOWN_REASON_MAX: _bindgen_ty_3 = _bindgen_ty_3::IFLA_PROTO_DOWN_REASON_VALUE;
pub const IFLA_INET_UNSPEC: _bindgen_ty_4 = _bindgen_ty_4::IFLA_INET_UNSPEC;
pub const IFLA_INET_CONF: _bindgen_ty_4 = _bindgen_ty_4::IFLA_INET_CONF;
pub const __IFLA_INET_MAX: _bindgen_ty_4 = _bindgen_ty_4::__IFLA_INET_MAX;
pub const IFLA_INET6_UNSPEC: _bindgen_ty_5 = _bindgen_ty_5::IFLA_INET6_UNSPEC;
pub const IFLA_INET6_FLAGS: _bindgen_ty_5 = _bindgen_ty_5::IFLA_INET6_FLAGS;
pub const IFLA_INET6_CONF: _bindgen_ty_5 = _bindgen_ty_5::IFLA_INET6_CONF;
pub const IFLA_INET6_STATS: _bindgen_ty_5 = _bindgen_ty_5::IFLA_INET6_STATS;
pub const IFLA_INET6_MCAST: _bindgen_ty_5 = _bindgen_ty_5::IFLA_INET6_MCAST;
pub const IFLA_INET6_CACHEINFO: _bindgen_ty_5 = _bindgen_ty_5::IFLA_INET6_CACHEINFO;
pub const IFLA_INET6_ICMP6STATS: _bindgen_ty_5 = _bindgen_ty_5::IFLA_INET6_ICMP6STATS;
pub const IFLA_INET6_TOKEN: _bindgen_ty_5 = _bindgen_ty_5::IFLA_INET6_TOKEN;
pub const IFLA_INET6_ADDR_GEN_MODE: _bindgen_ty_5 = _bindgen_ty_5::IFLA_INET6_ADDR_GEN_MODE;
pub const IFLA_INET6_RA_MTU: _bindgen_ty_5 = _bindgen_ty_5::IFLA_INET6_RA_MTU;
pub const __IFLA_INET6_MAX: _bindgen_ty_5 = _bindgen_ty_5::__IFLA_INET6_MAX;
pub const IFLA_BR_UNSPEC: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_UNSPEC;
pub const IFLA_BR_FORWARD_DELAY: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_FORWARD_DELAY;
pub const IFLA_BR_HELLO_TIME: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_HELLO_TIME;
pub const IFLA_BR_MAX_AGE: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MAX_AGE;
pub const IFLA_BR_AGEING_TIME: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_AGEING_TIME;
pub const IFLA_BR_STP_STATE: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_STP_STATE;
pub const IFLA_BR_PRIORITY: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_PRIORITY;
pub const IFLA_BR_VLAN_FILTERING: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_VLAN_FILTERING;
pub const IFLA_BR_VLAN_PROTOCOL: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_VLAN_PROTOCOL;
pub const IFLA_BR_GROUP_FWD_MASK: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_GROUP_FWD_MASK;
pub const IFLA_BR_ROOT_ID: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_ROOT_ID;
pub const IFLA_BR_BRIDGE_ID: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_BRIDGE_ID;
pub const IFLA_BR_ROOT_PORT: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_ROOT_PORT;
pub const IFLA_BR_ROOT_PATH_COST: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_ROOT_PATH_COST;
pub const IFLA_BR_TOPOLOGY_CHANGE: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_TOPOLOGY_CHANGE;
pub const IFLA_BR_TOPOLOGY_CHANGE_DETECTED: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_TOPOLOGY_CHANGE_DETECTED;
pub const IFLA_BR_HELLO_TIMER: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_HELLO_TIMER;
pub const IFLA_BR_TCN_TIMER: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_TCN_TIMER;
pub const IFLA_BR_TOPOLOGY_CHANGE_TIMER: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_TOPOLOGY_CHANGE_TIMER;
pub const IFLA_BR_GC_TIMER: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_GC_TIMER;
pub const IFLA_BR_GROUP_ADDR: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_GROUP_ADDR;
pub const IFLA_BR_FDB_FLUSH: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_FDB_FLUSH;
pub const IFLA_BR_MCAST_ROUTER: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_ROUTER;
pub const IFLA_BR_MCAST_SNOOPING: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_SNOOPING;
pub const IFLA_BR_MCAST_QUERY_USE_IFADDR: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_QUERY_USE_IFADDR;
pub const IFLA_BR_MCAST_QUERIER: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_QUERIER;
pub const IFLA_BR_MCAST_HASH_ELASTICITY: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_HASH_ELASTICITY;
pub const IFLA_BR_MCAST_HASH_MAX: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_HASH_MAX;
pub const IFLA_BR_MCAST_LAST_MEMBER_CNT: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_LAST_MEMBER_CNT;
pub const IFLA_BR_MCAST_STARTUP_QUERY_CNT: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_STARTUP_QUERY_CNT;
pub const IFLA_BR_MCAST_LAST_MEMBER_INTVL: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_LAST_MEMBER_INTVL;
pub const IFLA_BR_MCAST_MEMBERSHIP_INTVL: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_MEMBERSHIP_INTVL;
pub const IFLA_BR_MCAST_QUERIER_INTVL: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_QUERIER_INTVL;
pub const IFLA_BR_MCAST_QUERY_INTVL: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_QUERY_INTVL;
pub const IFLA_BR_MCAST_QUERY_RESPONSE_INTVL: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_QUERY_RESPONSE_INTVL;
pub const IFLA_BR_MCAST_STARTUP_QUERY_INTVL: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_STARTUP_QUERY_INTVL;
pub const IFLA_BR_NF_CALL_IPTABLES: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_NF_CALL_IPTABLES;
pub const IFLA_BR_NF_CALL_IP6TABLES: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_NF_CALL_IP6TABLES;
pub const IFLA_BR_NF_CALL_ARPTABLES: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_NF_CALL_ARPTABLES;
pub const IFLA_BR_VLAN_DEFAULT_PVID: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_VLAN_DEFAULT_PVID;
pub const IFLA_BR_PAD: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_PAD;
pub const IFLA_BR_VLAN_STATS_ENABLED: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_VLAN_STATS_ENABLED;
pub const IFLA_BR_MCAST_STATS_ENABLED: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_STATS_ENABLED;
pub const IFLA_BR_MCAST_IGMP_VERSION: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_IGMP_VERSION;
pub const IFLA_BR_MCAST_MLD_VERSION: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_MLD_VERSION;
pub const IFLA_BR_VLAN_STATS_PER_PORT: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_VLAN_STATS_PER_PORT;
pub const IFLA_BR_MULTI_BOOLOPT: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MULTI_BOOLOPT;
pub const IFLA_BR_MCAST_QUERIER_STATE: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_MCAST_QUERIER_STATE;
pub const IFLA_BR_FDB_N_LEARNED: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_FDB_N_LEARNED;
pub const IFLA_BR_FDB_MAX_LEARNED: _bindgen_ty_6 = _bindgen_ty_6::IFLA_BR_FDB_MAX_LEARNED;
pub const __IFLA_BR_MAX: _bindgen_ty_6 = _bindgen_ty_6::__IFLA_BR_MAX;
pub const BRIDGE_MODE_UNSPEC: _bindgen_ty_7 = _bindgen_ty_7::BRIDGE_MODE_UNSPEC;
pub const BRIDGE_MODE_HAIRPIN: _bindgen_ty_7 = _bindgen_ty_7::BRIDGE_MODE_HAIRPIN;
pub const IFLA_BRPORT_UNSPEC: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_UNSPEC;
pub const IFLA_BRPORT_STATE: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_STATE;
pub const IFLA_BRPORT_PRIORITY: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_PRIORITY;
pub const IFLA_BRPORT_COST: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_COST;
pub const IFLA_BRPORT_MODE: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MODE;
pub const IFLA_BRPORT_GUARD: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_GUARD;
pub const IFLA_BRPORT_PROTECT: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_PROTECT;
pub const IFLA_BRPORT_FAST_LEAVE: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_FAST_LEAVE;
pub const IFLA_BRPORT_LEARNING: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_LEARNING;
pub const IFLA_BRPORT_UNICAST_FLOOD: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_UNICAST_FLOOD;
pub const IFLA_BRPORT_PROXYARP: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_PROXYARP;
pub const IFLA_BRPORT_LEARNING_SYNC: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_LEARNING_SYNC;
pub const IFLA_BRPORT_PROXYARP_WIFI: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_PROXYARP_WIFI;
pub const IFLA_BRPORT_ROOT_ID: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_ROOT_ID;
pub const IFLA_BRPORT_BRIDGE_ID: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_BRIDGE_ID;
pub const IFLA_BRPORT_DESIGNATED_PORT: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_DESIGNATED_PORT;
pub const IFLA_BRPORT_DESIGNATED_COST: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_DESIGNATED_COST;
pub const IFLA_BRPORT_ID: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_ID;
pub const IFLA_BRPORT_NO: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_NO;
pub const IFLA_BRPORT_TOPOLOGY_CHANGE_ACK: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_TOPOLOGY_CHANGE_ACK;
pub const IFLA_BRPORT_CONFIG_PENDING: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_CONFIG_PENDING;
pub const IFLA_BRPORT_MESSAGE_AGE_TIMER: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MESSAGE_AGE_TIMER;
pub const IFLA_BRPORT_FORWARD_DELAY_TIMER: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_FORWARD_DELAY_TIMER;
pub const IFLA_BRPORT_HOLD_TIMER: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_HOLD_TIMER;
pub const IFLA_BRPORT_FLUSH: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_FLUSH;
pub const IFLA_BRPORT_MULTICAST_ROUTER: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MULTICAST_ROUTER;
pub const IFLA_BRPORT_PAD: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_PAD;
pub const IFLA_BRPORT_MCAST_FLOOD: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MCAST_FLOOD;
pub const IFLA_BRPORT_MCAST_TO_UCAST: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MCAST_TO_UCAST;
pub const IFLA_BRPORT_VLAN_TUNNEL: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_VLAN_TUNNEL;
pub const IFLA_BRPORT_BCAST_FLOOD: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_BCAST_FLOOD;
pub const IFLA_BRPORT_GROUP_FWD_MASK: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_GROUP_FWD_MASK;
pub const IFLA_BRPORT_NEIGH_SUPPRESS: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_NEIGH_SUPPRESS;
pub const IFLA_BRPORT_ISOLATED: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_ISOLATED;
pub const IFLA_BRPORT_BACKUP_PORT: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_BACKUP_PORT;
pub const IFLA_BRPORT_MRP_RING_OPEN: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MRP_RING_OPEN;
pub const IFLA_BRPORT_MRP_IN_OPEN: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MRP_IN_OPEN;
pub const IFLA_BRPORT_MCAST_EHT_HOSTS_LIMIT: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MCAST_EHT_HOSTS_LIMIT;
pub const IFLA_BRPORT_MCAST_EHT_HOSTS_CNT: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MCAST_EHT_HOSTS_CNT;
pub const IFLA_BRPORT_LOCKED: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_LOCKED;
pub const IFLA_BRPORT_MAB: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MAB;
pub const IFLA_BRPORT_MCAST_N_GROUPS: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MCAST_N_GROUPS;
pub const IFLA_BRPORT_MCAST_MAX_GROUPS: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_MCAST_MAX_GROUPS;
pub const IFLA_BRPORT_NEIGH_VLAN_SUPPRESS: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_NEIGH_VLAN_SUPPRESS;
pub const IFLA_BRPORT_BACKUP_NHID: _bindgen_ty_8 = _bindgen_ty_8::IFLA_BRPORT_BACKUP_NHID;
pub const __IFLA_BRPORT_MAX: _bindgen_ty_8 = _bindgen_ty_8::__IFLA_BRPORT_MAX;
pub const IFLA_INFO_UNSPEC: _bindgen_ty_9 = _bindgen_ty_9::IFLA_INFO_UNSPEC;
pub const IFLA_INFO_KIND: _bindgen_ty_9 = _bindgen_ty_9::IFLA_INFO_KIND;
pub const IFLA_INFO_DATA: _bindgen_ty_9 = _bindgen_ty_9::IFLA_INFO_DATA;
pub const IFLA_INFO_XSTATS: _bindgen_ty_9 = _bindgen_ty_9::IFLA_INFO_XSTATS;
pub const IFLA_INFO_SLAVE_KIND: _bindgen_ty_9 = _bindgen_ty_9::IFLA_INFO_SLAVE_KIND;
pub const IFLA_INFO_SLAVE_DATA: _bindgen_ty_9 = _bindgen_ty_9::IFLA_INFO_SLAVE_DATA;
pub const __IFLA_INFO_MAX: _bindgen_ty_9 = _bindgen_ty_9::__IFLA_INFO_MAX;
pub const IFLA_VLAN_UNSPEC: _bindgen_ty_10 = _bindgen_ty_10::IFLA_VLAN_UNSPEC;
pub const IFLA_VLAN_ID: _bindgen_ty_10 = _bindgen_ty_10::IFLA_VLAN_ID;
pub const IFLA_VLAN_FLAGS: _bindgen_ty_10 = _bindgen_ty_10::IFLA_VLAN_FLAGS;
pub const IFLA_VLAN_EGRESS_QOS: _bindgen_ty_10 = _bindgen_ty_10::IFLA_VLAN_EGRESS_QOS;
pub const IFLA_VLAN_INGRESS_QOS: _bindgen_ty_10 = _bindgen_ty_10::IFLA_VLAN_INGRESS_QOS;
pub const IFLA_VLAN_PROTOCOL: _bindgen_ty_10 = _bindgen_ty_10::IFLA_VLAN_PROTOCOL;
pub const __IFLA_VLAN_MAX: _bindgen_ty_10 = _bindgen_ty_10::__IFLA_VLAN_MAX;
pub const IFLA_VLAN_QOS_UNSPEC: _bindgen_ty_11 = _bindgen_ty_11::IFLA_VLAN_QOS_UNSPEC;
pub const IFLA_VLAN_QOS_MAPPING: _bindgen_ty_11 = _bindgen_ty_11::IFLA_VLAN_QOS_MAPPING;
pub const __IFLA_VLAN_QOS_MAX: _bindgen_ty_11 = _bindgen_ty_11::__IFLA_VLAN_QOS_MAX;
pub const IFLA_MACVLAN_UNSPEC: _bindgen_ty_12 = _bindgen_ty_12::IFLA_MACVLAN_UNSPEC;
pub const IFLA_MACVLAN_MODE: _bindgen_ty_12 = _bindgen_ty_12::IFLA_MACVLAN_MODE;
pub const IFLA_MACVLAN_FLAGS: _bindgen_ty_12 = _bindgen_ty_12::IFLA_MACVLAN_FLAGS;
pub const IFLA_MACVLAN_MACADDR_MODE: _bindgen_ty_12 = _bindgen_ty_12::IFLA_MACVLAN_MACADDR_MODE;
pub const IFLA_MACVLAN_MACADDR: _bindgen_ty_12 = _bindgen_ty_12::IFLA_MACVLAN_MACADDR;
pub const IFLA_MACVLAN_MACADDR_DATA: _bindgen_ty_12 = _bindgen_ty_12::IFLA_MACVLAN_MACADDR_DATA;
pub const IFLA_MACVLAN_MACADDR_COUNT: _bindgen_ty_12 = _bindgen_ty_12::IFLA_MACVLAN_MACADDR_COUNT;
pub const IFLA_MACVLAN_BC_QUEUE_LEN: _bindgen_ty_12 = _bindgen_ty_12::IFLA_MACVLAN_BC_QUEUE_LEN;
pub const IFLA_MACVLAN_BC_QUEUE_LEN_USED: _bindgen_ty_12 = _bindgen_ty_12::IFLA_MACVLAN_BC_QUEUE_LEN_USED;
pub const IFLA_MACVLAN_BC_CUTOFF: _bindgen_ty_12 = _bindgen_ty_12::IFLA_MACVLAN_BC_CUTOFF;
pub const __IFLA_MACVLAN_MAX: _bindgen_ty_12 = _bindgen_ty_12::__IFLA_MACVLAN_MAX;
pub const IFLA_VRF_UNSPEC: _bindgen_ty_13 = _bindgen_ty_13::IFLA_VRF_UNSPEC;
pub const IFLA_VRF_TABLE: _bindgen_ty_13 = _bindgen_ty_13::IFLA_VRF_TABLE;
pub const __IFLA_VRF_MAX: _bindgen_ty_13 = _bindgen_ty_13::__IFLA_VRF_MAX;
pub const IFLA_VRF_PORT_UNSPEC: _bindgen_ty_14 = _bindgen_ty_14::IFLA_VRF_PORT_UNSPEC;
pub const IFLA_VRF_PORT_TABLE: _bindgen_ty_14 = _bindgen_ty_14::IFLA_VRF_PORT_TABLE;
pub const __IFLA_VRF_PORT_MAX: _bindgen_ty_14 = _bindgen_ty_14::__IFLA_VRF_PORT_MAX;
pub const IFLA_MACSEC_UNSPEC: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_UNSPEC;
pub const IFLA_MACSEC_SCI: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_SCI;
pub const IFLA_MACSEC_PORT: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_PORT;
pub const IFLA_MACSEC_ICV_LEN: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_ICV_LEN;
pub const IFLA_MACSEC_CIPHER_SUITE: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_CIPHER_SUITE;
pub const IFLA_MACSEC_WINDOW: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_WINDOW;
pub const IFLA_MACSEC_ENCODING_SA: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_ENCODING_SA;
pub const IFLA_MACSEC_ENCRYPT: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_ENCRYPT;
pub const IFLA_MACSEC_PROTECT: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_PROTECT;
pub const IFLA_MACSEC_INC_SCI: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_INC_SCI;
pub const IFLA_MACSEC_ES: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_ES;
pub const IFLA_MACSEC_SCB: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_SCB;
pub const IFLA_MACSEC_REPLAY_PROTECT: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_REPLAY_PROTECT;
pub const IFLA_MACSEC_VALIDATION: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_VALIDATION;
pub const IFLA_MACSEC_PAD: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_PAD;
pub const IFLA_MACSEC_OFFLOAD: _bindgen_ty_15 = _bindgen_ty_15::IFLA_MACSEC_OFFLOAD;
pub const __IFLA_MACSEC_MAX: _bindgen_ty_15 = _bindgen_ty_15::__IFLA_MACSEC_MAX;
pub const IFLA_XFRM_UNSPEC: _bindgen_ty_16 = _bindgen_ty_16::IFLA_XFRM_UNSPEC;
pub const IFLA_XFRM_LINK: _bindgen_ty_16 = _bindgen_ty_16::IFLA_XFRM_LINK;
pub const IFLA_XFRM_IF_ID: _bindgen_ty_16 = _bindgen_ty_16::IFLA_XFRM_IF_ID;
pub const IFLA_XFRM_COLLECT_METADATA: _bindgen_ty_16 = _bindgen_ty_16::IFLA_XFRM_COLLECT_METADATA;
pub const __IFLA_XFRM_MAX: _bindgen_ty_16 = _bindgen_ty_16::__IFLA_XFRM_MAX;
pub const IFLA_IPVLAN_UNSPEC: _bindgen_ty_17 = _bindgen_ty_17::IFLA_IPVLAN_UNSPEC;
pub const IFLA_IPVLAN_MODE: _bindgen_ty_17 = _bindgen_ty_17::IFLA_IPVLAN_MODE;
pub const IFLA_IPVLAN_FLAGS: _bindgen_ty_17 = _bindgen_ty_17::IFLA_IPVLAN_FLAGS;
pub const __IFLA_IPVLAN_MAX: _bindgen_ty_17 = _bindgen_ty_17::__IFLA_IPVLAN_MAX;
pub const IFLA_NETKIT_UNSPEC: _bindgen_ty_18 = _bindgen_ty_18::IFLA_NETKIT_UNSPEC;
pub const IFLA_NETKIT_PEER_INFO: _bindgen_ty_18 = _bindgen_ty_18::IFLA_NETKIT_PEER_INFO;
pub const IFLA_NETKIT_PRIMARY: _bindgen_ty_18 = _bindgen_ty_18::IFLA_NETKIT_PRIMARY;
pub const IFLA_NETKIT_POLICY: _bindgen_ty_18 = _bindgen_ty_18::IFLA_NETKIT_POLICY;
pub const IFLA_NETKIT_PEER_POLICY: _bindgen_ty_18 = _bindgen_ty_18::IFLA_NETKIT_PEER_POLICY;
pub const IFLA_NETKIT_MODE: _bindgen_ty_18 = _bindgen_ty_18::IFLA_NETKIT_MODE;
pub const IFLA_NETKIT_SCRUB: _bindgen_ty_18 = _bindgen_ty_18::IFLA_NETKIT_SCRUB;
pub const IFLA_NETKIT_PEER_SCRUB: _bindgen_ty_18 = _bindgen_ty_18::IFLA_NETKIT_PEER_SCRUB;
pub const __IFLA_NETKIT_MAX: _bindgen_ty_18 = _bindgen_ty_18::__IFLA_NETKIT_MAX;
pub const VNIFILTER_ENTRY_STATS_UNSPEC: _bindgen_ty_19 = _bindgen_ty_19::VNIFILTER_ENTRY_STATS_UNSPEC;
pub const VNIFILTER_ENTRY_STATS_RX_BYTES: _bindgen_ty_19 = _bindgen_ty_19::VNIFILTER_ENTRY_STATS_RX_BYTES;
pub const VNIFILTER_ENTRY_STATS_RX_PKTS: _bindgen_ty_19 = _bindgen_ty_19::VNIFILTER_ENTRY_STATS_RX_PKTS;
pub const VNIFILTER_ENTRY_STATS_RX_DROPS: _bindgen_ty_19 = _bindgen_ty_19::VNIFILTER_ENTRY_STATS_RX_DROPS;
pub const VNIFILTER_ENTRY_STATS_RX_ERRORS: _bindgen_ty_19 = _bindgen_ty_19::VNIFILTER_ENTRY_STATS_RX_ERRORS;
pub const VNIFILTER_ENTRY_STATS_TX_BYTES: _bindgen_ty_19 = _bindgen_ty_19::VNIFILTER_ENTRY_STATS_TX_BYTES;
pub const VNIFILTER_ENTRY_STATS_TX_PKTS: _bindgen_ty_19 = _bindgen_ty_19::VNIFILTER_ENTRY_STATS_TX_PKTS;
pub const VNIFILTER_ENTRY_STATS_TX_DROPS: _bindgen_ty_19 = _bindgen_ty_19::VNIFILTER_ENTRY_STATS_TX_DROPS;
pub const VNIFILTER_ENTRY_STATS_TX_ERRORS: _bindgen_ty_19 = _bindgen_ty_19::VNIFILTER_ENTRY_STATS_TX_ERRORS;
pub const VNIFILTER_ENTRY_STATS_PAD: _bindgen_ty_19 = _bindgen_ty_19::VNIFILTER_ENTRY_STATS_PAD;
pub const __VNIFILTER_ENTRY_STATS_MAX: _bindgen_ty_19 = _bindgen_ty_19::__VNIFILTER_ENTRY_STATS_MAX;
pub const VXLAN_VNIFILTER_ENTRY_UNSPEC: _bindgen_ty_20 = _bindgen_ty_20::VXLAN_VNIFILTER_ENTRY_UNSPEC;
pub const VXLAN_VNIFILTER_ENTRY_START: _bindgen_ty_20 = _bindgen_ty_20::VXLAN_VNIFILTER_ENTRY_START;
pub const VXLAN_VNIFILTER_ENTRY_END: _bindgen_ty_20 = _bindgen_ty_20::VXLAN_VNIFILTER_ENTRY_END;
pub const VXLAN_VNIFILTER_ENTRY_GROUP: _bindgen_ty_20 = _bindgen_ty_20::VXLAN_VNIFILTER_ENTRY_GROUP;
pub const VXLAN_VNIFILTER_ENTRY_GROUP6: _bindgen_ty_20 = _bindgen_ty_20::VXLAN_VNIFILTER_ENTRY_GROUP6;
pub const VXLAN_VNIFILTER_ENTRY_STATS: _bindgen_ty_20 = _bindgen_ty_20::VXLAN_VNIFILTER_ENTRY_STATS;
pub const __VXLAN_VNIFILTER_ENTRY_MAX: _bindgen_ty_20 = _bindgen_ty_20::__VXLAN_VNIFILTER_ENTRY_MAX;
pub const VXLAN_VNIFILTER_UNSPEC: _bindgen_ty_21 = _bindgen_ty_21::VXLAN_VNIFILTER_UNSPEC;
pub const VXLAN_VNIFILTER_ENTRY: _bindgen_ty_21 = _bindgen_ty_21::VXLAN_VNIFILTER_ENTRY;
pub const __VXLAN_VNIFILTER_MAX: _bindgen_ty_21 = _bindgen_ty_21::__VXLAN_VNIFILTER_MAX;
pub const IFLA_VXLAN_UNSPEC: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_UNSPEC;
pub const IFLA_VXLAN_ID: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_ID;
pub const IFLA_VXLAN_GROUP: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_GROUP;
pub const IFLA_VXLAN_LINK: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_LINK;
pub const IFLA_VXLAN_LOCAL: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_LOCAL;
pub const IFLA_VXLAN_TTL: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_TTL;
pub const IFLA_VXLAN_TOS: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_TOS;
pub const IFLA_VXLAN_LEARNING: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_LEARNING;
pub const IFLA_VXLAN_AGEING: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_AGEING;
pub const IFLA_VXLAN_LIMIT: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_LIMIT;
pub const IFLA_VXLAN_PORT_RANGE: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_PORT_RANGE;
pub const IFLA_VXLAN_PROXY: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_PROXY;
pub const IFLA_VXLAN_RSC: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_RSC;
pub const IFLA_VXLAN_L2MISS: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_L2MISS;
pub const IFLA_VXLAN_L3MISS: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_L3MISS;
pub const IFLA_VXLAN_PORT: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_PORT;
pub const IFLA_VXLAN_GROUP6: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_GROUP6;
pub const IFLA_VXLAN_LOCAL6: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_LOCAL6;
pub const IFLA_VXLAN_UDP_CSUM: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_UDP_CSUM;
pub const IFLA_VXLAN_UDP_ZERO_CSUM6_TX: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_UDP_ZERO_CSUM6_TX;
pub const IFLA_VXLAN_UDP_ZERO_CSUM6_RX: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_UDP_ZERO_CSUM6_RX;
pub const IFLA_VXLAN_REMCSUM_TX: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_REMCSUM_TX;
pub const IFLA_VXLAN_REMCSUM_RX: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_REMCSUM_RX;
pub const IFLA_VXLAN_GBP: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_GBP;
pub const IFLA_VXLAN_REMCSUM_NOPARTIAL: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_REMCSUM_NOPARTIAL;
pub const IFLA_VXLAN_COLLECT_METADATA: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_COLLECT_METADATA;
pub const IFLA_VXLAN_LABEL: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_LABEL;
pub const IFLA_VXLAN_GPE: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_GPE;
pub const IFLA_VXLAN_TTL_INHERIT: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_TTL_INHERIT;
pub const IFLA_VXLAN_DF: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_DF;
pub const IFLA_VXLAN_VNIFILTER: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_VNIFILTER;
pub const IFLA_VXLAN_LOCALBYPASS: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_LOCALBYPASS;
pub const IFLA_VXLAN_LABEL_POLICY: _bindgen_ty_22 = _bindgen_ty_22::IFLA_VXLAN_LABEL_POLICY;
pub const __IFLA_VXLAN_MAX: _bindgen_ty_22 = _bindgen_ty_22::__IFLA_VXLAN_MAX;
pub const IFLA_GENEVE_UNSPEC: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_UNSPEC;
pub const IFLA_GENEVE_ID: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_ID;
pub const IFLA_GENEVE_REMOTE: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_REMOTE;
pub const IFLA_GENEVE_TTL: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_TTL;
pub const IFLA_GENEVE_TOS: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_TOS;
pub const IFLA_GENEVE_PORT: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_PORT;
pub const IFLA_GENEVE_COLLECT_METADATA: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_COLLECT_METADATA;
pub const IFLA_GENEVE_REMOTE6: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_REMOTE6;
pub const IFLA_GENEVE_UDP_CSUM: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_UDP_CSUM;
pub const IFLA_GENEVE_UDP_ZERO_CSUM6_TX: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_UDP_ZERO_CSUM6_TX;
pub const IFLA_GENEVE_UDP_ZERO_CSUM6_RX: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_UDP_ZERO_CSUM6_RX;
pub const IFLA_GENEVE_LABEL: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_LABEL;
pub const IFLA_GENEVE_TTL_INHERIT: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_TTL_INHERIT;
pub const IFLA_GENEVE_DF: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_DF;
pub const IFLA_GENEVE_INNER_PROTO_INHERIT: _bindgen_ty_23 = _bindgen_ty_23::IFLA_GENEVE_INNER_PROTO_INHERIT;
pub const __IFLA_GENEVE_MAX: _bindgen_ty_23 = _bindgen_ty_23::__IFLA_GENEVE_MAX;
pub const IFLA_BAREUDP_UNSPEC: _bindgen_ty_24 = _bindgen_ty_24::IFLA_BAREUDP_UNSPEC;
pub const IFLA_BAREUDP_PORT: _bindgen_ty_24 = _bindgen_ty_24::IFLA_BAREUDP_PORT;
pub const IFLA_BAREUDP_ETHERTYPE: _bindgen_ty_24 = _bindgen_ty_24::IFLA_BAREUDP_ETHERTYPE;
pub const IFLA_BAREUDP_SRCPORT_MIN: _bindgen_ty_24 = _bindgen_ty_24::IFLA_BAREUDP_SRCPORT_MIN;
pub const IFLA_BAREUDP_MULTIPROTO_MODE: _bindgen_ty_24 = _bindgen_ty_24::IFLA_BAREUDP_MULTIPROTO_MODE;
pub const __IFLA_BAREUDP_MAX: _bindgen_ty_24 = _bindgen_ty_24::__IFLA_BAREUDP_MAX;
pub const IFLA_PPP_UNSPEC: _bindgen_ty_25 = _bindgen_ty_25::IFLA_PPP_UNSPEC;
pub const IFLA_PPP_DEV_FD: _bindgen_ty_25 = _bindgen_ty_25::IFLA_PPP_DEV_FD;
pub const __IFLA_PPP_MAX: _bindgen_ty_25 = _bindgen_ty_25::__IFLA_PPP_MAX;
pub const IFLA_GTP_UNSPEC: _bindgen_ty_26 = _bindgen_ty_26::IFLA_GTP_UNSPEC;
pub const IFLA_GTP_FD0: _bindgen_ty_26 = _bindgen_ty_26::IFLA_GTP_FD0;
pub const IFLA_GTP_FD1: _bindgen_ty_26 = _bindgen_ty_26::IFLA_GTP_FD1;
pub const IFLA_GTP_PDP_HASHSIZE: _bindgen_ty_26 = _bindgen_ty_26::IFLA_GTP_PDP_HASHSIZE;
pub const IFLA_GTP_ROLE: _bindgen_ty_26 = _bindgen_ty_26::IFLA_GTP_ROLE;
pub const IFLA_GTP_CREATE_SOCKETS: _bindgen_ty_26 = _bindgen_ty_26::IFLA_GTP_CREATE_SOCKETS;
pub const IFLA_GTP_RESTART_COUNT: _bindgen_ty_26 = _bindgen_ty_26::IFLA_GTP_RESTART_COUNT;
pub const IFLA_GTP_LOCAL: _bindgen_ty_26 = _bindgen_ty_26::IFLA_GTP_LOCAL;
pub const IFLA_GTP_LOCAL6: _bindgen_ty_26 = _bindgen_ty_26::IFLA_GTP_LOCAL6;
pub const __IFLA_GTP_MAX: _bindgen_ty_26 = _bindgen_ty_26::__IFLA_GTP_MAX;
pub const IFLA_BOND_UNSPEC: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_UNSPEC;
pub const IFLA_BOND_MODE: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_MODE;
pub const IFLA_BOND_ACTIVE_SLAVE: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_ACTIVE_SLAVE;
pub const IFLA_BOND_MIIMON: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_MIIMON;
pub const IFLA_BOND_UPDELAY: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_UPDELAY;
pub const IFLA_BOND_DOWNDELAY: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_DOWNDELAY;
pub const IFLA_BOND_USE_CARRIER: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_USE_CARRIER;
pub const IFLA_BOND_ARP_INTERVAL: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_ARP_INTERVAL;
pub const IFLA_BOND_ARP_IP_TARGET: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_ARP_IP_TARGET;
pub const IFLA_BOND_ARP_VALIDATE: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_ARP_VALIDATE;
pub const IFLA_BOND_ARP_ALL_TARGETS: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_ARP_ALL_TARGETS;
pub const IFLA_BOND_PRIMARY: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_PRIMARY;
pub const IFLA_BOND_PRIMARY_RESELECT: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_PRIMARY_RESELECT;
pub const IFLA_BOND_FAIL_OVER_MAC: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_FAIL_OVER_MAC;
pub const IFLA_BOND_XMIT_HASH_POLICY: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_XMIT_HASH_POLICY;
pub const IFLA_BOND_RESEND_IGMP: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_RESEND_IGMP;
pub const IFLA_BOND_NUM_PEER_NOTIF: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_NUM_PEER_NOTIF;
pub const IFLA_BOND_ALL_SLAVES_ACTIVE: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_ALL_SLAVES_ACTIVE;
pub const IFLA_BOND_MIN_LINKS: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_MIN_LINKS;
pub const IFLA_BOND_LP_INTERVAL: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_LP_INTERVAL;
pub const IFLA_BOND_PACKETS_PER_SLAVE: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_PACKETS_PER_SLAVE;
pub const IFLA_BOND_AD_LACP_RATE: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_AD_LACP_RATE;
pub const IFLA_BOND_AD_SELECT: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_AD_SELECT;
pub const IFLA_BOND_AD_INFO: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_AD_INFO;
pub const IFLA_BOND_AD_ACTOR_SYS_PRIO: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_AD_ACTOR_SYS_PRIO;
pub const IFLA_BOND_AD_USER_PORT_KEY: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_AD_USER_PORT_KEY;
pub const IFLA_BOND_AD_ACTOR_SYSTEM: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_AD_ACTOR_SYSTEM;
pub const IFLA_BOND_TLB_DYNAMIC_LB: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_TLB_DYNAMIC_LB;
pub const IFLA_BOND_PEER_NOTIF_DELAY: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_PEER_NOTIF_DELAY;
pub const IFLA_BOND_AD_LACP_ACTIVE: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_AD_LACP_ACTIVE;
pub const IFLA_BOND_MISSED_MAX: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_MISSED_MAX;
pub const IFLA_BOND_NS_IP6_TARGET: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_NS_IP6_TARGET;
pub const IFLA_BOND_COUPLED_CONTROL: _bindgen_ty_27 = _bindgen_ty_27::IFLA_BOND_COUPLED_CONTROL;
pub const __IFLA_BOND_MAX: _bindgen_ty_27 = _bindgen_ty_27::__IFLA_BOND_MAX;
pub const IFLA_BOND_AD_INFO_UNSPEC: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_INFO_UNSPEC;
pub const IFLA_BOND_AD_INFO_AGGREGATOR: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_INFO_AGGREGATOR;
pub const IFLA_BOND_AD_INFO_NUM_PORTS: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_INFO_NUM_PORTS;
pub const IFLA_BOND_AD_INFO_ACTOR_KEY: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_INFO_ACTOR_KEY;
pub const IFLA_BOND_AD_INFO_PARTNER_KEY: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_INFO_PARTNER_KEY;
pub const IFLA_BOND_AD_INFO_PARTNER_MAC: _bindgen_ty_28 = _bindgen_ty_28::IFLA_BOND_AD_INFO_PARTNER_MAC;
pub const __IFLA_BOND_AD_INFO_MAX: _bindgen_ty_28 = _bindgen_ty_28::__IFLA_BOND_AD_INFO_MAX;
pub const IFLA_BOND_SLAVE_UNSPEC: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_SLAVE_UNSPEC;
pub const IFLA_BOND_SLAVE_STATE: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_SLAVE_STATE;
pub const IFLA_BOND_SLAVE_MII_STATUS: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_SLAVE_MII_STATUS;
pub const IFLA_BOND_SLAVE_LINK_FAILURE_COUNT: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_SLAVE_LINK_FAILURE_COUNT;
pub const IFLA_BOND_SLAVE_PERM_HWADDR: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_SLAVE_PERM_HWADDR;
pub const IFLA_BOND_SLAVE_QUEUE_ID: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_SLAVE_QUEUE_ID;
pub const IFLA_BOND_SLAVE_AD_AGGREGATOR_ID: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_SLAVE_AD_AGGREGATOR_ID;
pub const IFLA_BOND_SLAVE_AD_ACTOR_OPER_PORT_STATE: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_SLAVE_AD_ACTOR_OPER_PORT_STATE;
pub const IFLA_BOND_SLAVE_AD_PARTNER_OPER_PORT_STATE: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_SLAVE_AD_PARTNER_OPER_PORT_STATE;
pub const IFLA_BOND_SLAVE_PRIO: _bindgen_ty_29 = _bindgen_ty_29::IFLA_BOND_SLAVE_PRIO;
pub const __IFLA_BOND_SLAVE_MAX: _bindgen_ty_29 = _bindgen_ty_29::__IFLA_BOND_SLAVE_MAX;
pub const IFLA_VF_INFO_UNSPEC: _bindgen_ty_30 = _bindgen_ty_30::IFLA_VF_INFO_UNSPEC;
pub const IFLA_VF_INFO: _bindgen_ty_30 = _bindgen_ty_30::IFLA_VF_INFO;
pub const __IFLA_VF_INFO_MAX: _bindgen_ty_30 = _bindgen_ty_30::__IFLA_VF_INFO_MAX;
pub const IFLA_VF_UNSPEC: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_UNSPEC;
pub const IFLA_VF_MAC: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_MAC;
pub const IFLA_VF_VLAN: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_VLAN;
pub const IFLA_VF_TX_RATE: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_TX_RATE;
pub const IFLA_VF_SPOOFCHK: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_SPOOFCHK;
pub const IFLA_VF_LINK_STATE: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_LINK_STATE;
pub const IFLA_VF_RATE: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_RATE;
pub const IFLA_VF_RSS_QUERY_EN: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_RSS_QUERY_EN;
pub const IFLA_VF_STATS: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_STATS;
pub const IFLA_VF_TRUST: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_TRUST;
pub const IFLA_VF_IB_NODE_GUID: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_IB_NODE_GUID;
pub const IFLA_VF_IB_PORT_GUID: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_IB_PORT_GUID;
pub const IFLA_VF_VLAN_LIST: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_VLAN_LIST;
pub const IFLA_VF_BROADCAST: _bindgen_ty_31 = _bindgen_ty_31::IFLA_VF_BROADCAST;
pub const __IFLA_VF_MAX: _bindgen_ty_31 = _bindgen_ty_31::__IFLA_VF_MAX;
pub const IFLA_VF_VLAN_INFO_UNSPEC: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_VLAN_INFO_UNSPEC;
pub const IFLA_VF_VLAN_INFO: _bindgen_ty_32 = _bindgen_ty_32::IFLA_VF_VLAN_INFO;
pub const __IFLA_VF_VLAN_INFO_MAX: _bindgen_ty_32 = _bindgen_ty_32::__IFLA_VF_VLAN_INFO_MAX;
pub const IFLA_VF_LINK_STATE_AUTO: _bindgen_ty_33 = _bindgen_ty_33::IFLA_VF_LINK_STATE_AUTO;
pub const IFLA_VF_LINK_STATE_ENABLE: _bindgen_ty_33 = _bindgen_ty_33::IFLA_VF_LINK_STATE_ENABLE;
pub const IFLA_VF_LINK_STATE_DISABLE: _bindgen_ty_33 = _bindgen_ty_33::IFLA_VF_LINK_STATE_DISABLE;
pub const __IFLA_VF_LINK_STATE_MAX: _bindgen_ty_33 = _bindgen_ty_33::__IFLA_VF_LINK_STATE_MAX;
pub const IFLA_VF_STATS_RX_PACKETS: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_STATS_RX_PACKETS;
pub const IFLA_VF_STATS_TX_PACKETS: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_STATS_TX_PACKETS;
pub const IFLA_VF_STATS_RX_BYTES: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_STATS_RX_BYTES;
pub const IFLA_VF_STATS_TX_BYTES: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_STATS_TX_BYTES;
pub const IFLA_VF_STATS_BROADCAST: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_STATS_BROADCAST;
pub const IFLA_VF_STATS_MULTICAST: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_STATS_MULTICAST;
pub const IFLA_VF_STATS_PAD: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_STATS_PAD;
pub const IFLA_VF_STATS_RX_DROPPED: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_STATS_RX_DROPPED;
pub const IFLA_VF_STATS_TX_DROPPED: _bindgen_ty_34 = _bindgen_ty_34::IFLA_VF_STATS_TX_DROPPED;
pub const __IFLA_VF_STATS_MAX: _bindgen_ty_34 = _bindgen_ty_34::__IFLA_VF_STATS_MAX;
pub const IFLA_VF_PORT_UNSPEC: _bindgen_ty_35 = _bindgen_ty_35::IFLA_VF_PORT_UNSPEC;
pub const IFLA_VF_PORT: _bindgen_ty_35 = _bindgen_ty_35::IFLA_VF_PORT;
pub const __IFLA_VF_PORT_MAX: _bindgen_ty_35 = _bindgen_ty_35::__IFLA_VF_PORT_MAX;
pub const IFLA_PORT_UNSPEC: _bindgen_ty_36 = _bindgen_ty_36::IFLA_PORT_UNSPEC;
pub const IFLA_PORT_VF: _bindgen_ty_36 = _bindgen_ty_36::IFLA_PORT_VF;
pub const IFLA_PORT_PROFILE: _bindgen_ty_36 = _bindgen_ty_36::IFLA_PORT_PROFILE;
pub const IFLA_PORT_VSI_TYPE: _bindgen_ty_36 = _bindgen_ty_36::IFLA_PORT_VSI_TYPE;
pub const IFLA_PORT_INSTANCE_UUID: _bindgen_ty_36 = _bindgen_ty_36::IFLA_PORT_INSTANCE_UUID;
pub const IFLA_PORT_HOST_UUID: _bindgen_ty_36 = _bindgen_ty_36::IFLA_PORT_HOST_UUID;
pub const IFLA_PORT_REQUEST: _bindgen_ty_36 = _bindgen_ty_36::IFLA_PORT_REQUEST;
pub const IFLA_PORT_RESPONSE: _bindgen_ty_36 = _bindgen_ty_36::IFLA_PORT_RESPONSE;
pub const __IFLA_PORT_MAX: _bindgen_ty_36 = _bindgen_ty_36::__IFLA_PORT_MAX;
pub const PORT_REQUEST_PREASSOCIATE: _bindgen_ty_37 = _bindgen_ty_37::PORT_REQUEST_PREASSOCIATE;
pub const PORT_REQUEST_PREASSOCIATE_RR: _bindgen_ty_37 = _bindgen_ty_37::PORT_REQUEST_PREASSOCIATE_RR;
pub const PORT_REQUEST_ASSOCIATE: _bindgen_ty_37 = _bindgen_ty_37::PORT_REQUEST_ASSOCIATE;
pub const PORT_REQUEST_DISASSOCIATE: _bindgen_ty_37 = _bindgen_ty_37::PORT_REQUEST_DISASSOCIATE;
pub const PORT_VDP_RESPONSE_SUCCESS: _bindgen_ty_38 = _bindgen_ty_38::PORT_VDP_RESPONSE_SUCCESS;
pub const PORT_VDP_RESPONSE_INVALID_FORMAT: _bindgen_ty_38 = _bindgen_ty_38::PORT_VDP_RESPONSE_INVALID_FORMAT;
pub const PORT_VDP_RESPONSE_INSUFFICIENT_RESOURCES: _bindgen_ty_38 = _bindgen_ty_38::PORT_VDP_RESPONSE_INSUFFICIENT_RESOURCES;
pub const PORT_VDP_RESPONSE_UNUSED_VTID: _bindgen_ty_38 = _bindgen_ty_38::PORT_VDP_RESPONSE_UNUSED_VTID;
pub const PORT_VDP_RESPONSE_VTID_VIOLATION: _bindgen_ty_38 = _bindgen_ty_38::PORT_VDP_RESPONSE_VTID_VIOLATION;
pub const PORT_VDP_RESPONSE_VTID_VERSION_VIOALTION: _bindgen_ty_38 = _bindgen_ty_38::PORT_VDP_RESPONSE_VTID_VERSION_VIOALTION;
pub const PORT_VDP_RESPONSE_OUT_OF_SYNC: _bindgen_ty_38 = _bindgen_ty_38::PORT_VDP_RESPONSE_OUT_OF_SYNC;
pub const PORT_PROFILE_RESPONSE_SUCCESS: _bindgen_ty_38 = _bindgen_ty_38::PORT_PROFILE_RESPONSE_SUCCESS;
pub const PORT_PROFILE_RESPONSE_INPROGRESS: _bindgen_ty_38 = _bindgen_ty_38::PORT_PROFILE_RESPONSE_INPROGRESS;
pub const PORT_PROFILE_RESPONSE_INVALID: _bindgen_ty_38 = _bindgen_ty_38::PORT_PROFILE_RESPONSE_INVALID;
pub const PORT_PROFILE_RESPONSE_BADSTATE: _bindgen_ty_38 = _bindgen_ty_38::PORT_PROFILE_RESPONSE_BADSTATE;
pub const PORT_PROFILE_RESPONSE_INSUFFICIENT_RESOURCES: _bindgen_ty_38 = _bindgen_ty_38::PORT_PROFILE_RESPONSE_INSUFFICIENT_RESOURCES;
pub const PORT_PROFILE_RESPONSE_ERROR: _bindgen_ty_38 = _bindgen_ty_38::PORT_PROFILE_RESPONSE_ERROR;
pub const IFLA_IPOIB_UNSPEC: _bindgen_ty_39 = _bindgen_ty_39::IFLA_IPOIB_UNSPEC;
pub const IFLA_IPOIB_PKEY: _bindgen_ty_39 = _bindgen_ty_39::IFLA_IPOIB_PKEY;
pub const IFLA_IPOIB_MODE: _bindgen_ty_39 = _bindgen_ty_39::IFLA_IPOIB_MODE;
pub const IFLA_IPOIB_UMCAST: _bindgen_ty_39 = _bindgen_ty_39::IFLA_IPOIB_UMCAST;
pub const __IFLA_IPOIB_MAX: _bindgen_ty_39 = _bindgen_ty_39::__IFLA_IPOIB_MAX;
pub const IPOIB_MODE_DATAGRAM: _bindgen_ty_40 = _bindgen_ty_40::IPOIB_MODE_DATAGRAM;
pub const IPOIB_MODE_CONNECTED: _bindgen_ty_40 = _bindgen_ty_40::IPOIB_MODE_CONNECTED;
pub const HSR_PROTOCOL_HSR: _bindgen_ty_41 = _bindgen_ty_41::HSR_PROTOCOL_HSR;
pub const HSR_PROTOCOL_PRP: _bindgen_ty_41 = _bindgen_ty_41::HSR_PROTOCOL_PRP;
pub const HSR_PROTOCOL_MAX: _bindgen_ty_41 = _bindgen_ty_41::HSR_PROTOCOL_MAX;
pub const IFLA_HSR_UNSPEC: _bindgen_ty_42 = _bindgen_ty_42::IFLA_HSR_UNSPEC;
pub const IFLA_HSR_SLAVE1: _bindgen_ty_42 = _bindgen_ty_42::IFLA_HSR_SLAVE1;
pub const IFLA_HSR_SLAVE2: _bindgen_ty_42 = _bindgen_ty_42::IFLA_HSR_SLAVE2;
pub const IFLA_HSR_MULTICAST_SPEC: _bindgen_ty_42 = _bindgen_ty_42::IFLA_HSR_MULTICAST_SPEC;
pub const IFLA_HSR_SUPERVISION_ADDR: _bindgen_ty_42 = _bindgen_ty_42::IFLA_HSR_SUPERVISION_ADDR;
pub const IFLA_HSR_SEQ_NR: _bindgen_ty_42 = _bindgen_ty_42::IFLA_HSR_SEQ_NR;
pub const IFLA_HSR_VERSION: _bindgen_ty_42 = _bindgen_ty_42::IFLA_HSR_VERSION;
pub const IFLA_HSR_PROTOCOL: _bindgen_ty_42 = _bindgen_ty_42::IFLA_HSR_PROTOCOL;
pub const IFLA_HSR_INTERLINK: _bindgen_ty_42 = _bindgen_ty_42::IFLA_HSR_INTERLINK;
pub const __IFLA_HSR_MAX: _bindgen_ty_42 = _bindgen_ty_42::__IFLA_HSR_MAX;
pub const IFLA_STATS_UNSPEC: _bindgen_ty_43 = _bindgen_ty_43::IFLA_STATS_UNSPEC;
pub const IFLA_STATS_LINK_64: _bindgen_ty_43 = _bindgen_ty_43::IFLA_STATS_LINK_64;
pub const IFLA_STATS_LINK_XSTATS: _bindgen_ty_43 = _bindgen_ty_43::IFLA_STATS_LINK_XSTATS;
pub const IFLA_STATS_LINK_XSTATS_SLAVE: _bindgen_ty_43 = _bindgen_ty_43::IFLA_STATS_LINK_XSTATS_SLAVE;
pub const IFLA_STATS_LINK_OFFLOAD_XSTATS: _bindgen_ty_43 = _bindgen_ty_43::IFLA_STATS_LINK_OFFLOAD_XSTATS;
pub const IFLA_STATS_AF_SPEC: _bindgen_ty_43 = _bindgen_ty_43::IFLA_STATS_AF_SPEC;
pub const __IFLA_STATS_MAX: _bindgen_ty_43 = _bindgen_ty_43::__IFLA_STATS_MAX;
pub const IFLA_STATS_GETSET_UNSPEC: _bindgen_ty_44 = _bindgen_ty_44::IFLA_STATS_GETSET_UNSPEC;
pub const IFLA_STATS_GET_FILTERS: _bindgen_ty_44 = _bindgen_ty_44::IFLA_STATS_GET_FILTERS;
pub const IFLA_STATS_SET_OFFLOAD_XSTATS_L3_STATS: _bindgen_ty_44 = _bindgen_ty_44::IFLA_STATS_SET_OFFLOAD_XSTATS_L3_STATS;
pub const __IFLA_STATS_GETSET_MAX: _bindgen_ty_44 = _bindgen_ty_44::__IFLA_STATS_GETSET_MAX;
pub const LINK_XSTATS_TYPE_UNSPEC: _bindgen_ty_45 = _bindgen_ty_45::LINK_XSTATS_TYPE_UNSPEC;
pub const LINK_XSTATS_TYPE_BRIDGE: _bindgen_ty_45 = _bindgen_ty_45::LINK_XSTATS_TYPE_BRIDGE;
pub const LINK_XSTATS_TYPE_BOND: _bindgen_ty_45 = _bindgen_ty_45::LINK_XSTATS_TYPE_BOND;
pub const __LINK_XSTATS_TYPE_MAX: _bindgen_ty_45 = _bindgen_ty_45::__LINK_XSTATS_TYPE_MAX;
pub const IFLA_OFFLOAD_XSTATS_UNSPEC: _bindgen_ty_46 = _bindgen_ty_46::IFLA_OFFLOAD_XSTATS_UNSPEC;
pub const IFLA_OFFLOAD_XSTATS_CPU_HIT: _bindgen_ty_46 = _bindgen_ty_46::IFLA_OFFLOAD_XSTATS_CPU_HIT;
pub const IFLA_OFFLOAD_XSTATS_HW_S_INFO: _bindgen_ty_46 = _bindgen_ty_46::IFLA_OFFLOAD_XSTATS_HW_S_INFO;
pub const IFLA_OFFLOAD_XSTATS_L3_STATS: _bindgen_ty_46 = _bindgen_ty_46::IFLA_OFFLOAD_XSTATS_L3_STATS;
pub const __IFLA_OFFLOAD_XSTATS_MAX: _bindgen_ty_46 = _bindgen_ty_46::__IFLA_OFFLOAD_XSTATS_MAX;
pub const IFLA_OFFLOAD_XSTATS_HW_S_INFO_UNSPEC: _bindgen_ty_47 = _bindgen_ty_47::IFLA_OFFLOAD_XSTATS_HW_S_INFO_UNSPEC;
pub const IFLA_OFFLOAD_XSTATS_HW_S_INFO_REQUEST: _bindgen_ty_47 = _bindgen_ty_47::IFLA_OFFLOAD_XSTATS_HW_S_INFO_REQUEST;
pub const IFLA_OFFLOAD_XSTATS_HW_S_INFO_USED: _bindgen_ty_47 = _bindgen_ty_47::IFLA_OFFLOAD_XSTATS_HW_S_INFO_USED;
pub const __IFLA_OFFLOAD_XSTATS_HW_S_INFO_MAX: _bindgen_ty_47 = _bindgen_ty_47::__IFLA_OFFLOAD_XSTATS_HW_S_INFO_MAX;
pub const XDP_ATTACHED_NONE: _bindgen_ty_48 = _bindgen_ty_48::XDP_ATTACHED_NONE;
pub const XDP_ATTACHED_DRV: _bindgen_ty_48 = _bindgen_ty_48::XDP_ATTACHED_DRV;
pub const XDP_ATTACHED_SKB: _bindgen_ty_48 = _bindgen_ty_48::XDP_ATTACHED_SKB;
pub const XDP_ATTACHED_HW: _bindgen_ty_48 = _bindgen_ty_48::XDP_ATTACHED_HW;
pub const XDP_ATTACHED_MULTI: _bindgen_ty_48 = _bindgen_ty_48::XDP_ATTACHED_MULTI;
pub const IFLA_XDP_UNSPEC: _bindgen_ty_49 = _bindgen_ty_49::IFLA_XDP_UNSPEC;
pub const IFLA_XDP_FD: _bindgen_ty_49 = _bindgen_ty_49::IFLA_XDP_FD;
pub const IFLA_XDP_ATTACHED: _bindgen_ty_49 = _bindgen_ty_49::IFLA_XDP_ATTACHED;
pub const IFLA_XDP_FLAGS: _bindgen_ty_49 = _bindgen_ty_49::IFLA_XDP_FLAGS;
pub const IFLA_XDP_PROG_ID: _bindgen_ty_49 = _bindgen_ty_49::IFLA_XDP_PROG_ID;
pub const IFLA_XDP_DRV_PROG_ID: _bindgen_ty_49 = _bindgen_ty_49::IFLA_XDP_DRV_PROG_ID;
pub const IFLA_XDP_SKB_PROG_ID: _bindgen_ty_49 = _bindgen_ty_49::IFLA_XDP_SKB_PROG_ID;
pub const IFLA_XDP_HW_PROG_ID: _bindgen_ty_49 = _bindgen_ty_49::IFLA_XDP_HW_PROG_ID;
pub const IFLA_XDP_EXPECTED_FD: _bindgen_ty_49 = _bindgen_ty_49::IFLA_XDP_EXPECTED_FD;
pub const __IFLA_XDP_MAX: _bindgen_ty_49 = _bindgen_ty_49::__IFLA_XDP_MAX;
pub const IFLA_EVENT_NONE: _bindgen_ty_50 = _bindgen_ty_50::IFLA_EVENT_NONE;
pub const IFLA_EVENT_REBOOT: _bindgen_ty_50 = _bindgen_ty_50::IFLA_EVENT_REBOOT;
pub const IFLA_EVENT_FEATURES: _bindgen_ty_50 = _bindgen_ty_50::IFLA_EVENT_FEATURES;
pub const IFLA_EVENT_BONDING_FAILOVER: _bindgen_ty_50 = _bindgen_ty_50::IFLA_EVENT_BONDING_FAILOVER;
pub const IFLA_EVENT_NOTIFY_PEERS: _bindgen_ty_50 = _bindgen_ty_50::IFLA_EVENT_NOTIFY_PEERS;
pub const IFLA_EVENT_IGMP_RESEND: _bindgen_ty_50 = _bindgen_ty_50::IFLA_EVENT_IGMP_RESEND;
pub const IFLA_EVENT_BONDING_OPTIONS: _bindgen_ty_50 = _bindgen_ty_50::IFLA_EVENT_BONDING_OPTIONS;
pub const IFLA_TUN_UNSPEC: _bindgen_ty_51 = _bindgen_ty_51::IFLA_TUN_UNSPEC;
pub const IFLA_TUN_OWNER: _bindgen_ty_51 = _bindgen_ty_51::IFLA_TUN_OWNER;
pub const IFLA_TUN_GROUP: _bindgen_ty_51 = _bindgen_ty_51::IFLA_TUN_GROUP;
pub const IFLA_TUN_TYPE: _bindgen_ty_51 = _bindgen_ty_51::IFLA_TUN_TYPE;
pub const IFLA_TUN_PI: _bindgen_ty_51 = _bindgen_ty_51::IFLA_TUN_PI;
pub const IFLA_TUN_VNET_HDR: _bindgen_ty_51 = _bindgen_ty_51::IFLA_TUN_VNET_HDR;
pub const IFLA_TUN_PERSIST: _bindgen_ty_51 = _bindgen_ty_51::IFLA_TUN_PERSIST;
pub const IFLA_TUN_MULTI_QUEUE: _bindgen_ty_51 = _bindgen_ty_51::IFLA_TUN_MULTI_QUEUE;
pub const IFLA_TUN_NUM_QUEUES: _bindgen_ty_51 = _bindgen_ty_51::IFLA_TUN_NUM_QUEUES;
pub const IFLA_TUN_NUM_DISABLED_QUEUES: _bindgen_ty_51 = _bindgen_ty_51::IFLA_TUN_NUM_DISABLED_QUEUES;
pub const __IFLA_TUN_MAX: _bindgen_ty_51 = _bindgen_ty_51::__IFLA_TUN_MAX;
pub const IFLA_RMNET_UNSPEC: _bindgen_ty_52 = _bindgen_ty_52::IFLA_RMNET_UNSPEC;
pub const IFLA_RMNET_MUX_ID: _bindgen_ty_52 = _bindgen_ty_52::IFLA_RMNET_MUX_ID;
pub const IFLA_RMNET_FLAGS: _bindgen_ty_52 = _bindgen_ty_52::IFLA_RMNET_FLAGS;
pub const __IFLA_RMNET_MAX: _bindgen_ty_52 = _bindgen_ty_52::__IFLA_RMNET_MAX;
pub const IFLA_MCTP_UNSPEC: _bindgen_ty_53 = _bindgen_ty_53::IFLA_MCTP_UNSPEC;
pub const IFLA_MCTP_NET: _bindgen_ty_53 = _bindgen_ty_53::IFLA_MCTP_NET;
pub const IFLA_MCTP_PHYS_BINDING: _bindgen_ty_53 = _bindgen_ty_53::IFLA_MCTP_PHYS_BINDING;
pub const __IFLA_MCTP_MAX: _bindgen_ty_53 = _bindgen_ty_53::__IFLA_MCTP_MAX;
pub const IFLA_DSA_UNSPEC: _bindgen_ty_54 = _bindgen_ty_54::IFLA_DSA_UNSPEC;
pub const IFLA_DSA_CONDUIT: _bindgen_ty_54 = _bindgen_ty_54::IFLA_DSA_CONDUIT;
pub const IFLA_DSA_MASTER: _bindgen_ty_54 = _bindgen_ty_54::IFLA_DSA_CONDUIT;
pub const __IFLA_DSA_MAX: _bindgen_ty_54 = _bindgen_ty_54::__IFLA_DSA_MAX;
pub const IFA_UNSPEC: _bindgen_ty_55 = _bindgen_ty_55::IFA_UNSPEC;
pub const IFA_ADDRESS: _bindgen_ty_55 = _bindgen_ty_55::IFA_ADDRESS;
pub const IFA_LOCAL: _bindgen_ty_55 = _bindgen_ty_55::IFA_LOCAL;
pub const IFA_LABEL: _bindgen_ty_55 = _bindgen_ty_55::IFA_LABEL;
pub const IFA_BROADCAST: _bindgen_ty_55 = _bindgen_ty_55::IFA_BROADCAST;
pub const IFA_ANYCAST: _bindgen_ty_55 = _bindgen_ty_55::IFA_ANYCAST;
pub const IFA_CACHEINFO: _bindgen_ty_55 = _bindgen_ty_55::IFA_CACHEINFO;
pub const IFA_MULTICAST: _bindgen_ty_55 = _bindgen_ty_55::IFA_MULTICAST;
pub const IFA_FLAGS: _bindgen_ty_55 = _bindgen_ty_55::IFA_FLAGS;
pub const IFA_RT_PRIORITY: _bindgen_ty_55 = _bindgen_ty_55::IFA_RT_PRIORITY;
pub const IFA_TARGET_NETNSID: _bindgen_ty_55 = _bindgen_ty_55::IFA_TARGET_NETNSID;
pub const IFA_PROTO: _bindgen_ty_55 = _bindgen_ty_55::IFA_PROTO;
pub const __IFA_MAX: _bindgen_ty_55 = _bindgen_ty_55::__IFA_MAX;
pub const NDA_UNSPEC: _bindgen_ty_56 = _bindgen_ty_56::NDA_UNSPEC;
pub const NDA_DST: _bindgen_ty_56 = _bindgen_ty_56::NDA_DST;
pub const NDA_LLADDR: _bindgen_ty_56 = _bindgen_ty_56::NDA_LLADDR;
pub const NDA_CACHEINFO: _bindgen_ty_56 = _bindgen_ty_56::NDA_CACHEINFO;
pub const NDA_PROBES: _bindgen_ty_56 = _bindgen_ty_56::NDA_PROBES;
pub const NDA_VLAN: _bindgen_ty_56 = _bindgen_ty_56::NDA_VLAN;
pub const NDA_PORT: _bindgen_ty_56 = _bindgen_ty_56::NDA_PORT;
pub const NDA_VNI: _bindgen_ty_56 = _bindgen_ty_56::NDA_VNI;
pub const NDA_IFINDEX: _bindgen_ty_56 = _bindgen_ty_56::NDA_IFINDEX;
pub const NDA_MASTER: _bindgen_ty_56 = _bindgen_ty_56::NDA_MASTER;
pub const NDA_LINK_NETNSID: _bindgen_ty_56 = _bindgen_ty_56::NDA_LINK_NETNSID;
pub const NDA_SRC_VNI: _bindgen_ty_56 = _bindgen_ty_56::NDA_SRC_VNI;
pub const NDA_PROTOCOL: _bindgen_ty_56 = _bindgen_ty_56::NDA_PROTOCOL;
pub const NDA_NH_ID: _bindgen_ty_56 = _bindgen_ty_56::NDA_NH_ID;
pub const NDA_FDB_EXT_ATTRS: _bindgen_ty_56 = _bindgen_ty_56::NDA_FDB_EXT_ATTRS;
pub const NDA_FLAGS_EXT: _bindgen_ty_56 = _bindgen_ty_56::NDA_FLAGS_EXT;
pub const NDA_NDM_STATE_MASK: _bindgen_ty_56 = _bindgen_ty_56::NDA_NDM_STATE_MASK;
pub const NDA_NDM_FLAGS_MASK: _bindgen_ty_56 = _bindgen_ty_56::NDA_NDM_FLAGS_MASK;
pub const __NDA_MAX: _bindgen_ty_56 = _bindgen_ty_56::__NDA_MAX;
pub const NDTPA_UNSPEC: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_UNSPEC;
pub const NDTPA_IFINDEX: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_IFINDEX;
pub const NDTPA_REFCNT: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_REFCNT;
pub const NDTPA_REACHABLE_TIME: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_REACHABLE_TIME;
pub const NDTPA_BASE_REACHABLE_TIME: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_BASE_REACHABLE_TIME;
pub const NDTPA_RETRANS_TIME: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_RETRANS_TIME;
pub const NDTPA_GC_STALETIME: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_GC_STALETIME;
pub const NDTPA_DELAY_PROBE_TIME: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_DELAY_PROBE_TIME;
pub const NDTPA_QUEUE_LEN: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_QUEUE_LEN;
pub const NDTPA_APP_PROBES: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_APP_PROBES;
pub const NDTPA_UCAST_PROBES: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_UCAST_PROBES;
pub const NDTPA_MCAST_PROBES: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_MCAST_PROBES;
pub const NDTPA_ANYCAST_DELAY: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_ANYCAST_DELAY;
pub const NDTPA_PROXY_DELAY: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_PROXY_DELAY;
pub const NDTPA_PROXY_QLEN: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_PROXY_QLEN;
pub const NDTPA_LOCKTIME: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_LOCKTIME;
pub const NDTPA_QUEUE_LENBYTES: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_QUEUE_LENBYTES;
pub const NDTPA_MCAST_REPROBES: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_MCAST_REPROBES;
pub const NDTPA_PAD: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_PAD;
pub const NDTPA_INTERVAL_PROBE_TIME_MS: _bindgen_ty_57 = _bindgen_ty_57::NDTPA_INTERVAL_PROBE_TIME_MS;
pub const __NDTPA_MAX: _bindgen_ty_57 = _bindgen_ty_57::__NDTPA_MAX;
pub const NDTA_UNSPEC: _bindgen_ty_58 = _bindgen_ty_58::NDTA_UNSPEC;
pub const NDTA_NAME: _bindgen_ty_58 = _bindgen_ty_58::NDTA_NAME;
pub const NDTA_THRESH1: _bindgen_ty_58 = _bindgen_ty_58::NDTA_THRESH1;
pub const NDTA_THRESH2: _bindgen_ty_58 = _bindgen_ty_58::NDTA_THRESH2;
pub const NDTA_THRESH3: _bindgen_ty_58 = _bindgen_ty_58::NDTA_THRESH3;
pub const NDTA_CONFIG: _bindgen_ty_58 = _bindgen_ty_58::NDTA_CONFIG;
pub const NDTA_PARMS: _bindgen_ty_58 = _bindgen_ty_58::NDTA_PARMS;
pub const NDTA_STATS: _bindgen_ty_58 = _bindgen_ty_58::NDTA_STATS;
pub const NDTA_GC_INTERVAL: _bindgen_ty_58 = _bindgen_ty_58::NDTA_GC_INTERVAL;
pub const NDTA_PAD: _bindgen_ty_58 = _bindgen_ty_58::NDTA_PAD;
pub const __NDTA_MAX: _bindgen_ty_58 = _bindgen_ty_58::__NDTA_MAX;
pub const FDB_NOTIFY_BIT: _bindgen_ty_59 = _bindgen_ty_59::FDB_NOTIFY_BIT;
pub const FDB_NOTIFY_INACTIVE_BIT: _bindgen_ty_59 = _bindgen_ty_59::FDB_NOTIFY_INACTIVE_BIT;
pub const NFEA_UNSPEC: _bindgen_ty_60 = _bindgen_ty_60::NFEA_UNSPEC;
pub const NFEA_ACTIVITY_NOTIFY: _bindgen_ty_60 = _bindgen_ty_60::NFEA_ACTIVITY_NOTIFY;
pub const NFEA_DONT_REFRESH: _bindgen_ty_60 = _bindgen_ty_60::NFEA_DONT_REFRESH;
pub const __NFEA_MAX: _bindgen_ty_60 = _bindgen_ty_60::__NFEA_MAX;
pub const RTM_BASE: _bindgen_ty_61 = _bindgen_ty_61::RTM_BASE;
pub const RTM_NEWLINK: _bindgen_ty_61 = _bindgen_ty_61::RTM_BASE;
pub const RTM_DELLINK: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELLINK;
pub const RTM_GETLINK: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETLINK;
pub const RTM_SETLINK: _bindgen_ty_61 = _bindgen_ty_61::RTM_SETLINK;
pub const RTM_NEWADDR: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWADDR;
pub const RTM_DELADDR: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELADDR;
pub const RTM_GETADDR: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETADDR;
pub const RTM_NEWROUTE: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWROUTE;
pub const RTM_DELROUTE: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELROUTE;
pub const RTM_GETROUTE: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETROUTE;
pub const RTM_NEWNEIGH: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWNEIGH;
pub const RTM_DELNEIGH: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELNEIGH;
pub const RTM_GETNEIGH: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETNEIGH;
pub const RTM_NEWRULE: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWRULE;
pub const RTM_DELRULE: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELRULE;
pub const RTM_GETRULE: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETRULE;
pub const RTM_NEWQDISC: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWQDISC;
pub const RTM_DELQDISC: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELQDISC;
pub const RTM_GETQDISC: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETQDISC;
pub const RTM_NEWTCLASS: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWTCLASS;
pub const RTM_DELTCLASS: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELTCLASS;
pub const RTM_GETTCLASS: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETTCLASS;
pub const RTM_NEWTFILTER: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWTFILTER;
pub const RTM_DELTFILTER: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELTFILTER;
pub const RTM_GETTFILTER: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETTFILTER;
pub const RTM_NEWACTION: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWACTION;
pub const RTM_DELACTION: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELACTION;
pub const RTM_GETACTION: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETACTION;
pub const RTM_NEWPREFIX: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWPREFIX;
pub const RTM_GETMULTICAST: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETMULTICAST;
pub const RTM_GETANYCAST: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETANYCAST;
pub const RTM_NEWNEIGHTBL: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWNEIGHTBL;
pub const RTM_GETNEIGHTBL: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETNEIGHTBL;
pub const RTM_SETNEIGHTBL: _bindgen_ty_61 = _bindgen_ty_61::RTM_SETNEIGHTBL;
pub const RTM_NEWNDUSEROPT: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWNDUSEROPT;
pub const RTM_NEWADDRLABEL: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWADDRLABEL;
pub const RTM_DELADDRLABEL: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELADDRLABEL;
pub const RTM_GETADDRLABEL: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETADDRLABEL;
pub const RTM_GETDCB: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETDCB;
pub const RTM_SETDCB: _bindgen_ty_61 = _bindgen_ty_61::RTM_SETDCB;
pub const RTM_NEWNETCONF: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWNETCONF;
pub const RTM_DELNETCONF: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELNETCONF;
pub const RTM_GETNETCONF: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETNETCONF;
pub const RTM_NEWMDB: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWMDB;
pub const RTM_DELMDB: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELMDB;
pub const RTM_GETMDB: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETMDB;
pub const RTM_NEWNSID: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWNSID;
pub const RTM_DELNSID: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELNSID;
pub const RTM_GETNSID: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETNSID;
pub const RTM_NEWSTATS: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWSTATS;
pub const RTM_GETSTATS: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETSTATS;
pub const RTM_SETSTATS: _bindgen_ty_61 = _bindgen_ty_61::RTM_SETSTATS;
pub const RTM_NEWCACHEREPORT: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWCACHEREPORT;
pub const RTM_NEWCHAIN: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWCHAIN;
pub const RTM_DELCHAIN: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELCHAIN;
pub const RTM_GETCHAIN: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETCHAIN;
pub const RTM_NEWNEXTHOP: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWNEXTHOP;
pub const RTM_DELNEXTHOP: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELNEXTHOP;
pub const RTM_GETNEXTHOP: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETNEXTHOP;
pub const RTM_NEWLINKPROP: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWLINKPROP;
pub const RTM_DELLINKPROP: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELLINKPROP;
pub const RTM_GETLINKPROP: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETLINKPROP;
pub const RTM_NEWVLAN: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWVLAN;
pub const RTM_DELVLAN: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELVLAN;
pub const RTM_GETVLAN: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETVLAN;
pub const RTM_NEWNEXTHOPBUCKET: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWNEXTHOPBUCKET;
pub const RTM_DELNEXTHOPBUCKET: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELNEXTHOPBUCKET;
pub const RTM_GETNEXTHOPBUCKET: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETNEXTHOPBUCKET;
pub const RTM_NEWTUNNEL: _bindgen_ty_61 = _bindgen_ty_61::RTM_NEWTUNNEL;
pub const RTM_DELTUNNEL: _bindgen_ty_61 = _bindgen_ty_61::RTM_DELTUNNEL;
pub const RTM_GETTUNNEL: _bindgen_ty_61 = _bindgen_ty_61::RTM_GETTUNNEL;
pub const __RTM_MAX: _bindgen_ty_61 = _bindgen_ty_61::__RTM_MAX;
pub const RTN_UNSPEC: _bindgen_ty_62 = _bindgen_ty_62::RTN_UNSPEC;
pub const RTN_UNICAST: _bindgen_ty_62 = _bindgen_ty_62::RTN_UNICAST;
pub const RTN_LOCAL: _bindgen_ty_62 = _bindgen_ty_62::RTN_LOCAL;
pub const RTN_BROADCAST: _bindgen_ty_62 = _bindgen_ty_62::RTN_BROADCAST;
pub const RTN_ANYCAST: _bindgen_ty_62 = _bindgen_ty_62::RTN_ANYCAST;
pub const RTN_MULTICAST: _bindgen_ty_62 = _bindgen_ty_62::RTN_MULTICAST;
pub const RTN_BLACKHOLE: _bindgen_ty_62 = _bindgen_ty_62::RTN_BLACKHOLE;
pub const RTN_UNREACHABLE: _bindgen_ty_62 = _bindgen_ty_62::RTN_UNREACHABLE;
pub const RTN_PROHIBIT: _bindgen_ty_62 = _bindgen_ty_62::RTN_PROHIBIT;
pub const RTN_THROW: _bindgen_ty_62 = _bindgen_ty_62::RTN_THROW;
pub const RTN_NAT: _bindgen_ty_62 = _bindgen_ty_62::RTN_NAT;
pub const RTN_XRESOLVE: _bindgen_ty_62 = _bindgen_ty_62::RTN_XRESOLVE;
pub const __RTN_MAX: _bindgen_ty_62 = _bindgen_ty_62::__RTN_MAX;
pub const RTAX_UNSPEC: _bindgen_ty_63 = _bindgen_ty_63::RTAX_UNSPEC;
pub const RTAX_LOCK: _bindgen_ty_63 = _bindgen_ty_63::RTAX_LOCK;
pub const RTAX_MTU: _bindgen_ty_63 = _bindgen_ty_63::RTAX_MTU;
pub const RTAX_WINDOW: _bindgen_ty_63 = _bindgen_ty_63::RTAX_WINDOW;
pub const RTAX_RTT: _bindgen_ty_63 = _bindgen_ty_63::RTAX_RTT;
pub const RTAX_RTTVAR: _bindgen_ty_63 = _bindgen_ty_63::RTAX_RTTVAR;
pub const RTAX_SSTHRESH: _bindgen_ty_63 = _bindgen_ty_63::RTAX_SSTHRESH;
pub const RTAX_CWND: _bindgen_ty_63 = _bindgen_ty_63::RTAX_CWND;
pub const RTAX_ADVMSS: _bindgen_ty_63 = _bindgen_ty_63::RTAX_ADVMSS;
pub const RTAX_REORDERING: _bindgen_ty_63 = _bindgen_ty_63::RTAX_REORDERING;
pub const RTAX_HOPLIMIT: _bindgen_ty_63 = _bindgen_ty_63::RTAX_HOPLIMIT;
pub const RTAX_INITCWND: _bindgen_ty_63 = _bindgen_ty_63::RTAX_INITCWND;
pub const RTAX_FEATURES: _bindgen_ty_63 = _bindgen_ty_63::RTAX_FEATURES;
pub const RTAX_RTO_MIN: _bindgen_ty_63 = _bindgen_ty_63::RTAX_RTO_MIN;
pub const RTAX_INITRWND: _bindgen_ty_63 = _bindgen_ty_63::RTAX_INITRWND;
pub const RTAX_QUICKACK: _bindgen_ty_63 = _bindgen_ty_63::RTAX_QUICKACK;
pub const RTAX_CC_ALGO: _bindgen_ty_63 = _bindgen_ty_63::RTAX_CC_ALGO;
pub const RTAX_FASTOPEN_NO_COOKIE: _bindgen_ty_63 = _bindgen_ty_63::RTAX_FASTOPEN_NO_COOKIE;
pub const __RTAX_MAX: _bindgen_ty_63 = _bindgen_ty_63::__RTAX_MAX;
pub const PREFIX_UNSPEC: _bindgen_ty_64 = _bindgen_ty_64::PREFIX_UNSPEC;
pub const PREFIX_ADDRESS: _bindgen_ty_64 = _bindgen_ty_64::PREFIX_ADDRESS;
pub const PREFIX_CACHEINFO: _bindgen_ty_64 = _bindgen_ty_64::PREFIX_CACHEINFO;
pub const __PREFIX_MAX: _bindgen_ty_64 = _bindgen_ty_64::__PREFIX_MAX;
pub const TCA_UNSPEC: _bindgen_ty_65 = _bindgen_ty_65::TCA_UNSPEC;
pub const TCA_KIND: _bindgen_ty_65 = _bindgen_ty_65::TCA_KIND;
pub const TCA_OPTIONS: _bindgen_ty_65 = _bindgen_ty_65::TCA_OPTIONS;
pub const TCA_STATS: _bindgen_ty_65 = _bindgen_ty_65::TCA_STATS;
pub const TCA_XSTATS: _bindgen_ty_65 = _bindgen_ty_65::TCA_XSTATS;
pub const TCA_RATE: _bindgen_ty_65 = _bindgen_ty_65::TCA_RATE;
pub const TCA_FCNT: _bindgen_ty_65 = _bindgen_ty_65::TCA_FCNT;
pub const TCA_STATS2: _bindgen_ty_65 = _bindgen_ty_65::TCA_STATS2;
pub const TCA_STAB: _bindgen_ty_65 = _bindgen_ty_65::TCA_STAB;
pub const TCA_PAD: _bindgen_ty_65 = _bindgen_ty_65::TCA_PAD;
pub const TCA_DUMP_INVISIBLE: _bindgen_ty_65 = _bindgen_ty_65::TCA_DUMP_INVISIBLE;
pub const TCA_CHAIN: _bindgen_ty_65 = _bindgen_ty_65::TCA_CHAIN;
pub const TCA_HW_OFFLOAD: _bindgen_ty_65 = _bindgen_ty_65::TCA_HW_OFFLOAD;
pub const TCA_INGRESS_BLOCK: _bindgen_ty_65 = _bindgen_ty_65::TCA_INGRESS_BLOCK;
pub const TCA_EGRESS_BLOCK: _bindgen_ty_65 = _bindgen_ty_65::TCA_EGRESS_BLOCK;
pub const TCA_DUMP_FLAGS: _bindgen_ty_65 = _bindgen_ty_65::TCA_DUMP_FLAGS;
pub const TCA_EXT_WARN_MSG: _bindgen_ty_65 = _bindgen_ty_65::TCA_EXT_WARN_MSG;
pub const __TCA_MAX: _bindgen_ty_65 = _bindgen_ty_65::__TCA_MAX;
pub const NDUSEROPT_UNSPEC: _bindgen_ty_66 = _bindgen_ty_66::NDUSEROPT_UNSPEC;
pub const NDUSEROPT_SRCADDR: _bindgen_ty_66 = _bindgen_ty_66::NDUSEROPT_SRCADDR;
pub const __NDUSEROPT_MAX: _bindgen_ty_66 = _bindgen_ty_66::__NDUSEROPT_MAX;
pub const TCA_ROOT_UNSPEC: _bindgen_ty_67 = _bindgen_ty_67::TCA_ROOT_UNSPEC;
pub const TCA_ROOT_TAB: _bindgen_ty_67 = _bindgen_ty_67::TCA_ROOT_TAB;
pub const TCA_ROOT_FLAGS: _bindgen_ty_67 = _bindgen_ty_67::TCA_ROOT_FLAGS;
pub const TCA_ROOT_COUNT: _bindgen_ty_67 = _bindgen_ty_67::TCA_ROOT_COUNT;
pub const TCA_ROOT_TIME_DELTA: _bindgen_ty_67 = _bindgen_ty_67::TCA_ROOT_TIME_DELTA;
pub const TCA_ROOT_EXT_WARN_MSG: _bindgen_ty_67 = _bindgen_ty_67::TCA_ROOT_EXT_WARN_MSG;
pub const __TCA_ROOT_MAX: _bindgen_ty_67 = _bindgen_ty_67::__TCA_ROOT_MAX;
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum nlmsgerr_attrs {
NLMSGERR_ATTR_UNUSED = 0,
NLMSGERR_ATTR_MSG = 1,
NLMSGERR_ATTR_OFFS = 2,
NLMSGERR_ATTR_COOKIE = 3,
NLMSGERR_ATTR_POLICY = 4,
NLMSGERR_ATTR_MISS_TYPE = 5,
NLMSGERR_ATTR_MISS_NEST = 6,
__NLMSGERR_ATTR_MAX = 7,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum nl_mmap_status {
NL_MMAP_STATUS_UNUSED = 0,
NL_MMAP_STATUS_RESERVED = 1,
NL_MMAP_STATUS_VALID = 2,
NL_MMAP_STATUS_COPY = 3,
NL_MMAP_STATUS_SKIP = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_1 {
NETLINK_UNCONNECTED = 0,
NETLINK_CONNECTED = 1,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum netlink_attribute_type {
NL_ATTR_TYPE_INVALID = 0,
NL_ATTR_TYPE_FLAG = 1,
NL_ATTR_TYPE_U8 = 2,
NL_ATTR_TYPE_U16 = 3,
NL_ATTR_TYPE_U32 = 4,
NL_ATTR_TYPE_U64 = 5,
NL_ATTR_TYPE_S8 = 6,
NL_ATTR_TYPE_S16 = 7,
NL_ATTR_TYPE_S32 = 8,
NL_ATTR_TYPE_S64 = 9,
NL_ATTR_TYPE_BINARY = 10,
NL_ATTR_TYPE_STRING = 11,
NL_ATTR_TYPE_NUL_STRING = 12,
NL_ATTR_TYPE_NESTED = 13,
NL_ATTR_TYPE_NESTED_ARRAY = 14,
NL_ATTR_TYPE_BITFIELD32 = 15,
NL_ATTR_TYPE_SINT = 16,
NL_ATTR_TYPE_UINT = 17,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum netlink_policy_type_attr {
NL_POLICY_TYPE_ATTR_UNSPEC = 0,
NL_POLICY_TYPE_ATTR_TYPE = 1,
NL_POLICY_TYPE_ATTR_MIN_VALUE_S = 2,
NL_POLICY_TYPE_ATTR_MAX_VALUE_S = 3,
NL_POLICY_TYPE_ATTR_MIN_VALUE_U = 4,
NL_POLICY_TYPE_ATTR_MAX_VALUE_U = 5,
NL_POLICY_TYPE_ATTR_MIN_LENGTH = 6,
NL_POLICY_TYPE_ATTR_MAX_LENGTH = 7,
NL_POLICY_TYPE_ATTR_POLICY_IDX = 8,
NL_POLICY_TYPE_ATTR_POLICY_MAXTYPE = 9,
NL_POLICY_TYPE_ATTR_BITFIELD32_MASK = 10,
NL_POLICY_TYPE_ATTR_PAD = 11,
NL_POLICY_TYPE_ATTR_MASK = 12,
__NL_POLICY_TYPE_ATTR_MAX = 13,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_2 {
IFLA_UNSPEC = 0,
IFLA_ADDRESS = 1,
IFLA_BROADCAST = 2,
IFLA_IFNAME = 3,
IFLA_MTU = 4,
IFLA_LINK = 5,
IFLA_QDISC = 6,
IFLA_STATS = 7,
IFLA_COST = 8,
IFLA_PRIORITY = 9,
IFLA_MASTER = 10,
IFLA_WIRELESS = 11,
IFLA_PROTINFO = 12,
IFLA_TXQLEN = 13,
IFLA_MAP = 14,
IFLA_WEIGHT = 15,
IFLA_OPERSTATE = 16,
IFLA_LINKMODE = 17,
IFLA_LINKINFO = 18,
IFLA_NET_NS_PID = 19,
IFLA_IFALIAS = 20,
IFLA_NUM_VF = 21,
IFLA_VFINFO_LIST = 22,
IFLA_STATS64 = 23,
IFLA_VF_PORTS = 24,
IFLA_PORT_SELF = 25,
IFLA_AF_SPEC = 26,
IFLA_GROUP = 27,
IFLA_NET_NS_FD = 28,
IFLA_EXT_MASK = 29,
IFLA_PROMISCUITY = 30,
IFLA_NUM_TX_QUEUES = 31,
IFLA_NUM_RX_QUEUES = 32,
IFLA_CARRIER = 33,
IFLA_PHYS_PORT_ID = 34,
IFLA_CARRIER_CHANGES = 35,
IFLA_PHYS_SWITCH_ID = 36,
IFLA_LINK_NETNSID = 37,
IFLA_PHYS_PORT_NAME = 38,
IFLA_PROTO_DOWN = 39,
IFLA_GSO_MAX_SEGS = 40,
IFLA_GSO_MAX_SIZE = 41,
IFLA_PAD = 42,
IFLA_XDP = 43,
IFLA_EVENT = 44,
IFLA_NEW_NETNSID = 45,
IFLA_IF_NETNSID = 46,
IFLA_CARRIER_UP_COUNT = 47,
IFLA_CARRIER_DOWN_COUNT = 48,
IFLA_NEW_IFINDEX = 49,
IFLA_MIN_MTU = 50,
IFLA_MAX_MTU = 51,
IFLA_PROP_LIST = 52,
IFLA_ALT_IFNAME = 53,
IFLA_PERM_ADDRESS = 54,
IFLA_PROTO_DOWN_REASON = 55,
IFLA_PARENT_DEV_NAME = 56,
IFLA_PARENT_DEV_BUS_NAME = 57,
IFLA_GRO_MAX_SIZE = 58,
IFLA_TSO_MAX_SIZE = 59,
IFLA_TSO_MAX_SEGS = 60,
IFLA_ALLMULTI = 61,
IFLA_DEVLINK_PORT = 62,
IFLA_GSO_IPV4_MAX_SIZE = 63,
IFLA_GRO_IPV4_MAX_SIZE = 64,
IFLA_DPLL_PIN = 65,
IFLA_MAX_PACING_OFFLOAD_HORIZON = 66,
__IFLA_MAX = 67,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_3 {
IFLA_PROTO_DOWN_REASON_UNSPEC = 0,
IFLA_PROTO_DOWN_REASON_MASK = 1,
IFLA_PROTO_DOWN_REASON_VALUE = 2,
__IFLA_PROTO_DOWN_REASON_CNT = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_4 {
IFLA_INET_UNSPEC = 0,
IFLA_INET_CONF = 1,
__IFLA_INET_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_5 {
IFLA_INET6_UNSPEC = 0,
IFLA_INET6_FLAGS = 1,
IFLA_INET6_CONF = 2,
IFLA_INET6_STATS = 3,
IFLA_INET6_MCAST = 4,
IFLA_INET6_CACHEINFO = 5,
IFLA_INET6_ICMP6STATS = 6,
IFLA_INET6_TOKEN = 7,
IFLA_INET6_ADDR_GEN_MODE = 8,
IFLA_INET6_RA_MTU = 9,
__IFLA_INET6_MAX = 10,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum in6_addr_gen_mode {
IN6_ADDR_GEN_MODE_EUI64 = 0,
IN6_ADDR_GEN_MODE_NONE = 1,
IN6_ADDR_GEN_MODE_STABLE_PRIVACY = 2,
IN6_ADDR_GEN_MODE_RANDOM = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_6 {
IFLA_BR_UNSPEC = 0,
IFLA_BR_FORWARD_DELAY = 1,
IFLA_BR_HELLO_TIME = 2,
IFLA_BR_MAX_AGE = 3,
IFLA_BR_AGEING_TIME = 4,
IFLA_BR_STP_STATE = 5,
IFLA_BR_PRIORITY = 6,
IFLA_BR_VLAN_FILTERING = 7,
IFLA_BR_VLAN_PROTOCOL = 8,
IFLA_BR_GROUP_FWD_MASK = 9,
IFLA_BR_ROOT_ID = 10,
IFLA_BR_BRIDGE_ID = 11,
IFLA_BR_ROOT_PORT = 12,
IFLA_BR_ROOT_PATH_COST = 13,
IFLA_BR_TOPOLOGY_CHANGE = 14,
IFLA_BR_TOPOLOGY_CHANGE_DETECTED = 15,
IFLA_BR_HELLO_TIMER = 16,
IFLA_BR_TCN_TIMER = 17,
IFLA_BR_TOPOLOGY_CHANGE_TIMER = 18,
IFLA_BR_GC_TIMER = 19,
IFLA_BR_GROUP_ADDR = 20,
IFLA_BR_FDB_FLUSH = 21,
IFLA_BR_MCAST_ROUTER = 22,
IFLA_BR_MCAST_SNOOPING = 23,
IFLA_BR_MCAST_QUERY_USE_IFADDR = 24,
IFLA_BR_MCAST_QUERIER = 25,
IFLA_BR_MCAST_HASH_ELASTICITY = 26,
IFLA_BR_MCAST_HASH_MAX = 27,
IFLA_BR_MCAST_LAST_MEMBER_CNT = 28,
IFLA_BR_MCAST_STARTUP_QUERY_CNT = 29,
IFLA_BR_MCAST_LAST_MEMBER_INTVL = 30,
IFLA_BR_MCAST_MEMBERSHIP_INTVL = 31,
IFLA_BR_MCAST_QUERIER_INTVL = 32,
IFLA_BR_MCAST_QUERY_INTVL = 33,
IFLA_BR_MCAST_QUERY_RESPONSE_INTVL = 34,
IFLA_BR_MCAST_STARTUP_QUERY_INTVL = 35,
IFLA_BR_NF_CALL_IPTABLES = 36,
IFLA_BR_NF_CALL_IP6TABLES = 37,
IFLA_BR_NF_CALL_ARPTABLES = 38,
IFLA_BR_VLAN_DEFAULT_PVID = 39,
IFLA_BR_PAD = 40,
IFLA_BR_VLAN_STATS_ENABLED = 41,
IFLA_BR_MCAST_STATS_ENABLED = 42,
IFLA_BR_MCAST_IGMP_VERSION = 43,
IFLA_BR_MCAST_MLD_VERSION = 44,
IFLA_BR_VLAN_STATS_PER_PORT = 45,
IFLA_BR_MULTI_BOOLOPT = 46,
IFLA_BR_MCAST_QUERIER_STATE = 47,
IFLA_BR_FDB_N_LEARNED = 48,
IFLA_BR_FDB_MAX_LEARNED = 49,
__IFLA_BR_MAX = 50,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_7 {
BRIDGE_MODE_UNSPEC = 0,
BRIDGE_MODE_HAIRPIN = 1,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_8 {
IFLA_BRPORT_UNSPEC = 0,
IFLA_BRPORT_STATE = 1,
IFLA_BRPORT_PRIORITY = 2,
IFLA_BRPORT_COST = 3,
IFLA_BRPORT_MODE = 4,
IFLA_BRPORT_GUARD = 5,
IFLA_BRPORT_PROTECT = 6,
IFLA_BRPORT_FAST_LEAVE = 7,
IFLA_BRPORT_LEARNING = 8,
IFLA_BRPORT_UNICAST_FLOOD = 9,
IFLA_BRPORT_PROXYARP = 10,
IFLA_BRPORT_LEARNING_SYNC = 11,
IFLA_BRPORT_PROXYARP_WIFI = 12,
IFLA_BRPORT_ROOT_ID = 13,
IFLA_BRPORT_BRIDGE_ID = 14,
IFLA_BRPORT_DESIGNATED_PORT = 15,
IFLA_BRPORT_DESIGNATED_COST = 16,
IFLA_BRPORT_ID = 17,
IFLA_BRPORT_NO = 18,
IFLA_BRPORT_TOPOLOGY_CHANGE_ACK = 19,
IFLA_BRPORT_CONFIG_PENDING = 20,
IFLA_BRPORT_MESSAGE_AGE_TIMER = 21,
IFLA_BRPORT_FORWARD_DELAY_TIMER = 22,
IFLA_BRPORT_HOLD_TIMER = 23,
IFLA_BRPORT_FLUSH = 24,
IFLA_BRPORT_MULTICAST_ROUTER = 25,
IFLA_BRPORT_PAD = 26,
IFLA_BRPORT_MCAST_FLOOD = 27,
IFLA_BRPORT_MCAST_TO_UCAST = 28,
IFLA_BRPORT_VLAN_TUNNEL = 29,
IFLA_BRPORT_BCAST_FLOOD = 30,
IFLA_BRPORT_GROUP_FWD_MASK = 31,
IFLA_BRPORT_NEIGH_SUPPRESS = 32,
IFLA_BRPORT_ISOLATED = 33,
IFLA_BRPORT_BACKUP_PORT = 34,
IFLA_BRPORT_MRP_RING_OPEN = 35,
IFLA_BRPORT_MRP_IN_OPEN = 36,
IFLA_BRPORT_MCAST_EHT_HOSTS_LIMIT = 37,
IFLA_BRPORT_MCAST_EHT_HOSTS_CNT = 38,
IFLA_BRPORT_LOCKED = 39,
IFLA_BRPORT_MAB = 40,
IFLA_BRPORT_MCAST_N_GROUPS = 41,
IFLA_BRPORT_MCAST_MAX_GROUPS = 42,
IFLA_BRPORT_NEIGH_VLAN_SUPPRESS = 43,
IFLA_BRPORT_BACKUP_NHID = 44,
__IFLA_BRPORT_MAX = 45,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_9 {
IFLA_INFO_UNSPEC = 0,
IFLA_INFO_KIND = 1,
IFLA_INFO_DATA = 2,
IFLA_INFO_XSTATS = 3,
IFLA_INFO_SLAVE_KIND = 4,
IFLA_INFO_SLAVE_DATA = 5,
__IFLA_INFO_MAX = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_10 {
IFLA_VLAN_UNSPEC = 0,
IFLA_VLAN_ID = 1,
IFLA_VLAN_FLAGS = 2,
IFLA_VLAN_EGRESS_QOS = 3,
IFLA_VLAN_INGRESS_QOS = 4,
IFLA_VLAN_PROTOCOL = 5,
__IFLA_VLAN_MAX = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_11 {
IFLA_VLAN_QOS_UNSPEC = 0,
IFLA_VLAN_QOS_MAPPING = 1,
__IFLA_VLAN_QOS_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_12 {
IFLA_MACVLAN_UNSPEC = 0,
IFLA_MACVLAN_MODE = 1,
IFLA_MACVLAN_FLAGS = 2,
IFLA_MACVLAN_MACADDR_MODE = 3,
IFLA_MACVLAN_MACADDR = 4,
IFLA_MACVLAN_MACADDR_DATA = 5,
IFLA_MACVLAN_MACADDR_COUNT = 6,
IFLA_MACVLAN_BC_QUEUE_LEN = 7,
IFLA_MACVLAN_BC_QUEUE_LEN_USED = 8,
IFLA_MACVLAN_BC_CUTOFF = 9,
__IFLA_MACVLAN_MAX = 10,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum macvlan_mode {
MACVLAN_MODE_PRIVATE = 1,
MACVLAN_MODE_VEPA = 2,
MACVLAN_MODE_BRIDGE = 4,
MACVLAN_MODE_PASSTHRU = 8,
MACVLAN_MODE_SOURCE = 16,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum macvlan_macaddr_mode {
MACVLAN_MACADDR_ADD = 0,
MACVLAN_MACADDR_DEL = 1,
MACVLAN_MACADDR_FLUSH = 2,
MACVLAN_MACADDR_SET = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_13 {
IFLA_VRF_UNSPEC = 0,
IFLA_VRF_TABLE = 1,
__IFLA_VRF_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_14 {
IFLA_VRF_PORT_UNSPEC = 0,
IFLA_VRF_PORT_TABLE = 1,
__IFLA_VRF_PORT_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_15 {
IFLA_MACSEC_UNSPEC = 0,
IFLA_MACSEC_SCI = 1,
IFLA_MACSEC_PORT = 2,
IFLA_MACSEC_ICV_LEN = 3,
IFLA_MACSEC_CIPHER_SUITE = 4,
IFLA_MACSEC_WINDOW = 5,
IFLA_MACSEC_ENCODING_SA = 6,
IFLA_MACSEC_ENCRYPT = 7,
IFLA_MACSEC_PROTECT = 8,
IFLA_MACSEC_INC_SCI = 9,
IFLA_MACSEC_ES = 10,
IFLA_MACSEC_SCB = 11,
IFLA_MACSEC_REPLAY_PROTECT = 12,
IFLA_MACSEC_VALIDATION = 13,
IFLA_MACSEC_PAD = 14,
IFLA_MACSEC_OFFLOAD = 15,
__IFLA_MACSEC_MAX = 16,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_16 {
IFLA_XFRM_UNSPEC = 0,
IFLA_XFRM_LINK = 1,
IFLA_XFRM_IF_ID = 2,
IFLA_XFRM_COLLECT_METADATA = 3,
__IFLA_XFRM_MAX = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum macsec_validation_type {
MACSEC_VALIDATE_DISABLED = 0,
MACSEC_VALIDATE_CHECK = 1,
MACSEC_VALIDATE_STRICT = 2,
__MACSEC_VALIDATE_END = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum macsec_offload {
MACSEC_OFFLOAD_OFF = 0,
MACSEC_OFFLOAD_PHY = 1,
MACSEC_OFFLOAD_MAC = 2,
__MACSEC_OFFLOAD_END = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_17 {
IFLA_IPVLAN_UNSPEC = 0,
IFLA_IPVLAN_MODE = 1,
IFLA_IPVLAN_FLAGS = 2,
__IFLA_IPVLAN_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum ipvlan_mode {
IPVLAN_MODE_L2 = 0,
IPVLAN_MODE_L3 = 1,
IPVLAN_MODE_L3S = 2,
IPVLAN_MODE_MAX = 3,
}
#[repr(i32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum netkit_action {
NETKIT_NEXT = -1,
NETKIT_PASS = 0,
NETKIT_DROP = 2,
NETKIT_REDIRECT = 7,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum netkit_mode {
NETKIT_L2 = 0,
NETKIT_L3 = 1,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum netkit_scrub {
NETKIT_SCRUB_NONE = 0,
NETKIT_SCRUB_DEFAULT = 1,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_18 {
IFLA_NETKIT_UNSPEC = 0,
IFLA_NETKIT_PEER_INFO = 1,
IFLA_NETKIT_PRIMARY = 2,
IFLA_NETKIT_POLICY = 3,
IFLA_NETKIT_PEER_POLICY = 4,
IFLA_NETKIT_MODE = 5,
IFLA_NETKIT_SCRUB = 6,
IFLA_NETKIT_PEER_SCRUB = 7,
__IFLA_NETKIT_MAX = 8,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_19 {
VNIFILTER_ENTRY_STATS_UNSPEC = 0,
VNIFILTER_ENTRY_STATS_RX_BYTES = 1,
VNIFILTER_ENTRY_STATS_RX_PKTS = 2,
VNIFILTER_ENTRY_STATS_RX_DROPS = 3,
VNIFILTER_ENTRY_STATS_RX_ERRORS = 4,
VNIFILTER_ENTRY_STATS_TX_BYTES = 5,
VNIFILTER_ENTRY_STATS_TX_PKTS = 6,
VNIFILTER_ENTRY_STATS_TX_DROPS = 7,
VNIFILTER_ENTRY_STATS_TX_ERRORS = 8,
VNIFILTER_ENTRY_STATS_PAD = 9,
__VNIFILTER_ENTRY_STATS_MAX = 10,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_20 {
VXLAN_VNIFILTER_ENTRY_UNSPEC = 0,
VXLAN_VNIFILTER_ENTRY_START = 1,
VXLAN_VNIFILTER_ENTRY_END = 2,
VXLAN_VNIFILTER_ENTRY_GROUP = 3,
VXLAN_VNIFILTER_ENTRY_GROUP6 = 4,
VXLAN_VNIFILTER_ENTRY_STATS = 5,
__VXLAN_VNIFILTER_ENTRY_MAX = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_21 {
VXLAN_VNIFILTER_UNSPEC = 0,
VXLAN_VNIFILTER_ENTRY = 1,
__VXLAN_VNIFILTER_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_22 {
IFLA_VXLAN_UNSPEC = 0,
IFLA_VXLAN_ID = 1,
IFLA_VXLAN_GROUP = 2,
IFLA_VXLAN_LINK = 3,
IFLA_VXLAN_LOCAL = 4,
IFLA_VXLAN_TTL = 5,
IFLA_VXLAN_TOS = 6,
IFLA_VXLAN_LEARNING = 7,
IFLA_VXLAN_AGEING = 8,
IFLA_VXLAN_LIMIT = 9,
IFLA_VXLAN_PORT_RANGE = 10,
IFLA_VXLAN_PROXY = 11,
IFLA_VXLAN_RSC = 12,
IFLA_VXLAN_L2MISS = 13,
IFLA_VXLAN_L3MISS = 14,
IFLA_VXLAN_PORT = 15,
IFLA_VXLAN_GROUP6 = 16,
IFLA_VXLAN_LOCAL6 = 17,
IFLA_VXLAN_UDP_CSUM = 18,
IFLA_VXLAN_UDP_ZERO_CSUM6_TX = 19,
IFLA_VXLAN_UDP_ZERO_CSUM6_RX = 20,
IFLA_VXLAN_REMCSUM_TX = 21,
IFLA_VXLAN_REMCSUM_RX = 22,
IFLA_VXLAN_GBP = 23,
IFLA_VXLAN_REMCSUM_NOPARTIAL = 24,
IFLA_VXLAN_COLLECT_METADATA = 25,
IFLA_VXLAN_LABEL = 26,
IFLA_VXLAN_GPE = 27,
IFLA_VXLAN_TTL_INHERIT = 28,
IFLA_VXLAN_DF = 29,
IFLA_VXLAN_VNIFILTER = 30,
IFLA_VXLAN_LOCALBYPASS = 31,
IFLA_VXLAN_LABEL_POLICY = 32,
__IFLA_VXLAN_MAX = 33,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum ifla_vxlan_df {
VXLAN_DF_UNSET = 0,
VXLAN_DF_SET = 1,
VXLAN_DF_INHERIT = 2,
__VXLAN_DF_END = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum ifla_vxlan_label_policy {
VXLAN_LABEL_FIXED = 0,
VXLAN_LABEL_INHERIT = 1,
__VXLAN_LABEL_END = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_23 {
IFLA_GENEVE_UNSPEC = 0,
IFLA_GENEVE_ID = 1,
IFLA_GENEVE_REMOTE = 2,
IFLA_GENEVE_TTL = 3,
IFLA_GENEVE_TOS = 4,
IFLA_GENEVE_PORT = 5,
IFLA_GENEVE_COLLECT_METADATA = 6,
IFLA_GENEVE_REMOTE6 = 7,
IFLA_GENEVE_UDP_CSUM = 8,
IFLA_GENEVE_UDP_ZERO_CSUM6_TX = 9,
IFLA_GENEVE_UDP_ZERO_CSUM6_RX = 10,
IFLA_GENEVE_LABEL = 11,
IFLA_GENEVE_TTL_INHERIT = 12,
IFLA_GENEVE_DF = 13,
IFLA_GENEVE_INNER_PROTO_INHERIT = 14,
__IFLA_GENEVE_MAX = 15,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum ifla_geneve_df {
GENEVE_DF_UNSET = 0,
GENEVE_DF_SET = 1,
GENEVE_DF_INHERIT = 2,
__GENEVE_DF_END = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_24 {
IFLA_BAREUDP_UNSPEC = 0,
IFLA_BAREUDP_PORT = 1,
IFLA_BAREUDP_ETHERTYPE = 2,
IFLA_BAREUDP_SRCPORT_MIN = 3,
IFLA_BAREUDP_MULTIPROTO_MODE = 4,
__IFLA_BAREUDP_MAX = 5,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_25 {
IFLA_PPP_UNSPEC = 0,
IFLA_PPP_DEV_FD = 1,
__IFLA_PPP_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum ifla_gtp_role {
GTP_ROLE_GGSN = 0,
GTP_ROLE_SGSN = 1,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_26 {
IFLA_GTP_UNSPEC = 0,
IFLA_GTP_FD0 = 1,
IFLA_GTP_FD1 = 2,
IFLA_GTP_PDP_HASHSIZE = 3,
IFLA_GTP_ROLE = 4,
IFLA_GTP_CREATE_SOCKETS = 5,
IFLA_GTP_RESTART_COUNT = 6,
IFLA_GTP_LOCAL = 7,
IFLA_GTP_LOCAL6 = 8,
__IFLA_GTP_MAX = 9,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_27 {
IFLA_BOND_UNSPEC = 0,
IFLA_BOND_MODE = 1,
IFLA_BOND_ACTIVE_SLAVE = 2,
IFLA_BOND_MIIMON = 3,
IFLA_BOND_UPDELAY = 4,
IFLA_BOND_DOWNDELAY = 5,
IFLA_BOND_USE_CARRIER = 6,
IFLA_BOND_ARP_INTERVAL = 7,
IFLA_BOND_ARP_IP_TARGET = 8,
IFLA_BOND_ARP_VALIDATE = 9,
IFLA_BOND_ARP_ALL_TARGETS = 10,
IFLA_BOND_PRIMARY = 11,
IFLA_BOND_PRIMARY_RESELECT = 12,
IFLA_BOND_FAIL_OVER_MAC = 13,
IFLA_BOND_XMIT_HASH_POLICY = 14,
IFLA_BOND_RESEND_IGMP = 15,
IFLA_BOND_NUM_PEER_NOTIF = 16,
IFLA_BOND_ALL_SLAVES_ACTIVE = 17,
IFLA_BOND_MIN_LINKS = 18,
IFLA_BOND_LP_INTERVAL = 19,
IFLA_BOND_PACKETS_PER_SLAVE = 20,
IFLA_BOND_AD_LACP_RATE = 21,
IFLA_BOND_AD_SELECT = 22,
IFLA_BOND_AD_INFO = 23,
IFLA_BOND_AD_ACTOR_SYS_PRIO = 24,
IFLA_BOND_AD_USER_PORT_KEY = 25,
IFLA_BOND_AD_ACTOR_SYSTEM = 26,
IFLA_BOND_TLB_DYNAMIC_LB = 27,
IFLA_BOND_PEER_NOTIF_DELAY = 28,
IFLA_BOND_AD_LACP_ACTIVE = 29,
IFLA_BOND_MISSED_MAX = 30,
IFLA_BOND_NS_IP6_TARGET = 31,
IFLA_BOND_COUPLED_CONTROL = 32,
__IFLA_BOND_MAX = 33,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_28 {
IFLA_BOND_AD_INFO_UNSPEC = 0,
IFLA_BOND_AD_INFO_AGGREGATOR = 1,
IFLA_BOND_AD_INFO_NUM_PORTS = 2,
IFLA_BOND_AD_INFO_ACTOR_KEY = 3,
IFLA_BOND_AD_INFO_PARTNER_KEY = 4,
IFLA_BOND_AD_INFO_PARTNER_MAC = 5,
__IFLA_BOND_AD_INFO_MAX = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_29 {
IFLA_BOND_SLAVE_UNSPEC = 0,
IFLA_BOND_SLAVE_STATE = 1,
IFLA_BOND_SLAVE_MII_STATUS = 2,
IFLA_BOND_SLAVE_LINK_FAILURE_COUNT = 3,
IFLA_BOND_SLAVE_PERM_HWADDR = 4,
IFLA_BOND_SLAVE_QUEUE_ID = 5,
IFLA_BOND_SLAVE_AD_AGGREGATOR_ID = 6,
IFLA_BOND_SLAVE_AD_ACTOR_OPER_PORT_STATE = 7,
IFLA_BOND_SLAVE_AD_PARTNER_OPER_PORT_STATE = 8,
IFLA_BOND_SLAVE_PRIO = 9,
__IFLA_BOND_SLAVE_MAX = 10,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_30 {
IFLA_VF_INFO_UNSPEC = 0,
IFLA_VF_INFO = 1,
__IFLA_VF_INFO_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_31 {
IFLA_VF_UNSPEC = 0,
IFLA_VF_MAC = 1,
IFLA_VF_VLAN = 2,
IFLA_VF_TX_RATE = 3,
IFLA_VF_SPOOFCHK = 4,
IFLA_VF_LINK_STATE = 5,
IFLA_VF_RATE = 6,
IFLA_VF_RSS_QUERY_EN = 7,
IFLA_VF_STATS = 8,
IFLA_VF_TRUST = 9,
IFLA_VF_IB_NODE_GUID = 10,
IFLA_VF_IB_PORT_GUID = 11,
IFLA_VF_VLAN_LIST = 12,
IFLA_VF_BROADCAST = 13,
__IFLA_VF_MAX = 14,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_32 {
IFLA_VF_VLAN_INFO_UNSPEC = 0,
IFLA_VF_VLAN_INFO = 1,
__IFLA_VF_VLAN_INFO_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_33 {
IFLA_VF_LINK_STATE_AUTO = 0,
IFLA_VF_LINK_STATE_ENABLE = 1,
IFLA_VF_LINK_STATE_DISABLE = 2,
__IFLA_VF_LINK_STATE_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_34 {
IFLA_VF_STATS_RX_PACKETS = 0,
IFLA_VF_STATS_TX_PACKETS = 1,
IFLA_VF_STATS_RX_BYTES = 2,
IFLA_VF_STATS_TX_BYTES = 3,
IFLA_VF_STATS_BROADCAST = 4,
IFLA_VF_STATS_MULTICAST = 5,
IFLA_VF_STATS_PAD = 6,
IFLA_VF_STATS_RX_DROPPED = 7,
IFLA_VF_STATS_TX_DROPPED = 8,
__IFLA_VF_STATS_MAX = 9,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_35 {
IFLA_VF_PORT_UNSPEC = 0,
IFLA_VF_PORT = 1,
__IFLA_VF_PORT_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_36 {
IFLA_PORT_UNSPEC = 0,
IFLA_PORT_VF = 1,
IFLA_PORT_PROFILE = 2,
IFLA_PORT_VSI_TYPE = 3,
IFLA_PORT_INSTANCE_UUID = 4,
IFLA_PORT_HOST_UUID = 5,
IFLA_PORT_REQUEST = 6,
IFLA_PORT_RESPONSE = 7,
__IFLA_PORT_MAX = 8,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_37 {
PORT_REQUEST_PREASSOCIATE = 0,
PORT_REQUEST_PREASSOCIATE_RR = 1,
PORT_REQUEST_ASSOCIATE = 2,
PORT_REQUEST_DISASSOCIATE = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_38 {
PORT_VDP_RESPONSE_SUCCESS = 0,
PORT_VDP_RESPONSE_INVALID_FORMAT = 1,
PORT_VDP_RESPONSE_INSUFFICIENT_RESOURCES = 2,
PORT_VDP_RESPONSE_UNUSED_VTID = 3,
PORT_VDP_RESPONSE_VTID_VIOLATION = 4,
PORT_VDP_RESPONSE_VTID_VERSION_VIOALTION = 5,
PORT_VDP_RESPONSE_OUT_OF_SYNC = 6,
PORT_PROFILE_RESPONSE_SUCCESS = 256,
PORT_PROFILE_RESPONSE_INPROGRESS = 257,
PORT_PROFILE_RESPONSE_INVALID = 258,
PORT_PROFILE_RESPONSE_BADSTATE = 259,
PORT_PROFILE_RESPONSE_INSUFFICIENT_RESOURCES = 260,
PORT_PROFILE_RESPONSE_ERROR = 261,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_39 {
IFLA_IPOIB_UNSPEC = 0,
IFLA_IPOIB_PKEY = 1,
IFLA_IPOIB_MODE = 2,
IFLA_IPOIB_UMCAST = 3,
__IFLA_IPOIB_MAX = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_40 {
IPOIB_MODE_DATAGRAM = 0,
IPOIB_MODE_CONNECTED = 1,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_41 {
HSR_PROTOCOL_HSR = 0,
HSR_PROTOCOL_PRP = 1,
HSR_PROTOCOL_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_42 {
IFLA_HSR_UNSPEC = 0,
IFLA_HSR_SLAVE1 = 1,
IFLA_HSR_SLAVE2 = 2,
IFLA_HSR_MULTICAST_SPEC = 3,
IFLA_HSR_SUPERVISION_ADDR = 4,
IFLA_HSR_SEQ_NR = 5,
IFLA_HSR_VERSION = 6,
IFLA_HSR_PROTOCOL = 7,
IFLA_HSR_INTERLINK = 8,
__IFLA_HSR_MAX = 9,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_43 {
IFLA_STATS_UNSPEC = 0,
IFLA_STATS_LINK_64 = 1,
IFLA_STATS_LINK_XSTATS = 2,
IFLA_STATS_LINK_XSTATS_SLAVE = 3,
IFLA_STATS_LINK_OFFLOAD_XSTATS = 4,
IFLA_STATS_AF_SPEC = 5,
__IFLA_STATS_MAX = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_44 {
IFLA_STATS_GETSET_UNSPEC = 0,
IFLA_STATS_GET_FILTERS = 1,
IFLA_STATS_SET_OFFLOAD_XSTATS_L3_STATS = 2,
__IFLA_STATS_GETSET_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_45 {
LINK_XSTATS_TYPE_UNSPEC = 0,
LINK_XSTATS_TYPE_BRIDGE = 1,
LINK_XSTATS_TYPE_BOND = 2,
__LINK_XSTATS_TYPE_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_46 {
IFLA_OFFLOAD_XSTATS_UNSPEC = 0,
IFLA_OFFLOAD_XSTATS_CPU_HIT = 1,
IFLA_OFFLOAD_XSTATS_HW_S_INFO = 2,
IFLA_OFFLOAD_XSTATS_L3_STATS = 3,
__IFLA_OFFLOAD_XSTATS_MAX = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_47 {
IFLA_OFFLOAD_XSTATS_HW_S_INFO_UNSPEC = 0,
IFLA_OFFLOAD_XSTATS_HW_S_INFO_REQUEST = 1,
IFLA_OFFLOAD_XSTATS_HW_S_INFO_USED = 2,
__IFLA_OFFLOAD_XSTATS_HW_S_INFO_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_48 {
XDP_ATTACHED_NONE = 0,
XDP_ATTACHED_DRV = 1,
XDP_ATTACHED_SKB = 2,
XDP_ATTACHED_HW = 3,
XDP_ATTACHED_MULTI = 4,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_49 {
IFLA_XDP_UNSPEC = 0,
IFLA_XDP_FD = 1,
IFLA_XDP_ATTACHED = 2,
IFLA_XDP_FLAGS = 3,
IFLA_XDP_PROG_ID = 4,
IFLA_XDP_DRV_PROG_ID = 5,
IFLA_XDP_SKB_PROG_ID = 6,
IFLA_XDP_HW_PROG_ID = 7,
IFLA_XDP_EXPECTED_FD = 8,
__IFLA_XDP_MAX = 9,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_50 {
IFLA_EVENT_NONE = 0,
IFLA_EVENT_REBOOT = 1,
IFLA_EVENT_FEATURES = 2,
IFLA_EVENT_BONDING_FAILOVER = 3,
IFLA_EVENT_NOTIFY_PEERS = 4,
IFLA_EVENT_IGMP_RESEND = 5,
IFLA_EVENT_BONDING_OPTIONS = 6,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_51 {
IFLA_TUN_UNSPEC = 0,
IFLA_TUN_OWNER = 1,
IFLA_TUN_GROUP = 2,
IFLA_TUN_TYPE = 3,
IFLA_TUN_PI = 4,
IFLA_TUN_VNET_HDR = 5,
IFLA_TUN_PERSIST = 6,
IFLA_TUN_MULTI_QUEUE = 7,
IFLA_TUN_NUM_QUEUES = 8,
IFLA_TUN_NUM_DISABLED_QUEUES = 9,
__IFLA_TUN_MAX = 10,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_52 {
IFLA_RMNET_UNSPEC = 0,
IFLA_RMNET_MUX_ID = 1,
IFLA_RMNET_FLAGS = 2,
__IFLA_RMNET_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_53 {
IFLA_MCTP_UNSPEC = 0,
IFLA_MCTP_NET = 1,
IFLA_MCTP_PHYS_BINDING = 2,
__IFLA_MCTP_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_54 {
IFLA_DSA_UNSPEC = 0,
IFLA_DSA_CONDUIT = 1,
__IFLA_DSA_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_55 {
IFA_UNSPEC = 0,
IFA_ADDRESS = 1,
IFA_LOCAL = 2,
IFA_LABEL = 3,
IFA_BROADCAST = 4,
IFA_ANYCAST = 5,
IFA_CACHEINFO = 6,
IFA_MULTICAST = 7,
IFA_FLAGS = 8,
IFA_RT_PRIORITY = 9,
IFA_TARGET_NETNSID = 10,
IFA_PROTO = 11,
__IFA_MAX = 12,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_56 {
NDA_UNSPEC = 0,
NDA_DST = 1,
NDA_LLADDR = 2,
NDA_CACHEINFO = 3,
NDA_PROBES = 4,
NDA_VLAN = 5,
NDA_PORT = 6,
NDA_VNI = 7,
NDA_IFINDEX = 8,
NDA_MASTER = 9,
NDA_LINK_NETNSID = 10,
NDA_SRC_VNI = 11,
NDA_PROTOCOL = 12,
NDA_NH_ID = 13,
NDA_FDB_EXT_ATTRS = 14,
NDA_FLAGS_EXT = 15,
NDA_NDM_STATE_MASK = 16,
NDA_NDM_FLAGS_MASK = 17,
__NDA_MAX = 18,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_57 {
NDTPA_UNSPEC = 0,
NDTPA_IFINDEX = 1,
NDTPA_REFCNT = 2,
NDTPA_REACHABLE_TIME = 3,
NDTPA_BASE_REACHABLE_TIME = 4,
NDTPA_RETRANS_TIME = 5,
NDTPA_GC_STALETIME = 6,
NDTPA_DELAY_PROBE_TIME = 7,
NDTPA_QUEUE_LEN = 8,
NDTPA_APP_PROBES = 9,
NDTPA_UCAST_PROBES = 10,
NDTPA_MCAST_PROBES = 11,
NDTPA_ANYCAST_DELAY = 12,
NDTPA_PROXY_DELAY = 13,
NDTPA_PROXY_QLEN = 14,
NDTPA_LOCKTIME = 15,
NDTPA_QUEUE_LENBYTES = 16,
NDTPA_MCAST_REPROBES = 17,
NDTPA_PAD = 18,
NDTPA_INTERVAL_PROBE_TIME_MS = 19,
__NDTPA_MAX = 20,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_58 {
NDTA_UNSPEC = 0,
NDTA_NAME = 1,
NDTA_THRESH1 = 2,
NDTA_THRESH2 = 3,
NDTA_THRESH3 = 4,
NDTA_CONFIG = 5,
NDTA_PARMS = 6,
NDTA_STATS = 7,
NDTA_GC_INTERVAL = 8,
NDTA_PAD = 9,
__NDTA_MAX = 10,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_59 {
FDB_NOTIFY_BIT = 1,
FDB_NOTIFY_INACTIVE_BIT = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_60 {
NFEA_UNSPEC = 0,
NFEA_ACTIVITY_NOTIFY = 1,
NFEA_DONT_REFRESH = 2,
__NFEA_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_61 {
RTM_BASE = 16,
RTM_DELLINK = 17,
RTM_GETLINK = 18,
RTM_SETLINK = 19,
RTM_NEWADDR = 20,
RTM_DELADDR = 21,
RTM_GETADDR = 22,
RTM_NEWROUTE = 24,
RTM_DELROUTE = 25,
RTM_GETROUTE = 26,
RTM_NEWNEIGH = 28,
RTM_DELNEIGH = 29,
RTM_GETNEIGH = 30,
RTM_NEWRULE = 32,
RTM_DELRULE = 33,
RTM_GETRULE = 34,
RTM_NEWQDISC = 36,
RTM_DELQDISC = 37,
RTM_GETQDISC = 38,
RTM_NEWTCLASS = 40,
RTM_DELTCLASS = 41,
RTM_GETTCLASS = 42,
RTM_NEWTFILTER = 44,
RTM_DELTFILTER = 45,
RTM_GETTFILTER = 46,
RTM_NEWACTION = 48,
RTM_DELACTION = 49,
RTM_GETACTION = 50,
RTM_NEWPREFIX = 52,
RTM_GETMULTICAST = 58,
RTM_GETANYCAST = 62,
RTM_NEWNEIGHTBL = 64,
RTM_GETNEIGHTBL = 66,
RTM_SETNEIGHTBL = 67,
RTM_NEWNDUSEROPT = 68,
RTM_NEWADDRLABEL = 72,
RTM_DELADDRLABEL = 73,
RTM_GETADDRLABEL = 74,
RTM_GETDCB = 78,
RTM_SETDCB = 79,
RTM_NEWNETCONF = 80,
RTM_DELNETCONF = 81,
RTM_GETNETCONF = 82,
RTM_NEWMDB = 84,
RTM_DELMDB = 85,
RTM_GETMDB = 86,
RTM_NEWNSID = 88,
RTM_DELNSID = 89,
RTM_GETNSID = 90,
RTM_NEWSTATS = 92,
RTM_GETSTATS = 94,
RTM_SETSTATS = 95,
RTM_NEWCACHEREPORT = 96,
RTM_NEWCHAIN = 100,
RTM_DELCHAIN = 101,
RTM_GETCHAIN = 102,
RTM_NEWNEXTHOP = 104,
RTM_DELNEXTHOP = 105,
RTM_GETNEXTHOP = 106,
RTM_NEWLINKPROP = 108,
RTM_DELLINKPROP = 109,
RTM_GETLINKPROP = 110,
RTM_NEWVLAN = 112,
RTM_DELVLAN = 113,
RTM_GETVLAN = 114,
RTM_NEWNEXTHOPBUCKET = 116,
RTM_DELNEXTHOPBUCKET = 117,
RTM_GETNEXTHOPBUCKET = 118,
RTM_NEWTUNNEL = 120,
RTM_DELTUNNEL = 121,
RTM_GETTUNNEL = 122,
__RTM_MAX = 123,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_62 {
RTN_UNSPEC = 0,
RTN_UNICAST = 1,
RTN_LOCAL = 2,
RTN_BROADCAST = 3,
RTN_ANYCAST = 4,
RTN_MULTICAST = 5,
RTN_BLACKHOLE = 6,
RTN_UNREACHABLE = 7,
RTN_PROHIBIT = 8,
RTN_THROW = 9,
RTN_NAT = 10,
RTN_XRESOLVE = 11,
__RTN_MAX = 12,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum rt_scope_t {
RT_SCOPE_UNIVERSE = 0,
RT_SCOPE_SITE = 200,
RT_SCOPE_LINK = 253,
RT_SCOPE_HOST = 254,
RT_SCOPE_NOWHERE = 255,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum rt_class_t {
RT_TABLE_UNSPEC = 0,
RT_TABLE_COMPAT = 252,
RT_TABLE_DEFAULT = 253,
RT_TABLE_MAIN = 254,
RT_TABLE_LOCAL = 255,
RT_TABLE_MAX = 4294967295,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum rtattr_type_t {
RTA_UNSPEC = 0,
RTA_DST = 1,
RTA_SRC = 2,
RTA_IIF = 3,
RTA_OIF = 4,
RTA_GATEWAY = 5,
RTA_PRIORITY = 6,
RTA_PREFSRC = 7,
RTA_METRICS = 8,
RTA_MULTIPATH = 9,
RTA_PROTOINFO = 10,
RTA_FLOW = 11,
RTA_CACHEINFO = 12,
RTA_SESSION = 13,
RTA_MP_ALGO = 14,
RTA_TABLE = 15,
RTA_MARK = 16,
RTA_MFC_STATS = 17,
RTA_VIA = 18,
RTA_NEWDST = 19,
RTA_PREF = 20,
RTA_ENCAP_TYPE = 21,
RTA_ENCAP = 22,
RTA_EXPIRES = 23,
RTA_PAD = 24,
RTA_UID = 25,
RTA_TTL_PROPAGATE = 26,
RTA_IP_PROTO = 27,
RTA_SPORT = 28,
RTA_DPORT = 29,
RTA_NH_ID = 30,
__RTA_MAX = 31,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_63 {
RTAX_UNSPEC = 0,
RTAX_LOCK = 1,
RTAX_MTU = 2,
RTAX_WINDOW = 3,
RTAX_RTT = 4,
RTAX_RTTVAR = 5,
RTAX_SSTHRESH = 6,
RTAX_CWND = 7,
RTAX_ADVMSS = 8,
RTAX_REORDERING = 9,
RTAX_HOPLIMIT = 10,
RTAX_INITCWND = 11,
RTAX_FEATURES = 12,
RTAX_RTO_MIN = 13,
RTAX_INITRWND = 14,
RTAX_QUICKACK = 15,
RTAX_CC_ALGO = 16,
RTAX_FASTOPEN_NO_COOKIE = 17,
__RTAX_MAX = 18,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_64 {
PREFIX_UNSPEC = 0,
PREFIX_ADDRESS = 1,
PREFIX_CACHEINFO = 2,
__PREFIX_MAX = 3,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_65 {
TCA_UNSPEC = 0,
TCA_KIND = 1,
TCA_OPTIONS = 2,
TCA_STATS = 3,
TCA_XSTATS = 4,
TCA_RATE = 5,
TCA_FCNT = 6,
TCA_STATS2 = 7,
TCA_STAB = 8,
TCA_PAD = 9,
TCA_DUMP_INVISIBLE = 10,
TCA_CHAIN = 11,
TCA_HW_OFFLOAD = 12,
TCA_INGRESS_BLOCK = 13,
TCA_EGRESS_BLOCK = 14,
TCA_DUMP_FLAGS = 15,
TCA_EXT_WARN_MSG = 16,
__TCA_MAX = 17,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_66 {
NDUSEROPT_UNSPEC = 0,
NDUSEROPT_SRCADDR = 1,
__NDUSEROPT_MAX = 2,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum rtnetlink_groups {
RTNLGRP_NONE = 0,
RTNLGRP_LINK = 1,
RTNLGRP_NOTIFY = 2,
RTNLGRP_NEIGH = 3,
RTNLGRP_TC = 4,
RTNLGRP_IPV4_IFADDR = 5,
RTNLGRP_IPV4_MROUTE = 6,
RTNLGRP_IPV4_ROUTE = 7,
RTNLGRP_IPV4_RULE = 8,
RTNLGRP_IPV6_IFADDR = 9,
RTNLGRP_IPV6_MROUTE = 10,
RTNLGRP_IPV6_ROUTE = 11,
RTNLGRP_IPV6_IFINFO = 12,
RTNLGRP_DECnet_IFADDR = 13,
RTNLGRP_NOP2 = 14,
RTNLGRP_DECnet_ROUTE = 15,
RTNLGRP_DECnet_RULE = 16,
RTNLGRP_NOP4 = 17,
RTNLGRP_IPV6_PREFIX = 18,
RTNLGRP_IPV6_RULE = 19,
RTNLGRP_ND_USEROPT = 20,
RTNLGRP_PHONET_IFADDR = 21,
RTNLGRP_PHONET_ROUTE = 22,
RTNLGRP_DCB = 23,
RTNLGRP_IPV4_NETCONF = 24,
RTNLGRP_IPV6_NETCONF = 25,
RTNLGRP_MDB = 26,
RTNLGRP_MPLS_ROUTE = 27,
RTNLGRP_NSID = 28,
RTNLGRP_MPLS_NETCONF = 29,
RTNLGRP_IPV4_MROUTE_R = 30,
RTNLGRP_IPV6_MROUTE_R = 31,
RTNLGRP_NEXTHOP = 32,
RTNLGRP_BRVLAN = 33,
RTNLGRP_MCTP_IFADDR = 34,
RTNLGRP_TUNNEL = 35,
RTNLGRP_STATS = 36,
__RTNLGRP_MAX = 37,
}
#[repr(u32)]
#[non_exhaustive]
#[derive(Debug, Copy, Clone, Hash, PartialEq, Eq)]
pub enum _bindgen_ty_67 {
TCA_ROOT_UNSPEC = 0,
TCA_ROOT_TAB = 1,
TCA_ROOT_FLAGS = 2,
TCA_ROOT_COUNT = 3,
TCA_ROOT_TIME_DELTA = 4,
TCA_ROOT_EXT_WARN_MSG = 5,
__TCA_ROOT_MAX = 6,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union __kernel_sockaddr_storage__bindgen_ty_1 {
pub __bindgen_anon_1: __kernel_sockaddr_storage__bindgen_ty_1__bindgen_ty_1,
pub __align: *mut crate::ctypes::c_void,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union rta_session__bindgen_ty_1 {
pub ports: rta_session__bindgen_ty_1__bindgen_ty_1,
pub icmpt: rta_session__bindgen_ty_1__bindgen_ty_2,
pub spi: __u32,
}
impl<T> __IncompleteArrayField<T> {
#[inline]
pub const fn new() -> Self {
__IncompleteArrayField(::core::marker::PhantomData, [])
}
#[inline]
pub fn as_ptr(&self) -> *const T {
self as *const _ as *const T
}
#[inline]
pub fn as_mut_ptr(&mut self) -> *mut T {
self as *mut _ as *mut T
}
#[inline]
pub unsafe fn as_slice(&self, len: usize) -> &[T] {
::core::slice::from_raw_parts(self.as_ptr(), len)
}
#[inline]
pub unsafe fn as_mut_slice(&mut self, len: usize) -> &mut [T] {
::core::slice::from_raw_parts_mut(self.as_mut_ptr(), len)
}
}
impl<T> ::core::fmt::Debug for __IncompleteArrayField<T> {
fn fmt(&self, fmt: &mut ::core::fmt::Formatter<'_>) -> ::core::fmt::Result {
fmt.write_str("__IncompleteArrayField")
}
}
impl nlmsgerr_attrs {
pub const NLMSGERR_ATTR_MAX: nlmsgerr_attrs = nlmsgerr_attrs::NLMSGERR_ATTR_MISS_NEST;
}
impl netlink_policy_type_attr {
pub const NL_POLICY_TYPE_ATTR_MAX: netlink_policy_type_attr = netlink_policy_type_attr::NL_POLICY_TYPE_ATTR_MASK;
}
impl macsec_validation_type {
pub const MACSEC_VALIDATE_MAX: macsec_validation_type = macsec_validation_type::MACSEC_VALIDATE_STRICT;
}
impl macsec_offload {
pub const MACSEC_OFFLOAD_MAX: macsec_offload = macsec_offload::MACSEC_OFFLOAD_MAC;
}
impl ifla_vxlan_df {
pub const VXLAN_DF_MAX: ifla_vxlan_df = ifla_vxlan_df::VXLAN_DF_INHERIT;
}
impl ifla_vxlan_label_policy {
pub const VXLAN_LABEL_MAX: ifla_vxlan_label_policy = ifla_vxlan_label_policy::VXLAN_LABEL_INHERIT;
}
impl ifla_geneve_df {
pub const GENEVE_DF_MAX: ifla_geneve_df = ifla_geneve_df::GENEVE_DF_INHERIT;
}
