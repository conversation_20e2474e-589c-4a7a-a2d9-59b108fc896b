# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
rust-version = "1.61"
name = "memchr"
version = "2.7.4"
authors = [
    "<PERSON> <<EMAIL>>",
    "bluss",
]
build = false
exclude = [
    "/.github",
    "/benchmarks",
    "/fuzz",
    "/scripts",
    "/tmp",
]
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = """
Provides extremely fast (uses SIMD on x86_64, aarch64 and wasm32) routines for
1, 2 or 3 byte search and single substring search.
"""
homepage = "https://github.com/BurntSushi/memchr"
documentation = "https://docs.rs/memchr/"
readme = "README.md"
keywords = [
    "memchr",
    "memmem",
    "substring",
    "find",
    "search",
]
license = "Unlicense OR MIT"
repository = "https://github.com/BurntSushi/memchr"

[package.metadata.docs.rs]
rustdoc-args = ["--generate-link-to-definition"]

[profile.bench]
debug = 2

[profile.release]
debug = 2

[profile.test]
opt-level = 3
debug = 2

[lib]
name = "memchr"
path = "src/lib.rs"
bench = false

[dependencies.compiler_builtins]
version = "0.1.2"
optional = true

[dependencies.core]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-core"

[dependencies.log]
version = "0.4.20"
optional = true

[dev-dependencies.quickcheck]
version = "1.0.3"
default-features = false

[features]
alloc = []
default = ["std"]
libc = []
logging = ["dep:log"]
rustc-dep-of-std = [
    "core",
    "compiler_builtins",
]
std = ["alloc"]
use_std = ["std"]
