[package]
name = "memchr"
version = "2.7.4"  #:version
authors = ["<PERSON> <<EMAIL>>", "bluss"]
description = """
Provides extremely fast (uses SIMD on x86_64, aarch64 and wasm32) routines for
1, 2 or 3 byte search and single substring search.
"""
documentation = "https://docs.rs/memchr/"
homepage = "https://github.com/BurntSushi/memchr"
repository = "https://github.com/BurntSushi/memchr"
readme = "README.md"
keywords = ["memchr", "memmem", "substring", "find", "search"]
license = "Unlicense OR MIT"
exclude = ["/.github", "/benchmarks", "/fuzz", "/scripts", "/tmp"]
edition = "2021"
rust-version = "1.61"

[lib]
name = "memchr"
bench = false

[features]
default = ["std"]

# The 'std' feature permits the memchr crate to use the standard library. This
# permits this crate to use runtime CPU feature detection to automatically
# accelerate searching via vector instructions. Without the standard library,
# this automatic detection is not possible.
std = ["alloc"]

# The 'alloc' feature enables some APIs that require allocation, such as
# 'Finder::into_owned'. Note that this feature does not enable runtime CPU
# feature detection. That still requires 'std'.
alloc = []

# When enabled (it's disabled by default), the `log` crate will be used to
# emit a spattering of log messages. For the most part, the log messages are
# meant to indicate what strategies are being employed. For example, whether
# a vector or a scalar algorithm is used for substring search. This can be
# useful when debugging performance problems.
#
# This is disabled by default.
logging = ["dep:log"]

# The 'use_std' feature is DEPRECATED. It will be removed in memchr 3. Until
# then, it is alias for the 'std' feature.
use_std = ["std"]

# The 'libc' feature has been DEPRECATED and no longer has any effect.
libc = []

# Internal feature, only used when building as part of libstd, not part of the
# stable interface of this crate.
rustc-dep-of-std = ['core', 'compiler_builtins']

[dependencies]
# Only used when the `logging` feature is enabled (disabled by default).
log = { version = "0.4.20", optional = true }
# Internal feature, only used when building as part of libstd, not part of the
# stable interface of this crate.
core = { version = '1.0.0', optional = true, package = 'rustc-std-workspace-core' }
compiler_builtins = { version = '0.1.2', optional = true }

[dev-dependencies]
quickcheck = { version = "1.0.3", default-features = false }

[profile.release]
debug = true

[profile.bench]
debug = true

[profile.test]
opt-level = 3
debug = true

[package.metadata.docs.rs]
rustdoc-args = ["--generate-link-to-definition"]
