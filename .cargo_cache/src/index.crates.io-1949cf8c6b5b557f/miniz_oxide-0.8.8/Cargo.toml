# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "miniz_oxide"
version = "0.8.8"
authors = [
    "Frommi <<EMAIL>>",
    "oyvindln <<EMAIL>>",
    "<NAME_EMAIL>",
]
build = false
exclude = [
    "benches/*",
    "tests/*",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "DEFLATE compression and decompression library rewritten in Rust based on miniz"
homepage = "https://github.com/Frommi/miniz_oxide/tree/master/miniz_oxide"
documentation = "https://docs.rs/miniz_oxide"
readme = "Readme.md"
keywords = [
    "zlib",
    "miniz",
    "deflate",
    "encoding",
]
categories = ["compression"]
license = "MIT OR Zlib OR Apache-2.0"
repository = "https://github.com/Frommi/miniz_oxide/tree/master/miniz_oxide"
resolver = "1"

[features]
block-boundary = []
default = ["with-alloc"]
rustc-dep-of-std = [
    "core",
    "alloc",
    "compiler_builtins",
    "adler2/rustc-dep-of-std",
]
simd = ["simd-adler32"]
std = []
with-alloc = []

[lib]
name = "miniz_oxide"
path = "src/lib.rs"

[dependencies.adler2]
version = "2.0"
default-features = false

[dependencies.alloc]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-alloc"

[dependencies.compiler_builtins]
version = "0.1.2"
optional = true

[dependencies.core]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-core"

[dependencies.serde]
version = "1.0"
features = ["derive"]
optional = true

[dependencies.simd-adler32]
version = "0.3.3"
optional = true
default-features = false

[dev-dependencies]

[lints.rust.unexpected_cfgs]
level = "warn"
priority = 0
check-cfg = ["cfg(fuzzing)"]
