[package]
name = "miniz_oxide"
authors = ["Frommi <<EMAIL>>", "oyvindln <<EMAIL>>", "<PERSON> Geld<PERSON>ich <EMAIL>"]
version = "0.8.8"
license = "MIT OR Zlib OR Apache-2.0"
readme = "Readme.md"
keywords = ["zlib", "miniz", "deflate", "encoding"]
categories = ["compression"]
repository = "https://github.com/Frommi/miniz_oxide/tree/master/miniz_oxide"
homepage = "https://github.com/Frommi/miniz_oxide/tree/master/miniz_oxide"
documentation = "https://docs.rs/miniz_oxide"
description = "DEFLATE compression and decompression library rewritten in Rust based on miniz"
edition = "2021"
exclude = ["benches/*", "tests/*"]

[lib]
name = "miniz_oxide"

[dependencies]
adler2 = { version = "2.0", default-features = false }
simd-adler32 = { version = "0.3.3", default-features = false, optional = true }
serde = { version = "1.0", features = ["derive"], optional = true }

# Internal feature, only used when building as part of libstd, not part of the
# stable interface of this crate.
core = { version = '1.0.0', optional = true, package = 'rustc-std-workspace-core' }
alloc = { version = '1.0.0', optional = true, package = 'rustc-std-workspace-alloc' }
compiler_builtins = { version = '0.1.2', optional = true }


[dev-dependencies]
## Messes with minimum rust version and drags in deps just for running tests
## so just comment out for now and enable manually when needed for enabling benches
# criterion = "0.5"

[[bench]]
name = "benchmark"
harness = false

[features]
default = ["with-alloc"]
with-alloc = []
std = []
block-boundary = []

# Internal feature, only used when building as part of libstd, not part of the
# stable interface of this crate.
rustc-dep-of-std = ['core', 'alloc', 'compiler_builtins', 'adler2/rustc-dep-of-std']

simd = ['simd-adler32']

# Disable unexpected cfg name warning from to !cfg(fuzzing) - compiler is not aware of the fuzzing feature since it comes from the environment
# see https://github.com/rust-fuzz/cargo-fuzz/issues/372
[lints.rust]
unexpected_cfgs = { level = "warn", check-cfg = ['cfg(fuzzing)'] }
