# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2018"
rust-version = "1.65"
name = "object"
version = "0.36.7"
build = "build.rs"
include = [
    "/build.rs",
    "/Cargo.toml",
    "/CHANGELOG.md",
    "/README.md",
    "/LICENSE-APACHE",
    "/LICENSE-MIT",
    "/src",
    "/tests",
]
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "A unified interface for reading and writing object file formats."
readme = "README.md"
keywords = [
    "object",
    "elf",
    "mach-o",
    "pe",
    "coff",
]
license = "Apache-2.0 OR MIT"
repository = "https://github.com/gimli-rs/object"
resolver = "2"

[package.metadata.docs.rs]
features = ["doc"]

[lib]
name = "object"
path = "src/lib.rs"

[[test]]
name = "integration"
path = "tests/integration.rs"

[[test]]
name = "parse_self"
path = "tests/parse_self.rs"

[dependencies.alloc]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-alloc"

[dependencies.compiler_builtins]
version = "0.1.2"
optional = true

[dependencies.core]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-core"

[dependencies.crc32fast]
version = "1.2"
optional = true
default-features = false

[dependencies.flate2]
version = "1"
optional = true

[dependencies.hashbrown]
version = "0.15.0"
features = ["default-hasher"]
optional = true
default-features = false

[dependencies.indexmap]
version = "2.0"
optional = true
default-features = false

[dependencies.memchr]
version = "2.4.1"
default-features = false

[dependencies.ruzstd]
version = "0.7.0"
optional = true

[dependencies.wasmparser]
version = "0.222.0"
optional = true
default-features = false

[features]
all = [
    "read",
    "write",
    "build",
    "std",
    "compression",
    "wasm",
]
archive = []
build = [
    "build_core",
    "write_std",
    "elf",
]
build_core = [
    "read_core",
    "write_core",
]
cargo-all = []
coff = []
compression = [
    "dep:flate2",
    "dep:ruzstd",
    "std",
]
default = [
    "read",
    "compression",
]
doc = [
    "read_core",
    "write_std",
    "build_core",
    "std",
    "compression",
    "archive",
    "coff",
    "elf",
    "macho",
    "pe",
    "wasm",
    "xcoff",
]
elf = []
macho = []
pe = ["coff"]
read = [
    "read_core",
    "archive",
    "coff",
    "elf",
    "macho",
    "pe",
    "xcoff",
    "unaligned",
]
read_core = []
rustc-dep-of-std = [
    "core",
    "compiler_builtins",
    "alloc",
    "memchr/rustc-dep-of-std",
]
std = ["memchr/std"]
unaligned = []
unstable = []
unstable-all = [
    "all",
    "unstable",
]
wasm = ["dep:wasmparser"]
write = [
    "write_std",
    "coff",
    "elf",
    "macho",
    "pe",
    "xcoff",
]
write_core = [
    "dep:crc32fast",
    "dep:indexmap",
    "dep:hashbrown",
]
write_std = [
    "write_core",
    "std",
    "indexmap?/std",
    "crc32fast?/std",
]
xcoff = []
