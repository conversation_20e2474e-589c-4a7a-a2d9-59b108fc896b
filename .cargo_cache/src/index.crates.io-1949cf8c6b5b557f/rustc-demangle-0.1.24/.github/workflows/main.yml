name: CI
on: [push, pull_request]

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    strategy:
      matrix:
        rust: [stable, beta, nightly]
    steps:
    - uses: actions/checkout@v2
    - name: Install Rust
      run: rustup update ${{ matrix.rust }} && rustup default ${{ matrix.rust }}
    - run: cargo build --all
    - run: cargo test --all
    - run: cargo build --features std

  fuzz_targets:
    name: Fuzz Targets
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    # Note that building with fuzzers requires nightly since it uses unstable
    # flags to rustc.
    - run: rustup update nightly && rustup default nightly
    - run: cargo install cargo-fuzz --vers "^0.11"
    - run: cargo fuzz build --dev

  rustfmt:
    name: Rustfmt
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Install Rust
      run: rustup update stable && rustup default stable && rustup component add rustfmt
    - run: cargo fmt -- --check

  publish_docs:
    name: Publish Documentation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Install Rust
        run: rustup update stable && rustup default stable
      - name: Build documentation
        run: cargo doc --no-deps
      - name: Publish documentation
        run: |
          cd target/doc
          git init
          git add .
          git -c user.name='ci' -c user.email='ci' commit -m init
          git push -f -q https://git:${{ secrets.github_token }}@github.com/${{ github.repository }} HEAD:gh-pages
        if: github.event_name == 'push' && github.event.ref == 'refs/heads/main'
