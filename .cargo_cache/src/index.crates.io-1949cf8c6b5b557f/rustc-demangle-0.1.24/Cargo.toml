# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
name = "rustc-demangle"
version = "0.1.24"
authors = ["<PERSON> <<EMAIL>>"]
build = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = """
Rust compiler symbol demangling.
"""
homepage = "https://github.com/rust-lang/rustc-demangle"
documentation = "https://docs.rs/rustc-demangle"
readme = "README.md"
license = "MIT/Apache-2.0"
repository = "https://github.com/rust-lang/rustc-demangle"

[package.metadata.docs.rs]
features = ["std"]
rustdoc-args = [
    "--cfg",
    "docsrs",
]

[profile.release]
lto = true

[lib]
name = "rustc_demangle"
path = "src/lib.rs"

[dependencies.compiler_builtins]
version = "0.1.2"
optional = true

[dependencies.core]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-core"

[features]
rustc-dep-of-std = [
    "core",
    "compiler_builtins",
]
std = []
