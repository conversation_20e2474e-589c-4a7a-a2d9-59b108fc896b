[package]
name = "rustc-demangle"
version = "0.1.24"
authors = ["<PERSON> <<EMAIL>>"]
license = "MIT/Apache-2.0"
readme = "README.md"
repository = "https://github.com/rust-lang/rustc-demangle"
homepage = "https://github.com/rust-lang/rustc-demangle"
documentation = "https://docs.rs/rustc-demangle"
description = """
Rust compiler symbol demangling.
"""

[workspace]
members = ["crates/capi", "fuzz"]

[dependencies]
core = { version = '1.0.0', optional = true, package = 'rustc-std-workspace-core' }
compiler_builtins = { version = '0.1.2', optional = true }

[features]
rustc-dep-of-std = ['core', 'compiler_builtins']
std = []

[profile.release]
lto = true

[package.metadata.docs.rs]
features = ["std"]
rustdoc-args = ["--cfg", "docsrs"]
