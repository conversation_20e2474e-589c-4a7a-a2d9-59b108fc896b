# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "rustc-literal-escaper"
version = "0.0.2"
build = false
autolib = false
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = "Provides code to unescape string literals"
readme = "README.md"
license = "Apache-2.0 OR MIT"
repository = "https://github.com/rust-lang/literal-escaper"

[features]
rustc-dep-of-std = ["dep:std"]

[lib]
name = "rustc_literal_escaper"
path = "src/lib.rs"

[dependencies.std]
version = "1.0.0"
optional = true
package = "rustc-std-workspace-std"
