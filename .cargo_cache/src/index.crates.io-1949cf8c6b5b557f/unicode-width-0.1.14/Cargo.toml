# THIS FILE IS AUTOMATICALLY GENERATED BY CARGO
#
# When uploading crates to the registry Cargo will automatically
# "normalize" Cargo.toml files for maximal compatibility
# with all versions of Cargo and also rewrite `path` dependencies
# to registry (e.g., crates.io) dependencies.
#
# If you are reading this file be aware that the original Cargo.toml
# will likely look very different (and much more reasonable).
# See Cargo.toml.orig for the original contents.

[package]
edition = "2021"
name = "unicode-width"
version = "0.1.14"
authors = [
    "kwantam <<EMAIL>>",
    "Man<PERSON> Goregaokar <<EMAIL>>",
]
build = false
exclude = ["/.github/*"]
autobins = false
autoexamples = false
autotests = false
autobenches = false
description = """
Determine displayed width of `char` and `str` types
according to Unicode Standard Annex #11 rules.
"""
homepage = "https://github.com/unicode-rs/unicode-width"
readme = "README.md"
keywords = [
    "text",
    "width",
    "unicode",
]
categories = [
    "command-line-interface",
    "internationalization",
    "no-std::no-alloc",
    "text-processing",
]
license = "MIT OR Apache-2.0"
repository = "https://github.com/unicode-rs/unicode-width"

[lib]
name = "unicode_width"
path = "src/lib.rs"

[[test]]
name = "tests"
path = "tests/tests.rs"

[[bench]]
name = "benches"
path = "benches/benches.rs"

[dependencies.compiler_builtins]
version = "0.1"
optional = true

[dependencies.core]
version = "1.0"
optional = true
package = "rustc-std-workspace-core"

[dependencies.std]
version = "1.0"
optional = true
package = "rustc-std-workspace-std"

[features]
cjk = []
default = ["cjk"]
no_std = []
rustc-dep-of-std = [
    "std",
    "core",
    "compiler_builtins",
]
