[package]

name = "unicode-width"
version = "0.1.14"
authors = [
    "kwan<PERSON> <<EMAIL>>",
    "<PERSON><PERSON> <<EMAIL>>",
]

homepage = "https://github.com/unicode-rs/unicode-width"
repository = "https://github.com/unicode-rs/unicode-width"
license = "MIT OR Apache-2.0"
categories = [
    "command-line-interface",
    "internationalization",
    "no-std::no-alloc",
    "text-processing",
]
keywords = ["text", "width", "unicode"]
readme = "README.md"
description = """
Determine displayed width of `char` and `str` types
according to Unicode Standard Annex #11 rules.
"""
edition = "2021"

exclude = ["/.github/*"]

[dependencies]
std = { version = "1.0", package = "rustc-std-workspace-std", optional = true }
core = { version = "1.0", package = "rustc-std-workspace-core", optional = true }
compiler_builtins = { version = "0.1", optional = true }

[features]
cjk = []
default = ["cjk"]
rustc-dep-of-std = ['std', 'core', 'compiler_builtins']

# Legacy, now a no-op
no_std = []
