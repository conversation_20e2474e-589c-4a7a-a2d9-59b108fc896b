#!/bin/bash

# Сборка только ESP-IDF части проекта (предполагается, что Rust библиотека уже собрана)

echo "=== Сборка ESP-IDF проекта (без Rust) ==="

# Проверка наличия Rust библиотеки
if [ -f "rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a" ]; then
    echo "✓ Rust библиотека найдена"
else
    echo "⚠ Rust библиотека не найдена!"
    echo "Сначала соберите её:"
    echo "  ./.scripts/build_rust_lib_in_docker.sh"
    echo ""
    exit 1
fi

docker run --rm --interactive \
    -v $PWD:/$PWD \
    -w /$PWD \
    -u $UID \
    -e HOME=/tmp \
    -e IDF_TARGET=esp32s3 \
    espressif/esp-matter:latest_idf_v5.2.3 /bin/bash -s <<EOF 

echo "Информация о ESP-IDF:"
idf.py --version

echo "Целевая платформа: \$IDF_TARGET"

echo ""
echo "----- Сборка ESP-IDF проекта... -----"
idf.py build

echo ""
echo "----- Слияние бинарников -------"
cd build
esptool.py --chip esp32-s3 merge_bin -o A630_merged.bin @flash_args
cd ..

echo ""
echo "----- Копирование артефактов -------"
rm -rf build-artifacts-target-s3
mkdir build-artifacts-target-s3
cp ./build/A630.bin ./build-artifacts-target-s3/A630.bin
cp ./build/ota_data_initial.bin ./build-artifacts-target-s3/ota_data_initial.bin
cp ./build/bootloader/bootloader.bin ./build-artifacts-target-s3/bootloader.bin
cp ./build/partition_table/partition-table.bin ./build-artifacts-target-s3/partition-table.bin
cp ./build/A630_merged.bin ./build-artifacts-target-s3/A630_merged.bin

echo ""
echo "✓ Артефакты сборки скопированы в build-artifacts-target-s3/"

EOF

# Проверка результата
if [ $? -eq 0 ]; then
    echo ""
    echo "✓ Сборка ESP-IDF проекта завершена успешно!"
    echo "Артефакты сборки: build-artifacts-target-s3/"
    ls -la build-artifacts-target-s3/
else
    echo ""
    echo "✗ Ошибка при сборке ESP-IDF проекта"
    exit 1
fi
