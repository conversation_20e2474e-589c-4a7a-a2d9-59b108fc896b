#!/bin/bash

# Сборка проекта A630 с Rust библиотекой
# Сначала собирается Rust библиотека, потом основной проект ESP-IDF

echo "=== Сборка проекта A630 с Rust ==="

# ЭТАП 1: Сборка Rust библиотеки
echo ""
echo "--- ЭТАП 1: Сборка Rust библиотеки ---"

if ./.scripts/build_rust_lib_in_docker.sh; then
    echo "✓ Rust библиотека собрана успешно"
else
    echo "✗ Ошибка сборки Rust библиотеки"
    exit 1
fi

# ЭТАП 2: Сборка основного проекта ESP-IDF
echo ""
echo "--- ЭТАП 2: Сборка основного проекта ESP-IDF ---"

if ./.scripts/build_C_only_in_docker.sh; then
    echo "✓ Основной проект собран успешно"
else
    echo "✗ Ошибка сборки основного проекта"
    exit 1
fi