#!/bin/bash

# Сборка только ESP-IDF части проекта (предполагается, что Rust библиотека уже собрана)

echo "=== Сборка ESP-IDF проекта (без Rust) ==="

# Проверяем наличие Rust библиотеки
if [ -f "rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a" ]; then
    echo "✓ Rust библиотека найдена"
    ls -lh rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a
elif [ -d "rust_lib" ]; then
    echo "⚠ Rust библиотека не найдена!"
    echo "Сначала соберите её одним из способов:"
    echo "  ./.scripts/build_rust_no_std.sh"
    echo "  ./.scripts/build_rust_minimal.sh"
    echo ""
    read -p "Продолжить без Rust библиотеки? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo "ℹ Директория rust_lib не найдена, продолжаем без Rust"
fi

# Сборка ESP-IDF проекта
echo "Сборка ESP-IDF проекта..."

docker run --rm --interactive \
    -v $PWD:/$PWD \
    -w /$PWD \
    -u $UID \
    -e HOME=/tmp \
    -e IDF_TARGET=esp32s3 \
    espressif/esp-matter:latest_idf_v5.2.3 /bin/bash -s <<EOF 

echo "Информация о ESP-IDF:"
idf.py --version

echo "Целевая платформа: \$IDF_TARGET"

echo "Сборка ESP-IDF проекта..."
idf.py build

echo "----- merging bins -------"
cd build
esptool.py --chip esp32-s3 merge_bin -o A630_merged.bin @flash_args
cd ..

echo "----- copying files -------"
rm -rf build-artifacts-target-s3
mkdir build-artifacts-target-s3
cp ./build/A630.bin ./build-artifacts-target-s3/A630.bin
cp ./build/ota_data_initial.bin ./build-artifacts-target-s3/ota_data_initial.bin
cp ./build/bootloader/bootloader.bin ./build-artifacts-target-s3/bootloader.bin
cp ./build/partition_table/partition-table.bin ./build-artifacts-target-s3/partition-table.bin
cp ./build/A630_merged.bin ./build-artifacts-target-s3/A630_merged.bin

echo "✓ Артефакты сборки скопированы в build-artifacts-target-s3/"
ls -la build-artifacts-target-s3/

EOF

# Проверяем результат
if [ $? -eq 0 ]; then
    echo ""
    echo "✓ Сборка ESP-IDF проекта завершена успешно!"
    echo "Артефакты сборки: build-artifacts-target-s3/"
    ls -la build-artifacts-target-s3/
else
    echo ""
    echo "✗ Ошибка при сборке ESP-IDF проекта"
    exit 1
fi
