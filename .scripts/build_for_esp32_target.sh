# build firmware
docker run --rm --interactive -v $PWD:/$PWD -w /$PWD -u $UID -e HOME=/tmp espressif/esp-matter:latest_idf_v5.2.3 /bin/bash -s <<EOF 
mv sdkconfig sdkconfig_tmp
mv sdkconfig_for_esp32 sdkconfig
idf.py fullclean
idf.py build
echo "------ merging image -----"
cd build
esptool.py --chip esp32 merge_bin -o A630_esp32_merged.bin @flash_args
cd ..
echo "------ copying files -----"
rm -r build-artifacts-target_esp32
mkdir build-artifacts-target_esp32
cp ./build/A630.bin ./build-artifacts-target_esp32/A630.bin
cp ./build/ota_data_initial.bin ./build-artifacts-target_esp32/ota_data_initial.bin
cp ./build/bootloader/bootloader.bin ./build-artifacts-target_esp32/bootloader.bin
cp ./build/partition_table/partition-table.bin ./build-artifacts-target_esp32/partition-table.bin
cp ./build/A630_esp32_merged.bin ./build-artifacts-target_esp32/A630_esp32_merged.bin
echo "------ cleaning up build -----"
idf.py fullclean
mv sdkconfig sdkconfig_for_esp32 
mv sdkconfig_tmp sdkconfig
EOF
