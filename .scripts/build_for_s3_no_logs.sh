# build firmware
docker run --rm --interactive -v $PWD:/$PWD -w /$PWD -u $UID -e HOME=/tmp espressif/esp-matter:latest_idf_v5.2.3 /bin/bash -s <<EOF 
mv sdkconfig sdkconfig_tmp2
mv sdkconfig_log_level_ERR sdkconfig
idf.py fullclean
idf.py build
echo "------ merging image -----"
cd build
esptool.py --chip esp32-s3 merge_bin -o A630_merged.bin @flash_args
cd ..
echo "------ copying files -----"
rm -r build-artifacts-target_s3_err_lvl_logs
mkdir build-artifacts-target_s3_err_lvl_logs
cp ./build/A630.bin ./build-artifacts-target_s3_err_lvl_logs/A630.bin
cp ./build/ota_data_initial.bin ./build-artifacts-target_s3_err_lvl_logs/ota_data_initial.bin
cp ./build/bootloader/bootloader.bin ./build-artifacts-target_s3_err_lvl_logs/bootloader.bin
cp ./build/partition_table/partition-table.bin ./build-artifacts-target_s3_err_lvl_logs/partition-table.bin
cp ./build/A630_merged.bin ./build-artifacts-target_s3_err_lvl_logs/A630_merged.bin
echo "------ cleaning up build -----"
idf.py fullclean
mv sdkconfig sdkconfig_log_level_ERR 
mv sdkconfig_tmp2 sdkconfig
EOF
