#!/bin/bash

# Сборка проекта A630 с Rust библиотекой
# Сначала собираем Rust библиотеку, потом основной проект

echo "=== Сборка проекта A630 с Rust ==="

# Функция для проверки успешности выполнения команды
check_result() {
    if [ $? -ne 0 ]; then
        echo "✗ Ошибка на этапе: $1"
        exit 1
    fi
    echo "✓ Завершен этап: $1"
}

# ЭТАП 1: Сборка Rust библиотеки
echo ""
echo "--- ЭТАП 1: Сборка Rust библиотеки ---"

# Проверяем наличие директории rust_lib
if [ ! -d "rust_lib" ]; then
    echo "Предупреждение: Директория rust_lib не найдена, пропускаем сборку Rust"
else
    echo "Сборка Rust библиотеки..."
    if ./.scripts/build_rust_no_std.sh; then
        echo "✓ Rust библиотека собрана успешно"
    else
        echo "✗ Ошибка сборки Rust библиотеки, пробуем альтернативный метод..."
        if ./.scripts/build_rust_minimal.sh; then
            echo "✓ Rust библиотека собрана альтернативным методом"
        else
            echo "✗ Не удалось собрать Rust библиотеку"
            echo "Продолжаем без Rust (возможны ошибки линковки)"
        fi
    fi
fi

# ЭТАП 2: Сборка основного проекта ESP-IDF
echo ""
echo "--- ЭТАП 2: Сборка основного проекта ESP-IDF ---"

# build firmware
docker run --rm --interactive -v $PWD:/$PWD -w /$PWD -u $UID -e HOME=/tmp -e IDF_TARGET=esp32s3 espressif/esp-matter:latest_idf_v5.2.3 /bin/bash -s <<EOF

echo "Проверка наличия Rust библиотеки..."
if [ -f "rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a" ]; then
    echo "✓ Rust библиотека найдена"
    ls -lh rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a
else
    echo "⚠ Rust библиотека не найдена, сборка может завершиться с ошибкой"
fi

echo "Сборка ESP-IDF проекта..."
idf.py build
echo "----- merging bins -------"
cd build
esptool.py --chip esp32-s3 merge_bin -o A630_merged.bin @flash_args
cd ..

echo "----- copying files -------"
rm -rf build-artifacts-target-s3
mkdir build-artifacts-target-s3
cp ./build/A630.bin ./build-artifacts-target-s3/A630.bin
cp ./build/ota_data_initial.bin ./build-artifacts-target-s3/ota_data_initial.bin
cp ./build/bootloader/bootloader.bin ./build-artifacts-target-s3/bootloader.bin
cp ./build/partition_table/partition-table.bin ./build-artifacts-target-s3/partition-table.bin
cp ./build/A630_merged.bin ./build-artifacts-target-s3/A630_merged.bin

echo "✓ Артефакты сборки скопированы в build-artifacts-target-s3/"
ls -la build-artifacts-target-s3/

EOF

check_result "Сборка ESP-IDF проекта"

echo ""
echo "=== Сборка проекта A630 завершена успешно! ==="
echo "Артефакты сборки:"
if [ -f "rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a" ]; then
    echo "  - Rust библиотека: rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a"
fi
echo "  - ESP32 прошивка: build-artifacts-target-s3/"
ls -la build-artifacts-target-s3/