# build firmware
docker run --rm --interactive -v $PWD:/$PWD -w /$PWD -u $UID -e HOME=/tmp espressif/esp-matter:latest_idf_v5.2.3 /bin/bash -s <<EOF 
idf.py build
echo "----- merging bins -------"
cd build
esptool.py --chip esp32-s3 merge_bin -o A630_merged.bin @flash_args
cd ..
echo "----- copying files -------"
rm -r build-artifacts-target-s3
mkdir build-artifacts-target-s3
cp ./build/A630.bin ./build-artifacts-target-s3/A630.bin
cp ./build/ota_data_initial.bin ./build-artifacts-target-s3/ota_data_initial.bin
cp ./build/bootloader/bootloader.bin ./build-artifacts-target-s3/bootloader.bin
cp ./build/partition_table/partition-table.bin ./build-artifacts-target-s3/partition-table.bin
cp ./build/A630_merged.bin ./build-artifacts-target-s3/A630_merged.bin
EOF