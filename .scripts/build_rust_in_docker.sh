#!/bin/bash

# Скрипт для сборки Rust библиотеки в Docker контейнере
# Использует официальный Rust образ с добавлением ESP32 toolchain

echo "=== Сборка Rust библиотеки в Docker ==="

# Проверяем наличие директории rust_lib
if [ ! -d "rust_lib" ]; then
    echo "Ошибка: Директория rust_lib не найдена!"
    exit 1
fi

# Создаем директорию для кэша Cargo если её нет
mkdir -p .cargo_cache

# Запускаем Docker контейнер для сборки Rust
docker run --rm \
    -v $PWD:/workspace \
    -v $PWD/.cargo_cache:/usr/local/cargo/registry \
    -w /workspace/rust_lib \
    -u $(id -u):$(id -g) \
    --env CARGO_HOME=/usr/local/cargo \
    rust:1.75 /bin/bash -c "
        echo 'Установка ESP32 Rust toolchain...'
        
        # Установка espup
        cargo install espup --locked
        
        # Настройка ESP32 toolchain
        espup install --targets esp32
        
        # Источник переменных окружения
        . \$HOME/export-esp.sh
        
        # Установка дополнительных компонентов
        rustup component add rust-src
        rustup target add xtensa-esp32-espidf
        
        echo 'Сборка Rust библиотеки...'
        
        # Сборка библиотеки
        cargo build --release --target=xtensa-esp32-espidf
        
        echo 'Проверка созданной библиотеки...'
        ls -la target/xtensa-esp32-espidf/release/liba630_rust_lib.a
        
        echo 'Rust библиотека успешно собрана!'
    "

# Проверяем результат
if [ $? -eq 0 ]; then
    echo "✓ Rust библиотека успешно собрана"
    echo "Файл: rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a"
else
    echo "✗ Ошибка при сборке Rust библиотеки"
    exit 1
fi
