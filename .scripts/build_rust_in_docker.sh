#!/bin/bash

# Скрипт для сборки Rust библиотеки в Docker контейнере
# Использует официальный образ espressif/idf-rust

echo "=== Сборка Rust библиотеки в Docker (официальный образ Espressif) ==="

if [ ! -d "rust_lib" ]; then
    echo "Ошибка: Директория rust_lib не найдена!"
    exit 1
fi

mkdir -p .cargo_cache

DOCKER_IMAGE="espressif/idf-rust:esp32s3_1.88.0.0"

echo "Используется Docker образ: $DOCKER_IMAGE"

docker run --rm \
    -v $PWD:/workspace \
    -v $PWD/.cargo_cache:/opt/rust/cargo/registry \
    -w /workspace/rust_lib \
    -u $(id -u):$(id -g) \
    --env CARGO_HOME=/opt/rust/cargo \
    $DOCKER_IMAGE /bin/bash -c "
        # echo 'Проверка Rust toolchain...'
        # rustc --version
        # cargo --version

        # echo 'Проверка ESP32 target...'
        # rustup target list --installed | grep xtensa

        echo 'Сборка Rust библиотеки...'
        cargo build --release --target=xtensa-esp32s3-espidf

        echo 'Проверка созданной библиотеки...'
        ls -la target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a

        echo 'Rust библиотека успешно собрана!'
    "

# Проверка результат
if [ $? -eq 0 ]; then
    echo "✓ Rust библиотека успешно собрана"
    echo "Файл: rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a"
else
    echo "✗ Ошибка при сборке Rust библиотеки"
    exit 1
fi
