#!/bin/bash

# Минимальная сборка Rust библиотеки без дополнительных зависимостей
# Использует только предустановленный toolchain в официальном образе

echo "=== Минимальная сборка Rust библиотеки ==="

# Проверяем наличие директории rust_lib
if [ ! -d "rust_lib" ]; then
    echo "Ошибка: Директория rust_lib не найдена!"
    exit 1
fi

# Создаем директорию для кэша Cargo если её нет
mkdir -p .cargo_cache

# Определяем образ для ESP32-S3
DOCKER_IMAGE="espressif/idf-rust:esp32s3_latest"

echo "Используется Docker образ: $DOCKER_IMAGE"

# Проверяем доступность образа
echo "Проверка доступности образа..."
if ! docker pull $DOCKER_IMAGE; then
    echo "✗ Не удалось загрузить образ $DOCKER_IMAGE"
    echo "Попробуем альтернативные теги..."
    
    # Пробуем другие теги
    for tag in "esp32s3_1.88.0.0" "esp32s3_1.87.0.0" "esp32_latest"; do
        ALT_IMAGE="espressif/idf-rust:$tag"
        echo "Пробуем $ALT_IMAGE..."
        if docker pull $ALT_IMAGE; then
            echo "✓ Найден рабочий образ: $ALT_IMAGE"
            DOCKER_IMAGE=$ALT_IMAGE
            break
        fi
    done
fi

# Запускаем Docker контейнер для сборки Rust
docker run --rm \
    -v $PWD:/workspace \
    -v $PWD/.cargo_cache:/opt/rust/cargo/registry \
    -w /workspace/rust_lib \
    -u $(id -u):$(id -g) \
    --env CARGO_HOME=/opt/rust/cargo \
    --env RUST_BACKTRACE=1 \
    $DOCKER_IMAGE /bin/bash -c "
        echo '=== Диагностика среды ==='
        echo 'Rust toolchain:'
        rustc --version
        cargo --version
        
        echo 'Активный toolchain:'
        rustup show active-toolchain 2>/dev/null || echo 'rustup недоступен (используется системный Rust)'
        
        echo 'Доступные targets (если rustup доступен):'
        rustup target list --installed 2>/dev/null | grep -E '(xtensa|riscv)' || echo 'Используется предустановленный toolchain'
        
        echo 'Переменные окружения:'
        env | grep -E '(ESP|IDF|RUST|CARGO)' | sort
        
        echo '=== Подготовка к сборке ==='
        
        # Удаляем rust-toolchain.toml если он есть (может конфликтовать)
        if [ -f rust-toolchain.toml ]; then
            echo 'Удаляем rust-toolchain.toml для избежания конфликтов'
            rm rust-toolchain.toml
        fi
        
        # Очистка предыдущих артефактов
        echo 'Очистка предыдущих артефактов...'
        cargo clean
        
        echo '=== Сборка ==='
        
        # Проверка синтаксиса
        echo 'Проверка синтаксиса...'
        if cargo check --target=xtensa-esp32s3-espidf -Zbuild-std=std,panic_abort; then
            echo '✓ Синтаксис корректен'
        else
            echo '✗ Ошибки в синтаксисе'
            exit 1
        fi

        # Сборка библиотеки
        echo 'Сборка Rust библиотеки для ESP32-S3...'
        if cargo build --release --target=xtensa-esp32s3-espidf -Zbuild-std=std,panic_abort; then
            echo '✓ Сборка успешна'
        else
            echo '✗ Ошибка сборки'
            exit 1
        fi
        
        echo '=== Проверка результата ==='
        
        # Проверка созданной библиотеки
        if [ -f target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a ]; then
            echo '✓ Библиотека создана:'
            ls -la target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a
            echo 'Размер библиотеки:'
            du -h target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a
            
            # Проверка символов
            echo 'Экспортируемые символы:'
            nm target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a | grep ' T ' | grep rust_ || echo 'Символы rust_ не найдены'
            
            echo '✓ Rust библиотека успешно собрана!'
        else
            echo '✗ Ошибка: библиотека не найдена!'
            echo 'Содержимое target директории:'
            find target -name '*.a' -o -name '*.rlib' | head -10
            exit 1
        fi
    "

# Проверяем результат
if [ $? -eq 0 ]; then
    echo ""
    echo "✓ Минимальная сборка Rust библиотеки завершена успешно"
    echo "Файл: rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a"
    
    # Дополнительная проверка на хосте
    if [ -f "rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a" ]; then
        echo "Размер на хосте: $(ls -lh rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a | awk '{print $5}')"
    fi
else
    echo ""
    echo "✗ Ошибка при минимальной сборке Rust библиотеки"
    echo "Попробуйте:"
    echo "  1. Проверить доступность Docker образа"
    echo "  2. Проверить конфигурацию rust_lib/Cargo.toml"
    echo "  3. Запустить ./.scripts/test_rust_build.sh для диагностики"
    exit 1
fi
