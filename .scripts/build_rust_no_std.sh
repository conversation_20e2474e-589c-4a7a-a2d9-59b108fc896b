#!/bin/bash

# Сборка Rust библиотеки без std (no_std) для максимальной совместимости
# Создает временную версию lib.rs без зависимостей от std

echo "=== Сборка Rust библиотеки (no_std версия) ==="

# Проверяем наличие директории rust_lib
if [ ! -d "rust_lib" ]; then
    echo "Ошибка: Директория rust_lib не найдена!"
    exit 1
fi

# Создаем директорию для кэша Cargo если её нет
mkdir -p .cargo_cache

# Определяем образ для ESP32-S3
DOCKER_IMAGE="espressif/idf-rust:esp32s3_latest"

echo "Используется Docker образ: $DOCKER_IMAGE"

# Создаем резервную копию оригинального lib.rs
cp rust_lib/src/lib.rs rust_lib/src/lib.rs.backup

# Создаем no_std версию lib.rs
cat > rust_lib/src/lib.rs << 'EOF'
#![no_std]
#![no_main]

use core::panic::PanicInfo;

// Обработчик паники для no_std
#[panic_handler]
fn panic(_info: &PanicInfo) -> ! {
    loop {}
}

// Простая функция для демонстрации - сложение двух чисел
#[no_mangle]
pub extern "C" fn rust_add_numbers(a: i32, b: i32) -> i32 {
    a + b
}

// Функция для подсчета длины строки
#[no_mangle]
pub extern "C" fn rust_string_length(s: *const u8) -> u32 {
    if s.is_null() {
        return 0;
    }
    
    let mut len = 0;
    unsafe {
        let mut ptr = s;
        while *ptr != 0 {
            len += 1;
            ptr = ptr.add(1);
        }
    }
    len
}

// Функция для вычисления факториала
#[no_mangle]
pub extern "C" fn rust_factorial(n: u32) -> u32 {
    if n <= 1 {
        1
    } else {
        n * rust_factorial(n - 1)
    }
}

// Функция для проверки четности числа
#[no_mangle]
pub extern "C" fn rust_is_even(n: i32) -> i32 {
    if n % 2 == 0 { 1 } else { 0 }
}

// Функция для поиска максимума в массиве
#[no_mangle]
pub extern "C" fn rust_find_max(arr: *const i32, len: u32) -> i32 {
    if arr.is_null() || len == 0 {
        return 0;
    }
    
    unsafe {
        let mut max = *arr;
        for i in 1..len {
            let val = *arr.add(i as usize);
            if val > max {
                max = val;
            }
        }
        max
    }
}
EOF

# Создаем минимальный Cargo.toml
cat > rust_lib/Cargo.toml << 'EOF'
[package]
name = "a630_rust_lib"
version = "0.1.0"
edition = "2021"

[lib]
name = "a630_rust_lib"
crate-type = ["staticlib"]

# Никаких зависимостей для no_std версии
[dependencies]

[profile.release]
opt-level = "s"
debug = false
codegen-units = 1
panic = "abort"

[profile.dev]
debug = true
opt-level = 1
EOF

# Запускаем Docker контейнер для сборки Rust
docker run --rm \
    -v $PWD:/workspace \
    -v $PWD/.cargo_cache:/opt/rust/cargo/registry \
    -w /workspace/rust_lib \
    -u $(id -u):$(id -g) \
    --env CARGO_HOME=/opt/rust/cargo \
    --env RUST_BACKTRACE=1 \
    $DOCKER_IMAGE /bin/bash -c "
        echo '=== Диагностика среды ==='
        echo 'Rust toolchain:'
        rustc --version
        cargo --version
        
        echo '=== Подготовка к сборке ==='
        
        # Удаляем конфликтные файлы
        rm -f rust-toolchain.toml
        rm -rf .cargo
        
        # Очистка предыдущих артефактов
        echo 'Очистка предыдущих артефактов...'
        cargo clean
        
        echo '=== Сборка no_std версии ==='
        
        # Сборка библиотеки без std
        echo 'Сборка Rust библиотеки (no_std) для ESP32-S3...'
        if cargo build --release --target=xtensa-esp32s3-espidf; then
            echo '✓ Сборка успешна'
        else
            echo '✗ Ошибка сборки, пробуем с -Zbuild-std=core,alloc'
            if cargo build --release --target=xtensa-esp32s3-espidf -Zbuild-std=core,alloc; then
                echo '✓ Сборка с build-std успешна'
            else
                echo '✗ Ошибка сборки даже с build-std'
                exit 1
            fi
        fi
        
        echo '=== Проверка результата ==='
        
        # Проверка созданной библиотеки
        if [ -f target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a ]; then
            echo '✓ Библиотека создана:'
            ls -la target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a
            echo 'Размер библиотеки:'
            du -h target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a
            
            # Проверка символов
            echo 'Экспортируемые символы:'
            nm target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a | grep ' T ' | grep rust_ || echo 'Символы rust_ не найдены'
            
            echo '✓ Rust библиотека (no_std) успешно собрана!'
        else
            echo '✗ Ошибка: библиотека не найдена!'
            echo 'Содержимое target директории:'
            find target -name '*.a' -o -name '*.rlib' | head -10
            exit 1
        fi
    "

# Сохраняем результат
BUILD_RESULT=$?

# Восстанавливаем оригинальные файлы
if [ -f rust_lib/src/lib.rs.backup ]; then
    mv rust_lib/src/lib.rs.backup rust_lib/src/lib.rs
    echo "Восстановлен оригинальный lib.rs"
fi

# Восстанавливаем оригинальный Cargo.toml (создаем заново)
cat > rust_lib/Cargo.toml << 'EOF'
[package]
name = "a630_rust_lib"
version = "0.1.0"
edition = "2021"

[lib]
name = "a630_rust_lib"
crate-type = ["staticlib"]

[dependencies]
# Минимальные зависимости для статической библиотеки
# Убираем esp-idf-sys чтобы избежать конфликтов с no_std

[profile.release]
opt-level = "s"
debug = false
# Убираем lto = true чтобы избежать конфликта с embed-bitcode
# lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
debug = true
opt-level = 1
EOF

# Проверяем результат
if [ $BUILD_RESULT -eq 0 ]; then
    echo ""
    echo "✓ No_std сборка Rust библиотеки завершена успешно"
    echo "Файл: rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a"
    
    # Дополнительная проверка на хосте
    if [ -f "rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a" ]; then
        echo "Размер на хосте: $(ls -lh rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a | awk '{print $5}')"
    fi
    
    echo ""
    echo "ВАЖНО: Обновите заголовочный файл rust_lib/include/rust_functions.h"
    echo "Замените 'const char*' на 'const uint8_t*' для rust_string_length"
else
    echo ""
    echo "✗ Ошибка при no_std сборке Rust библиотеки"
    exit 1
fi
