#!/bin/bash

# Оптимизированная двухэтапная сборка с кэшированием Rust toolchain
# Создает Docker образ с предустановленным ESP32 Rust toolchain для ускорения сборки

echo "=== Оптимизированная сборка A630 с Rust (с кэшированием) ==="

# Функция для проверки успешности выполнения команды
check_result() {
    if [ $? -ne 0 ]; then
        echo "✗ Ошибка на этапе: $1"
        exit 1
    fi
    echo "✓ Завершен этап: $1"
}

# Имя Docker образа для Rust ESP32
RUST_ESP32_IMAGE="a630-rust-esp32:latest"

# Проверяем, существует ли образ с Rust ESP32 toolchain
if ! docker image inspect $RUST_ESP32_IMAGE >/dev/null 2>&1; then
    echo "--- Создание Docker образа с Rust ESP32 toolchain ---"
    
    # Создаем временный Dockerfile
    cat > /tmp/Dockerfile.rust-esp32 << 'DOCKERFILE_END'
FROM rust:1.75

# Установка необходимых системных пакетов
RUN apt-get update && apt-get install -y \
    git \
    wget \
    flex \
    bison \
    gperf \
    python3 \
    python3-pip \
    python3-venv \
    cmake \
    ninja-build \
    ccache \
    libffi-dev \
    libssl-dev \
    dfu-util \
    libusb-1.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Создание пользователя для сборки
RUN useradd -m -s /bin/bash builder

# Переключение на пользователя builder
USER builder
WORKDIR /home/<USER>

# Установка espup
RUN cargo install espup --locked

# Установка ESP32 toolchain
RUN espup install --targets esp32

# Установка дополнительных компонентов Rust
RUN . $HOME/export-esp.sh && \
    rustup component add rust-src && \
    rustup target add xtensa-esp32-espidf

# Создание рабочей директории
WORKDIR /workspace

# Команда по умолчанию
CMD ["/bin/bash"]
DOCKERFILE_END

    # Сборка Docker образа
    docker build -f /tmp/Dockerfile.rust-esp32 -t $RUST_ESP32_IMAGE .
    check_result "Создание Docker образа с Rust ESP32 toolchain"
    
    # Удаляем временный Dockerfile
    rm /tmp/Dockerfile.rust-esp32
else
    echo "✓ Docker образ $RUST_ESP32_IMAGE уже существует"
fi

# ЭТАП 1: Сборка Rust библиотеки
echo ""
echo "--- ЭТАП 1: Сборка Rust библиотеки (с кэшированием) ---"

# Проверяем наличие директории rust_lib
if [ ! -d "rust_lib" ]; then
    echo "Ошибка: Директория rust_lib не найдена!"
    exit 1
fi

# Создаем директорию для кэша Cargo если её нет
mkdir -p .cargo_cache

# Запускаем Docker контейнер для сборки Rust с кэшированным toolchain
docker run --rm \
    -v $PWD:/workspace \
    -v $PWD/.cargo_cache:/home/<USER>/.cargo/registry \
    -w /workspace/rust_lib \
    -u $(id -u):$(id -g) \
    $RUST_ESP32_IMAGE /bin/bash -c "
        # Источник переменных окружения ESP32
        . /home/<USER>/export-esp.sh
        
        echo 'Сборка Rust библиотеки...'
        cargo build --release --target=xtensa-esp32-espidf
        
        echo 'Проверка созданной библиотеки...'
        ls -la target/xtensa-esp32-espidf/release/liba630_rust_lib.a
    "

check_result "Сборка Rust библиотеки"

# Проверяем, что библиотека создана
if [ ! -f "rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a" ]; then
    echo "✗ Rust библиотека не найдена!"
    exit 1
fi

echo "✓ Rust библиотека готова: $(ls -lh rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a | awk '{print $5}')"

# ЭТАП 2: Сборка основного проекта ESP-IDF
echo ""
echo "--- ЭТАП 2: Сборка основного проекта ESP-IDF ---"

# Запускаем Docker контейнер для сборки ESP-IDF проекта
docker run --rm --interactive \
    -v $PWD:/$PWD \
    -w /$PWD \
    -u $UID \
    -e HOME=/tmp \
    espressif/esp-matter:latest_idf_v5.2.3 /bin/bash -s <<EOF 

echo "Проверка наличия Rust библиотеки..."
if [ -f "rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a" ]; then
    echo "✓ Rust библиотека найдена"
    ls -lh rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a
else
    echo "✗ Rust библиотека не найдена!"
    exit 1
fi

echo "Сборка ESP-IDF проекта..."
idf.py build

echo "----- merging bins -------"
cd build
esptool.py --chip esp32-s3 merge_bin -o A630_merged.bin @flash_args
cd ..

echo "----- copying files -------"
rm -rf build-artifacts-target-s3
mkdir build-artifacts-target-s3
cp ./build/A630.bin ./build-artifacts-target-s3/A630.bin
cp ./build/ota_data_initial.bin ./build-artifacts-target-s3/ota_data_initial.bin
cp ./build/bootloader/bootloader.bin ./build-artifacts-target-s3/bootloader.bin
cp ./build/partition_table/partition-table.bin ./build-artifacts-target-s3/partition-table.bin
cp ./build/A630_merged.bin ./build-artifacts-target-s3/A630_merged.bin

echo "✓ Артефакты сборки скопированы в build-artifacts-target-s3/"
ls -la build-artifacts-target-s3/

EOF

check_result "Сборка ESP-IDF проекта"

echo ""
echo "=== Оптимизированная сборка завершена успешно! ==="
echo "Артефакты сборки:"
echo "  - Rust библиотека: rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a"
echo "  - ESP32 прошивка: build-artifacts-target-s3/"
ls -la build-artifacts-target-s3/

echo ""
echo "Примечание: Docker образ $RUST_ESP32_IMAGE сохранен для ускорения будущих сборок"
echo "Для очистки образа используйте: docker rmi $RUST_ESP32_IMAGE"
