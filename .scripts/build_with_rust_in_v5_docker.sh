#!/bin/bash

# Двухэтапная сборка проекта с Rust в Docker контейнерах
# Этап 1: Сборка Rust библиотеки
# Этап 2: Сборка основного проекта ESP-IDF

echo "=== Двухэтапная сборка проекта A630 с Rust ==="

# Функция для проверки успешности выполнения команды
check_result() {
    if [ $? -ne 0 ]; then
        echo "✗ Ошибка на этапе: $1"
        exit 1
    fi
    echo "✓ Завершен этап: $1"
}

# ЭТАП 1: Сборка Rust библиотеки
echo ""
echo "--- ЭТАП 1: Сборка Rust библиотеки ---"

# Проверяем наличие директории rust_lib
if [ ! -d "rust_lib" ]; then
    echo "Ошибка: Директория rust_lib не найдена!"
    exit 1
fi

# Создаем директорию для кэша Cargo если её нет
mkdir -p .cargo_cache

# Запускаем Docker контейнер для сборки Rust
docker run --rm \
    -v $PWD:/workspace \
    -v $PWD/.cargo_cache:/usr/local/cargo/registry \
    -w /workspace/rust_lib \
    -u $(id -u):$(id -g) \
    --env CARGO_HOME=/usr/local/cargo \
    rust:1.75 /bin/bash -c "
        echo 'Установка ESP32 Rust toolchain...'
        
        # Установка espup
        cargo install espup --locked
        
        # Настройка ESP32 toolchain
        espup install --targets esp32
        
        # Источник переменных окружения
        . \$HOME/export-esp.sh
        
        # Установка дополнительных компонентов
        rustup component add rust-src
        rustup target add xtensa-esp32-espidf
        
        echo 'Сборка Rust библиотеки...'
        
        # Сборка библиотеки
        cargo build --release --target=xtensa-esp32-espidf
        
        echo 'Проверка созданной библиотеки...'
        ls -la target/xtensa-esp32-espidf/release/liba630_rust_lib.a
    "

check_result "Сборка Rust библиотеки"

# Проверяем, что библиотека создана
if [ ! -f "rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a" ]; then
    echo "✗ Rust библиотека не найдена!"
    exit 1
fi

echo "✓ Rust библиотека готова: $(ls -lh rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a | awk '{print $5}')"

# ЭТАП 2: Сборка основного проекта ESP-IDF
echo ""
echo "--- ЭТАП 2: Сборка основного проекта ESP-IDF ---"

# Запускаем Docker контейнер для сборки ESP-IDF проекта
docker run --rm --interactive \
    -v $PWD:/$PWD \
    -w /$PWD \
    -u $UID \
    -e HOME=/tmp \
    espressif/esp-matter:latest_idf_v5.2.3 /bin/bash -s <<EOF 

echo "Проверка наличия Rust библиотеки..."
if [ -f "rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a" ]; then
    echo "✓ Rust библиотека найдена"
    ls -lh rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a
else
    echo "✗ Rust библиотека не найдена!"
    exit 1
fi

echo "Сборка ESP-IDF проекта..."
idf.py build

echo "----- merging bins -------"
cd build
esptool.py --chip esp32-s3 merge_bin -o A630_merged.bin @flash_args
cd ..

echo "----- copying files -------"
rm -rf build-artifacts-target-s3
mkdir build-artifacts-target-s3
cp ./build/A630.bin ./build-artifacts-target-s3/A630.bin
cp ./build/ota_data_initial.bin ./build-artifacts-target-s3/ota_data_initial.bin
cp ./build/bootloader/bootloader.bin ./build-artifacts-target-s3/bootloader.bin
cp ./build/partition_table/partition-table.bin ./build-artifacts-target-s3/partition-table.bin
cp ./build/A630_merged.bin ./build-artifacts-target-s3/A630_merged.bin

echo "✓ Артефакты сборки скопированы в build-artifacts-target-s3/"
ls -la build-artifacts-target-s3/

EOF

check_result "Сборка ESP-IDF проекта"

echo ""
echo "=== Двухэтапная сборка завершена успешно! ==="
echo "Артефакты сборки:"
echo "  - Rust библиотека: rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a"
echo "  - ESP32 прошивка: build-artifacts-target-s3/"
ls -la build-artifacts-target-s3/
