#!/bin/bash

# Тестовый скрипт для проверки сборки Rust библиотеки

echo "=== Проверка окружения для сборки Rust библиотеки ==="

# Проверка наличия Docker
if ! command -v docker &> /dev/null; then
    echo "✗ Docker не установлен"
    exit 1
fi

echo "✓ Docker найден: $(docker --version)"

# Проверка наличия папки rust_lib
if [ ! -d "rust_lib" ]; then
    echo "✗ Директория rust_lib не найдена"
    exit 1
fi

echo "✓ Директория rust_lib найдена"

# Проверяем доступность образа
DOCKER_IMAGE="espressif/idf-rust:esp32s3_latest"
echo "Проверка доступности образа $DOCKER_IMAGE..."

if docker pull $DOCKER_IMAGE; then
    echo "✓ Образ $DOCKER_IMAGE загружен"
else
    echo "✗ Не удалось загрузить образ $DOCKER_IMAGE"
    echo "Попробуем альтернативные теги..."
    
    # Пробуем другие теги
    for tag in "esp32s3_1.88.0.0" "esp32s3_1.87.0.0" "latest"; do
        ALT_IMAGE="espressif/idf-rust:$tag"
        echo "Пробуем $ALT_IMAGE..."
        if docker pull $ALT_IMAGE; then
            echo "✓ Найден рабочий образ: $ALT_IMAGE"
            DOCKER_IMAGE=$ALT_IMAGE
            break
        fi
    done
fi

# Тестируем среду в контейнере
echo "Тестирование среды в контейнере..."
docker run --rm \
    -v $PWD:/workspace \
    -w /workspace/rust_lib \
    $DOCKER_IMAGE /bin/bash -c "
        echo 'Rust toolchain:'
        rustc --version
        cargo --version

        echo 'Активный toolchain:'
        rustup show active-toolchain 2>/dev/null || echo 'Используется системный Rust (нормально для официальных образов)'

        echo 'Доступные targets:'
        rustup target list --installed 2>/dev/null | grep -E '(xtensa|riscv)' || echo 'Используется предустановленный ESP toolchain'

        echo 'Переменные окружения ESP:'
        env | grep -E '(ESP|IDF)' | sort || echo 'ESP переменные не найдены'

        # Удаляем rust-toolchain.toml если он есть
        if [ -f rust-toolchain.toml ]; then
            echo 'Удаляем rust-toolchain.toml для избежания конфликтов'
            rm rust-toolchain.toml
        fi

        echo 'Проверка Cargo.toml:'
        if [ -f Cargo.toml ]; then
            echo 'Cargo.toml найден'
            cat Cargo.toml
        else
            echo 'Cargo.toml не найден!'
            exit 1
        fi

        echo 'Проверка src/lib.rs:'
        if [ -f src/lib.rs ]; then
            echo 'src/lib.rs найден'
            head -10 src/lib.rs
        else
            echo 'src/lib.rs не найден!'
            exit 1
        fi

        echo 'Тест компиляции (check):'
        cargo check --target=xtensa-esp32s3-espidf
    "

if [ $? -eq 0 ]; then
    echo "✓ Тестирование среды прошло успешно"
    echo "Можно запускать полную сборку: ./.scripts/build_rust_in_docker.sh"
else
    echo "✗ Ошибки в тестировании среды"
    echo "Проверьте конфигурацию Rust библиотеки"
    exit 1
fi
