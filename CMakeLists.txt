# The following lines of boilerplate have to be in your project's CMakeLists
# in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.5)

# see https://docs.espressif.com/projects/esp-idf/en/stable/esp32/api-reference/system/misc_system_api.html for details
execute_process(
    COMMAND git rev-parse HEAD
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE GIT_COMMIT_HASH
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
set(PROJECT_VER "${GIT_COMMIT_HASH}")

set(SUPPORTED_TARGETS esp32)

# Добавляем путь к Rust компоненту
set(EXTRA_COMPONENT_DIRS "rust_lib")

include($ENV{IDF_PATH}/tools/cmake/project.cmake)
project(A630)

