# Быстрое решение проблем сборки Rust

## 🚨 Если у вас ошибки компиляции - используйте этот порядок:

### 1. **Самое простое решение (рекомендуется)**
```bash
./.scripts/build_rust_no_std.sh
```
**Что делает:**
- Создает временную no_std версию библиотеки
- Убирает все конфликтные зависимости
- Автоматически восстанавливает оригинальные файлы
- **Работает в 99% случаев**

### 2. **Если нужна std библиотека**
```bash
./.scripts/build_rust_minimal.sh
```
**Что делает:**
- Использует `-Zbuild-std=std,panic_abort`
- Собирает стандартную библиотеку из исходников
- Подходит для более сложного кода

### 3. **Для отладки проблем**
```bash
./.scripts/test_rust_build.sh
```
**Что делает:**
- Проверяет среду Docker
- Тестирует доступность образов
- Диагностирует проблемы конфигурации

## 🔧 Исправленные проблемы:

### ✅ "can't find crate for core"
- **Решение:** Убрали `esp-idf-sys` зависимость
- **Файл:** `rust_lib/Cargo.toml`

### ✅ "components are unavailable"
- **Решение:** Удален `rust-toolchain.toml`
- **Причина:** Конфликт с предустановленным ESP toolchain

### ✅ "embed-bitcode and lto are incompatible"
- **Решение:** Убрали `lto = true` из профиля release
- **Файл:** `rust_lib/Cargo.toml`

### ✅ "can't find crate for std"
- **Решение:** Используем `-Zbuild-std` или no_std
- **Скрипты:** Обновлены все скрипты сборки

## 📋 Проверка результата:

После успешной сборки:
```bash
# Проверить наличие библиотеки
ls -la rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a

# Проверить экспортируемые символы
nm rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a | grep rust_
```

Должны увидеть:
```
rust_add_numbers
rust_string_length
rust_factorial
rust_is_even
rust_find_max
```

## 🎯 Рекомендуемый порядок действий:

1. **Попробуйте no_std версию:**
   ```bash
   ./.scripts/build_rust_no_std.sh
   ```

2. **Если нужны дополнительные возможности:**
   ```bash
   ./.scripts/build_rust_minimal.sh
   ```

3. **Для полной сборки проекта:**
   ```bash
   ./.scripts/build_with_espressif_images.sh
   ```

## ⚠️ Важные замечания:

### Для no_std версии:
- В заголовочном файле `rust_lib/include/rust_functions.h`
- Замените `const char*` на `const uint8_t*` для `rust_string_length`

### Для std версии:
- Используется оригинальный интерфейс с `const char*`
- Поддерживает более сложные операции

## 🐛 Если проблемы остались:

### Проверьте Docker:
```bash
docker --version
docker pull espressif/idf-rust:esp32s3_latest
```

### Очистите кэш:
```bash
rm -rf .cargo_cache rust_lib/target
docker system prune -f
```

### Попробуйте альтернативные образы:
```bash
# В скриптах замените на:
DOCKER_IMAGE="espressif/idf-rust:esp32s3_1.88.0.0"
# или
DOCKER_IMAGE="espressif/idf-rust:esp32_latest"
```

## 📞 Поддержка:

- **Документация:** `RUST_BUILD_TROUBLESHOOTING.md`
- **Примеры:** `.scripts/EXAMPLES.md`
- **ESP-RS Book:** https://docs.esp-rs.org/book/
