# Docker сборка проекта A630 с Rust

Этот документ описывает новые скрипты для сборки проекта A630 с поддержкой Rust в Docker контейнерах.
Используются **официальные образы Espressif** для максимальной совместимости и надежности.

## Доступные скрипты

### 1. `build_with_espressif_images.sh` ⭐ **Рекомендуется**
**Назначение**: Оптимизированная сборка с официальными образами Espressif.

**Использование**:
```bash
# Для ESP32-S3 (по умолчанию)
./.scripts/build_with_espressif_images.sh

# Для ESP32
ESP_TARGET=esp32 ./.scripts/build_with_espressif_images.sh

# Для ESP32-C3
ESP_TARGET=esp32c3 ./.scripts/build_with_espressif_images.sh
```

**Что делает**:
- Использует официальный образ `espressif/idf-rust`
- Автоматически определяет правильный Rust target
- Поддерживает ESP32, ESP32-S3, ESP32-C3
- Двухэтапная сборка с проверками

### 2. `build_rust_in_docker.sh`
**Назначение**: Сборка только Rust библиотеки в Docker контейнере.

**Использование**:
```bash
./.scripts/build_rust_in_docker.sh
```

**Что делает**:
- Запускает официальный образ `espressif/idf-rust:esp32s3_latest`
- Компилирует Rust библиотеку в статическую библиотеку (.a файл)

### 3. `build_with_rust_in_v5_docker.sh`
**Назначение**: Двухэтапная сборка проекта (Rust + ESP-IDF).

**Использование**:
```bash
./.scripts/build_with_rust_in_v5_docker.sh
```

**Этапы сборки**:
1. **Этап 1**: Сборка Rust библиотеки в `espressif/idf-rust` контейнере
2. **Этап 2**: Сборка основного проекта в `espressif/esp-matter` контейнере

### 4. `build_in_v5_docker.sh` ⭐ **Обновлен для Rust**
**Назначение**: Полная сборка проекта (Rust + ESP-IDF) - обновленная версия оригинального скрипта.

**Использование**:
```bash
./.scripts/build_in_v5_docker.sh
```

**Что делает**:
- Автоматически собирает Rust библиотеку (если директория rust_lib существует)
- Собирает основной ESP-IDF проект
- Создает финальные артефакты

### 5. `build_esp_only_docker.sh`
**Назначение**: Сборка только ESP-IDF части (предполагается, что Rust уже собран).

**Использование**:
```bash
./.scripts/build_esp_only_docker.sh
```

### 6. `clean_rust_build.sh`
**Назначение**: Очистка Rust артефактов сборки.

**Использование**:
```bash
./.scripts/clean_rust_build.sh
```

## Сравнение подходов

| Скрипт | Время первой сборки | Время повторной сборки | Особенности |
|--------|-------------------|----------------------|-------------|
| `build_with_espressif_images.sh` | ~5-10 мин | ~2-5 мин | Официальные образы, поддержка всех ESP32 |
| `build_with_rust_in_v5_docker.sh` | ~5-10 мин | ~5-10 мин | Официальные образы, ESP32-S3 |
| `build_rust_in_docker.sh` | ~2-5 мин | ~1-2 мин | Только Rust библиотека |

## Требования

### Системные требования
- Docker установлен и запущен
- Минимум 4 GB свободного места на диске
- Интернет соединение (для первой сборки)

### Права доступа
```bash
# Сделать скрипты исполняемыми
chmod +x .scripts/build_rust_in_docker.sh
chmod +x .scripts/build_with_rust_in_v5_docker.sh
chmod +x .scripts/build_with_rust_cached_docker.sh
chmod +x .scripts/clean_rust_build.sh
```

## Структура артефактов

После успешной сборки:

```
a630_aroma/
├── rust_lib/target/xtensa-esp32-espidf/release/
│   └── liba630_rust_lib.a          # Rust статическая библиотека
├── build-artifacts-target-s3/       # ESP32 прошивка
│   ├── A630.bin
│   ├── A630_merged.bin
│   ├── bootloader.bin
│   ├── ota_data_initial.bin
│   └── partition-table.bin
└── .cargo_cache/                    # Кэш Cargo зависимостей
```

## Отладка

### Проверка Rust библиотеки
```bash
# Проверить, что библиотека создана
ls -la rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a

# Проверить символы в библиотеке
nm rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a | grep rust_
```

### Проверка Docker образов
```bash
# Список Docker образов
docker images | grep -E "(rust|esp|a630)"

# Удаление неиспользуемых образов
docker system prune -f
```

### Логи сборки
Все скрипты выводят подробные логи. При ошибках:
1. Проверьте наличие файлов в `rust_lib/`
2. Убедитесь, что Docker запущен
3. Проверьте свободное место на диске

## Интеграция с CI/CD

Для автоматизации сборки в CI/CD системах рекомендуется:

```yaml
# Пример для GitHub Actions
- name: Build with Rust
  run: |
    chmod +x .scripts/build_with_rust_cached_docker.sh
    ./.scripts/build_with_rust_cached_docker.sh
```

## Миграция с существующих скриптов

Если вы использовали `build_in_v5_docker.sh`:

1. **Без изменений**: Продолжайте использовать старый скрипт (Rust код не будет компилироваться)
2. **С Rust**: Замените на `build_with_rust_cached_docker.sh`

## Производительность

### Время сборки (примерные значения)
- **Первая сборка**: 15-20 минут
- **Повторная сборка** (с кэшем): 2-5 минут
- **Только Rust**: 1-2 минуты
- **Только ESP-IDF**: 1-3 минуты

### Оптимизация
- Используйте `build_with_rust_cached_docker.sh` для разработки
- Регулярно очищайте кэш командой `clean_rust_build.sh`
- Для CI/CD можно кэшировать Docker образы
