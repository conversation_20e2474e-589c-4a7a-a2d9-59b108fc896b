# Решение проблем сборки Rust для ESP32

## Проблема 1: "can't find crate for `core`"

### Описание ошибки
```
Compiling chrono v0.4.41
error[E0463]: can't find crate for `core`
```

### Причина
Ошибка возникает из-за конфликта между `#![no_std]` и зависимостями, которые требуют стандартной библиотеки.

## Проблема 2: "components are unavailable for download"

### Описание ошибки
```
error: some components are unavailable for download for channel 'nightly-2024-02-01':
'rust-std' for target 'riscv32imc-esp-espidf',
'rust-std' for target 'xtensa-esp32-espidf',
'rust-std' for target 'xtensa-esp32s3-espidf'
```

### Причина
Конфликт между `rust-toolchain.toml` и предустановленным ESP toolchain в официальных образах Espressif.

## Проблема 3: "options -C embed-bitcode=no and -C lto are incompatible"

### Описание ошибки
```
error: options `-C embed-bitcode=no` and `-C lto` are incompatible
```

### Причина
Конфликт между настройками LTO в Cargo.toml и флагами компилятора ESP32.

## Проблема 4: "can't find crate for std"

### Описание ошибки
```
error[E0463]: can't find crate for `std`
  |
  = note: the `xtensa-esp32s3-espidf` target may not be installed
```

### Причина
Отсутствие стандартной библиотеки для ESP32 target.

### Решение

#### 1. Обновлен Cargo.toml
```toml
[package]
name = "a630_rust_lib"
version = "0.1.0"
edition = "2021"

[lib]
name = "a630_rust_lib"
crate-type = ["staticlib"]

[dependencies]
# Минимальные зависимости для статической библиотеки
# Убираем esp-idf-sys чтобы избежать конфликтов с no_std

[profile.release]
opt-level = "s"
debug = false
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
debug = true
opt-level = 1
```

#### 2. Обновлен src/lib.rs
```rust
// Убираем #![no_std] для совместимости с официальными образами Espressif
// Используем стандартные типы C для максимальной совместимости

use std::os::raw::{c_char, c_int, c_uint};

// Простая функция для демонстрации - сложение двух чисел
#[no_mangle]
pub extern "C" fn rust_add_numbers(a: c_int, b: c_int) -> c_int {
    a + b
}

// ... остальные функции
```

#### 3. Добавлен .cargo/config.toml
```toml
[build]
# Конфигурация для сборки под ESP32 с официальными образами Espressif

[target.xtensa-esp32-espidf]
linker = "ldproxy"

[target.xtensa-esp32s3-espidf]
linker = "ldproxy"

[target.riscv32imc-esp-espidf]
linker = "ldproxy"

[env]
# Переменные окружения для ESP32
ESP_IDF_VERSION = { value = "5.2.3" }

# Настройки для статической библиотеки
[target.'cfg(target_os = "espidf")']
rustflags = [
    # Оптимизации для размера
    "-C", "opt-level=s",
    # Отключаем неиспользуемый код
    "-C", "lto=fat",
]
```

#### 4. Обновлен build.rs
```rust
fn main() {
    // Минимальный build.rs для статической библиотеки
    println!("cargo:rerun-if-changed=src/lib.rs");
}
```

#### 5. Удален rust-toolchain.toml
Файл `rust-toolchain.toml` удален, так как официальные образы Espressif уже содержат правильно настроенный ESP Rust toolchain. Наличие этого файла вызывает конфликты.

## Тестирование исправлений

### 1. Быстрый тест среды
```bash
./.scripts/test_rust_build.sh
```

### 2. No_std сборка (наиболее надежная)
```bash
./.scripts/build_rust_no_std.sh
```

### 3. Минимальная сборка с build-std
```bash
./.scripts/build_rust_minimal.sh
```

### 4. Полная сборка с диагностикой
```bash
./.scripts/build_rust_in_docker.sh
```

### 5. Проверка результата
```bash
ls -la rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a
```

## Альтернативные решения

### Если проблема сохраняется

#### Вариант 1: Использование конкретной версии образа
```bash
# В скрипте build_rust_in_docker.sh замените:
DOCKER_IMAGE="espressif/idf-rust:esp32s3_1.88.0.0"
```

#### Вариант 2: Минимальная Rust библиотека
Создайте совсем простую версию без внешних зависимостей:

```rust
// src/lib.rs - минимальная версия
#![no_std]
#![no_main]

use core::panic::PanicInfo;

#[panic_handler]
fn panic(_info: &PanicInfo) -> ! {
    loop {}
}

#[no_mangle]
pub extern "C" fn rust_add_numbers(a: i32, b: i32) -> i32 {
    a + b
}
```

#### Вариант 3: Использование esp-idf-template
```bash
# Создание проекта с шаблоном
cargo generate esp-rs/esp-idf-template
```

## Проверка совместимости

### Проверка версий в контейнере
```bash
docker run --rm espressif/idf-rust:esp32s3_latest /bin/bash -c "
    rustc --version
    cargo --version
    rustup target list --installed | grep xtensa
"
```

### Проверка доступных образов
```bash
# Поиск доступных тегов
docker search espressif/idf-rust

# Или проверьте на Docker Hub:
# https://hub.docker.com/r/espressif/idf-rust/tags
```

## Дополнительные советы

### 1. Очистка кэша
```bash
# Очистка Cargo кэша
rm -rf .cargo_cache rust_lib/target

# Очистка Docker кэша
docker system prune -f
```

### 2. Отладка сборки
```bash
# Запуск с подробным выводом
cargo build --release --target=xtensa-esp32s3-espidf --verbose
```

### 3. Проверка символов
```bash
# После успешной сборки
nm rust_lib/target/xtensa-esp32s3-espidf/release/liba630_rust_lib.a | grep rust_
```

## Контакты для поддержки

- [ESP-RS Book](https://docs.esp-rs.org/book/)
- [ESP-IDF Rust GitHub](https://github.com/esp-rs/esp-idf-template)
- [Espressif Docker Images](https://hub.docker.com/r/espressif/idf-rust)
