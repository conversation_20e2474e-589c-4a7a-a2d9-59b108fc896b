# Интеграция Rust в проект A630

Этот документ описывает, как настроить и использовать Rust код в проекте ESP32.

## Предварительные требования

### 1. Установка Rust
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

### 2. Установка ESP32 Rust toolchain
```bash
# Установка espup для управления ESP32 Rust toolchain
cargo install espup
espup install

# Источник переменных окружения (добавьте в ~/.bashrc или ~/.zshrc)
source ~/export-esp.sh
```

### 3. Установка дополнительных компонентов
```bash
rustup component add rust-src
rustup target add xtensa-esp32-espidf
```

## Структура проекта

```
a630_aroma/
├── rust_lib/                    # Rust библиотека
│   ├── src/
│   │   └── lib.rs              # Основной код Rust библиотеки
│   ├── include/
│   │   └── rust_functions.h    # C заголовочный файл
│   ├── Cargo.toml              # Конфигурация Rust проекта
│   ├── build.rs                # Скрипт сборки
│   ├── rust-toolchain.toml     # Конфигурация toolchain
│   └── CMakeLists.txt          # CMake конфигурация для Rust
├── main/
│   ├── main.cpp                # Основной файл (модифицирован)
│   └── CMakeLists.txt          # CMake конфигурация (модифицирована)
└── CMakeLists.txt              # Основной CMake файл (модифицирован)
```

## Доступные Rust функции

В файле `rust_lib/src/lib.rs` реализованы следующие функции:

1. **rust_add_numbers(a, b)** - сложение двух чисел
2. **rust_string_length(s)** - подсчет длины C-строки
3. **rust_factorial(n)** - вычисление факториала
4. **rust_is_even(n)** - проверка четности числа
5. **rust_find_max(arr, len)** - поиск максимума в массиве

## Сборка проекта

### 1. Настройка переменных окружения
```bash
# Убедитесь, что переменные ESP32 Rust настроены
source ~/export-esp.sh

# Убедитесь, что IDF_PATH установлен
export IDF_PATH=/path/to/esp-idf
```

### 2. Сборка проекта
```bash
cd /path/to/a630_aroma
idf.py build
```

### 3. Прошивка устройства
```bash
idf.py flash monitor
```

## Добавление новых Rust функций

### 1. Добавьте функцию в rust_lib/src/lib.rs
```rust
#[no_mangle]
pub extern "C" fn rust_new_function(param: c_int) -> c_int {
    // Ваша реализация
    param * 2
}
```

### 2. Добавьте объявление в rust_lib/include/rust_functions.h
```c
int32_t rust_new_function(int32_t param);
```

### 3. Используйте функцию в C++ коде
```cpp
#include "rust_functions.h"

int result = rust_new_function(42);
```

## Отладка

### Проверка сборки Rust библиотеки
```bash
cd rust_lib
cargo build --target=xtensa-esp32-espidf --release
```

### Проверка статической библиотеки
```bash
ls -la rust_lib/target/xtensa-esp32-espidf/release/liba630_rust_lib.a
```

## Возможные проблемы и решения

### 1. Ошибка "xtensa-esp32-espidf target not found"
```bash
rustup target add xtensa-esp32-espidf
```

### 2. Ошибка компиляции Rust кода
- Убедитесь, что переменные окружения ESP32 настроены
- Проверьте версию Rust toolchain (должна быть nightly)

### 3. Ошибки линковки
- Убедитесь, что статическая библиотека создается корректно
- Проверьте пути в CMakeLists.txt файлах

## Производительность

Rust код компилируется с оптимизациями для размера (`opt-level = "s"`), что подходит для микроконтроллеров с ограниченной памятью.

## Дальнейшее развитие

1. Добавление более сложных алгоритмов на Rust
2. Использование Rust для критически важных по производительности частей
3. Интеграция с ESP32-специфичными библиотеками Rust
4. Постепенный перевод существующего C++ кода на Rust
