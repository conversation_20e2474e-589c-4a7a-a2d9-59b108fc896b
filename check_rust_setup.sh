#!/bin/bash

echo "=== Проверка настройки Rust для ESP32 ==="

# Проверка установки Rust
echo "1. Проверка Rust..."
if command -v rustc &> /dev/null; then
    echo "✓ Rust установлен: $(rustc --version)"
else
    echo "✗ Rust не установлен"
    exit 1
fi

# Проверка Cargo
echo "2. Проверка Cargo..."
if command -v cargo &> /dev/null; then
    echo "✓ Cargo установлен: $(cargo --version)"
else
    echo "✗ Cargo не установлен"
    exit 1
fi

# Проверка nightly toolchain
echo "3. Проверка nightly toolchain..."
if rustup toolchain list | grep -q nightly; then
    echo "✓ Nightly toolchain установлен"
else
    echo "✗ Nightly toolchain не установлен"
    echo "Установите командой: rustup toolchain install nightly"
fi

# Проверка ESP32 target
echo "4. Проверка ESP32 target..."
if rustup target list --installed | grep -q xtensa-esp32-espidf; then
    echo "✓ ESP32 target установлен"
else
    echo "✗ ESP32 target не установлен"
    echo "Установите командой: rustup target add xtensa-esp32-espidf"
fi

# Проверка rust-src компонента
echo "5. Проверка rust-src..."
if rustup component list --installed | grep -q rust-src; then
    echo "✓ rust-src установлен"
else
    echo "✗ rust-src не установлен"
    echo "Установите командой: rustup component add rust-src"
fi

# Проверка переменных окружения ESP32
echo "6. Проверка переменных окружения ESP32..."
if [ -n "$LIBCLANG_PATH" ] && [ -n "$ESP_IDF_VERSION" ]; then
    echo "✓ ESP32 переменные окружения настроены"
    echo "  LIBCLANG_PATH: $LIBCLANG_PATH"
    echo "  ESP_IDF_VERSION: $ESP_IDF_VERSION"
else
    echo "✗ ESP32 переменные окружения не настроены"
    echo "Выполните: source ~/export-esp.sh"
fi

# Проверка IDF_PATH
echo "7. Проверка IDF_PATH..."
if [ -n "$IDF_PATH" ] && [ -d "$IDF_PATH" ]; then
    echo "✓ IDF_PATH настроен: $IDF_PATH"
else
    echo "✗ IDF_PATH не настроен или директория не существует"
fi

# Тест сборки Rust библиотеки
echo "8. Тест сборки Rust библиотеки..."
cd rust_lib
if cargo check --target=xtensa-esp32-espidf &> /dev/null; then
    echo "✓ Rust библиотека компилируется без ошибок"
else
    echo "✗ Ошибки при компиляции Rust библиотеки"
    echo "Запустите 'cargo check --target=xtensa-esp32-espidf' для подробностей"
fi
cd ..

echo ""
echo "=== Проверка завершена ==="
