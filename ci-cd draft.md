
## CI/CD интеграция

### GitHub Actions
```yaml
name: Build A630 with Rust

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        target: [esp32, esp32s3, esp32c3]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Build for ${{ matrix.target }}
      run: |
        chmod +x .scripts/build_with_espressif_images.sh
        ESP_TARGET=${{ matrix.target }} ./.scripts/build_with_espressif_images.sh
    
    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: firmware-${{ matrix.target }}
        path: build-artifacts-target-${{ matrix.target }}/
```

### GitLab CI
```yaml
stages:
  - build

variables:
  DOCKER_DRIVER: overlay2

build_esp32s3:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  script:
    - chmod +x .scripts/build_with_espressif_images.sh
    - ./.scripts/build_with_espressif_images.sh
  artifacts:
    paths:
      - build-artifacts-target-esp32s3/
    expire_in: 1 week
```

## Отладка проблем

### Проверка доступности образов
```bash
# Проверка доступных тегов
docker search espressif/idf-rust

# Загрузка конкретного образа
docker pull espressif/idf-rust:esp32s3_latest

# Список локальных образов
docker images | grep espressif
```

### Интерактивная отладка
```bash
# Запуск интерактивного контейнера для отладки
docker run -it --rm \
  -v $PWD:/workspace \
  -w /workspace/rust_lib \
  espressif/idf-rust:esp32s3_latest \
  /bin/bash

# Внутри контейнера:
# rustc --version
# cargo --version
# rustup target list --installed
# cargo build --release --target=xtensa-esp32s3-espidf
```

### Проверка переменных окружения
```bash
# Проверка настроек ESP-IDF в контейнере
docker run --rm \
  -v $PWD:/workspace \
  espressif/esp-matter:latest_idf_v5.2.3 \
  /bin/bash -c "env | grep -E '(IDF|ESP)'"
```

## Оптимизация производительности

### Кэширование Docker слоев
```bash
# Предварительная загрузка образов
docker pull espressif/idf-rust:esp32s3_latest
docker pull espressif/esp-matter:latest_idf_v5.2.3

# Использование Docker BuildKit для ускорения
export DOCKER_BUILDKIT=1
```

### Параллельная сборка
```bash
# Сборка для нескольких платформ параллельно
ESP_TARGET=esp32 ./.scripts/build_with_espressif_images.sh &
ESP_TARGET=esp32s3 ./.scripts/build_with_espressif_images.sh &
ESP_TARGET=esp32c3 ./.scripts/build_with_espressif_images.sh &
wait
```

### Мониторинг ресурсов
```bash
# Мониторинг использования Docker
docker stats

# Очистка неиспользуемых ресурсов
docker system prune -f
```

## Интеграция с IDE

### VS Code DevContainer
Создайте `.devcontainer/devcontainer.json`:
```json
{
  "name": "A630 Rust Development",
  "image": "espressif/idf-rust:esp32s3_latest",
  "workspaceFolder": "/workspace",
  "mounts": [
    "source=${localWorkspaceFolder},target=/workspace,type=bind"
  ],
  "customizations": {
    "vscode": {
      "extensions": [
        "rust-lang.rust-analyzer",
        "espressif.esp-idf-extension"
      ]
    }
  }
}
```
