/* include directives ---------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>

#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_timer.h"



/* local defines for constants ----------------------------------------------*/
#define SAVE_PERIOD (30*60) //in seconds  

namespace bsh_sys::ble_internal
{

static const char *TAG = "-ble-";
static const char *SECOND_TAG = "=up_time=";



/* local typedef declarations -----------------------------------------------*/



/* local objects declarations -----------------------------------------------*/
static uint32_t up_time = 0;
static bool timer_initialized = false;
static bool time_loaded = false;
static esp_timer_handle_t timer;



/*=================================================================*\
 * local function declarations
\*=================================================================*/
static void save_time (uint32_t time);
static uint32_t load_time();
static void init_timer();
static void timer_handler (void *);



/*=================================================================*\
 * exported functions definitions
\*=================================================================*/
uint32_t up_time_get ()
{
    if (!time_loaded) up_time = load_time();
    return up_time;
}



void up_time_reset ()
{
    up_time = 0;
    save_time(0);
    ESP_LOGI(TAG,"%s reset", SECOND_TAG);
}



void up_time_start_clock()
{
    if (!time_loaded) up_time = load_time();
    if (!timer_initialized) init_timer();
    esp_timer_stop (timer);
    esp_timer_start_periodic (timer, 1000000); // every second
    ESP_LOGI(TAG,"%s start", SECOND_TAG);
}



void up_time_stop_clock()
{
    if (!timer_initialized) init_timer();
    esp_timer_stop (timer);
    ESP_LOGI(TAG,"%s stop", SECOND_TAG);
}



/*=================================================================*\
 * local function definitions
\*=================================================================*/
static void save_time (uint32_t time)
{
    nvs_handle _handle;

    ESP_ERROR_CHECK(nvs_open("ble_cfg", NVS_READWRITE, &_handle));
    ESP_LOGI (TAG, "%s saved: %lu",SECOND_TAG, time);

    nvs_set_u32(_handle, "up_time", time);
    nvs_commit(_handle);
    nvs_close(_handle);
}



static uint32_t load_time()
{
    nvs_handle _handle;
    esp_err_t err;
    uint32_t time;    
    
    ESP_ERROR_CHECK(nvs_open("ble_cfg", NVS_READWRITE, &_handle));
    
    err = nvs_get_u32(_handle, "up_time", &time);
    if (err != ESP_OK) 
    {
        ESP_LOGW(TAG, "%s cant load up time. set to 0", SECOND_TAG);
        nvs_set_u32(_handle, "up_time", 0);
        nvs_commit(_handle);
        nvs_close(_handle);
        time_loaded = true;
        return 0;

    } else {
        nvs_close(_handle);
        ESP_LOGI (TAG, "%s loaded ble up time: %lu", SECOND_TAG, time);
        time_loaded = true;
        return time;
    }
}



static void init_timer()
{
	const esp_timer_create_args_t timer_args = {timer_handler, 0, {}, 0, 0};

	int ret = 0;
	if((ret = esp_timer_create(&timer_args, &timer)) != ESP_OK) {
        ESP_LOGE(TAG,"cant create timer %d", ret);
        return;
    }	
    timer_initialized = true;
}



static void timer_handler (void *)
{
    up_time ++;
    if (up_time % SAVE_PERIOD == 0) save_time(up_time);
}

}   // bsh_sys::ble_internal namespace