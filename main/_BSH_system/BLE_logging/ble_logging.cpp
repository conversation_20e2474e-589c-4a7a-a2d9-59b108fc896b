#include <string.h>

#include "esp_log.h"

#include "ble_logging.h"
#include "bluetooth_LE.h"
#include "system.h"
#include "commissioning.h"
#include "mqtt_logs.h"
#include "logs_levels.h"
#include "SYS_LOG_LEVELS.h"



#define ARR_SIZE(arr) (sizeof(arr) / sizeof((arr)[0]))



namespace bsh_sys::ble_logging {



static const char *TAG = "-ble_logging-";

typedef enum {
    SET,
    GET,
    CONNECT,
    HELP,
    LOGS,
    LOGS_LEVEL,
    MQTT_LOGS,
    COMMANDS_QTY,
} command_type_t;

typedef struct {
    command_type_t type;
    const char *text;
    size_t arguments_qty;
    size_t arguments2_qty;
    const char **possible_arguments;
    const char **possible_arguments2;
} command_t;



// Аргументы команд
static const char *set_args[]        = {"wifi_ssid", "wifi_pswd", "balancer_link"};
static const char *get_args[]        = {"device_info"};
static const char *connect_args[]    = {"wifi", "balancer"};
// static const char *help_args[]       = {NULL}; // пусто
static const char *logs_args[]       = {"on", "off", "save", "load", "print"};
static const char *mqtt_logs_args[]  = {"off", "err", "warn", "info"};
static const char *log_lvl_2__levels[] = {"off", "err", "warn", "info"};
static const char *log_lvl_1__modules[] = {
                            default_log_levels[0].tag,
                            default_log_levels[1].tag,
                            default_log_levels[2].tag,
                            default_log_levels[3].tag,
                            default_log_levels[4].tag,
                            default_log_levels[5].tag,
                            default_log_levels[6].tag,
                            };


// Список команд
command_t commands[] = {
    {SET,        "set",             ARR_SIZE(set_args),           0, set_args,      NULL},
    {GET,        "get",             ARR_SIZE(get_args),           0, get_args,      NULL},
    {CONNECT,    "connect",         ARR_SIZE(connect_args),       0, connect_args,  NULL},
    {HELP,       "help",                              0,          0, NULL,          NULL},
    {LOGS,       "logs",            ARR_SIZE(logs_args),          0, logs_args,     NULL},
    {LOGS_LEVEL, "logs_level",      ARR_SIZE(log_lvl_1__modules), 
                                        ARR_SIZE(log_lvl_2__levels), 
                                        log_lvl_1__modules, log_lvl_2__levels},
    {MQTT_LOGS,  "mqtt_logs_level", ARR_SIZE(mqtt_logs_args),     0, mqtt_logs_args, NULL},
};



static void print_help();



void log_to_ble(const char * message)
{
    bsh_sys::ble::send_to_logging_char(message);
}



void treat_ble_command(uint8_t *data, uint16_t length)
{
    ESP_LOGW(TAG, "ble command: %s", (char *)data);
    ESP_LOG_BUFFER_HEXDUMP(TAG, (void *)data, length, ESP_LOG_INFO);

    // first token - command
    char *command = strtok((char *)data, " ");
    if (command == NULL)
    {
        ESP_LOGE(TAG,"command not found");
        print_help();
        return;
    }
    
    // check if its existing command
    command_type_t command_index = COMMANDS_QTY;
    for(size_t i = 0; i < COMMANDS_QTY; i++)
    {
        if (strcmp(commands[i].text, command) == 0)
        {
            command_index = (command_type_t)i;
            ESP_LOGI(TAG, "command type: %s, index %i", commands[i].text, i);
            break;
        }
    }

    if(command_index >= COMMANDS_QTY)
    {
        ESP_LOGE(TAG, "unknown command");
        print_help();
        return;
    }


    // next token - parameter
    char *parameter = strtok(NULL, " ");  // NULL means next search on same data

    // find parameter index
    uint8_t parameter_index = 0xFF;
    for (size_t i = 0; i < commands[command_index].arguments_qty; i++) {
        const char *arg = commands[command_index].possible_arguments[i];
        if (arg && strcmp(arg, parameter) == 0) {
            ESP_LOGI(TAG, "parameter index: %zu: %s", i, parameter);
            parameter_index = i;
            break;
        }
    }
    if (parameter_index == 0xFF && command_index != HELP) 
    {
        ESP_LOGE(TAG,"unknown parameter");
        print_help();
        return;
    }
    if(parameter == NULL && command_index != HELP) 
    {
        ESP_LOGE(TAG,"no parameter");
        print_help();
        return;  // for now all commands except help have parameter
    }

    // execute command
    switch (command_index)
    {
    case SET:
        switch (parameter_index)
        {
            case 0:  //  "wifi_ssid"
                bsh_sys::commiss::set_wifi_ssid(strtok(NULL, " "));
                break; 
            case 1:  //  "wifi_pswd"
                bsh_sys::commiss::set_wifi_passw(strtok(NULL, " "));
                break;
            case 2:  //  "balancer_link"
                bsh_sys::commiss::set_balancer_link(strtok(NULL, " "));
                break;
        }
        break;
    case GET:
        if(parameter_index == 0)
        {
            bsh_sys::print_app_info();
        }
        break;

    case CONNECT:
        switch (parameter_index)
        {
        case 0:
            bsh_sys::no_autoconnect();
            bsh_sys::conn_to_wifi();
            break;
        
        case 1:
            bsh_sys::no_autoconnect();
            bsh_sys::conn_to_balancer();
            break;
        default:
            break;
        }
        break;

    case HELP:
        print_help();
        break;

    case LOGS:
        if(parameter_index == 0 || parameter_index == 1) bsh_sys::log_levels::turns_logs_on_off(parameter_index == 0);
        if(parameter_index == 2) bsh_sys::log_levels::save_log_levels();
        if(parameter_index == 3) bsh_sys::log_levels::load_log_levels();
        if(parameter_index == 4) bsh_sys::log_levels::print_log_levels();

        break;

    case MQTT_LOGS:
        bsh_sys::mqtt_logs::set_log_level((bsh_sys::mqtt_logs::mqtt_log_level_t)parameter_index);
        break;

    case LOGS_LEVEL:
        {
        char *parameter2 = strtok(NULL, " ");
        
        if (parameter2 == NULL)
        {
            ESP_LOGE(TAG,"missing second parameter for 'set log level' command");
            print_help();
            return;
        }

        uint8_t parameter2_index = 0xFF;
        for (size_t i = 0; i < commands[command_index].arguments2_qty; i++) {
            const char *arg = commands[command_index].possible_arguments2[i];
            if (arg && strcmp(arg, parameter2) == 0) {
                ESP_LOGI(TAG, "parameter index: %zu: %s", i, parameter2);
                parameter2_index = i;
                break;
            }
        }

        if (parameter2_index == 0xFF) 
        {
            ESP_LOGE(TAG,"unknown second parameter");
            print_help();
            return;
        }

        bsh_sys::log_levels::set_log_level((bsh_sys::log_levels::module_t)parameter_index, (esp_log_level_t)parameter2_index);
        }
        break;
    
    case COMMANDS_QTY:
        break;
    }
}



static void print_help()
{
    if(!bsh_sys::log_levels::are_logs_on()) return;

    log_to_ble("Commands format is 'command [parameter] [value]'");
    log_to_ble("Possible commands are:");
    char tmp[200] = {};
    for (size_t i = 0; i < COMMANDS_QTY; i++)
    {
        snprintf(tmp, sizeof(tmp), "    command: %s", commands[i].text);
        log_to_ble(tmp);
        tmp[0] = '\0';

        if(commands[i].possible_arguments == NULL) continue;
        // first parameter
        log_to_ble("              parameters:");
        snprintf(tmp + strlen(tmp) , sizeof(tmp), "                ");
        for (size_t j = 0; j < commands[i].arguments_qty; j++)
        {
            snprintf(tmp + strlen(tmp) , sizeof(tmp), " %s | ", commands[i].possible_arguments[j]);
        }
        log_to_ble(tmp);
        tmp[0] = '\0';

        // second parameter
        if(commands[i].possible_arguments2 == NULL) continue;
        log_to_ble("              second parameter:");
        snprintf(tmp + strlen(tmp) , sizeof(tmp), "                 ");
        for (size_t j = 0; j < commands[i].arguments2_qty; j++)
        {
            snprintf(tmp + strlen(tmp), sizeof(tmp), " %s | ", commands[i].possible_arguments2[j]);
        }
        log_to_ble(tmp);
    }
}


} // bsh_sys::ble_logging