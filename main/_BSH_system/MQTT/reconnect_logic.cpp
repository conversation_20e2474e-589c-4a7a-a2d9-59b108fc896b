#include <stdint.h>

#include "mqtt_client.h"
#include "esp_timer.h"
#include "esp_log.h"
#include "esp_random.h"

#include "reconnect_logic.h"




#define WRONG_PASSW_THRESH 7
#define INIT_RECONNECT_TIME 15 // s
#define MAX_RECONNECT_TIME 16*60 // s



namespace bsh_sys::mqtt {



/* local objects declarations -----------------------------------------------*/
static const char *TAG = "-mqtt_task-";
static esp_mqtt_connect_return_code_t last_err_code = MQTT_CONNECTION_ACCEPTED;
static uint8_t wrong_passw_counter = 0;
static int prev_reconnect_time = 0;   // s
static void (*reconnect_cb)() = NULL;
static esp_timer_handle_t reconnect_timer = NULL;
static bool init_done = false;


/* local function declarations  ------------------------------------ */
static int get_next_reconnect_time(int prev_time);
static int get_jitter();
static void reconnect_timer_cb(void* arg);



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
void reconnect_init(void (*connect_cb)())
{
    if(connect_cb == NULL)
    {
        ESP_LOGE(TAG, "null ptr");
        init_done = false;
        return;
    }
    reconnect_cb = connect_cb;

    esp_timer_create_args_t timerArgs = {0};
    timerArgs.callback = reconnect_timer_cb;
    ESP_ERROR_CHECK(esp_timer_create(&timerArgs, &reconnect_timer));

    init_done = true;
}



void connect_result(int result)
{
    if (!init_done) return;

    switch (result)
    {
    case MQTT_CONNECTION_ACCEPTED:
        stop_reconnecting();
        reset_reconnect_count();
        break;
    case MQTT_CONNECTION_REFUSE_PROTOCOL:                
    case MQTT_CONNECTION_REFUSE_SERVER_UNAVAILABLE:  
        prev_reconnect_time = get_next_reconnect_time(prev_reconnect_time);
        esp_timer_start_once(reconnect_timer, prev_reconnect_time * 1000000);    
        break;

    case MQTT_CONNECTION_REFUSE_ID_REJECTED:             
    case MQTT_CONNECTION_REFUSE_BAD_USERNAME: 
    case MQTT_CONNECTION_REFUSE_NOT_AUTHORIZED:
        wrong_passw_counter++;
        if(wrong_passw_counter >= WRONG_PASSW_THRESH)
        {
            stop_reconnecting();
        } else {
            esp_timer_start_once(reconnect_timer, INIT_RECONNECT_TIME * 1000000);    
        }
        break;
    }
}



void reset_reconnect_count()
{
    if (!init_done) return;
    prev_reconnect_time = 0;
    wrong_passw_counter = 0;
}



void start_reconnecting()
{
    if (!init_done) return;
    
    reset_reconnect_count();
    esp_timer_start_once(reconnect_timer, prev_reconnect_time* 1000000);
}



void stop_reconnecting()
{
    if (!init_done) return;
    esp_timer_stop(reconnect_timer);
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static int get_next_reconnect_time(int prev_time)
{
    if(prev_reconnect_time == 0) return INIT_RECONNECT_TIME;

    int time = prev_time * 2;
    if (time > MAX_RECONNECT_TIME) time = MAX_RECONNECT_TIME;
    ESP_LOGI (TAG,"next reconn time =%i", time);
    time = time + time * get_jitter() / 100;
    ESP_LOGI (TAG,"time with jitter=%i", time);

    return time;
}



static void reconnect_timer_cb(void *arg)
{
    if(reconnect_cb != NULL)
    {
        reconnect_cb();
    } else {
        ESP_LOGE(TAG,"null ptr");
    }
}



// from -10 to 10
static int get_jitter()
{
    uint32_t rnd = esp_random();
    rnd = rnd % 20;
    int res = rnd - 10;
    return res;
}



} // end of bsh_sys::mqtt namespace
