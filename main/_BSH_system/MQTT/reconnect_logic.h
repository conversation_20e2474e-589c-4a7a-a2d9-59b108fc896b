/**
 *    Reconnect logic is:
 *      if Wrong device password err - try 7 times then stop. 
 *      continue after reboot or device reconnect to system
*/


namespace bsh_sys::mqtt {

    // doesn't start connecting. just init
    // calls connect_cb when its time to reconnect
    void reconnect_init(void (*connect_cb)());

    void start_reconnecting(); // not used at the moment. module started by calling connect_result() after first connect attmpt
    void stop_reconnecting();

    // to provide connection result
    void connect_result(int result);

    void reset_reconnect_count();

}

