#include <string.h>

#include "esp_err.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "freertos/task.h"

#include "uart.h"
#include "mqtt_logs.h"



#define QUEUE_SIZE 3 // uart incoming internal queue size
#define UART_DIRVER_BUF_SIZE 512 // uart internal buffer size and also this module buffer size
#define THIS_MODULE_BUFFER_SIZE 1024



namespace bsh_sys::uart{



/* ======= local defines for constants =========*/
static const char *TAG = "-uart-";

static uint8_t *uart_in_buffer = NULL;
static uint8_t *free_place_in_buffer = NULL;



/* ======= local object declarations =========*/
static uart_settings_t *uart_settings = NULL;
static TaskHandle_t     uart_task_handle;
static QueueHandle_t    uart_in_queue;   // driver puts data here
static void             (* in_data_cb)(const uint8_t *data, const uint16_t data_len) = NULL;
static bool             initialized = false;


/* ======= local function declarations ========= */
static void uart_in_task(void *);
static void uart_out_task(void *);



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
esp_err_t start(uart_settings_t *settings)
{
    // TODO check settings correctness
    uart_settings = settings;
    in_data_cb = settings->incoming_data_cb;
    initialized = true;

    // Install UART driver, and get the queue.
    // uart queue object size is sizeof(uart_event_t)   i.e. not big
    // When TX buffer size is set to 0, uart_write_bytes call will block untill data is sent
    uart_driver_install(settings->uart_N, UART_DIRVER_BUF_SIZE, 0, QUEUE_SIZE, &uart_in_queue, 0);
    uart_param_config(settings->uart_N, &uart_settings->uart_config);

    //Set UART pins (using UART0 default pins ie no changes.)
    uart_set_pin(settings->uart_N, settings->tx_pin, settings->rx_pin, 
                   settings->rts_pin, settings->cts_pin);

    esp_err_t ret;
    ret = xTaskCreate(uart_in_task, "uart_in_task", 4096, NULL, 12 + 2, &uart_task_handle); // task with higher priority, to react quick
    if (ret != pdPASS) 
    {
        ESP_LOGE(TAG, "Failed to initialize uart in task (%s), restarting device...", esp_err_to_name(ret));
        esp_restart();
    }
    ESP_LOGI(TAG,"uart in task created");
    ret = xTaskCreate(uart_out_task, "uart_out_task", 4096, NULL, 12, &uart_task_handle); // task with higher priority, to react quick
    if (ret != pdPASS) 
    {
        ESP_LOGE(TAG, "Failed to initialize uart out task (%s), restarting device...", esp_err_to_name(ret));
        esp_restart();
    }
    ESP_LOGI(TAG,"uart out task created");

    return ESP_OK;
}



void change_settings(uart_settings_t *settings)
{
    // TODO
}



void put_to_queue(const uint8_t *data, uint16_t data_len)
{
    if (!initialized) 
    {
        ESP_LOGE (TAG,"cant sent to uart - not initialized");
        return;
    }
    
    if (data_len >= UART_OBJ_SIZE)
    {
        ESP_LOGE (TAG,"too many bytes to send");
        // still sends what fits
    }
    bsh_sys::uart::uart_out_queue_obj_t to_send;
    to_send.data_size = data_len;
    memcpy(to_send.data, data, data_len <= UART_OBJ_SIZE ? data_len : UART_OBJ_SIZE);
    xQueueSend(uart_settings->sending_queue, &to_send ,0);
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static void uart_in_task(void *)
{   
    uart_event_t event;
    uart_in_buffer = (uint8_t*)malloc(THIS_MODULE_BUFFER_SIZE);
    free_place_in_buffer = uart_in_buffer;
    int length = 0;
    
    while (1)
    {
        if (xQueueReceive(uart_in_queue, (void *)&event, (TickType_t)portMAX_DELAY))
        {
            switch (event.type) {
            case UART_DATA:
                ESP_ERROR_CHECK(uart_get_buffered_data_len(uart_settings->uart_N, (size_t*)&length));
                uart_read_bytes(uart_settings->uart_N, free_place_in_buffer, length, portMAX_DELAY);
                free_place_in_buffer += length;
                ESP_LOGI(TAG, "\n\n<---- UART in data: %d bytes  or %i bytes", event.size, length);
                ESP_LOG_BUFFER_HEXDUMP(TAG, uart_in_buffer, length, ESP_LOG_INFO);
                break;

            case UART_FIFO_OVF:
                ESP_LOGE(TAG, "    hw fifo overflow");
                // If fifo overflow happened, you should consider adding flow control for your application.
                // The ISR has already reset the rx FIFO,
                // As an example, we directly flush the rx buffer here in order to read more data.
                uart_flush_input(uart_settings->uart_N);
                xQueueReset(uart_in_queue);
                break;
            // Event of UART ring buffer full
            case UART_BUFFER_FULL:
                ESP_LOGE(TAG, "    ring buffer full");
                // If buffer full happened, you should consider encreasing your buffer size
                // As an example, we directly flush the rx buffer here in order to read more data.
                uart_flush_input(uart_settings->uart_N);
                xQueueReset(uart_in_queue);
                break;
            // Event of UART RX break detected
            case UART_BREAK: ESP_LOGE(TAG, "    uart rx break"); break;
            // Event of UART parity check error
            case UART_PARITY_ERR: ESP_LOGE(TAG, "    uart parity error"); break;
            // Event of UART frame error
            case UART_FRAME_ERR: ESP_LOGE(TAG, "    uart frame error"); break;
            // UART_PATTERN_DET
            case UART_PATTERN_DET: ESP_LOGE(TAG, "    uart pattern detected");break;
            // Others
            default: ESP_LOGW(TAG, "    uart event type: %d", event.type); break;
            }
        }

        // treating data in buffer
        if(uart_in_buffer != free_place_in_buffer)
        {
            in_data_cb(uart_in_buffer, free_place_in_buffer - uart_in_buffer);
            bzero(uart_in_buffer, THIS_MODULE_BUFFER_SIZE);
            free_place_in_buffer = uart_in_buffer;
        }


        vTaskDelay (pdMS_TO_TICKS(10));
    }
}


static void uart_out_task(void *)
{

    static uart_out_queue_obj_t to_send;
    
    while (1)
    {
        xQueueReceive(uart_settings->sending_queue, &to_send, portMAX_DELAY);
        if (to_send.data_size > UART_OBJ_SIZE)
        {
            ESP_LOGE(TAG,"too big uart out packet");
            ESP_LOG_BUFFER_HEXDUMP (TAG, &to_send, 50, ESP_LOG_WARN);
            continue;
        }
        // this call will block untill data is sent, coz TX buffer is set to 0
        int sent = uart_write_bytes(uart_settings->uart_N, &to_send.data, (size_t)to_send.data_size);
        
        ESP_LOGI (TAG, "\n\n------>  sent  %i bytes to UART", sent);
        ESP_LOG_BUFFER_HEXDUMP (TAG, to_send.data, to_send.data_size, ESP_LOG_INFO);

        if(sent == -1) bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_ERR, TAG, "err sending uart pkt", NULL, 0);

        vTaskDelay (pdMS_TO_TICKS(100));
    }
}



}   // bsh_sys::uart namespace