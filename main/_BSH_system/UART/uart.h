/**
 * This module deals with UART send/recieve.
 * 
 * Data to send is picked from uart_out queue (injected at initializastion at uart_settings_t).
 * Object is the queue is uart_out_queue_obj_t, of UART_OBJ_SIZE + 2(data size) bytes size
 * <PERSON><PERSON><PERSON> starts separate task to send data, and it blocks till packet from queue is sent.
 *  * There is no direct function to send data, as it would be blocking.
 * 
 * Received data is put into uart RX buffer. 
 * Uart incoming queue contains a pointer to this buffer, to keep queue obj small(dont copy data).
 * Its assumed incoming data will be treated fast, hence the approach (and small incoming queue).
 * Module creates dedicated task to deal with incoming data.
 * The task calls "incoming_data_cb" (injected at init) to treat data.
 */ 



#pragma once

#include <stdbool.h>
#include <stdint.h>

#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "driver/uart.h"

#define UART_OBJ_SIZE 250



namespace bsh_sys::uart {



typedef struct     // data to be injected at init
{
    QueueHandle_t sending_queue;  // this queue contains raw data to be sent to MCU
    uart_config_t uart_config;    //esp uart config struct. sets baud, etc

    // pins config
    uart_port_t uart_N;
    int8_t tx_pin;
    int8_t rx_pin;
    int8_t rts_pin;
    int8_t cts_pin;

    // this is callback to be called when data received
    // make this function fast-returning
    void (* incoming_data_cb)(const uint8_t *data, const uint16_t data_len);
} uart_settings_t; 



typedef struct     // incoming queue data object
{
    uint16_t data_size;
    char data[UART_OBJ_SIZE];
} uart_out_queue_obj_t;



/**
 * @brief Initializes UART per passed settings
 */
esp_err_t start(uart_settings_t *settings);



/**
 * @brief chages UART settings
 */
// void change_settings(uart_settings_t *settings);   TODO not implemented



/**
 * @brief Wraps raw data into uart_out_queue_obj_t object,
 *        and puts into queue
 */
void put_to_queue (const uint8_t *data, uint16_t data_len);



} // bsh_sys::uart namespace