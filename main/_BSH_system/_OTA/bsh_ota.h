#pragma once

#include "esp_event.h"
#include "esp_http_client.h"

#define OTA_BUFFER_SIZE 1024    // bigger buffer doesnt increase speed any more. also big buffer drives wifi disconnect somehow?



namespace bsh_sys::ota {



ESP_EVENT_DECLARE_BASE(OTA_EVENTS);

typedef enum {
    OTA_EVT_HTTP_MAX_REDIRECT = ESP_ERR_HTTP_MAX_REDIRECT - ESP_ERR_HTTP_BASE,              /*!< The error exceeds the number of HTTP redirects */
    OTA_EVT_HTTP_ERR_CONNECT = ESP_ERR_HTTP_CONNECT - ESP_ERR_HTTP_BASE,               /*!< Error open the HTTP connection */
    OTA_EVT_HTTP_ERR_WRITE_DATA = ESP_ERR_HTTP_WRITE_DATA - ESP_ERR_HTTP_BASE,            /*!< Error write HTTP data */
    OTA_EVT_HTTP_ERR_FETCH_HEADER = ESP_ERR_HTTP_FETCH_HEADER - ESP_ERR_HTTP_BASE,          /*!< Error read HTTP header from server */
    OTA_EVT_HTTP_ERR_INVALID_TRANSPORT = ESP_ERR_HTTP_INVALID_TRANSPORT - ESP_ERR_HTTP_BASE,     /*!< There are no transport support for the input scheme */
    OTA_EVT_HTTP_ERR_CONNECTING = ESP_ERR_HTTP_CONNECTING - ESP_ERR_HTTP_BASE,           /*!< HTTP connection hasn't been established yet */
    OTA_EVT_HTTP_ERR_EAGAIN = ESP_ERR_HTTP_EAGAIN - ESP_ERR_HTTP_BASE,                /*!< Mapping of errno EAGAIN to esp_err_t */
    OTA_EVT_HTTP_ERR_CONNECTION_CLOSED = ESP_ERR_HTTP_CONNECTION_CLOSED - ESP_ERR_HTTP_BASE,     /*!< Read FIN from peer and the connection closed */

    OTA_EVT_ERR_DOWNLOAD_CRASHED,
    OTA_EVT_ERR_WRONG_LINK,
    OTA_EVT_ERR_WRONG_FIRMWARE,

    OTA_EVT_STARTED,
    OTA_EVT_PERCENT,    
    OTA_EVT_COMPLETED,
    OTA_EVT_FAILED,
    OTA_EVT_TLS_CONN_ERR,   // wrong cert which cant be parsed drives this err too
} ota_events_t;



/**
 *  starts OTA upgrade.
 * Its BLOCKING FUNCTION !!! will not return till OTA is completed/failed.
 * ota_progress_cb can be used to show progress to user.
 * 
 * \param firmware_link - link to custom firmware. ignored for M/B/Prod optaion
 * \param loop_handle - event loop to post events to.  when progress is posted, percents sent as int * to value
 * \param user_name   - user name for http basic auth
 * \param password    - password for http basic auth
 */
void ota_start(const char * firmware_link, 
    esp_event_loop_handle_t loop_handle,
    char *user_name, 
    char *password);



/**
 * returns OTA status. 0 if not started. % of completion if started
 */
int ota_in_progress();



/**
 * Set 'verify server certificate' flag for next OTA
 */

void set_dont_verify_cert_flag(bool dont_verify);



/**
 *  Saves new certificate in NVS. 
 *  Certificate is used for server verification during OTA.
 * 
 *  Returns 0 if successfully saved. and Err code othervise
 */
int save_new_sertificate(char *cert);



} //end of bsh_sys::ota