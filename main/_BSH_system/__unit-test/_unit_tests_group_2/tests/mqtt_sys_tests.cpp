#include "CppUTest/TestHarness.h"
#include <iostream>
#include <string.h>

#include "mqtt_sys_protocol.h"
#include "rsa_encryption.h"
#include "queue.h"
#include "../../MQTT/mqtt.h"
#include "../../RSA_encryption/rsa_encryption.h"



unsigned char RSA_public_key_start[2000]        asm("_binary_esp32_rsa_pub_pair_start"); 
unsigned char *RSA_public_key_end          asm("_binary_esp32_rsa_pub_pair_end");
unsigned char RSA_private_key_start[2000]       asm("_binary_esp32_rsa_priv_pair_start");
unsigned char *RSA_private_key_end         asm("_binary_esp32_rsa_priv_pair_end");



void incoming_packet_tr(uint8_t *data, uint16_t  data_len) {}


extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}


static void read_public_key();
static void read_private_key();
static bool compare_mqtt_blocks(bsh_sys::mqtt_protocol::mqtt_in_block_t *b1, bsh_sys::mqtt_protocol::mqtt_in_block_t *b2);



TEST_GROUP(mqtt_sys_protocol)
{
    void setup()
    {
        static bool init_done = false;

        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();

        if(!init_done)
        {
            read_public_key();
            read_private_key();

            bsh_sys::rsa::init();

            // mqtt sys init
            using namespace bsh_sys::mqtt_protocol;
            esp_event_loop_args_t evt_loop_arg = {
                .queue_size = 10,     
            };
            esp_event_loop_handle_t evt_loop_handle;
            esp_event_loop_create(&evt_loop_arg, &evt_loop_handle);
            init (evt_loop_handle);

            // mqtt init
            esp_event_loop_handle_t evt_loop_handle2;
            esp_event_loop_create(&evt_loop_arg, &evt_loop_handle2);
            bsh_sys::mqtt::mqtt_settings_t mq_st = {
                .incoming_packet_treat = incoming_packet_tr,
                .loop_handle = evt_loop_handle2,
            };
            bsh_sys::mqtt::start(&mq_st);

            init_done = true;
        }
    }

    void teardown()
    {
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
        clear_esp_event_mock();
    }
};


TEST (mqtt_sys_protocol, wifi_db_to_bork_level){
    using namespace bsh_sys::mqtt_protocol;

    CHECK(wifi_db_to_bork_level(-80) == WFL_WEAK);
    CHECK(wifi_db_to_bork_level(-65) == WFL_MIDDLE);
    CHECK(wifi_db_to_bork_level(-50) == WFL_STRONG);
    CHECK(wifi_db_to_bork_level(-30) == WFL_BEST);
}



TEST (mqtt_sys_protocol, get_status_topic_name){
    using namespace bsh_sys::mqtt_protocol;

    const char *correct_result = "/users/89163222233/devices/0102030405/state";
    const char *user_id = "89163222233";
    const uint8_t device_id[] = {1,2,3,4,5};

    char *result = get_status_topic_name(user_id, device_id, sizeof(device_id));
    // printf("%s", result);
    CHECK(strlen(correct_result) == strlen(result));
    MEMCMP_EQUAL(correct_result, result, strlen(correct_result));
}




TEST (mqtt_sys_protocol, get_commands_topic_name){
    using namespace bsh_sys::mqtt_protocol;

    const char *correct_result = "/users/89163222233/devices/0102030405/commands";
    const char *user_id = "89163222233";
    const uint8_t device_id[] = {1,2,3,4,5};

    char *result = get_commands_topic_name(user_id, device_id, sizeof(device_id));
    // printf("%s", result);
    CHECK(strlen(correct_result) == strlen(result));
    MEMCMP_EQUAL(correct_result, result, strlen(correct_result));
}



TEST (mqtt_sys_protocol, get_sys_actor_index){
    using namespace bsh_sys::mqtt_protocol;

    char *actor = "LOG";
    CHECK(get_sys_actor_index (actor) == 5);
}




TEST (mqtt_sys_protocol, send_blocks){
    using namespace bsh_sys::mqtt_protocol;

    mqtt_out_block_t data_to_send = {
        .actor = {'A','C','T'},
        .index = 0,     
        .capability = 0,       
        .action_type = REPLY,
        .value_type = INT,
        .value = {.in_int = 0x32}
    };


    uint8_t qos = 1;
    send_blocks (&data_to_send, 1, qos);

    // checking result
    bsh_sys::mqtt::mqtt_out_obj_t out_obj = {};
    xQueueReceive(get_queue(2), &out_obj, 0);

    CHECK (out_obj.qos_level == qos);
    CHECK (out_obj.data_len == 13);

    uint8_t correct_result[] = {1,1,'A','C','T',0,0,128,0,50,0,0,0};
    MEMCMP_EQUAL(correct_result, out_obj.out_data, sizeof(correct_result));

    // send two blocks
    mqtt_out_block_t blocks[] = {data_to_send, data_to_send};
    uint8_t qos2 = 0;
    send_blocks (blocks, 2, qos2);
    bsh_sys::mqtt::mqtt_out_obj_t out_obj2 = {};
    xQueueReceive(get_queue(2), &out_obj2, 0);

    CHECK (out_obj2.qos_level == qos2);
    CHECK (out_obj2.data_len == 24);
    uint8_t correct_result2[] = {1,2,'A','C','T',0,0,128,0,50,0,0,0,'A','C','T',0,0,1,0,50,0,0,0};
    MEMCMP_EQUAL(correct_result2, out_obj2.out_data, sizeof(correct_result));
}



TEST (mqtt_sys_protocol, send_err_message_to_broker){

    using namespace bsh_sys::mqtt_protocol;

    send_err_message_to_broker(14);

    // checking result
    bsh_sys::mqtt::mqtt_out_obj_t out_obj = {};
    xQueueReceive(get_queue(2), &out_obj, 0);
    uint8_t correct_result[] = {1,1,'E','R','R',0,0,128,0,0xF6,3,0,0}; // 14 turns into 1014 when sent to mqtt
    CHECK (out_obj.qos_level == 0);
    CHECK (out_obj.data_len == 13);
    MEMCMP_EQUAL(correct_result, out_obj.out_data, sizeof(correct_result));
}



TEST (mqtt_sys_protocol, send_test_message){

    using namespace bsh_sys::mqtt_protocol;

    send_test_message();

    // checking result
    bsh_sys::mqtt::mqtt_out_obj_t out_obj = {};
    xQueueReceive(get_queue(2), &out_obj, 0);
    uint8_t correct_result[] = {1,1,'M','S','G',0,0,128,0,7,0,0,0};
    CHECK (out_obj.qos_level == 1);
    CHECK (out_obj.data_len == 13);
    MEMCMP_EQUAL(correct_result, out_obj.out_data, sizeof(correct_result));
}



TEST (mqtt_sys_protocol, get_out_queue){
    using namespace bsh_sys::mqtt_protocol;
    CHECK(get_out_queue() != NULL);
    CHECK(get_incoming_queue() != NULL);
    CHECK(get_incoming_sys_queue() != NULL);
}




TEST (mqtt_sys_protocol, incoming_mqtt_data){
    using namespace bsh_sys::mqtt_protocol;
    uint8_t incoming_data[500] = {1,2,'A','C','T',0,0,1,0,0,50,0,0,0,'A','C','T',0,0,1,0,0,50,0,0,0};
    
    bsh_sys::rsa::sign_data((const unsigned char *)incoming_data, 26, (unsigned char *)(incoming_data + 26));

    incoming_mqtt_data(incoming_data, 26 + SIGNATURE_SIZE);

    // checking result
    mqtt_in_block_t result = {};
    xQueueReceive(get_queue(0), &result, 0);

    mqtt_in_block_t correct_block3 = {
        .actor = {'A','C','T'},
        .index = 0,
        .capability = 0, 
        .action_type = WRITE,
        .increment = ABSOLUTE,
        .value_type = INT,
        .set_value = {.in_int = 50},
        .firm_url = NULL 
    };

    CHECK(compare_mqtt_blocks(&correct_block3, &result));

    xQueueReceive(get_queue(0), &result, 0);
    CHECK(compare_mqtt_blocks(&correct_block3, &result));
    CHECK(xQueueReceive(get_queue(0), &result, 0) == 0);
}



static bool compare_mqtt_blocks(bsh_sys::mqtt_protocol::mqtt_in_block_t *b1, bsh_sys::mqtt_protocol::mqtt_in_block_t *b2)
{
    bool res =         
        b1->actor[0] == b2->actor[0] &&
        b1->actor[1] == b2->actor[1] &&
        b1->actor[2] == b2->actor[2] &&
        b1->index == b2->index &&
        b1->capability == b2->capability &&
        b1->action_type == b2->action_type &&
        b1->increment == b2->increment &&
        b1->value_type == b2->value_type &&
        b1->set_value.in_int == b2->set_value.in_int &&
        b1->firm_url == b2->firm_url;

    return res; 
}




#include <fstream>
#include <string>


static void read_public_key()
{
    using namespace std;
    using namespace bsh_sys::rsa;
    
    string file_name("esp32_rsa_pub_pair");
    ifstream ifs(file_name.c_str(), ios::in | ios::binary | ios::ate);

    ifstream::pos_type file_size = ifs.tellg();
    ifs.seekg(0, ios::beg);

    // printf("\n key file size: %i \n", file_size);

    ifs.read((char *)RSA_public_key_start, file_size);
    RSA_public_key_end = RSA_public_key_start + file_size +1;

    // print_key((char *)RSA_public_key_start, (char *)RSA_public_key_end);
}



static void read_private_key()
{
    using namespace std;
    using namespace bsh_sys::rsa;
    
    string file_name("esp32_rsa_priv_pair");
    ifstream ifs(file_name.c_str(), ios::in | ios::binary | ios::ate);

    ifstream::pos_type file_size = ifs.tellg();
    ifs.seekg(0, ios::beg);

    // printf("\n key file size: %i \n", file_size);

    ifs.read((char *)RSA_private_key_start, file_size);
    RSA_private_key_end = RSA_private_key_start + file_size +1;

    // print_key((char *)RSA_private_key_start, (char *)RSA_private_key_end);
}
