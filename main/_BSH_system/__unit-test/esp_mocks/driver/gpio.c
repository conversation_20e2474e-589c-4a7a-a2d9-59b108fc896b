#include <string.h>
#include <stdbool.h>

#include "esp_err.h"
#include "gpio.h"



/* objects declarations ---------------------------------------*/
gpio_struct_t GPIO = {0};                   // fake GPIO struct

// case when isr_register is used (single callback for all gpios)
static void (*registered_cb)(void*) = NULL;    // holds isr callback, registered last
static bool register_isr_called = false;           // to test for multiple inits

// case when add_handler is user - pergpio isr handlers
static bool gpio_install_isr_service_called = false;
static gpio_isr_handler_t handlers_arr[GPIO_NUM_MAX] = {};
static uint8_t handlers_qty = 0;



// static gpio_event_t events_array[50] = {0};     // array of events, holds buttons states and time
// static uint8_t events_qty = 0;                     // qty of events in arr
// // static gpio_event_t gpio_state_at_startup = {0};

// /* local functions delcarations -------------------------------------*/
// static void set_gpio_outputs(uint8_t event_number_in_array);
// static void clear_event(gpio_event_t *event);
// static void copy_event_from_to(gpio_event_t *event_from, gpio_event_t *event_to);



/*===================================================================*\
 * exported functions definitions
\*====================================================================*/
esp_err_t gpio_isr_register(void (*fn)(void*), void *arg, 
                                int intr_alloc_flags, 
                                gpio_isr_handle_t *handle)
{
    register_isr_called = true;
    registered_cb = fn;
    return 0;
}



esp_err_t gpio_install_isr_service(int intr_alloc_flags)
{
    gpio_install_isr_service_called = true;
    // TODO check its not used together with gpio_isr_register

    return ESP_OK;
}



esp_err_t gpio_isr_handler_add(gpio_num_t gpio_num, gpio_isr_handler_t isr_handler, void *args)
{
    handlers_arr[gpio_num] = isr_handler;
    handlers_qty ++;

    return ESP_OK;
}



/**
 * can be called multiple times, to set several buttons states
 * must be called BEFORE add_gpio_event.. functions
 */
// void set_state_at_power_on (button_io_t btn, button_state_t state)
// {
//     switch (btn)
//     {
//     case BTN_LGT: gpio_state_at_startup.lgh = state; break;
//     case BTN_ARM: gpio_state_at_startup.arm = state ; break;
//     case BTN_SPD: gpio_state_at_startup.spd = state; break;
//     case BTN_HUM: gpio_state_at_startup.hum = state; break;
//     case BTN_PWR: gpio_state_at_startup.pwr = state; break;
    
//     default:
//         break;
//     }

//         printf ("   set startup state: %u %u %u %u %u   %u\n", 
//             gpio_state_at_startup.lgh,
//             gpio_state_at_startup.arm,
//             gpio_state_at_startup.spd,
//             gpio_state_at_startup.hum,
//             gpio_state_at_startup.pwr,
//             gpio_state_at_startup.event_time);
// }



// returns 0 when button is touched
int gpio_get_level(gpio_num_t gpio_num)
{
    uint64_t in = ((uint64_t)GPIO.in) | ((uint64_t)GPIO.in1.data << 32);
    return (in & (1LLU << gpio_num));
}



esp_err_t gpio_set_level(gpio_num_t gpio_num, uint32_t level)
{
    GPIO.in &= 0LLu << gpio_num;
    GPIO.in |= ((uint64_t)level) << gpio_num;
    return ESP_OK;
}



void * get_registered_cb()
{
    return (void *)registered_cb;
}



void call_registered_cb()
{
    registered_cb(NULL);
}



void call_handler_for_gpio(gpio_num_t gpio_n)
{
    if(handlers_arr[gpio_n] != NULL) handlers_arr[gpio_n](NULL);
}


void call_handler_for_gpio_with_pin_state(gpio_num_t gpio_n, bool pin_state)
{
    set_pin_state(gpio_n, pin_state);
    call_handler_for_gpio(gpio_n);
}


void clear_gpio_mock()
{
    memset (&GPIO, 0, sizeof(gpio_struct_t));
 
    registered_cb = NULL;
    register_isr_called = 0;
 
    gpio_install_isr_service_called = false;
    memset(handlers_arr, 0, sizeof(handlers_arr));
    handlers_qty = 0;
}



void set_pin_state(gpio_num_t gpio_n, bool high_or_low)
{
    gpio_set_level(gpio_n, (uint32_t)high_or_low);
}



// void add_gpio_event_absolute_time(gpio_num_t pin, uint32_t time, pin_state_t b_st)
// {
//     if (events_qty == 0)
//     {
//         copy_event_from_to(&gpio_state_at_startup, &(events_array[0]));
//     } else {
//         copy_event_from_to(&(events_array[events_qty-1]),&(events_array[events_qty]));
//     }

//     switch (butn) {
//     case BTN_LGT: events_array[events_qty].lgh = b_st; break;
//     case BTN_ARM: events_array[events_qty].arm = b_st; break;
//     case BTN_SPD: events_array[events_qty].spd = b_st; break;
//     case BTN_HUM: events_array[events_qty].hum = b_st; break;
//     case BTN_PWR: events_array[events_qty].pwr = b_st; break;
//     }
//     events_array[events_qty].event_time = time;

//     printf ("   gpio evt set(abs): %u %u %u %u %u   %u\n", 
//                 events_array[events_qty].lgh,
//                 events_array[events_qty].arm,
//                 events_array[events_qty].spd,
//                 events_array[events_qty].hum,
//                 events_array[events_qty].pwr,
//                 events_array[events_qty].event_time);

//     events_qty ++;
// }



// void add_gpio_event_with_offset(button_io_t butn, uint32_t offset, button_state_t b_st)
// {
//     if (events_qty == 0)
//     {
//         copy_event_from_to(&gpio_state_at_startup, &(events_array[events_qty]));
//         events_array[events_qty].event_time = offset;
//     } else {
//         copy_event_from_to(&(events_array[events_qty-1]),&(events_array[events_qty]));
//         events_array[events_qty].event_time = events_array[events_qty - 1].event_time + offset;
//     }

//     switch (butn)
//     {
//     case BTN_LGT: events_array[events_qty].lgh = b_st; break;
//     case BTN_ARM: events_array[events_qty].arm = b_st; break;
//     case BTN_SPD: events_array[events_qty].spd = b_st; break;
//     case BTN_HUM: events_array[events_qty].hum = b_st; break;
//     case BTN_PWR: events_array[events_qty].pwr = b_st; break;
//     }

//     printf ("   gpio evt set(offset): %u %u %u %u %u   %u\n", 
//             events_array[events_qty].lgh,
//             events_array[events_qty].arm,
//             events_array[events_qty].spd,
//             events_array[events_qty].hum,
//             events_array[events_qty].pwr,
//             events_array[events_qty].event_time);

//     events_qty ++;
// }



esp_err_t gpio_config(const gpio_config_t *pGPIOConfig)
{
    return 0;
}





/*===================================================================*\
 * local functions definitions
\*====================================================================*/
static void set_gpio_outputs(uint8_t event_number_in_array)
{
    // uint8_t num = event_number_in_array;

    // GPIO.status = 0;  // status indicates which gpio changed (=1)
    
    // if (num == 0) // i.e. fist event
    // {
    //     if (events_array[num].lgh != gpio_state_at_startup.lgh) GPIO.status = (1LLU << BTN_LGT);
    //     if (events_array[num].arm != gpio_state_at_startup.arm) GPIO.status = (1LLU << BTN_ARM);
    //     if (events_array[num].spd != gpio_state_at_startup.spd) GPIO.status = (1LLU << BTN_SPD);
    //     if (events_array[num].hum != gpio_state_at_startup.hum) GPIO.status = (1LLU << BTN_HUM);
    //     if (events_array[num].pwr != gpio_state_at_startup.pwr) GPIO.status = (1LLU << BTN_PWR);

    // } else {

    //     if (events_array[num].lgh != events_array[num-1].lgh) GPIO.status = (1LLU << BTN_LGT);
    //     if (events_array[num].arm != events_array[num-1].arm) GPIO.status = (1LLU << BTN_ARM);
    //     if (events_array[num].spd != events_array[num-1].spd) GPIO.status = (1LLU << BTN_SPD);
    //     if (events_array[num].hum != events_array[num-1].hum) GPIO.status = (1LLU << BTN_HUM);
    //     if (events_array[num].pwr != events_array[num-1].pwr) GPIO.status = (1LLU << BTN_PWR);
    // }

    // GPIO.in = 0xffffffff;      
    // // "in" indicates current state. 0 = low level. 1 = high lvl. 
    // // when button is touched - level is low    
    // // ! except light button - when it touched- level is high - schematic works that way
    // if (!events_array[num].lgh) GPIO.in ^= (1LLU << BTN_LGT);  // ! - coz schematic works that way
    // if (events_array[num].arm) GPIO.in ^= (1LLU << BTN_ARM);
    // if (events_array[num].spd) GPIO.in ^= (1LLU << BTN_SPD);
    // if (events_array[num].hum) GPIO.in ^= (1LLU << BTN_HUM);
    // if (events_array[num].pwr) GPIO.in ^= (1LLU << BTN_PWR);
}



// static void clear_event(gpio_event_t *event)
// {
//     event->arm = 0;
//     event->hum = 0;
//     event->lgh = 0;
//     event->pwr = 0;
//     event->spd = 0;
//     event->event_time = 0;
// }



// static void copy_event_from_to(gpio_event_t *event_from, gpio_event_t *event_to)
// {
//     event_to->lgh = event_from->lgh;
//     event_to->arm = event_from->arm;
//     event_to->spd = event_from->spd;
//     event_to->hum = event_from->hum;
//     event_to->pwr = event_from->pwr;
//     event_to->event_time = event_from->event_time;

//     // printf ("========= %p  \n", event_to);
//     // printf ("========= %u  ", event_to->lgh);
//     // printf ("========= %u  ", event_to->arm);
//     // printf ("========= %u  ", event_to->spd);
//     // printf ("========= %u  ", event_to->hum);
//     // printf ("========= %u  ", event_to->pwr);
//     // printf ("========= %u\n", event_to->event_time);
// }