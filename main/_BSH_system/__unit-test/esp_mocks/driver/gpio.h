#pragma once
#include <stdint.h>
#include <stdbool.h>

#include "esp_err.h"



#ifdef __cplusplus
extern "C" {
#endif



#define IRAM_ATTR



#define GPIO_MODE_INPUT 1
#define GPIO_MODE_INPUT_OUTPUT_OD 2
#define GPIO_PULLUP_ENABLE 1
#define GPIO_PULLUP_DISABLE 0
#define GPIO_PULLDOWN_ENABLE 1
#define GPIO_PULLDOWN_DISABLE 0
#define GPIO_INTR_ANYEDGE 0



typedef enum {
    GPIO_NUM_NC = -1,    /*!< Use to signal not connected to S/W */
    GPIO_NUM_0 = 0,     /*!< GPIO0, input and output */
    GPIO_NUM_1 = 1,     /*!< GPIO1, input and output */
    GPIO_NUM_2 = 2,     /*!< GPIO2, input and output */
    GPIO_NUM_3 = 3,     /*!< GPIO3, input and output */
    GPIO_NUM_4 = 4,     /*!< GPIO4, input and output */
    GPIO_NUM_5 = 5,     /*!< GPIO5, input and output */
    GPIO_NUM_6 = 6,     /*!< GPIO6, input and output */
    GPIO_NUM_7 = 7,     /*!< GPIO7, input and output */
    GPIO_NUM_8 = 8,     /*!< GPIO8, input and output */
    GPIO_NUM_9 = 9,     /*!< GPIO9, input and output */
    GPIO_NUM_10 = 10,   /*!< GPIO10, input and output */
    GPIO_NUM_11 = 11,   /*!< GPIO11, input and output */
    GPIO_NUM_12 = 12,   /*!< GPIO12, input and output */
    GPIO_NUM_13 = 13,   /*!< GPIO13, input and output */
    GPIO_NUM_14 = 14,   /*!< GPIO14, input and output */
    GPIO_NUM_15 = 15,   /*!< GPIO15, input and output */
    GPIO_NUM_16 = 16,   /*!< GPIO16, input and output */
    GPIO_NUM_17 = 17,   /*!< GPIO17, input and output */
    GPIO_NUM_18 = 18,   /*!< GPIO18, input and output */
    GPIO_NUM_19 = 19,   /*!< GPIO19, input and output */
    GPIO_NUM_20 = 20,   /*!< GPIO20, input and output */
    GPIO_NUM_21 = 21,   /*!< GPIO21, input and output */
    GPIO_NUM_22 = 22,   /*!< GPIO22, input and output */
    GPIO_NUM_23 = 23,   /*!< GPIO23, input and output */
    GPIO_NUM_25 = 25,   /*!< GPIO25, input and output */
    GPIO_NUM_26 = 26,   /*!< GPIO26, input and output */
    GPIO_NUM_27 = 27,   /*!< GPIO27, input and output */
    GPIO_NUM_28 = 28,   /*!< GPIO28, input and output */
    GPIO_NUM_29 = 29,   /*!< GPIO29, input and output */
    GPIO_NUM_30 = 30,   /*!< GPIO30, input and output */
    GPIO_NUM_31 = 31,   /*!< GPIO31, input and output */
    GPIO_NUM_32 = 32,   /*!< GPIO32, input and output */
    GPIO_NUM_33 = 33,   /*!< GPIO33, input and output */
    GPIO_NUM_34 = 34,   /*!< GPIO34, input mode only */
    GPIO_NUM_35 = 35,   /*!< GPIO35, input mode only */
    GPIO_NUM_36 = 36,   /*!< GPIO36, input mode only */
    GPIO_NUM_37 = 37,   /*!< GPIO37, input mode only */
    GPIO_NUM_38 = 38,   /*!< GPIO38, input mode only */
    GPIO_NUM_39 = 39,   /*!< GPIO39, input mode only */
    GPIO_NUM_MAX,
} gpio_num_t;



typedef struct gpio_isr_handle_t {
    int i;
} gpio_isr_handle_t;



typedef struct 
{
    uint32_t status;
    uint32_t in;
    uint32_t status_w1tc;
    union {
        struct {
            uint32_t intr_st                       :    22;
            uint32_t reserved22                    :    10;
        };
        uint32_t val;
    } status1;
    union {
        struct {
            uint32_t data                          :    22;
            uint32_t reserved22                    :    10;
        };
        uint32_t val;
    } in1;
    union {
        struct {
            uint32_t intr_st                       :    22;
            uint32_t reserved22                    :    10;
        };
        uint32_t val;
    } status1_w1tc;

} gpio_struct_t;

extern gpio_struct_t GPIO;


// typedef enum {
//     GPIO_MODE_DISABLE = GPIO_MODE_DEF_DISABLE,                                                         /*!< GPIO mode : disable input and output             */
//     GPIO_MODE_INPUT = GPIO_MODE_DEF_INPUT,                                                             /*!< GPIO mode : input only                           */
//     GPIO_MODE_OUTPUT = GPIO_MODE_DEF_OUTPUT,                                                           /*!< GPIO mode : output only mode                     */
//     GPIO_MODE_OUTPUT_OD = ((GPIO_MODE_DEF_OUTPUT) | (GPIO_MODE_DEF_OD)),                               /*!< GPIO mode : output only with open-drain mode     */
//     GPIO_MODE_INPUT_OUTPUT_OD = ((GPIO_MODE_DEF_INPUT) | (GPIO_MODE_DEF_OUTPUT) | (GPIO_MODE_DEF_OD)), /*!< GPIO mode : output and input with open-drain mode*/
//     GPIO_MODE_INPUT_OUTPUT = ((GPIO_MODE_DEF_INPUT) | (GPIO_MODE_DEF_OUTPUT)),                         /*!< GPIO mode : output and input mode                */
// } gpio_mode_t;



typedef struct {
    int mode;               /*!< GPIO mode: set input/output mode                     */
    int pull_up_en;       /*!< GPIO pull-up                                         */
    int pull_down_en;   /*!< GPIO pull-down                                       */
    int intr_type;      /*!< GPIO interrupt type                                  */
    uint64_t pin_bit_mask;          /*!< GPIO pin: set with bit mask, each bit maps to a GPIO */
} gpio_config_t;


typedef void (*gpio_isr_handler_t)(void *);


/**
 * This ISR function is called whenever ANY GPIO interrupt occurs. 
 * Can not be used together with gpio_install_isr_service() + gpio_isr_handler_add()
 */ 
esp_err_t gpio_isr_register(void (*fn)(void*), void *arg, int intr_alloc_flags, gpio_isr_handle_t *handle);



/**
 * This function to be used when you need per-gpio isr handler
 * First call this function, then add handlers by calling gpio_isr_handler_add()
*/
esp_err_t gpio_install_isr_service(int intr_alloc_flags);
esp_err_t gpio_isr_handler_add(gpio_num_t gpio_num, gpio_isr_handler_t isr_handler, void *args);



// gpio output level set
int gpio_get_level(gpio_num_t gpio_num);
esp_err_t gpio_set_level(gpio_num_t gpio_num, uint32_t level);



esp_err_t gpio_config(const gpio_config_t *pGPIOConfig);



/***************************************
 *        Mock methods
****************************************/
/**
 * clears all data in the module
 */
void clear_gpio_mock();



void set_pin_state(gpio_num_t gpio_n, bool high_or_low);


/**
 * Returns isr callback, registered via gpio_isr_register()
*/
void *get_registered_cb();
void call_registered_cb();



/**
 * For callbacks, registered via gpio_isr_handler_add()
*/
void call_handler_for_gpio(gpio_num_t gpio_n);
void call_handler_for_gpio_with_pin_state(gpio_num_t gpio_n, bool pin_state);




// typedef enum {
//     HIGH,
//     LOW,
// } mock_pin_state_t;

 

// typedef struct {
//     button_state_t lgh, arm, spd, hum, pwr;
//     uint32_t event_time;
//     mock_pin_state_t pins_state[GPIO_NUM_MAX];
// } gpio_event_t;



// mock set state methods
// void add_gpio_event_absolute_time(gpio_num_t pin, uint32_t time, pin_state_t b_st); // can be called multiple times to set many events
// void add_gpio_event_with_offset(gpio_num_t pin, uint32_t offset, pin_state_t b_st); // can be called multiple times to set many events
// void set_state_at_power_on (gpio_num_t btn, pin_state_t state);



#ifdef __cplusplus
}
#endif