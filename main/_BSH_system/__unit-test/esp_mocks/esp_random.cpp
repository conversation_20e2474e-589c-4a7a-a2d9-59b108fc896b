#include "esp_random.h"
#include <cstdlib>
#include <string.h> 



static void *set_random_to = NULL;

void esp_fill_random(void *buf, size_t len)
{
    if (set_random_to != NULL)
    {
        memcpy(buf, set_random_to, len);

        set_random_to = NULL;
        return;
    }

    uint8_t *bf = (uint8_t*)buf;
    for (size_t i = 0; i < len; i++)
    {
        bf[i] = (uint8_t)(std::rand() % 0xFF);
    }
}



// mock tools
void set_next_random_to (void *buf)
{
    set_random_to = buf;
}



uint32_t esp_random(void)
{
    return (uint32_t)rand();
}