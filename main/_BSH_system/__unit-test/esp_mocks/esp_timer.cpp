#include <stdint.h>
#include <string.h>

#include "esp_timer.h"
#include "esp_err.h"



static esp_timer timers[20] = {0};
static uint8_t timers_qty = 0;
static uint32_t curr_time = 0;
static uint64_t time_from_boot = 0;
static uint64_t increment_for_time_from_boot = 0;



/*===================================================================*\
 * faked timer functions
\*====================================================================*/
esp_err_t esp_timer_create(const esp_timer_create_args_t* create_args,
                           esp_timer_handle_t* out_handle)
{
    *out_handle = timers+timers_qty;

    memcpy(&((timers+timers_qty)->args), create_args, sizeof(esp_timer_create_args_t));
    timers[timers_qty].timer_number = timers_qty;
    timers_qty ++;
    return 0;
}



esp_err_t esp_timer_start_periodic(esp_timer_handle_t timer, uint64_t period)
{
    timer->timeout_us = curr_time + period;
    timer->ticking = true;
    timer->periodic = true;
    timer->period = period;

    return 0;
}



esp_err_t esp_timer_start_once(esp_timer_handle_t timer, uint64_t timeout_us)
{
    timer->timeout_us = curr_time + timeout_us;
    timer->ticking = true;
    timer->periodic = false;
    return 0;
}



esp_err_t esp_timer_stop(esp_timer_handle_t timer)
{
    timer->timeout_us = 0;
    timer->ticking = false;
    return 0;
}



esp_err_t esp_timer_delete(esp_timer_handle_t timer)
{
    timer->deleted = true;
    return 0;
}



int64_t esp_timer_get_time()
{
    uint64_t res = time_from_boot;
    time_from_boot += increment_for_time_from_boot;
    return res;
}



/*===================================================================*\
 * mock controls
\*====================================================================*/
void set_time_for__esp_timer_get_time(int64_t time)
{
    time_from_boot = time;
}



void increment_time_after_every_request(bool turn_on, uint64_t increment)
{
    if(turn_on)
    {
        increment_for_time_from_boot = increment;
    } else {
        increment_for_time_from_boot = 0;
    }
}



void call_timer_cb_if_its_due_time(uint64_t time)
{
    if (timers_qty == 0) return;

    curr_time = time;

    for (size_t i = 0; i < timers_qty; i++)
    {
        if (timers[i].deleted) continue;
        if (!(timers[i].ticking)) continue;

        if(timers[i].timeout_us == time)
        {
            if (!timers[i].periodic)
            // one-time timer
            {
                (timers[i].ticking) = false;
                timers[i].args.callback(timers[i].args.arg);
            } else { // periodic timer
                timers[i].timeout_us += timers[i].period;
                timers[i].args.callback(timers[i].args.arg);
            }
        }
    }
}



void clear_esp_timer_mock()
{
    timers_qty = 0;
    curr_time = 0;
    time_from_boot = 0;
    increment_for_time_from_boot = 0;
}



uint64_t get_current_time()
{
    return curr_time;
}



/*===================================================================*\
 * local functions definitions
\*====================================================================*/