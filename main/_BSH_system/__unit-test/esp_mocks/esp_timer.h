/**
 * <PERSON><PERSON><PERSON> fakes up to 20 esp timers.
 * 
 * call_timer_cb_if_its_due_time should be called with current system time as parameter
 * to call timers callbacks (if the time matches their due time)
*/



#pragma once

#include <stdbool.h>
#include <stdint.h>

#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef void (*esp_timer_cb_t)(void* arg);

/**
 * @brief Method for dispatching timer callback
 */
typedef enum {
    ESP_TIMER_TASK,     //!< Callback is called from timer task
#ifdef CONFIG_ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD
    ESP_TIMER_ISR,      //!< Callback is called from timer ISR
#endif
    ESP_TIMER_MAX,      //!< Count of the methods for dispatching timer callback
} esp_timer_dispatch_t;

typedef struct {
    esp_timer_cb_t callback;        //!< Function to call when timer expires
    void* arg;                      //!< Argument to pass to the callback
    esp_timer_dispatch_t dispatch_method;   //!< Call the callback from task or from ISR
    const char* name;               //!< Timer name, used in esp_timer_dump function
    bool skip_unhandled_events;     //!< Skip unhandled events for periodic timers
} esp_timer_create_args_t;


typedef struct {
    uint8_t timer_number;
    esp_timer_create_args_t args;
    bool deleted;
    bool ticking;
    uint64_t timeout_us;
    bool periodic;   // false means single-shot timer
    uint64_t period;
} esp_timer;

typedef esp_timer *esp_timer_handle_t;



esp_err_t esp_timer_create(const esp_timer_create_args_t* create_args,
                           esp_timer_handle_t* out_handle);

esp_err_t esp_timer_start_periodic(esp_timer_handle_t timer, uint64_t period);

esp_err_t esp_timer_start_once(esp_timer_handle_t timer, uint64_t timeout_us);

esp_err_t esp_timer_stop(esp_timer_handle_t timer);

esp_err_t esp_timer_delete(esp_timer_handle_t timer);



/**
 * @brief Get time in microseconds since boot
 * @return number of microseconds since underlying timer has been started
 */
int64_t esp_timer_get_time();




/*********************
 *  Mock tools
**********************/

void set_time_for__esp_timer_get_time(int64_t time);
void increment_time_after_every_request(bool turn_on, uint64_t increment);



/**
 * @brief check if time matches with any time due time.
 *      if matches- calls callback
 * @param time - current system time, in us
 */
void call_timer_cb_if_its_due_time(uint64_t time);
void clear_esp_timer_mock();
uint64_t get_current_time();



#ifdef __cplusplus
}
#endif
