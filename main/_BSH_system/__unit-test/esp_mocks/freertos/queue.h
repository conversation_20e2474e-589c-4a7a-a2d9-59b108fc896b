#pragma once

#define pdTRUE                  ( ( int ) 1 )
#define errQUEUE_FULL           ( ( int ) 0 )
#define errQUEUE_EMPTY          ( ( int ) 0 )



typedef struct QueueDefinition 
{
    uint8_t * pcHead;           /*< Points to the beginning of the queue storage area. */
    uint8_t * pcWriteTo;        /*< Points to the free next place in the storage area. */

    volatile uint32_t uxMessagesWaiting; /*< The number of items currently in the queue. */
    uint32_t queue_length;                   /*< The length of the queue defined as the number of items it will hold, not the number of bytes. */
    uint32_t item_size;                 /*< The size of each items that the queue will hold. */

} QueueDefinition_t;



/**
 * Type by which queues are referenced.  For example, a call to xQueueCreate()
 * returns an QueueHandle_t variable that can then be used as a parameter to
 * xQueueSend(), xQueueReceive(), etc.
 */
struct QueueDefinition; /* Using old naming convention so as not to break kernel aware debuggers. */
typedef struct QueueDefinition   * QueueHandle_t;


QueueHandle_t xQueueCreate( int uxQueueLength, int uxItemSize );

// xTicksToWait are ignored
int xQueueSend(QueueHandle_t xQueue, const void * const pvItemToQueue, uint32_t xTicksToWait );

// xTicksToWait are ignored
int xQueueReceive( QueueHandle_t xQueue, void * const pvBuffer, uint32_t xTicksToWait );



// mock tools
void clear_queues();
QueueDefinition_t *get_queue(uint8_t N);  // returns NULL if N is wrong