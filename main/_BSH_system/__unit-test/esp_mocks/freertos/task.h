#pragma once

#include <stdint.h>
#include "esp_event.h"



#ifdef __cplusplus
extern "C" {
#endif



/*
 * Defines the prototype to which task functions must conform.  Defined in this
 * file to ensure the type is known before portable.h is included.
 */
typedef void (* TaskFunction_t)( void * );

typedef void * TaskHandle_t;

typedef int BaseType_t;
typedef unsigned int UBaseType_t;

BaseType_t xTaskCreate( TaskFunction_t pvTaskCode,
                        const char * const pcName,     /*lint !e971 Unqualified char types are allowed for strings and single characters only. */
                        const uint32_t usStackDepth,
                        void * const pvParameters,
                        UBaseType_t uxPriority,
                        TaskHandle_t * const pxCreatedTask);

void vTaskSuspendAll( void );
void xTaskResumeAll ( void );
void vTaskDelete(TaskHandle_t task);
void vTaskDelay( const TickType_t xTicksToDelay );

#define taskDISABLE_INTERRUPTS()           {}
#define taskENABLE_INTERRUPTS()            {}
#define pdMS_TO_TICKS(a) a
#define portMAX_DELAY ( TickType_t ) 0xffffffffUL




#ifdef __cplusplus
}
#endif



