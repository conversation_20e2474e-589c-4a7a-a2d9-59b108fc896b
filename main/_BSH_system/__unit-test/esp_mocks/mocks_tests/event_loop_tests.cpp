#include "CppUTest/TestHarness.h"
#include "esp_event.h"

extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}


ESP_EVENT_DECLARE_BASE (TEST_BASE);
ESP_EVENT_DEFINE_BASE(TEST_BASE);

static void event_handler (void* event_handler_arg,
                                        esp_event_base_t event_base,
                                        int32_t event_id,
                                        void* event_data);
static esp_event_base_t handler_called_event_base;
static int32_t handler_called_event_id;



TEST_GROUP(event_loop)
{
    void setup()
    {
    }

    void teardown()
    {
        clear_esp_event_mock();  
    }
};



TEST(event_loop, some_test)
{
    printf("\n \n");
    esp_event_loop_args_t args = {
        .queue_size = 10,
        .task_name = "loop_task",
        .task_priority = 15,
        .task_stack_size = 4096,
        .task_core_id = 1,
    };

    esp_event_loop_handle_t handle = NULL;
    // print_event_arrays();
    esp_event_loop_create(&args, &handle);
    CHECK (handle != NULL);
    // print_event_arrays();


    // print_CB_array();
    // printf ("==== \n");
    esp_err_t err = esp_event_handler_register_with(handle, TEST_BASE, ESP_EVENT_ANY_ID, event_handler, NULL);
    CHECK(err == ESP_OK);
    // print_CB_array();

    handler_called_event_base = NULL;
    handler_called_event_id = 0;
    esp_event_post_to(handle, TEST_BASE, 12, NULL, 0, 0);
    CHECK(handler_called_event_base == TEST_BASE);
    CHECK(handler_called_event_id == 12);
    esp_event_post_to(handle, TEST_BASE, 15, NULL, 0, 0);
    CHECK(handler_called_event_base == TEST_BASE);
    CHECK(handler_called_event_id == 15);
}




static void event_handler (void* event_handler_arg,
                                        esp_event_base_t event_base,
                                        int32_t event_id,
                                        void* event_data)
{
    handler_called_event_base = event_base;
    handler_called_event_id = event_id;
}