#include "CppUTest/TestHarness.h"
#include "queue.h"

#include <iostream>



extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}

static void Dump( const void * mem, unsigned int n );

TEST_GROUP(create_queue)
{
    void setup()
    {
    }

    void teardown()
    {
        clear_queues();
    }
};

TEST(create_queue, handle_filled_ok)
{
    // first queue
    QueueHandle_t q = xQueueCreate( 3, 5 );
    CHECK(q->pcHead != NULL);
    CHECK(q->pcHead == q->pcWriteTo);
    CHECK(q->uxMessagesWaiting == 0);
    CHECK(q->queue_length == 3);
    CHECK(q->item_size == 5);

    // second queue
    QueueHandle_t q2 = xQueueCreate( 8, 50 );
    CHECK(q2->pcHead != NULL);
    CHECK(q2->pcHead == q2->pcWriteTo);
    CHECK(q2->uxMessagesWaiting == 0);
    CHECK(q2->queue_length == 8);
    CHECK(q2->item_size == 50);

    clear_queues();
}

TEST(create_queue, data_pushed_in_and_out_correctly)
{

    uint8_t data[] = {1,2,3,4,5};
    uint8_t data2[] = {6,7,8,9,10};
    uint8_t data3[] = {11,12,13,14,15};
    uint8_t out[5] = {};
    QueueHandle_t q = xQueueCreate( 3, 5 );

    // pulling from empty queue is err
    CHECK(xQueueReceive(q, out, 0) == errQUEUE_EMPTY);

    // single block    
    xQueueSend(q, data, 0);
    xQueueReceive(q, out, 0);
    MEMCMP_EQUAL(data, out, 5);

    // returns err when queue is full and ok when not full
    xQueueSend(q, data, 0);
    xQueueSend(q, data2, 0);
    CHECK(xQueueSend(q, data3, 0) == pdTRUE);
    CHECK(xQueueSend(q, data, 0) == errQUEUE_FULL);
    // Dump(q->pcHead, 15);

    // max amount of blocks - pushes out correct data
    xQueueReceive(q, out, 0);
    MEMCMP_EQUAL(data, out, 5);
    xQueueReceive(q, out, 0);
    MEMCMP_EQUAL(data2, out, 5);
    xQueueReceive(q, out, 0);
    MEMCMP_EQUAL(data3, out, 5);
    CHECK(xQueueReceive(q, out, 0) == errQUEUE_EMPTY);
}



static void Dump( const void * mem, unsigned int n ) {
  const char * p = reinterpret_cast< const char *>( mem );
  for ( unsigned int i = 0; i < n; i++ ) {
     std::cout << std::hex << int(p[i]) << " ";
  }
  std::cout << std::endl;
}