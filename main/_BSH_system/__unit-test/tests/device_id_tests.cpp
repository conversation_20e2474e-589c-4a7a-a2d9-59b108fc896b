#include <string.h>

#include "CppUTest/TestHarness.h"
#include "device_id.h"
#include "esp_bt_device.h"
#include "nvs.h"



extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}



// guid format: bork_device_q781_cac4b175-fffe-4b19-8359-456d8279b87b
TEST_GROUP(GUID)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
    }

    void teardown()
    {
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};

TEST(GUID, all_tests)
{
    // prefix is in place
return;
    using namespace bsh_sys::devid;

    const char * prefix = {"bork_device_A630_"};
    char out[GUID_SIZE+1] = {};

    generate_GUID (prefix, out);

    MEMCMP_EQUAL(prefix, out, strlen(prefix));

    // randoms_in_place
    for (size_t i = 0; i < GUID_SIZE-2; i++)
    {
        if (out[i]==0 && out[i+1]==0 && out[i+2]==0) FAIL(" ");
    }

    // dashes_are_in_place
        uint8_t pref_len = strlen(prefix);
    CHECK(out[pref_len + 8] == '-');
    CHECK(out[pref_len + 13] == '-');
    CHECK(out[pref_len + 18] == '-');
    CHECK(out[pref_len + 23] == '-');

    // no_buffer_misses
    char out2[GUID_SIZE+11] = {};

    generate_GUID (prefix, out2+5);

    uint8_t zeros[5] = {};

    MEMCMP_EQUAL(zeros, out2, 5);
    MEMCMP_EQUAL(zeros, out2 + GUID_SIZE + 11 - 7, 5);  // 7 coz 1 extra byte , 1 byte end of string and 5 bytes free space

    // two_guids_generates_are_different
    char out3[GUID_SIZE+1] = {};
    char out4[GUID_SIZE+1] = {};

    generate_GUID (prefix, out3);
    generate_GUID (prefix, out4);

    CHECK(memcmp (out3, out4, GUID_SIZE) != 0);

    // prefix_is_in_place_and_two_gens_in_a_row_return_same_pointer
    char *ress = get_guid (prefix);
    char *ress2 = get_guid (prefix);

    MEMCMP_EQUAL(prefix, ress, strlen(prefix));
    CHECK(ress == ress2);

    if (ress != ress2) free(ress2);
    free(ress);  // module itself doesn't suppose to free this memory, as guid always kept in memory
}



// TEST(get_guid, prefix_is_in_place_and_two_gens_in_a_row_return_same_pointer) // cant split these coz of free()
// {
//     using namespace bsh_sys::devid;

//     const char * prefix = {"bork_device_A630_"};

//     char * res = get_guid (prefix);
//     char *res2 = get_guid (prefix);

//     MEMCMP_EQUAL(prefix, res, strlen(prefix));
//     CHECK(res == res2);

//     if (res != res2) free(res2);
//     free(res);  // module itself doesn't suppose to free this memory, as guid always kept in memory
// }



static uint8_t fake_mac[] = {5,6,7,8,9,10};
// ID consists of 4 bytes of random + 6 bytes of mac address
TEST_GROUP(write_device_id_to_buffer)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
        set_fake_address(fake_mac);
    }

    void teardown()
    {
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};

TEST(write_device_id_to_buffer, randoms_are_in_place)
{
    using namespace bsh_sys::devid;

    uint8_t buff[10] = {};

    write_device_id_to_buffer(buff);

    uint8_t zeros[] = {0,0,0,0};

    CHECK (memcmp(buff,zeros,4) != 0);
    clean_data();
}

TEST(write_device_id_to_buffer, mac_is_in_place)
{
    using namespace bsh_sys::devid;

    uint8_t buff[10] = {};

    write_device_id_to_buffer(buff);

    CHECK (memcmp(buff + 4, fake_mac, 6) == 0);
    clean_data();
}

TEST(write_device_id_to_buffer, no_buffer_misses)
{
    using namespace bsh_sys::devid;

    uint8_t buff[10+10] = {};

    write_device_id_to_buffer(buff+5);

    uint8_t zeros[] = {0,0,0,0,0};
    MEMCMP_EQUAL(zeros, buff, 5);
    MEMCMP_EQUAL(zeros, buff + 15, 5);

    clean_data();
}

TEST(write_device_id_to_buffer, two_calls_in_a_row_are_ok)
{
    using namespace bsh_sys::devid;

    uint8_t buff[10] = {};
    write_device_id_to_buffer(buff);

    uint8_t tmp_mac[4] = {0};
    memcpy (tmp_mac, buff, 4);

    write_device_id_to_buffer(buff);

    CHECK (memcmp(buff + 4, fake_mac, 6) == 0);
    CHECK (memcmp(buff, tmp_mac, 4) == 0);
    clean_data();
}



static uint8_t *ptr = NULL;
TEST_GROUP(get_device_id)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
    }

    void teardown()
    {
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};

TEST(get_device_id, all_tests)
{
    using namespace bsh_sys::devid;

    set_fake_address(fake_mac);
    ptr = get_device_id();
    
    //ptr is no null
    CHECK(ptr != NULL);

    // randoms are in place
    uint8_t zeros[] = {0,0,0,0};
    CHECK(memcmp(zeros, ptr, 4) != 0);
    
    // mac is in place
    MEMCMP_EQUAL(fake_mac, ptr+4, 6);
    
    // two calls in a row are ok
    uint8_t *ptr2 = get_device_id();
    CHECK(ptr == ptr2);
    

    char buffer5[11] = {};
    char *buffer5_correct = "05060708090a";
    write_device_id_to_buffer_as_string(buffer5);
    MEMCMP_EQUAL(buffer5_correct, buffer5 + 8, 12);

    clean_data();
}









