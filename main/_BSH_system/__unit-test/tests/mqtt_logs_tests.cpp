#include <string.h>

#include "CppUTest/TestHarness.h"
#include "mqtt_logs_buffer.h"
#include "mqtt_logs.h"
#include "nvs.h"
#include "esp_timer.h"



extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}



TEST_GROUP(mqtt_logs)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
        bsh_sys::mqtt_logs::init(NULL, 0, NULL);
    }

    void teardown()
    {
        // clean_data();
        bsh_sys::mqtt_logs::deinit();
        // bsh_sys::mqtt_logs::logs_buffer::deinit_buffer();
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};



TEST(mqtt_logs, post_log_msg_checks)
{
    using namespace bsh_sys::mqtt_logs;

    return;

    set_time_for__esp_timer_get_time(1234567);

    // check no messages in the buffer after init
    CHECK (bsh_sys::mqtt_logs::logs_buffer::get_message() == NULL);
    CHECK(get_logs_level() == MQTT_LOG_LVL_OFF);

    // check low logs level messages ignored
    const char *msg = "aaaaaaaaaaa";
    post_log_msg(MQTT_LOG_LVL_INFO, "-test-",msg, NULL, 0);
    CHECK (bsh_sys::mqtt_logs::logs_buffer::get_message() == NULL);

    // check set_log_levl works
    set_log_level(MQTT_LOG_LVL_INFO);
    CHECK(get_logs_level() == MQTT_LOG_LVL_INFO);

    // check simple string message is ok

    post_log_msg(MQTT_LOG_LVL_INFO, "-test-",msg, NULL, 0);
    const char *correct_string = "\"version\": \"1.1\",\n\"host\": \"12345678901234567890\",\n\"short_message\": \"aaaaaaaaaaa\",\n\"full_message\": \"aaaaaaaaaaa\",\n\"level\": 3,\n\"_subsystem\": \"info\",\n\"_datetime\": \"00000000000001234567\",\n\"_userId\": \"(null)\",\n";
    MEMCMP_EQUAL(correct_string, bsh_sys::mqtt_logs::logs_buffer::get_message(), strlen(correct_string));

    // check formatted message is ok
    post_log_msg(MQTT_LOG_LVL_INFO, "-test-", "some text %i", NULL, 0, 15);
    const char *correct_string2 = "\"version\": \"1.1\",\n\"host\": \"12345678901234567890\",\n\"short_message\": \"some text 15\",\n\"full_message\": \"some text 15\",\n\"level\": 3,\n\"_subsystem\": \"info\",\n\"_datetime\": \"00000000000001234567\",\n\"_userId\": \"(null)\",\n";
    MEMCMP_EQUAL(correct_string2, bsh_sys::mqtt_logs::logs_buffer::get_message(), strlen(correct_string2));

    // check hexdump message is ok
    const char *correct_string3 = "\"version\": \"1.1\",\n\"host\": \"12345678901234567890\",\n\"short_message\": \"some text\",\n\"full_message\": \"some text  0a0b0c0e11\",\n\"level\": 3,\n\"_subsystem\": \"info\",\n\"_datetime\": \"00000000000001234567\",\n\"_userId\": \"(null)\",\n";

    uint8_t dump[] = {10,11,12,14,17};
    post_log_msg(MQTT_LOG_LVL_INFO, "-test-", "some text", dump, 5);
    MEMCMP_EQUAL(correct_string3, bsh_sys::mqtt_logs::logs_buffer::get_message(), strlen(correct_string3));

    // printf ("\n======================= %s \n", bsh_sys::mqtt_logs::logs_buffer::get_message());
}