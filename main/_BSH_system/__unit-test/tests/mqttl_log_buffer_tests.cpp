#include <string.h>

#include "CppUTest/TestHarness.h"
#include "mqtt_logs_buffer.h"


extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}



TEST_GROUP(mqtt_logs_buffer_tests)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
        CHECK(bsh_sys::mqtt_logs::logs_buffer::init_buffer(50) == 0);
    }

    void teardown()
    {
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};

TEST(mqtt_logs_buffer_tests, empty_buffer_returns_NULL_msg)
{
    using namespace bsh_sys::mqtt_logs::logs_buffer;
    const char *result = get_message();
    CHECK(result == NULL);
}


TEST(mqtt_logs_buffer_tests, single_message_pushed_and_poped)
{
    using namespace bsh_sys::mqtt_logs::logs_buffer;

    char *msg = "1234567890";
    push_message(msg);
    const char *result = get_message();
    MEMCMP_EQUAL(msg, result, strlen(msg) + 1);   // +1 to check strin bull termination

    result = get_message();
    CHECK(result == NULL);
}



TEST(mqtt_logs_buffer_tests, several_messaged_pushed_and_poped)
{
    using namespace bsh_sys::mqtt_logs::logs_buffer;

    const char *msg1 = "111111111";
    const char *msg2 = "222222222";
    const char *msg3 = "333333333";
    const char *msg4 = "444444444";
    push_message(msg1);
    push_message(msg2);
    push_message(msg3);
    push_message(msg4);
    const char *result1 = get_message();
    MEMCMP_EQUAL(msg1, result1, strlen(msg1) + 1);
    const char *result2 = get_message();
    MEMCMP_EQUAL(msg2, result2, strlen(msg2) + 1);
    const char *result3 = get_message();
    MEMCMP_EQUAL(msg3, result3, strlen(msg3) + 1);
    const char *result4 = get_message();
    MEMCMP_EQUAL(msg4, result4, strlen(msg4) + 1);
    const char *result5 = get_message();
    CHECK(result5 == NULL);
}



TEST(mqtt_logs_buffer_tests, buffer_cycling_back)
{
    using namespace bsh_sys::mqtt_logs::logs_buffer;

    const char *msg1 = "555555555555555"; // 17 in buffer
    const char *msg2 = "666666666666666"; // 34 in buffer 
    const char *msg3 = "777777777";   // 
    const char *msg4 = "88888888888888";
    const char *msg5 = "99999999999999";  
    // total 60 symbols with buffer size 50
    push_message(msg1);
    push_message(msg2);
    push_message(msg3);
    // one more push would overfill, so pop one message
    const char *result1 = get_message();
    MEMCMP_EQUAL(msg1, result1, strlen(msg1)+1);
    const char *result2 = get_message();
    MEMCMP_EQUAL(msg2, result2, strlen(msg2)+1);

    // push one more message
    push_message(msg4);
    push_message(msg5);

    // popping all of them
    const char *result3 = get_message();
    MEMCMP_EQUAL(msg3, result3, strlen(msg3)+1);
    const char *result4 = get_message();
    MEMCMP_EQUAL(msg4, result4, strlen(msg4)+1);
    const char *result5 = get_message();
    MEMCMP_EQUAL(msg5, result5, strlen(msg5)+1);
    const char *result6 = get_message();
    CHECK(result6 == NULL);
}



TEST(mqtt_logs_buffer_tests, buffer_overfilled)
{
    using namespace bsh_sys::mqtt_logs::logs_buffer;

    const char *msg1 = "aaaaaaaaaaaaaa";  // 17 in buff
    const char *msg2 = "bbbbbbbbbbbbbb";  // 34 in buff
    const char *msg3 = "ccccccccc";  // 51 in buff
    const char *msg4 = "ddddddddddddd";   // has to drop "aaa" message
    const char *msg5 = "eeeeeeeeeeeeeeeeeeeeeeeeee";       // big so buffer has to drop "bbb" and "ccc" message

    push_message(msg1);
    push_message(msg2);
    push_message(msg3);
    push_message(msg4);
    push_message(msg5);

    const char *result1 = get_message();
    MEMCMP_EQUAL(msg4, result1, strlen(msg4) +1);
    const char *result2 = get_message();
    MEMCMP_EQUAL(msg5, result2, strlen(msg5) +1);

    CHECK(get_message() == NULL);
}



TEST(mqtt_logs_buffer_tests, formatted_input)
{
    using namespace bsh_sys::mqtt_logs::logs_buffer;

    va_list input_args;
    // message without hex dump
    push_formatted_message(1, "some_tag", "some_message", NULL, 0, input_args);
    char *corect_msg = "0000000000000000||some_tag||some_message";
    MEMCMP_EQUAL(corect_msg, bsh_sys::mqtt_logs::logs_buffer::get_message() + 1, strlen(corect_msg));

    // message with hexdump
    uint8_t dump[] = {1,2,3,4,5, 10};
    push_formatted_message(1, "some_tag", "some_message", dump, 6, input_args);
    char *corect_msg2 = "0000000000000000||some_tag||some_message  01020304050a";
    MEMCMP_EQUAL(corect_msg, bsh_sys::mqtt_logs::logs_buffer::get_message() + 1, strlen(corect_msg));

    // printf ("\n======================= %s \n", bsh_sys::mqtt_logs::logs_buffer::get_message());
}