#include <string.h>
#include <iostream>
#include <filesystem>

#include "CppUTest/TestHarness.h"

#include "rsa_encryption.h"

// #include "rsa_encryption.cpp"

// #include "rsa_encryption.h"
// #include "ctr_drbg.h"
// #include "entropy.h"
// #include "error.h"
// #include "sha256.h"
// #include "ctr_drbg.h"



// extern const unsigned char RSA_public_key_start[]   asm("_binary_esp32_rsa_pub_pair_start"); 
// extern const unsigned char RSA_public_key_end[]   asm("_binary_esp32_rsa_pub_pair_end");
// extern const unsigned char RSA_private_key_start[]   asm("_binary_esp32_rsa_priv_pair_start");
// extern const unsigned char RSA_private_key_end[]   asm("_binary_esp32_rsa_priv_pair_end");
unsigned char RSA_public_key_start[2000]        asm("_binary_esp32_rsa_pub_pair_start"); 
unsigned char *RSA_public_key_end          asm("_binary_esp32_rsa_pub_pair_end");
unsigned char RSA_private_key_start[2000]       asm("_binary_esp32_rsa_priv_pair_start");
unsigned char *RSA_private_key_end         asm("_binary_esp32_rsa_priv_pair_end");

extern "C"
{
	/*
	 * Add your c-only include files here
	 */
}

static void read_public_key();
static void read_private_key();


// guid format: bork_device_q781_cac4b175-fffe-4b19-8359-456d8279b87b
static bool init_result = 0;
TEST_GROUP(rsa)
{
    void setup()
    {
        MemoryLeakWarningPlugin::saveAndDisableNewDeleteOverloads();
        read_public_key();
        read_private_key();

        init_result = bsh_sys::rsa::init();
    }

    void teardown()
    {
        MemoryLeakWarningPlugin::restoreNewDeleteOverloads();
    }
};



TEST(rsa, init)
{
    CHECK (init_result);
    CHECK(bsh_sys::rsa::get_init_status());
}



TEST (rsa, sign_verify)
{
    using namespace bsh_sys::rsa;
    const char *source_string = "asdfasfdsadfsddfsadfsdfsdfasdffsd";

    uint8_t signature[SIGNATURE_SIZE] = {};

    sign_data((const unsigned char *)source_string, strlen(source_string), signature);

    int res =  verify_signature ((const unsigned char *)source_string, strlen(source_string), (const unsigned char *)signature);

    CHECK(res == 0);
    CHECK(signature[0] != 0);
}



TEST (rsa, encrypt_decrypt)
{
    using namespace bsh_sys::rsa;
    const char *source_string = "asdfasfdsadfsddfsadfsdfsdfasdffsd";

    unsigned char encrypt_buff[SIGNATURE_SIZE] = {};
    int res = encrypt_data(source_string, strlen(source_string), encrypt_buff);
    CHECK(res == 0);

    unsigned char decrypt_buff[SIGNATURE_SIZE] = {};
    res = decrypt_data ((const unsigned char *)encrypt_buff, SIGNATURE_SIZE, decrypt_buff, SIGNATURE_SIZE);
    CHECK(res == 0);

    MEMCMP_EQUAL(source_string, decrypt_buff, strlen(source_string));
}



TEST(rsa, calculate_sha256_hash)
{
    using namespace bsh_sys::rsa;
    const char *source_string = "asdfasfdsadfsddfsadfsdfsdfasdffsd";
    const uint8_t correct_result[] = {0xa8, 0xb7, 0xaf, 0x41, 0xb9, 0xc8, 0xce, 0xe6, 0x38, 0xbd, 0x55, 0x98, 0x94, 0x9a, 0xcc, 0xf9, 0x8c, 0x1b, 0x5a, 0x13, 0x1a, 0x34, 0x2c, 0xbf, 0x34, 0x28, 0xb2, 0x23, 0x16, 0xcd, 0x1d, 0xd5};
    sha_256_hash_t res;

    int res_code = calculate_sha256_hash((const unsigned char*)source_string, strlen(source_string), &res);

    CHECK(res_code == 0);
    MEMCMP_EQUAL(correct_result, res.hash, 256/8);
}



TEST(rsa, compare_sha256_hashes)
{
    using namespace bsh_sys::rsa;
    const char *source_string = "asdfasfdsadfsddfsadfsdfsdfasdffsd";
    sha_256_hash_t res1;
    sha_256_hash_t res2;

    int res_code = calculate_sha256_hash((const unsigned char*)source_string, strlen(source_string), &res1);
    CHECK(res_code == 0);
    res_code = calculate_sha256_hash((const unsigned char*)source_string, strlen(source_string), &res2);
    CHECK(res_code == 0);

    CHECK(compare_sha256_hashes((char *)res1.hash, (char*)res2.hash));
}



TEST(rsa, get_random)
{
    uint32_t prev_rnd = 0;
    for (size_t i = 0; i < 10; i++)
    {
        uint32_t rnd = bsh_sys::rsa::get_random();
        CHECK(prev_rnd != rnd);
        prev_rnd = rnd;
    }
}



// Positive tests for fix_pem_linebreaks function
TEST(rsa, fix_pem_linebreaks_valid_without_breaks)
{
    using namespace bsh_sys::rsa;

    // certificate without line breaks 
    char pem_buffer[1000] = "-----BEGIN CERTIFICATE-----MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKBUAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQHcfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-----END CERTIFICATE-----";
    int free_space = 100;

    // Expected result with proper line breaks (exactly 64 characters per line)
    const char* expected_result = "-----BEGIN CERTIFICATE-----\n"
                                  "MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxv\n"
                                  "Y2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNV\n"
                                  "BAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKB\n"
                                  "UAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQH\n"
                                  "cfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz01\n"
                                  "23456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123\n"
                                  "456789ABCDEFGHIJKLMNOPQRSTUVWXYZ\n"
                                  "-----END CERTIFICATE-----";

    int result = fix_pem_linebreaks(pem_buffer, free_space);

    CHECK_EQUAL(1, result); // Should return 1 (line breaks were added)

    // Verify the entire result matches expected format
    STRCMP_EQUAL(expected_result, pem_buffer);

    // Additional verification: check specific positions
    const char* begin_marker = "-----BEGIN CERTIFICATE-----";
    char* begin_pos = strstr(pem_buffer, begin_marker);
    CHECK(begin_pos != NULL);
    CHECK_EQUAL('\n', begin_pos[strlen(begin_marker)]);
}



TEST(rsa, fix_pem_linebreaks_valid_with_existing_breaks)
{
    using namespace bsh_sys::rsa;

    // certificate with proper line breaks already in place
    char pem_buffer[1000] = "-----BEGIN CERTIFICATE-----\n"
                            "MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxv\n"
                            "Y2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNV\n"
                            "BAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKB\n"
                            "UAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQH\n"
                            "cfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz01\n"
                            "23456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123\n"
                            "456789ABCDEFGHIJKLMNOPQRSTUVWXYZ\n"
                            "-----END CERTIFICATE-----";
    int free_space = 50;

    char original_buffer[1000];
    strcpy(original_buffer, pem_buffer);

    int result = fix_pem_linebreaks(pem_buffer, free_space);

    CHECK_EQUAL(0, result); // Should return 0 (no changes needed)

    STRCMP_EQUAL(original_buffer, pem_buffer);
}



// Negative tests for fix_pem_linebreaks function
TEST(rsa, fix_pem_linebreaks_null_pointer)
{
    using namespace bsh_sys::rsa;

    int result = fix_pem_linebreaks(NULL, 100);

    CHECK_EQUAL(-1, result);
}



TEST(rsa, fix_pem_linebreaks_zero_free_space)
{
    using namespace bsh_sys::rsa;

    // certificate without line breaks
    char pem_buffer[1000] = "-----BEGIN CERTIFICATE-----MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKBUAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQHcfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-----END CERTIFICATE-----";

    // Store original for comparison
    char original_buffer[1000];
    strcpy(original_buffer, pem_buffer);

    int result = fix_pem_linebreaks(pem_buffer, 0);

    CHECK_EQUAL(-1, result); // Should return -1 for zero free space
}



TEST(rsa, fix_pem_linebreaks_negative_free_space)
{
    using namespace bsh_sys::rsa;

    // certificate without line breaks
    char pem_buffer[1000] = "-----BEGIN CERTIFICATE-----MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKBUAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQHcfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-----END CERTIFICATE-----";

    // Store original for comparison
    char original_buffer[1000];
    strcpy(original_buffer, pem_buffer);

    int result = fix_pem_linebreaks(pem_buffer, -5);

    CHECK_EQUAL(-1, result); // Should return -1 for negative free space
}



TEST(rsa, fix_pem_linebreaks_missing_begin_marker)
{
    using namespace bsh_sys::rsa;

    // certificate without BEGIN marker (but with END marker)
    char pem_buffer[1000] = "MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKBUAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQHcfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-----END CERTIFICATE-----";
    int free_space = 50;

    // Store original for comparison
    char original_buffer[1000];
    strcpy(original_buffer, pem_buffer);

    int result = fix_pem_linebreaks(pem_buffer, free_space);

    CHECK_EQUAL(-1, result); // Should return -1 for missing BEGIN marker
}



TEST(rsa, fix_pem_linebreaks_missing_end_marker)
{
    using namespace bsh_sys::rsa;

    // certificate with BEGIN marker but without END marker
    char pem_buffer[1000] = "-----BEGIN CERTIFICATE-----MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKBUAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQHcfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    int free_space = 50;

    // Store original for comparison
    char original_buffer[1000];
    strcpy(original_buffer, pem_buffer);

    int result = fix_pem_linebreaks(pem_buffer, free_space);

    CHECK_EQUAL(-1, result); // Should return -1 for missing END marker
}



TEST(rsa, fix_pem_linebreaks_end_before_begin)
{
    using namespace bsh_sys::rsa;

    // Invalid PEM with END marker before BEGIN marker
    char pem_buffer[1000] = "-----END CERTIFICATE----------BEGIN CERTIFICATE-----MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKBUAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQHcfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    int free_space = 50;

    // Store original for comparison
    char original_buffer[1000];
    strcpy(original_buffer, pem_buffer);

    int result = fix_pem_linebreaks(pem_buffer, free_space);

    CHECK_EQUAL(-1, result); // Should return -1 when END marker comes before BEGIN
}



TEST(rsa, fix_pem_linebreaks_insufficient_space)
{
    using namespace bsh_sys::rsa;

    // certificate without line breaks that would need more space than available
    char pem_buffer[1000] = "-----BEGIN CERTIFICATE-----MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKBUAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQHcfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-----END CERTIFICATE-----";
    int free_space = 1; // Not enough space for all required line breaks

    // Store original for comparison
    char original_buffer[1000];
    strcpy(original_buffer, pem_buffer);

    int result = fix_pem_linebreaks(pem_buffer, free_space);

    CHECK_EQUAL(-1, result); // Should return -1 for insufficient space
}



TEST(rsa, fix_pem_linebreaks_memory_boundary_test)
{
    using namespace bsh_sys::rsa;

    // Test with exact memory boundaries
    char pem_buffer[1000];
    const char* input_pem = "-----BEGIN CERTIFICATE-----MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKBUAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQHcfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-----END CERTIFICATE-----";

    strcpy(pem_buffer, input_pem);
    int original_length = strlen(pem_buffer);
    int free_space = 50;

    int result = fix_pem_linebreaks(pem_buffer, free_space);

    CHECK_EQUAL(1, result); // Should return 1 (line breaks were added)

    // Verify the buffer length increased (due to added newlines)
    int new_length = strlen(pem_buffer);
    CHECK(new_length > original_length);

    // Verify null terminator is still present
    CHECK_EQUAL('\0', pem_buffer[new_length]);

    // Verify no buffer overflow occurred by checking a few bytes after the string
    // (This assumes the buffer was initialized properly)
    for (int i = new_length + 1; i < new_length + 10 && i < 1000; i++) {
        // These should be uninitialized or zero, not part of our string
        CHECK(pem_buffer[i] != 'M' && pem_buffer[i] != 'I'); // Not certificate data
    }
}



TEST(rsa, fix_pem_linebreaks_exact_space_requirement)
{
    using namespace bsh_sys::rsa;

    // Test with exactly the required space
    char pem_buffer[1000] = "-----BEGIN CERTIFICATE-----MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNVBAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKBUAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQHcfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ-----END CERTIFICATE-----";

    // Calculate exactly how many line breaks are needed
    // 1 after BEGIN + line breaks every 64 chars + 1 before END if needed
    const char* begin_pos = strstr(pem_buffer, "-----BEGIN CERTIFICATE-----");
    const char* end_pos = strstr(pem_buffer, "-----END CERTIFICATE-----");
    int content_length = end_pos - (begin_pos + strlen("-----BEGIN CERTIFICATE-----"));
    int required_breaks = 1 + (content_length / 64); // 1 after BEGIN + breaks in content
    if (*(end_pos - 1) != '\n') required_breaks++; // 1 before END if needed

    int free_space = required_breaks; // Exactly what's needed

    int result = fix_pem_linebreaks(pem_buffer, free_space);

    CHECK_EQUAL(1, result); // Should return 1 (line breaks were added)

    const char* expected_result = "-----BEGIN CERTIFICATE-----\n"
                                  "MIIBkTCB+wIJAKnL502ridDhMA0GCSqGSIb3DQEBCwUAMBQxEjAQBgNVBAMMCWxv\n"
                                  "Y2FsaG9zdDAeFw0yMzEwMTAwMDAwMDBaFw0yNDEwMDkwMDAwMDBaMBQxEjAQBgNV\n"
                                  "BAMMCWxvY2FsaG9zdDBcMA0GCSqGSIb3DQEBAQUAA0sAMEgCQQC7VJTUt9Us8cKB\n"
                                  "UAEUzPww3IuD5FqmHdwcDvM1SixtlDL7rt2DdcYXqiBLdVvKtfQHdcNzU70a5mQH\n"
                                  "cfHYCpVBAgMBAAEwDQYJKoZIhvcNAQELBQADQQBcxaFHijklmnopqrstuvwxyz01\n"
                                  "23456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123\n"
                                  "456789ABCDEFGHIJKLMNOPQRSTUVWXYZ\n"
                                  "-----END CERTIFICATE-----";

    STRCMP_EQUAL(expected_result, pem_buffer);
}





#include <fstream>
#include <string>



static void print_key(char *start, char* end)
{
    printf("\n key:\n");
    for (size_t i = 0; i < end - start; i++)
    {
        printf("%02x", *(start+i));
        if (i != 0 && i%16 == 0) printf("\n");
    }
    printf("\n key end \n \n");
}




static void read_public_key()
{
    using namespace std;
    using namespace bsh_sys::rsa;

    string file_name("esp32_rsa_pub_pair");
    ifstream ifs(file_name.c_str(), ios::in | ios::binary | ios::ate);

    ifstream::pos_type file_size = ifs.tellg();
    ifs.seekg(0, ios::beg);

    // std::cout<< std::filesystem::current_path();
    // printf("\n key file size: %i \n", file_size);

    ifs.read((char *)RSA_public_key_start, file_size);
    RSA_public_key_end = RSA_public_key_start + file_size +1;

    // print_key((char *)RSA_public_key_start, (char *)RSA_public_key_end);
}



static void read_private_key()
{
    using namespace std;
    using namespace bsh_sys::rsa;

    string file_name("esp32_rsa_priv_pair");
    ifstream ifs(file_name.c_str(), ios::in | ios::binary | ios::ate);

    ifstream::pos_type file_size = ifs.tellg();
    ifs.seekg(0, ios::beg);

    // printf("\n key file size: %i \n", file_size);

    ifs.read((char *)RSA_private_key_start, file_size);
    RSA_private_key_end = RSA_private_key_start + file_size +1;

    // print_key((char *)RSA_private_key_start, (char *)RSA_private_key_end);
}










