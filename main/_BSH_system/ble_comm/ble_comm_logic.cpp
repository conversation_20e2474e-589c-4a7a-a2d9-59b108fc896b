#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <stdlib.h>

#include "esp_log.h"
#include "esp_timer.h"

#include "system.h"
#include "system_internal.h"
#include "ble_comm_logic.h"
#include "wifi.h"
#include "mqtt.h"
#include "rsa_encryption.h"
#include "bluetooth_LE.h"
#include "nvs_flash.h"
#include "device_id.h"
#include "commissioning.h"
#include "mqtt_sys_protocol.h"


// TODO remove mallocs for small pieces of memory. allocate it on stack !!!!!!!!!


#define LOG_LEVEL ESP_LOG_INFO

// cell phone commands and device replies
#define SET_WIFI_SSID_START               0x10
#define SET_WIFI_SSID_DATA                0x11
#define SET_WIFI_SSID_FINISH              0x12
#define SET_WIFI_SSID_CANCEL              0x13
 
#define SET_WIFI_PSWD_START               0x20
#define SET_WIFI_PSWD_DATA                0x21
#define SET_WIFI_PSWD_FINISH              0x22
#define SET_WIFI_PSWD_CANCEL              0x23
 
#define CONNECT                           0x30
#define CONNECT_TO_WIFI                   0x31
#define CONNECT_TO_BALANCER               0x32
#define CONNECT_TO_BROKER                 0x33

#define SET_BALANCER_LINK_START           0x40
#define SET_BALANCER_LINK_DATA            0x41
#define SET_BALANCER_LINK_FINISH          0x42
#define SET_BALANCER_LINK_CANCEL          0x43

#define SET_DEVICE_MQTT_PASSW_START       0x50
#define SET_DEVICE_MQTT_PASSW_DATA        0x51
#define SET_DEVICE_MQTT_PASSW_FINISH      0x52
#define SET_DEVICE_MQTT_PASSW_CANCEL      0x53

#define SET_USER_ID_START                 0x60
#define SET_USER_ID_DATA                  0x61
#define SET_USER_ID_FINISH                0x62
#define SET_USER_ID_CANCEL                0x63

#define SEND_MODEL                        0x70
#define SEND_ID                           0x80
#define SEND_FIRM_VERSION                 0x90
#define SEND_CHALLENGE                    0xA0
#define SEND_CHALLENGE_SIGN_SIZE          0xB0
#define SEND_CHALLENGE_SIGN_PIECE_SIZE    0xB1   // size / 20
#define SEND_CHALLENGE_SIGN_PIECE         0xB2   // too big to send in signle packet

#define OTA_SET_OTA_CERT_START            0xC0
#define OTA_SET_OTA_CERT_DATA             0xC1
#define OTA_SET_OTA_CERT_FINISH           0xC2
#define OTA_SET_OTA_CERT_CANCEL           0xC3
                                                 // reques also contating piece number, starting from 1
#define RESET_CMD                         0xF1   // should follow F2 and F3: 0xF1 0xF2 0xF3 in single packet
#define SEND_TO_UART                      0xF5
#define MQTT_PKT_VIA_BLE                  0xF6   // puts data into mqtt incoming queue 
#define ENABLE_TLM_TO_BLE                 0xF7   // enables sending telemetry to BLE  
#define START_TEST                        0xF9   // starts test function
#define OTA_HARDCODED_LINK                0xFA   // link leads to bork nextcloud folder - meant for development purposes
#define OTA_PROD                          0xFB   // link to be passed in same packet. meant for production ota updates
#define OTA_SET_DONT_VERIFY_SERVER        0xFC   // + second byte: 1 = dont verify 0 = verify (default). set it before starting OTA

#define DEVICE_SPECIFIC_CMD               0xFE   // some device-specific command. module translates DEVICE_COMMAND event with command as parameter

    #define SET_START_PACK_LENGTH       5 
    #define SET_FINISH_PACK_LENGTH      1 
    #define SET_CANCEL_PACK_LENGTH      1 



// device replies all start from 
#define DEVICE_REPLY_START 0xAA

#define CHALLENGE_SIZE 14   // TODO check size. = 15?? // 1 byte packet start + 4 random bytes + 10 bytes of ID
#define SIGN_PIECE_SIZE 17   

#define free_pointer_if_not_null(P) if(P != NULL) {free(P); P=NULL;}



namespace bsh_sys {

ESP_EVENT_DEFINE_BASE (BLE_COMMAND_EVENTS);

namespace ble_comm {



static const char *TAG = "-ble_comm-";
static uint32_t random_piece_of_challenge = 0;
static uint8_t *challenge_signature_buff = NULL;

int last_command_from_phone = 0xFFFF;
int64_t last_command_time = 0;
#define NO_REPLY_FILTER_TIME 90*1000*1000 // microseconds. no reply filtering happens after this time passed since last command.



/* ======= local function declarations ========= */
static int check_packet_integrity (uint8_t *data, uint16_t length);
static void send_reply_to_phone (uint8_t reply_to_what, uint8_t repeated_piece_N, uint8_t status);
static void process_ota_packet(uint8_t *pkt, uint16_t length);
static void ota_progress_cb(int percent);
static void start_test();

// helpers to treat data pieces, when received
static void getting_data_init_helper(const char *data, char **out_param, uint16_t *out_param_len, uint8_t *out_piece_size, uint8_t *out_piece_qty);
static void getting_data_proceed_helper (const char *data, char **out_param, uint8_t *out_piece_size);
static bool getting_data_finish_helper (const char *data, char **out_param, uint16_t *out_param_len, uint8_t *out_piece_size, uint8_t *out_piece_qty);
static void getting_data_cancel_helper (char *data, char **out_param);



/*=================================================================*\
 * exported function definitions
\*=================================================================*/

void got_ble_packet(uint8_t *data, uint16_t length)
{
    // esp_log_level_set(TAG, LOG_LEVEL);

    static char *ssid = NULL;
    static uint16_t ssid_length = 0;
    static uint8_t ssid_piece_size = 0;
    static uint8_t ssid_piece_qty = 0;

    static char *pswd = NULL;
    static uint16_t pswd_length = 0;
    static uint8_t pswd_piece_size = 0;
    static uint8_t pswd_piece_qty = 0;

    static char *link = NULL;
    static uint16_t link_length = 0;
    static uint8_t link_piece_size = 0;
    static uint8_t link_piece_qty = 0;

    static char *mq_psw = NULL;
    static uint16_t mq_psw_length = 0;
    static uint8_t mq_psw_piece_size = 0;
    static uint8_t mq_psw_piece_qty = 0;

    static char *mq_id = NULL;
    static uint16_t mq_id_length = 0;
    static uint8_t mq_id_piece_size = 0;
    static uint8_t mq_id_piece_qty = 0;

    static char *ota_cert = NULL;
    static uint16_t ota_cert_length = 0;
    static uint8_t ota_cert_piece_size = 0;
    static uint8_t ota_cert_piece_qty = 0;


    uint8_t *buff = NULL;
    uint8_t bf[10]; //buffer for short replies

    uint8_t shift_pos;
    uint32_t tmp;
    
    int ret = check_packet_integrity(data, length);
    if (ret) 
    {
        ESP_LOGE (TAG, "wrong packet. err:%i", ret);
        ESP_LOG_BUFFER_HEXDUMP(TAG, data, length < 200 ? length : 200 , ESP_LOG_ERROR);
        send_reply_to_phone(*data, 0, PACKET_ERR);
        return;
    }

    if (
        *data == SEND_MODEL || 
        *data == SEND_CHALLENGE || 
        *data == SEND_FIRM_VERSION 
        ) 
        {
            bsh_sys::mqtt::stop_reconnecting();
            bsh_sys::blsr::http_request_stop_reconnect();
            bsh_sys::wifi::stop_reconnecting();
        }

    last_command_from_phone = *data;
    last_command_time = esp_timer_get_time();

    switch (*data)
    {
    //  --------- SET WIFI SSID -----------
    case SET_WIFI_SSID_START:
        ESP_LOGI (TAG,"started setting SSID");
        getting_data_init_helper ((char *)data, &ssid, &ssid_length, &ssid_piece_size, &ssid_piece_qty);
        break;

    case SET_WIFI_SSID_DATA:
        ESP_LOGI (TAG,"got SSID data");
        getting_data_proceed_helper ((char *)data, &ssid, &ssid_piece_size);
        break;

    case SET_WIFI_SSID_FINISH:
        ESP_LOGI (TAG,"finishing setting SSID");
        if (getting_data_finish_helper ((char *)data, &ssid, &ssid_length, &ssid_piece_size, &ssid_piece_qty))
        {
            *(ssid+ssid_length) = '\0';
            commiss::set_wifi_ssid (ssid);
            free_pointer_if_not_null(ssid);
            send_reply_to_phone(*data, 0, WIFI_SSID_ACCEPTED);
        }
        break;

    case SET_WIFI_SSID_CANCEL:
        ESP_LOGI (TAG,"cancelling setting SSID");
        getting_data_cancel_helper ((char *)data, &ssid);
        break;


    //  --------- SET WIFI PSWD -----------
    case SET_WIFI_PSWD_START:
        ESP_LOGI (TAG,"started setting PSWD");
        getting_data_init_helper ((char *)data, &pswd, &pswd_length, &pswd_piece_size, &pswd_piece_qty);
        break;

    case SET_WIFI_PSWD_DATA:
        ESP_LOGI (TAG,"got PSWD data");
        getting_data_proceed_helper ((char *)data, &pswd, &pswd_piece_size);
        break;

    case SET_WIFI_PSWD_FINISH:
        ESP_LOGI (TAG,"finishing setting PSWD");
        if (getting_data_finish_helper ((char *)data, &pswd, &pswd_length, &pswd_piece_size, &pswd_piece_qty))
        {
            *(pswd+pswd_length) = '\0';
            bsh_sys::commiss::set_wifi_passw (pswd);
            free_pointer_if_not_null(pswd);
            send_reply_to_phone(*data, 0, WIFI_PSWD_ACCEPTED);
        }
        break;

    case SET_WIFI_PSWD_CANCEL:
        ESP_LOGI (TAG,"cancelling setting PSWD");
        getting_data_cancel_helper ((char *)data, &pswd);
        break;

    case CONNECT:
        bsh_sys::connect_to_smart_home();
        break;

    case CONNECT_TO_WIFI:
        {
            no_autoconnect();
            connect_to_wifi(); // connection status updates reported by system module via conn_result()
        }
        break;

    case CONNECT_TO_BALANCER:
        {
            no_autoconnect();
            connect_to_balancer();
        }
        break;

    case CONNECT_TO_BROKER:
        {
            no_autoconnect();
            connect_to_broker();
        }
        break;

    //  --------- SET BALANCER LINK -----------
    case SET_BALANCER_LINK_START:
        ESP_LOGI (TAG,"started setting balancer link");
        getting_data_init_helper ((char *)data, &link, &link_length, &link_piece_size, &link_piece_qty);
        break;

    case SET_BALANCER_LINK_DATA:
        ESP_LOGI (TAG,"got link data");
        getting_data_proceed_helper ((char *)data, &link, &link_piece_size);
        break;

    case SET_BALANCER_LINK_FINISH:
        ESP_LOGI (TAG,"finishing setting link");
        if (getting_data_finish_helper ((char *)data, &link, &link_length, &link_piece_size, &link_piece_qty))
        {
            *(link+link_length) = '\0';
            commiss::set_balancer_link (link);
            free_pointer_if_not_null(link);
            send_reply_to_phone(*data, 0, BALANCER_LINK_ACCEPTED);
        }
        break;

    case SET_BALANCER_LINK_CANCEL:
        ESP_LOGI (TAG,"cancelling setting link");
        getting_data_cancel_helper ((char *)data, &link);
        break;

    //  --------- SET MQTT PASSWORD -----------
    case SET_DEVICE_MQTT_PASSW_START:
        ESP_LOGI (TAG,"started setting mqtt passw");
        getting_data_init_helper ((char *)data, &mq_psw, &mq_psw_length, &mq_psw_piece_size, &mq_psw_piece_qty);
        break;

    case SET_DEVICE_MQTT_PASSW_DATA:
        ESP_LOGI (TAG,"got mqtt passw data");
        getting_data_proceed_helper ((char *)data, &mq_psw, &mq_psw_piece_size);
        break;

    case SET_DEVICE_MQTT_PASSW_FINISH:
        ESP_LOGI (TAG,"finishing setting mqtt passw");
        if (getting_data_finish_helper ((char *)data, &mq_psw, &mq_psw_length, &mq_psw_piece_size, &mq_psw_piece_qty))
        {
            *(mq_psw + mq_psw_length) = '\0';
            commiss::set_mqtt_passw (mq_psw);
            free_pointer_if_not_null(mq_psw);
            send_reply_to_phone(*data, 0, MQTT_PSWD_ACCEPTED);
        }
        break;

    case SET_DEVICE_MQTT_PASSW_CANCEL:
        ESP_LOGI (TAG,"cancelling setting mqtt passw");
        getting_data_cancel_helper ((char *)data, &mq_psw);
        break;

    //  --------- SET MQTT ID -----------
    case SET_USER_ID_START:
        ESP_LOGI (TAG,"started setting mqtt id");
        getting_data_init_helper ((char *)data, &mq_id, &mq_id_length, &mq_id_piece_size, &mq_id_piece_qty);
        break;

    case SET_USER_ID_DATA:
        ESP_LOGI (TAG,"got mqtt id data");
        getting_data_proceed_helper ((char *)data, &mq_id, &mq_id_piece_size);
        break;

    case SET_USER_ID_FINISH:
        ESP_LOGI (TAG,"finishing setting mqtt id");
        if (getting_data_finish_helper ((char *)data, &mq_id, &mq_id_length, &mq_id_piece_size, &mq_id_piece_qty))
        {
            *(mq_id+mq_id_length) = '\0';
            commiss::set_mqtt_user_id(mq_id);
            free_pointer_if_not_null(mq_id);
            send_reply_to_phone(*data, 0, MQTT_USER_ID_ACCEPTED);
        }
        break;

    case SET_USER_ID_CANCEL:
        ESP_LOGI (TAG,"cancelling setting mqtt id");
        getting_data_cancel_helper ((char *)data, &mq_id);
        break;

    //  ---------- set OTA server verify certificate ----------------
    case OTA_SET_OTA_CERT_START:
        ESP_LOGI (TAG,"started setting ota cert");
        getting_data_init_helper ((char *)data, &ota_cert, &ota_cert_length + OTA_CERT_EXTRA_SPACE_FOR_LINE_BREAKS, &ota_cert_piece_size, &ota_cert_piece_qty);
        break;

    case OTA_SET_OTA_CERT_DATA:
        ESP_LOGI (TAG,"got ota cert data");
        getting_data_proceed_helper ((char *)data, &ota_cert, &ota_cert_piece_size);
        break;

    case OTA_SET_OTA_CERT_FINISH:
        ESP_LOGI (TAG,"finishing setting ota cert");
        if (getting_data_finish_helper ((char *)data, &ota_cert, &ota_cert_length, &ota_cert_piece_size, &ota_cert_piece_qty))
        {
            if(strlen(ota_cert) > 8000)
            {
                ESP_LOGE(TAG,"too big sertificate. length: %i", strlen(ota_cert));
            } else {
                int res = bsh_sys::rsa::fix_pem_linebreaks(ota_cert, strlen(ota_cert) + OTA_CERT_EXTRA_SPACE_FOR_LINE_BREAKS);
                if (res == 0 || res == 1)
                {
                    bsh_sys::ota::save_new_sertificate(ota_cert);
                } else {
                    ESP_LOGE(TAG, "failed to fix ota cert linebreaks");
                    break;
                }
            }
            free_pointer_if_not_null(ota_cert);
            send_reply_to_phone(*data, 0, OTA_CERT_ACCEPTED);
        }
        break;

    case OTA_SET_OTA_CERT_CANCEL:
        ESP_LOGI (TAG,"cancelling setting ota cert");
        getting_data_cancel_helper ((char *)data, &ota_cert);
        break;

    //  --------- SEND commands -----------    
    case SEND_MODEL:
        {
        uint8_t buffer[3 + strlen(get_ble_device_name())] = {0};
        *buffer = DEVICE_REPLY_START;
        *(buffer+1) = SEND_MODEL;
        *(buffer+2) = strlen(get_ble_device_name());
        memcpy (buffer + 3, get_ble_device_name(), strlen(get_ble_device_name()));
        bsh_sys::ble::send_via_bt(buffer, 3 + strlen(get_ble_device_name()));
        }
        break;

    case SEND_ID:
        {
        uint8_t buffer[3 + DEVICE_ID_SIZE] = {0};
        *buffer = DEVICE_REPLY_START;
        *(buffer+1) = SEND_ID;
        *(buffer+2) = DEVICE_ID_SIZE;
        devid::write_device_id_to_buffer(buffer + 3);
        bsh_sys::ble::send_via_bt(buffer, 3 + DEVICE_ID_SIZE);
        }
        break;

    case SEND_FIRM_VERSION:
        {
        uint8_t buffer[6] = {0};
        *buffer = DEVICE_REPLY_START;
        *(buffer+1) = SEND_FIRM_VERSION;
        *(buffer+2) = 0;
        *(buffer+3) = get_system_config()->firware_version_major;
        *(buffer+4) = get_system_config()->firmware_version_minor;
        *(buffer+5) = get_system_config()->firmware_version_patch;
        bsh_sys::ble::send_via_bt(buffer, 6);
        }
        break;

    case SEND_CHALLENGE:
        {
        uint8_t buffer[CHALLENGE_SIZE + 2] = {0};
        *buffer = DEVICE_REPLY_START;
        *(buffer+1) = SEND_CHALLENGE;

        if (random_piece_of_challenge == 0)
        {
            tmp = bsh_sys::rsa::get_random();
            memcpy (&random_piece_of_challenge, &tmp, 4);
        }

        memcpy (buffer + 2, &random_piece_of_challenge,4);
        devid::write_device_id_to_buffer(buffer+4+2);
        bsh_sys::ble::send_via_bt(buffer, CHALLENGE_SIZE + 2);
        }
        break;

    case SEND_CHALLENGE_SIGN_SIZE:
        {
        uint8_t buffer[4] = {0};
        *buffer = DEVICE_REPLY_START;
        *(buffer + 1) = SEND_CHALLENGE_SIGN_SIZE;
        *((uint16_t *)(buffer+2)) = SIGNATURE_SIZE;
        bsh_sys::ble::send_via_bt(buffer, 4);
        }
        break;

    case SEND_CHALLENGE_SIGN_PIECE_SIZE:
        {
        uint8_t buffer[3] = {0};
        *buffer = DEVICE_REPLY_START; 
        *(buffer + 1) = SEND_CHALLENGE_SIGN_PIECE_SIZE;
        *(buffer + 2) = SIGN_PIECE_SIZE;
        bsh_sys::ble::send_via_bt(buffer, 3);
        }
        break;

    case SEND_CHALLENGE_SIGN_PIECE:

        // wrong piece number
        if (*(data+1) > SIGNATURE_SIZE / SIGN_PIECE_SIZE)
        {
            send_reply_to_phone(*data, 0, PACKET_ERR);
            break;
        }
        // challenge into buff
        if (random_piece_of_challenge == 0)
        {
            tmp = bsh_sys::rsa::get_random();
            memcpy (&random_piece_of_challenge, &tmp, 4);
        }

        // if signature is not generatred yet - generate
        if (challenge_signature_buff == NULL)
        {
            uint8_t buffer[CHALLENGE_SIZE] = {0};
            memcpy (buffer, &random_piece_of_challenge, 4);
            devid::write_device_id_to_buffer(buffer+4);

            // signature
            challenge_signature_buff = (uint8_t*)malloc (SIGNATURE_SIZE); // +2 here to make BT packet from this data later on
            bsh_sys::rsa::sign_data((unsigned char *)buffer, CHALLENGE_SIZE, (unsigned char *)challenge_signature_buff);
        }

        {

        // send piece
        shift_pos = (*(data+1)) * SIGN_PIECE_SIZE;  // piece N * piece size

        uint8_t send_buff[30] = {0};
        send_buff[0] = DEVICE_REPLY_START;
        send_buff[1] = SEND_CHALLENGE_SIGN_PIECE;

        // if last piece
        if (*(data+1) == SIGNATURE_SIZE / SIGN_PIECE_SIZE)  // this is correct. no -1 needed as numeration starts from 0
        {
            memcpy (&(send_buff[2]), challenge_signature_buff + shift_pos ,SIGN_PIECE_SIZE);

            bsh_sys::ble::send_via_bt(send_buff, 
                2 + SIGNATURE_SIZE - SIGN_PIECE_SIZE * (*(data+1) )   );
        } else {
            memcpy (&(send_buff[2]), challenge_signature_buff + shift_pos ,SIGN_PIECE_SIZE);
            bsh_sys::ble::send_via_bt(send_buff, SIGN_PIECE_SIZE + 2);
        }
        }
        break;

    // system commands
    case RESET_CMD:
        if (data[1] == 0xF2 && data[2] == 0xF3)
        {
            ESP_LOGE(TAG, "Restarting");
            esp_restart();
        }
        break;

    case SEND_TO_UART:
        bf[0] = 0xAA;
        bf[1] = SEND_TO_UART;
        bf[2] = 2; // data len
        bf[3] = 'O';
        bf[4] = 'K';
        bsh_sys::ble::send_via_bt(bf, 5);
        bsh_sys::uart::put_to_queue(data + 2, data[1]);
        break;

    case MQTT_PKT_VIA_BLE:
        ESP_LOGI(TAG, "Mqtt pkt via ble:");
        ESP_LOG_BUFFER_HEXDUMP (TAG, data, length, ESP_LOG_INFO);
        bsh_sys::mqtt_protocol::send_tlm_to_ble(true);
        bsh_sys::mqtt_protocol::treat_mqtt_packet(data+2, length-2);
        break;

    case ENABLE_TLM_TO_BLE:
        bsh_sys::mqtt_protocol::send_tlm_to_ble(data[1] != 0);
        break;

    case START_TEST:
        bf[0] = 0xAA;
        bf[1] = START_TEST;
        bf[2] = 2; // data len
        bf[3] = 'O';
        bf[4] = 'K';

        bsh_sys::ble::send_via_bt(bf, 5);
        start_test();
        break;

    case OTA_HARDCODED_LINK:
        bf[0] = 0xAA;
        bf[1] = OTA_HARDCODED_LINK;
        bf[2] = 2; // data len
        bf[3] = 'O';
        bf[4] = 'K';
        bsh_sys::ble::send_via_bt(bf, 5);
        process_ota_packet(data, length);
        break;

    case OTA_PROD:
        bf[0] = 0xAA;
        bf[1] = OTA_PROD;
        bf[2] = 2; // data len
        bf[3] = 'O';
        bf[4] = 'K';
        bsh_sys::ble::send_via_bt(bf, 5);
        process_ota_packet(data, length);
        break;

    case OTA_SET_DONT_VERIFY_SERVER:
        if(length >= 2)
        {
            bf[0] = 0xAA;
            bf[1] = OTA_SET_DONT_VERIFY_SERVER;
            bf[2] = 2; // data len
            bf[3] = 'O';
            bf[4] = 'K';
            bsh_sys::ble::send_via_bt(bf, 5);
            bsh_sys::ota::set_dont_verify_cert_flag(data[1] == 0 ? false : true );
        } else {
            ESP_LOGW(TAG,"wrong 'set_dont+verify' packet");
            send_reply_to_phone(*data, 0, PACKET_ERR);
        } 

        break;

    case DEVICE_SPECIFIC_CMD:
        // TODO
        if (length < 2) {
            ESP_LOGW (TAG,"wrong ble packet: no data except command byte");
            break;
        }

        esp_event_post_to(bsh_sys::get_event_loop_handle(), bsh_sys::BLE_COMMAND_EVENTS, *(data+1), NULL, 0, 0);

        break;

    default:
        send_reply_to_phone(*data, 0, PACKET_ERR);
        break;
    }
}



void conn_result(conn_result_t result)
{
    int l_cmd = last_command_from_phone;
    bool do_no_filtering = esp_timer_get_time() - l_cmd > NO_REPLY_FILTER_TIME;

    switch (result)
    {
        case CN_RES_WIFI_SSID_NOT_SET:
        case CN_RES_WIFI_PASSW_NOT_SET:
        case CN_RES_WIFI_CONNECTED:
        case CN_RES_WIFI_CONN_ERR_NETWORK_NOT_FOUND:
        case CN_RES_WIFI_CONN_ERR_WRONG_PASSW:
        // case CN_RES_WIFI_DISCONNECTED:   // adnroid app confused by sending disconnected to re-connect start
        case CN_RES_WIFI_CONNECTING:
        case CN_RES_WIFI_CONN_ERR_NO_IP:
            if(do_no_filtering || l_cmd == CONNECT || l_cmd == CONNECT_TO_WIFI)
            {
                send_reply_to_phone(CONNECT, 0, (uint8_t)result);
            }
            break;

        case CN_RES_BLR_GOT_LINK:
        case CN_RES_BLR_CANT_REACH_BALANCER:
        case CN_RES_BLR_CANT_PARCE_LINK:
        case CN_RES_BLR_NO_LINK_SET:  
        case CN_RES_BLR_CONNECTING:
        case CN_RES_BLR_NO_WIFI:
            if(do_no_filtering || l_cmd == CONNECT || l_cmd == CONNECT_TO_BALANCER)
            {
                send_reply_to_phone(CONNECT_TO_BALANCER, 0, (uint8_t)result);
            }
            break;

        case CN_RES_MQTT_NO_USER_NAME_SET:
        case CN_RES_MQTT_NO_USER_PSW_SET:
        case CN_RES_MQTT_BROKER_CONNECTED:
        case CN_RES_MQTT_BROKER_SUBSCRIBED:
        case CN_RES_MQTT_BROKER_MSG_PUBLISHED:
        case CN_RES_MQTT_BROKER_CANT_REACH_SERVER:
        case CN_RES_MQTT_BROKER_CONNECTION_REFUSED:
        case CN_RES_MQTT_BROKER_NO_LINK_SET:
        // case CN_RES_MQTT_BROKER_DISCONNECTED: // adnroid app confused by sending disconnected to re-connect start
        case CN_RES_MQTT_BROKER_CONNECTING:
        case CN_RES_MQTT_BROKER_NO_WIFI:
            if(do_no_filtering || l_cmd == CONNECT || l_cmd == CONNECT_TO_BROKER)
            {
                send_reply_to_phone(CONNECT_TO_BROKER, 0, (uint8_t)result);
            }
            break;

        default:
            break;
        }
}



/*=================================================================*\
 * local function definitions
\*=================================================================*/

// Checks packet for compliance with protocol. Returns 0 if ok, else - error code
static int check_packet_integrity (uint8_t *data, uint16_t length)
{
    // ESP_LOGW ("========","check packet: %i", *data);
    switch (*data)
    {
    case SET_WIFI_SSID_START:
    case SET_WIFI_PSWD_START:
    case SET_BALANCER_LINK_START:
    case SET_DEVICE_MQTT_PASSW_START:
    case SET_USER_ID_START:
        // check proper data length
        if (length != SET_START_PACK_LENGTH) return PACKET_ERR;
        return 0;
        break;
    case SET_WIFI_SSID_DATA:
    case SET_WIFI_PSWD_DATA:
    case SET_BALANCER_LINK_DATA:
    case SET_DEVICE_MQTT_PASSW_DATA:
    case SET_USER_ID_DATA:
        // check proper data length
        if (*(data+2) + 3 != length) return PACKET_ERR;
        return 0;
        break;
    case SET_WIFI_SSID_FINISH:
    case SET_WIFI_PSWD_FINISH:
    case SET_BALANCER_LINK_FINISH:
    case SET_DEVICE_MQTT_PASSW_FINISH:
    case SET_USER_ID_FINISH:
        // check proper data length
        if (length != SET_FINISH_PACK_LENGTH) return PACKET_ERR;
        return 0;
        break;
    case SET_WIFI_SSID_CANCEL:
    case SET_WIFI_PSWD_CANCEL:
    case SET_BALANCER_LINK_CANCEL:
    case SET_DEVICE_MQTT_PASSW_CANCEL:
    case SET_USER_ID_CANCEL:
        // check proper data length
        if (length != SET_CANCEL_PACK_LENGTH) return PACKET_ERR;
        return 0;
        break;
    case SEND_MODEL:                        
    case SEND_ID:
    case SEND_FIRM_VERSION:
    case SEND_CHALLENGE:                
    case SEND_CHALLENGE_SIGN_SIZE:
    case SEND_CHALLENGE_SIGN_PIECE_SIZE:
    case CONNECT:
    case CONNECT_TO_WIFI:
    case CONNECT_TO_BALANCER:
    case CONNECT_TO_BROKER:
        {

        // single byte command. check length = 1
        if (length != 1) return PACKET_ERR;
        return 0;
        break;
        }
    case SEND_CHALLENGE_SIGN_PIECE:
        if (length != 2) return PACKET_ERR;
        else return 0;
        break;

    case RESET_CMD:
        if (data[1] == 0xF2 && data[2] == 0xF3) return 0;
        else return PACKET_ERR;

    case SEND_TO_UART:
        if (data[1] == length - 2) return 0;
        else return PACKET_ERR;
        break;

    case START_TEST:
        return 0;

    case MQTT_PKT_VIA_BLE:
        if (*(data+1) != length - 2) return PACKET_ERR;
        return 0;

    case ENABLE_TLM_TO_BLE:
        if(length != 2) return PACKET_ERR;
        return 0;

    case OTA_SET_OTA_CERT_START:
        if(length != 5) return PACKET_ERR;
        if(*(data+1) > 10000 || *(data+1) < 10) return PACKET_ERR; // too big or too small certificate, or most probably code err. 
        return 0;
    case OTA_HARDCODED_LINK:
    case OTA_PROD:
    case OTA_SET_DONT_VERIFY_SERVER:
    case OTA_SET_OTA_CERT_DATA:
    case OTA_SET_OTA_CERT_FINISH:
    case OTA_SET_OTA_CERT_CANCEL:
        return 0;

    default:
        return PACKET_ERR; // = wrong command N
        break;
    }

    return PACKET_ERR;
}



/**
 * Send reply to phone
 * @param reply_to_what command we reply to
 * @param repeated_piece_N piece number we reply to - for multiple data pieces transfer
 * @param status  status we are sending
 */
static void send_reply_to_phone (uint8_t reply_to_what, uint8_t repeated_piece_N, uint8_t status)
{
    static uint8_t buff[4] = {};
    buff[0] = DEVICE_REPLY_START;
    buff[1] = reply_to_what;
    buff[2] = repeated_piece_N;
    buff[3] = status;
    bsh_sys::ble::send_via_bt(buff, 4);
}



static void  getting_data_init_helper(const char *data, 
                                        char **out_param, 
                                        uint16_t *out_param_len, 
                                        uint8_t *out_piece_size, 
                                        uint8_t *out_piece_qty) 
{
    if (*out_param != NULL) 
    {
        send_reply_to_phone(*data, 0, SETTING_ALREADY_STARTED);
        return;
    }

    *out_piece_qty = *(data + 3);
    *out_piece_size = *(data + 4);
    *out_param_len = *((uint16_t *)(data+1));
    *out_param = (char *)calloc(1, *out_param_len +1); //+1 in case of string. to add \0
    if (*out_param == NULL) 
    {
        send_reply_to_phone(*data, 0, INTERNAL_ERR);
        return;
    }
    send_reply_to_phone(*data, 0, CMD_OK);
}



static void getting_data_proceed_helper (const char *data,
                                         char **out_param, 
                                         uint8_t *out_piece_size)
{
    if (*out_param == NULL) 
    {
        send_reply_to_phone(*data, *(data + 1), SETTING_NOT_STARTED);
        return;
    }
    int piece_N = *(data+1);
    memcpy (*out_param + ((piece_N-1) * (*out_piece_size)) , data+3 , *(data+2));
    send_reply_to_phone(*data, *(data+1), CMD_OK);
}



static bool getting_data_finish_helper (const char *data, char **out_param, uint16_t *out_param_len, uint8_t *out_piece_size, uint8_t *out_piece_qty)
{
    if (*out_param == NULL) 
    { 
        send_reply_to_phone(*data, 0, SETTING_NOT_STARTED); 
        return false;
    }

    if ( *(data+1) == *out_piece_qty)  // if this is last piece - check total param length is ok
    {
        if (strlen(*out_param) != *out_param_len)
        {
            ESP_LOGW (TAG,"total length doesn't match piece sizes. 1: %u out param len %u",(*(data+1)-1) * (*out_piece_size) + (*(data + 2)), *out_param_len);
            // send_reply_to_phone(*data, 0, TOTAL_LENGTH_DOESNT_MATCH_PIECES);
            // return false;
        } 
    }    
    return true;
}



static void getting_data_cancel_helper (char *data, char **out_param)
{
    if (*out_param == NULL) 
    {
        send_reply_to_phone(*data, 0, SETTING_NOT_STARTED);
        return;
    }

    free_pointer_if_not_null(*out_param);
    send_reply_to_phone(*data, 0, CMD_OK);
}



static void start_test()
{
    ESP_LOGW (TAG,"- starting test - ");

    // fungene ota - friware available
    uint8_t test_pckt4[] = {0x55, 0xAA, 0x03, 0x07,   0x00, 0x08,   0xCA, 0x02, 0x00, 0x04,  0x00,0x01,0x00,0x00, 0xE2};
    get_uart_settings()->incoming_data_cb(test_pckt4, sizeof(test_pckt4));

    return;
    
    // heartbeat
    uint8_t test_pckt[] = {0x55, 0xAA, 0x03, 0x00, 0x00, 0x01, 0x00, 0x03};
    get_uart_settings()->incoming_data_cb(test_pckt, sizeof(test_pckt));

    // single dp point
    uint8_t test_pckt2[] = {0x55, 0xaa, 0x00, 0x06, 0x00, 0x08, 0x66, 0x02, 0x00, 0x01, 0x00, 0x00, 0x00, 0x1e, 0x94};
    get_uart_settings()->incoming_data_cb(test_pckt2, sizeof(test_pckt2));

    // multiple dp points
    uint8_t test_pckt3[] = {0x55, 0xaa, 0x03, 0x07, 0x00, 0x10, 0x66, 0x02, 0x00, 0x01, 0x00, 0x00, 0x00, 0x1e, 0x67, 0x02, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01, 0x0B};
    get_uart_settings()->incoming_data_cb(test_pckt3, sizeof(test_pckt3));
}



static void process_ota_packet(uint8_t *pkt, uint16_t length)
{
    if (pkt[0] != OTA_PROD && pkt[0] != OTA_HARDCODED_LINK) 
    {
        ESP_LOGE (TAG,"not ota pkt");
        return;
    }

    uint16_t len = pkt[1] + pkt[2]*256;
    if (pkt[0] == OTA_PROD && len + 3 != length)
    {
        ESP_LOGE (TAG,"ota pkt wrong length");
        return;
    }

    bsh_sys::blsr::http_request_stop_reconnect();
    ESP_LOGW (TAG,"ota cmd received");


    // save ota started
    nvs_handle save_handle;
    ESP_ERROR_CHECK(nvs_open("settings", NVS_READWRITE, &save_handle));
    ESP_ERROR_CHECK(nvs_set_u8(save_handle, "ota_start", (uint8_t)true));
    ESP_ERROR_CHECK(nvs_commit(save_handle));
    nvs_close(save_handle);
    ESP_LOGI(TAG,"ota started saved (ble)");

    if (pkt[0] == OTA_HARDCODED_LINK)
    {
        bsh_sys::ota::ota_start(get_system_config()->ota_hardcoded_link, 
                                    get_event_loop_handle(), 
                                    bsh_sys::commiss::get_mqtt_user_id(),
                                    bsh_sys::commiss::get_mqtt_passw());
    } else {
        char *link = (char *)malloc(len+1);
        memset(link, 0, len+1);
        memcpy(link, pkt+3, len);
        
        bsh_sys::ota::ota_start(link, 
                                    get_event_loop_handle(), 
                                    bsh_sys::commiss::get_mqtt_user_id(),
                                    bsh_sys::commiss::get_mqtt_passw());
    }
}



static void ota_progress_cb(int percent)
{
    uint8_t buff[8] = {};
    buff[0] = 'o';
    buff[1] = 't';
    buff[2] = 'a';

    snprintf ((char*)buff + 4, 3, "%i", percent);

    buff[7] = (uint8_t)percent;
    bsh_sys::ble::send_via_bt(buff, 8);
}

    } // bsh_sys::ble_comm namespace
} // bsh_sys namespace