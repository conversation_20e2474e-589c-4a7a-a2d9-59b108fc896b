/**
 *      This module reveives, saves and loads
 *      commissioning credentials:
 *      wifi ssid & passw
 *      mqtt broker login and passw
 *      balancer link
 */


#pragma once



namespace bsh_sys::commiss {


void init ();


// !!! all functions below accepts strings, e.i. shall be 0 at the end !!!

// WIFI credentials
void set_wifi_ssid(char *ssid);
void set_wifi_passw(char *psw);

char *get_wifi_ssid();
char *get_wifi_passw();



// MQTT credentials
void set_mqtt_user_id(char *id);  
void set_mqtt_passw(char *passw); 

char *get_mqtt_user_id();
char *get_mqtt_passw();
char *get_dev_id_as_string();



// balancer
void set_balancer_link(char* addr);
char *get_balancerlink();



} // commiss namespace