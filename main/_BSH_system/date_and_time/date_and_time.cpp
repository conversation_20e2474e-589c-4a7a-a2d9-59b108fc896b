#include <string.h>
#include <math.h>
#include <time.h>

#include "esp_log.h"
#include "esp_timer.h"

#include "date_and_time.h"
#include "mqtt_logs.h"



namespace bsh_sys::date_time {



/* ======= local object declarations =========*/
static const char *TAG = "-sys-";
static date_and_time_t date = {
    .week_day = SUN,
    .day_n = 0,
    .month = JAN,
    .year = 0,
    .hour = 0,
    .min = 0,
    .sec = 0,
};
static esp_timer_handle_t date_timer = NULL;


/* ======= local function declarations ========= */
static bool parse_week_day (const char *data, week_day_t *res);
static bool parse_month (const char *data, month_t *res);
static bool parse_int (const char *data, uint8_t data_len, uint16_t *res);
static void date_timer_cb(void *);



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
esp_err_t set_time_from_header(const char *data)
{

    bool res = true;

    res &= parse_week_day(data, &(date.week_day));
    res &= parse_month(data + 8, &(date.month));
    res &= parse_int(data + 5, 2, (uint16_t *)&(date.day_n));
    res &= parse_int(data + 12, 4, &(date.year));
    res &= parse_int(data + 17, 2, (uint16_t *)&(date.hour));
    res &= parse_int(data + 20, 2, (uint16_t *)&(date.min));
    res &= parse_int(data + 23, 2, (uint16_t *)&(date.sec));
    
    if (res != true)
    {
        date.year = 0;
        ESP_LOGE (TAG,"cant parse date");
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_ERR, TAG, "Cant parse time", NULL, 0);
        return ESP_ERR_NOT_FOUND;
    }

    if (date.day_n > 31 || date.hour > 24 || date.min > 60 || date.sec > 60)
    {
        date.year = 0;
        ESP_LOGE (TAG,"cant parse date");
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_ERR,TAG, "Cant parse time", NULL, 0);
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGW (TAG, "parsed date: year %i, day %i, month %i, week_day %i, hour %i, min %i, sec %i",
            date.year, date.day_n, date.month, date.week_day,
            date.hour, date.min, date.sec);


    if (date_timer == NULL)
    {
        const esp_timer_create_args_t timer_args = {date_timer_cb, 0, {}, 0, 0};

        int ret = 0;
        if((ret = esp_timer_create(&timer_args, &date_timer)) != ESP_OK) {
            ESP_LOGE(TAG,"cant create timer %d", ret);
            return ESP_ERR_NO_MEM;
        }
    }

    esp_timer_start_periodic (date_timer, 1000000);
    char buf[50] = {};
    snprintf(buf,50,"Time set to %i.%i.%i-%i:%i",date.year, date.month, date.day_n, date.hour, date.min), 
    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, buf, NULL, 0);
    return ESP_OK;
}



const date_and_time_t *get_date_time()
{
    return &date;
}



void write_date_time(date_and_time_t * where)
{
    memcpy(where, &date, sizeof(date_and_time_t));
}



void substract_seconds(date_and_time_t * where, uint64_t seconds)
{
    struct tm time_info = {0};

    time_info.tm_year = where->year - 1900;
    time_info.tm_mon = where->month; 
    time_info.tm_mday = where->day_n;
    time_info.tm_hour = where->hour;
    time_info.tm_min = where->min;
    time_info.tm_sec = where->sec;

    time_t initial_time = mktime(&time_info);

    initial_time -= seconds;
    if (initial_time < 0) initial_time = 0;

    struct tm *new_time_info = localtime(&initial_time);

    where->year = new_time_info->tm_year + 1900;
    where->month = (month_t)new_time_info->tm_mon;
    where->day_n = new_time_info->tm_mday;
    where->hour = new_time_info->tm_hour;
    where->min = new_time_info->tm_min;
    where->sec = new_time_info->tm_sec;
}



void date_time_to_string(char *where, date_and_time_t *d_t)
{
    snprintf(where, 10, "%04u-", d_t->year);
    snprintf(where + 5, 10, "%02u-", (uint8_t)d_t->month + 1);
    snprintf(where + 5 + 3, 10, "%02uT", d_t->day_n);

    snprintf(where + 5 + 3 + 3, 10, "%02u:", d_t->hour);
    snprintf(where + 5 + 3 + 3 + 3, 10, "%02u:", d_t->min);
    snprintf(where + 5 + 3 + 3 + 3 + 3, 10, "%02u", d_t->sec);
    *(where + 19) = '\0';
}



/*===============================================*\
 * Local function definitions
\*===============================================*/

/*
 * expects *data pointing right to first letter of day
 * and date to be one of Sun, Mon, Tue, Wen, Thu, Fri, Sat
 */ 
static bool parse_week_day (const char *data, week_day_t *res)
{
    char to_parse[4] = {'\0'};
    memcpy (to_parse, data, 3);

    // ESP_LOGW ("-", "prs day: %s %s", data, to_parse);

    if      (strcmp(to_parse, "Sun") == 0) { *res = SUN; } 
    else if (strcmp(to_parse, "Mon") == 0) { *res = MON; }
    else if (strcmp(to_parse, "Tue") == 0) { *res = TUE; }
    else if (strcmp(to_parse, "Wed") == 0) { *res = WEN; }
    else if (strcmp(to_parse, "Thu") == 0) { *res = THU; }
    else if (strcmp(to_parse, "Fri") == 0) { *res = FRI; }
    else if (strcmp(to_parse, "Sat") == 0) { *res = SAT; }
    else { return false; }
    // ESP_LOGW ("-", "ok: %i", *res);
    return true;
}



/*
 * expects *data pointing right to first letter of month
 * and date to be one of Jan, Feb, Mar, Apr, May, Jun, Jul, Aug, Sep, Oct, Nov, Dec
 */ 
static bool parse_month (const char *data, month_t *res)
{
    char to_parse[4] = {'\0'};
    memcpy (to_parse, data, 3);

    // ESP_LOGW ("-", "prs month: %s %s", data, to_parse);

    if      (strcmp(to_parse, "Jan") == 0) { *res = JAN; } 
    else if (strcmp(to_parse, "Feb") == 0) { *res = FEB; }
    else if (strcmp(to_parse, "Mar") == 0) { *res = MAR; }
    else if (strcmp(to_parse, "Apr") == 0) { *res = APR; }
    else if (strcmp(to_parse, "May") == 0) { *res = MAY; }
    else if (strcmp(to_parse, "Jun") == 0) { *res = JUN; }
    else if (strcmp(to_parse, "Jul") == 0) { *res = JUL; }
    else if (strcmp(to_parse, "Aug") == 0) { *res = AUG; }
    else if (strcmp(to_parse, "Sep") == 0) { *res = SEP; }
    else if (strcmp(to_parse, "Oct") == 0) { *res = OCT; }
    else if (strcmp(to_parse, "Nov") == 0) { *res = NOV; }
    else if (strcmp(to_parse, "Dec") == 0) { *res = DEC; }
    else {return false;} 
    // ESP_LOGW ("-", "ok: %i", *res);
    return true;
}



/*
 * Expects *data pointing to first digit of the integer
 */
static bool parse_int (const char *data, uint8_t data_len, uint16_t *res)
{
    uint64_t parsed = 0;

    if (data_len > 5) return false; // cant fit into uint16_t

    for (size_t i = 0; i < data_len; i++)
    {
        // return error if non-digits
        if (*(data + i) < 48 || *(data + i) > 57) return false;
        parsed += (*(data+i)-48) * pow(10, data_len - i - 1);
    }
    // printf ("----- %llu   \n", parsed);
    if (parsed > 0xFFFF) return false; // cant fit into uint16_t
    
    *res = (uint16_t)parsed;
    return true;
}



static void date_timer_cb(void *)
{
    date.sec ++;
    if (date.sec == 60)
    {
        date.sec = 0;
        date.min++;

        if(date.min == 60)
        {
            date.min = 0;
            date.hour++;
            if (date.hour == 24)
            {
                date.hour = 0;
                date.week_day = (week_day_t)(date.week_day + 1);
                if (date.week_day == SAT + 1) date.week_day = SUN;
        
                date.day_n ++;
                if ( 
                    ((date.month == JAN || date.month == MAR || date.month == MAY
                      || date.month == JUL || date.month == AUG || date.month == OCT || date.month == DEC) && date.day_n == 32 )

                    || ((date.month == APR || date.month ==  JUN || date.month == SEP || date.month == NOV) && date.day_n == 31)

                    || (date.month == FEB && date.day_n == 29)
                    )
                {
                    date.day_n = 1;
                    date.month = (month_t)(date.month + 1);

                    if (date.month == DEC + 1) 
                    {
                        date.month = JAN;
                        date.year++;
                    }
                }
            }
        }
    }
}



}  // bsh_sys::date_time