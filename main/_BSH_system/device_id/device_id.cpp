#include <stdbool.h>
#include <stdint.h>
#include <stdlib.h>
#include <string.h>

#include "nvs.h"
#include "esp_log.h"
#include "esp_bt_device.h"
#include "esp_random.h"

#include "device_id.h"
#include "mqtt_logs.h"



namespace bsh_sys::devid {



/* ======= local defines for constants =========*/
static const char *TAG = "-sys-";
static const char *GUID_PREFIX = NULL;
static bool id_loaded = false;
static uint8_t device_id [DEVICE_ID_SIZE];
static char *guid = NULL;



/* ======= local function declarations ========= */
static void load_device_id();



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
uint8_t * get_device_id()
{
    if (!id_loaded) load_device_id();
    return device_id;
}



void write_device_id_to_buffer (uint8_t *dest_buffer)
{
    if (dest_buffer == NULL) 
    {
        ESP_LOGE(TAG,"null buffer for ID");
        return;
    }

    if (!id_loaded) load_device_id();

    memcpy (dest_buffer, device_id, DEVICE_ID_SIZE);
}



int  write_device_id_to_buffer_as_string(char *buffer)
{
    if (!id_loaded) load_device_id();

    for (size_t i = 0; i < DEVICE_ID_SIZE; i++)
    {
        snprintf (buffer + i*2, 3 , "%02x",*(device_id+i));
    }
    buffer[DEVICE_ID_SIZE*2] = '\0';
    return DEVICE_ID_SIZE*2;
}



void generate_GUID (const char *guid_prefix, char * buff)
{

    if (guid_prefix == NULL || buff == NULL)
    {
        ESP_LOGE (TAG, "null pointer parameter");
        return;
    }

    GUID_PREFIX = guid_prefix;

    uint8_t rnd_piece_offset = strlen(GUID_PREFIX); // offset to random portion of guid
    uint8_t rnd_piece_size = GUID_SIZE - strlen(GUID_PREFIX);
    // copy prefix
    strcpy (buff, GUID_PREFIX);

    // generate random hex
    uint8_t rnd_buff[rnd_piece_size];
    esp_fill_random(rnd_buff, rnd_piece_size);

    // convert hex to string
    char tmp[3];
    for (size_t i = 0; i < rnd_piece_size; i++)
    {
        snprintf (tmp, 3 , "%02x", rnd_buff[i]);
        memcpy (buff + rnd_piece_offset + i, tmp, 1);
    }

    // set "-" marks and end_of_string
    for (size_t i = 0; i < 37; i++)
    {
        if (i == 8 || i == 13 || i == 18 || i == 23) 
        {
            *(buff + rnd_piece_offset + i) = '-';
        } 
        if (i == 36) 
        {
            *(buff + rnd_piece_offset + i) = '\0';
        } 
    }
    ESP_LOGW (TAG, "GUID generated: %s", buff);
    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "GUID generated: ", (uint8_t *)buff, GUID_SIZE);

}



char * get_guid(const char *guid_prefix)
{

    if (guid == NULL)
    {
        guid = (char *) malloc(GUID_SIZE);
    }

    generate_GUID(guid_prefix, guid);

    return guid;
}



/*===============================================*\
 * Local functions definitions
\*===============================================*/
static void load_device_id()
{
    nvs_handle _handle;
    esp_err_t err;
    size_t size = DEVICE_ID_SIZE;
    
    ESP_ERROR_CHECK(nvs_open("sys_cfg", NVS_READWRITE, &_handle));
    err = nvs_get_blob(_handle, "dev_ID", device_id, &size);
    if (err == ESP_ERR_NVS_NOT_FOUND || err == ESP_ERR_NVS_INVALID_NAME) {
        // generate random 4 bytes of ID
        bool generated_ok = false;
        uint8_t counter = 0;
        do
        {
            esp_fill_random(device_id, 4);
            generated_ok = true;
            for (size_t i = 1; i < 4; i++) {
                if (device_id[0] == device_id[i]) generated_ok = false;
            }
            counter ++;
        } while ( !(generated_ok || counter > 10));
        if (counter > 10) {
            ESP_LOGE (TAG,"can't generate random ID");
            ESP_LOG_BUFFER_HEXDUMP(TAG, device_id, 4, ESP_LOG_INFO);
        }
        // put MAC to last 6 bytes
        const uint8_t *tmp = esp_bt_dev_get_address();
        if (tmp == NULL)
        {
            ESP_LOGE (TAG, "can't get BLE ID: BLE not initiated");
            return;
        }
        memcpy (device_id + 4, tmp, 6);
        // save id
        err = nvs_set_blob(_handle, "dev_ID", device_id, DEVICE_ID_SIZE);
        ESP_ERROR_CHECK(nvs_commit(_handle));
        ESP_LOGI (TAG, "generated device ID");
        ESP_LOG_BUFFER_HEXDUMP(TAG, device_id, DEVICE_ID_SIZE, ESP_LOG_INFO);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG,"Generated device id: ", device_id, DEVICE_ID_SIZE);
    } else {
        ESP_LOGW (TAG, "loaded device ID");
        ESP_LOG_BUFFER_HEXDUMP(TAG, device_id, DEVICE_ID_SIZE, ESP_LOG_INFO);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "Loaded device id: ", device_id, DEVICE_ID_SIZE);
    }
    id_loaded = true;
    ESP_ERROR_CHECK(err);
    nvs_close(_handle);
}



} // bsh_sys::devid namespace