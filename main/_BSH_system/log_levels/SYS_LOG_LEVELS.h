#pragma once

#include "esp_log.h"

#include "logs_levels.h"


#if CONFIG_BUILD_TYPE_DEBUG             // defined in sdkconfig via Kconfig
    #define LOGS_OFF_BY_DEFAULT 0       
#elif CONFIG_BUILD_TYPE_RELEASE
    #define LOGS_OFF_BY_DEFAULT 1       // for release versions
#endif



typedef struct {
    bsh_sys::log_levels::module_t id;
    char tag[20];
    esp_log_level_t level;
} module_log_level_t;



// using namespace bsh_sys::log_levels;
static module_log_level_t default_log_levels[] = 
{

    { bsh_sys::log_levels::OTA,          "-ota-",              ESP_LOG_WARN},
    { bsh_sys::log_levels::WIFI,         "-wifi-",             ESP_LOG_WARN},
    { bsh_sys::log_levels::BLE,          "-ble-",              ESP_LOG_WARN},
    { bsh_sys::log_levels::BLE_COMM,     "-ble_comm-",         ESP_LOG_WARN},
    { bsh_sys::log_levels::BLE_LOGGING,  "-ble_logging-",      ESP_LOG_WARN},
    { bsh_sys::log_levels::SYS,          "-sys-",              ESP_LOG_WARN},
    { bsh_sys::log_levels::LOGS,         "-logs-",             ESP_LOG_WARN},
    { bsh_sys::log_levels::MQTT,         "-mqtt-",             ESP_LOG_WARN},
    { bsh_sys::log_levels::MQTT_LOGS,    "-mqtt_logs-",        ESP_LOG_WARN},
    { bsh_sys::log_levels::MQTT_SYS_PROT,"-sys_mqtt_prot-",    ESP_LOG_WARN},
    { bsh_sys::log_levels::BLCR_REQ,     "-blcr_req-",         ESP_LOG_WARN},
    { bsh_sys::log_levels::RSA,          "-rsa-",              ESP_LOG_ERROR},
    { bsh_sys::log_levels::TELEM_STORAGE,"-telem_storage-",    ESP_LOG_ERROR},
    { bsh_sys::log_levels::UART,         "-uart-",             ESP_LOG_ERROR},

    { bsh_sys::log_levels::EMU,          "-emu-",              ESP_LOG_ERROR},
    { bsh_sys::log_levels::DEVICE,       "-device-",           ESP_LOG_ERROR},

    { bsh_sys::log_levels::ALL_OTHER,    "*",                  ESP_LOG_INFO},

};