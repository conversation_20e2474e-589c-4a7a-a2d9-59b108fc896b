/**
 *  This module is used to set logs levels for different modules.
 *  Log levels can be saved to and loaded from NVS using save_log_levels() and load_log_levels().
 *  Intention is to manually save and load logs settings via ble terminal commands.
 *  No automatic save / laod to happen.
 *  Defaults are defined in SYS_LOG_LEVELS.h
 */

#include <string.h>

#include "esp_log.h"
#include "nvs_flash.h"
#include "nvs.h"

#include "logs_levels.h"
#include "SYS_LOG_LEVELS.h"



static const char* TAG = "-logs-";



namespace bsh_sys::log_levels {



static bool logs_on = false;



static const char* log_level_to_string(esp_log_level_t level);



/*===============================================*\
 * Exported functions definitions
\*===============================================*/

// Static initialization - called when module is first loaded
void __attribute__((constructor)) init_log_levels()
{
    turns_logs_on_off(LOGS_OFF_BY_DEFAULT);

    ESP_LOGI(TAG, "Log levels module initialized");
}



void set_log_level(module_t module, esp_log_level_t level) 
{
    default_log_levels[module].level = level;
    esp_log_level_set(default_log_levels[module].tag, level);
    ESP_LOGI(TAG, "Set log level for module %d %s to %d", module ,default_log_levels[module].tag, level);
}



void set_log_levels(const module_log_level_t *modules_arr, int size) 
{
    if (modules_arr == nullptr) 
    {
        ESP_LOGE(TAG, "Null pointer for modules array");
        return;
    }
    
    if (size <= 0 || size > MODULES_QTY) 
    {
        ESP_LOGE(TAG, "Invalid array size: %d", size);
        return;
    }
    
    for (int i = 0; i < size; i++) 
    {
        set_log_level(modules_arr[i].module, modules_arr[i].level);
    }
    
    ESP_LOGI(TAG, "Set log levels for %d modules", size);
}



esp_log_level_t get_log_level(module_t module) 
{
    return default_log_levels[module].level;
}



void print_log_levels() 
{
    ESP_LOGI(TAG, "Current log levels:");
    for (int i = 0; i < MODULES_QTY; i++) 
    {
        ESP_LOGI(TAG, "%d %s: %s",i, default_log_levels[i].tag, log_level_to_string(default_log_levels[i].level));
    }
}



void turns_logs_on_off(bool on_off)
{
    logs_on = on_off;
    ESP_LOGI(TAG, "Logs turned %s", on_off ? "on" : "off");
    if (on_off == true) 
    {
        for (int i = 0; i < MODULES_QTY; i++) 
        {
            esp_log_level_set(default_log_levels[i].tag, default_log_levels[i].level);
        }
    } else {
        for (int i = 0; i < MODULES_QTY; i++) 
        {
            esp_log_level_set(default_log_levels[i].tag, ESP_LOG_NONE);
        }
    }
}



bool are_logs_on()
{
    return logs_on;
}



void save_log_levels()
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    err = nvs_open("log_levels", NVS_READWRITE, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Error opening NVS handle: %s", esp_err_to_name(err));
        return;
    }

    size_t blob_size = sizeof(default_log_levels);
    err = nvs_set_blob(nvs_handle, "log_levels_blob", default_log_levels, blob_size);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Error saving log levels blob: %s", esp_err_to_name(err));
        nvs_close(nvs_handle);
        return;
    }

    // Commit changes
    err = nvs_commit(nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Error committing NVS changes: %s", esp_err_to_name(err));
    } else {
        ESP_LOGI(TAG, "Log levels saved successfully (blob size: %zu bytes)", blob_size);
    }

    // Close NVS handle
    nvs_close(nvs_handle);
}



void load_log_levels()
{
    nvs_handle_t nvs_handle;
    esp_err_t err;

    err = nvs_open("log_levels", NVS_READONLY, &nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Error opening NVS handle for reading: %s", esp_err_to_name(err));
        ESP_LOGW(TAG, "Using default log levels");
        return;
    }

    // Get the size of the blob first
    size_t required_size = 0;
    err = nvs_get_blob(nvs_handle, "log_levels_blob", NULL, &required_size);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Error getting blob size: %s", esp_err_to_name(err));
        ESP_LOGW(TAG, "Using default log levels");
        nvs_close(nvs_handle);
        return;
    }

    if (required_size != sizeof(default_log_levels)) {
        ESP_LOGE(TAG, "Blob size mismatch: expected %zu, got %zu", sizeof(default_log_levels), required_size);
        ESP_LOGW(TAG, "Using default log levels");
        nvs_close(nvs_handle);
        return;
    }

    err = nvs_get_blob(nvs_handle, "log_levels_blob", default_log_levels, &required_size);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Error loading log levels blob: %s", esp_err_to_name(err));
        ESP_LOGW(TAG, "Using default log levels");
        nvs_close(nvs_handle);
        return;
    }

    // Apply loaded log levels to ESP logging system
    for (int i = 0; i < MODULES_QTY; i++) {
        esp_log_level_set(default_log_levels[i].tag, default_log_levels[i].level);
    }

    ESP_LOGI(TAG, "Log levels loaded successfully from NVS (blob size: %zu bytes)", required_size);

    nvs_close(nvs_handle);
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static const char* log_level_to_string(esp_log_level_t level) 
{
    switch (level) {
        case ESP_LOG_NONE:    return "OFF";
        case ESP_LOG_ERROR:   return "ERROR";
        case ESP_LOG_WARN:    return "WARN";
        case ESP_LOG_INFO:    return "INFO";
        case ESP_LOG_DEBUG:   return "DEBUG";
        case ESP_LOG_VERBOSE: return "VERBOSE";
        default:              return "UNKNOWN";
    }
}



}   // end of bsh_sys::log_levels namespace
