/**
 *  This module is used to set logs levels for different modules.
 *  Logs levels are not saved in NVS and will be reset to defaults after reboot.
 *  Defaults are defined in SYS_LOG_LEVELS.h
 */
#pragma once



#include "esp_log.h"



namespace bsh_sys::log_levels {



typedef enum {    // !!! dont skip numbering, must be sequential
    OTA,
    WIFI,
    BLE,
    BLE_COMM,
    BLE_LOGGING,
    SYS,
    LOGS,
    MQTT,
    MQTT_LOGS,
    MQTT_SYS_PROT,
    BLCR_REQ,
    RSA,
    TELEM_STORAGE,
    UART,

    EMU,
    DEVICE,              // from device-specific code

    ALL_OTHER,

    MODULES_QTY,
} module_t;



typedef struct {
    module_t module;
    esp_log_level_t level;
} module_log_level_t;



/*
 *  Sets log level for single module
 */
void set_log_level(module_t module, esp_log_level_t level);



/*
 *  Sets log levels for multiple modules, provided as array
 */
void set_log_levels(const module_log_level_t *modules_arr, int size);



/*
 *  Returns log level for single module 
 */
esp_log_level_t get_log_level(module_t module);



/*
 *  Prints log levels for all modules
 */
void print_log_levels();



/*
 *  Turns logs on/off
 *  bydefault logs are off for production firmwares and on for debug.
 *  Setting is not saved and will be reset to default after reboot.
 */
void turns_logs_on_off(bool on_off);

bool are_logs_on();



void save_log_levels();
void load_log_levels();



}   // end of bsh_sys::log_levels namespace


