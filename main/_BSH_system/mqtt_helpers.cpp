#include <string.h>

#include "mqtt_helpers.h"
#include "system.h"
#include "mqtt_sys_protocol.h"
#include "telemetry_storage.h"



namespace bsh_sys::mqtt_helpers {



void update_ota_progress(uint8_t progress)
{
    using namespace bsh_sys::mqtt_protocol;

    mqtt_out_block_t block = {
        .actor = {'F','U','P'},
        .index = 0,
        .capability = 0,
        .action_type = REPLY,
        .value_type = INT,
        .value = {.in_int = progress},
    };

    bsh_sys::telem_storage::update_block(get_system_config()->actors_qty + bsh_sys::mqtt_protocol::FUP, &block);
}



void update_wifi_power(int8_t wifi_level)
{
    using namespace bsh_sys::mqtt_protocol;

    mqtt_out_block_t block = {};
    memcpy (block.actor, "WFI", 3);
    block.index = 0;
    block.capability = 0;
    block.action_type = REPLY;
    block.value_type = INT;
    block.value.in_int = (int)wifi_db_to_bork_level(wifi_level);

    bsh_sys::telem_storage::update_block(get_system_config()->actors_qty + bsh_sys::mqtt_protocol::WFI, &block);
}



void update_firmware_version(uint8_t major_version, uint8_t minor_version, uint8_t patch_version)
{
    using namespace bsh_sys::mqtt_protocol;

    mqtt_out_block_t block = {};
    memcpy (block.actor, "FVR", 3);
    block.index = 0;
    block.capability = 0;
    block.action_type = REPLY;
    block.value_type = INT;
    
    block.value.in_bytes[0] = 0;
    block.value.in_bytes[1] = major_version;
    block.value.in_bytes[2] = minor_version;
    block.value.in_bytes[3] = patch_version;

    bsh_sys::telem_storage::update_block(get_system_config()->actors_qty + bsh_sys::mqtt_protocol::FVR, &block);
}



void update_mqtt_log_level(int log_level)
{
    using namespace bsh_sys::mqtt_protocol;

    mqtt_out_block_t block = {};
    memcpy (block.actor, "LOG", 3);
    block.index = 0;
    block.capability = 0;
    block.action_type = REPLY;
    block.value_type = INT;
    block.value.in_int = log_level;

    bsh_sys::telem_storage::update_block(get_system_config()->actors_qty + bsh_sys::mqtt_protocol::LOG, &block);
}


}  // end of bsh_sys::mqtt_helpers