/**
 * This module contains functions to help posting data into telem storage
 */


#pragma once

#include <stdint.h>

namespace bsh_sys::mqtt_helpers {



void update_ota_progress(uint8_t progress);

void update_wifi_power(int8_t wifi_level);

void update_firmware_version(uint8_t major_version, uint8_t minor_version, uint8_t patch_version);

void update_mqtt_log_level(int log_level);



}  // end of bsh_sys::mqtt_helpers

