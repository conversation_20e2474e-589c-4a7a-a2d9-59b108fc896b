// maximum log message size is defined in mqtt_log_buffer.cpp in MAX_MSG_SIZE


// формат сообщения
// {
//  "version": "1.1", <!-- оставляем 1.1
//  "host": "my-host", <!-- оставляем deviceId
//  "short_message": "Пример сообщения", <!-- обрезаем до 500 байт
//  "full_message": "Пример полного сообщения", <!-- оставляем в исходном виде
//  "level": 6, <!-- соответствует значениям команды LOG
//  "_subsystem": "BLE", <!-- название подсистемы
//  "_datetime": "2024-08-01T11:23:33", <!-- время в формате ISO8601 в таймзоне UTC!!
//  "_userId": "79165920929", <!-- текущий userId
//  "_correlationId": "guid", <!-- продумать механизм его получения
// }



namespace bsh_sys::mqtt_logs {



typedef enum {
    MQTT_LOG_LVL_OFF  = 0,
    MQTT_LOG_LVL_ERR  = 1,
    MQTT_LOG_LVL_WARN = 2,
    MQTT_LOG_LVL_INFO = 3,
} mqtt_log_level_t;



/**
 * Initializes mqtt_log module (loads saved log level)
 * Module needs device id and mqtt user it to put together logs topic name.
 * If not available yet - can be NULL. Call refresh_topic_name() when ids are awailable.
 * Logs messages will be accepted and put to beffer even if topic is not ready yet.
*/
void init(uint8_t *dev_id, int dev_id_size, char *user_id);

/**
 * Refreshed logs topic name with provided ids
 */
void refresh_topic_name(uint8_t *dev_id, int dev_id_size, char *user_id);

void deinit();



/**
 *  Sets log level. Value is stored in NVS and loaded upon firmware start (within init function, see above)
 *  Possible levels:
 *  0 = off
 *  1 = error
 *  2 = warning
 *  3 = info
 */
void set_log_level(mqtt_log_level_t level);



/**
 *   Retuns current log level
*/
mqtt_log_level_t get_logs_level();



/**
 * Post log message
 * @param log_level - logging level
 * @param message - expected null-terminated string. formatting and varargs - same as for prtinf
*/
void post_log_msg(mqtt_log_level_t log_lvl, const char *module_tag, const char *message, uint8_t *hex_dump, uint16_t dump_size, ...);

char *log_lvl_to_string(mqtt_log_level_t level);



} // end of   bsh_sys:mqtt_logs namespace