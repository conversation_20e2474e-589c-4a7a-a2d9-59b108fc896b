#include <stdbool.h>
#include <string.h>

#include "esp_log.h"

#include "mqtt_sys_protocol.h"
#include "mqtt.h"
#include "rsa_encryption.h"
#include "bluetooth_LE.h"
#include "mqtt_logs.h"



#define LOG_INCOMING_RAW_DATA 0

#define MQTT_PROTOCOL_READ_BLOCK_LENGTH 6
#define MQTT_PROTOCOL_WRITE_BLOCK_LENGTH 12
#define MQTT_PROTOCOL_OUT_BLOCK_LENGTH 11    // without protocol version byte and blocks qty byte

#define MAX_OTA_CERT_LENGTH 8000

#define DEVICE_ERR_CODE_OFFSET_FOR_MQTT_PROTOCOL 1000



namespace bsh_sys::mqtt_protocol {


#define free_pointer_if_not_null(P) if(P != NULL) {free(P); P=NULL;}



ESP_EVENT_DEFINE_BASE(MQTT_SYS_PROTOCOL_EVENTS);



/* ======= local object declarations =========*/
static const char *TAG = "-sys_mqtt_prot-";
static QueueHandle_t incoming_queue;
static QueueHandle_t incoming_sys_queue;
static esp_event_loop_handle_t loop_handle;
static bool initialized = false;
static char *ota_link = NULL;
static char *ota_cert = NULL;
static char *status_topic_name = NULL;
static char *commands_topic_name = NULL;
static bool send_telemetry_to_ble = false;

static const char *system_actors_list[] = 
{
    "FUP",     // firmware upgrade
    "WFI",     // wifi signal level
    "FVR",     // firmware version

    "ORN",
    "RPL",
    "LOG",
    "OCT",      // ota server certificate
    "OCO",      // ota certificate check on/off

    "___",

    "RTL",     // regular telemetry on/ogg
    "RTT",     // regular telemetry update timer
    "BLE",     // BLE on/off
};



static const char *from_device_only_actors[] = 
{
    "MSG",
    "WRN",
    "ERR",
    "ORN",
    "RPL",
};



/* ======= local function declarations ========= */
static bool check_packet_signature(const char* data, uint16_t length);
static bool parse_command(uint8_t **data, uint16_t data_len, mqtt_in_block_t *block);
static bool is_system_actor(mqtt_in_block_t *block);
static bool is_from_device_only_actor(mqtt_in_block_t *block);
static void log_mqtt_block(mqtt_in_block_t *block);
static pckt_integrity_err_events_t check_packet_integrity (const char* data, uint16_t length);
static pckt_integrity_err_events_t check_block_integrity (mqtt_in_block_t *block);
static bool compare_mnemocodes (const char *mc1, const char *mc2);



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
void init (esp_event_loop_handle_t handle)
{
    if (initialized) return;

    loop_handle = handle;
    incoming_queue = xQueueCreate(IN_QUEUE_SIZE, sizeof(mqtt_in_block_t));
    incoming_sys_queue = xQueueCreate(IN_SYS_QUEUE_SIZE, sizeof(mqtt_in_block_t));

    initialized = true;
}



void incoming_mqtt_data (uint8_t *data, uint16_t data_len)
{

#if LOG_INCOMING_RAW_DATA == 1
    if (data_len < 400)
    {
        ESP_LOGI(TAG,"incoming mqtt data:");
        ESP_LOG_BUFFER_HEXDUMP(TAG, data, data_len, ESP_LOG_INFO);
    } else {
        ESP_LOGE (TAG,"too long data to log: %u",data_len);
    }
#endif

uint16_t data_len_left;
#if CHECK_SIGNATURE
    auto check_res = check_packet_integrity((const char *)data, data_len);
    if (PACKET_IS_OK != check_res)
    {
        esp_event_post_to(loop_handle, MQTT_SYS_PROTOCOL_EVENTS, check_res, NULL, 0, 0);
        bsh_sys::mqtt_logs::post_log_msg (bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, TAG, "wrong packet", data, data_len);
        return;
    }
    data_len_left = data_len - 2 - SIGNATURE_SIZE;
#else 
    data_len_left = data_len - 2;
#endif    

    treat_mqtt_packet(data, data_len_left);
}



void send_blocks (mqtt_out_block_t *blocks, uint8_t blocks_qty, uint8_t qos_level)
{
    bsh_sys::mqtt::mqtt_out_obj_t mqtt_out_queue_obj = {};
    mqtt_out_queue_obj.qos_level = qos_level;
    mqtt_out_queue_obj.data_len = 2 + MQTT_PROTOCOL_OUT_BLOCK_LENGTH * blocks_qty;

    mqtt_out_queue_obj.out_data[0] = PROTOCOL_VERSION;
    mqtt_out_queue_obj.out_data[1] = blocks_qty;
    char *ptr_to_write_to = mqtt_out_queue_obj.out_data + 2;
    for (size_t i = 0; i < blocks_qty; i++)
    {
        if ((blocks + i)->actor[0] == 0) 
        {
            mqtt_out_queue_obj.data_len -= MQTT_PROTOCOL_OUT_BLOCK_LENGTH;   // minus one block length
            mqtt_out_queue_obj.out_data[1] -= 1;    // minus one block
            continue;  // skip empty blockss
        }

        memcpy (ptr_to_write_to , (blocks+i)->actor, 3);
        *(ptr_to_write_to + 3) = (uint8_t)((blocks+i)->index);
        *(ptr_to_write_to + 4) = (uint8_t)((blocks+i)->capability);
        *(ptr_to_write_to + 5) = (uint8_t)((blocks+i)->action_type);
        *(ptr_to_write_to + 6) = (uint8_t)((blocks+i)->value_type);
        memcpy (ptr_to_write_to + 7, &((blocks+i)->value), 4);
    
        ptr_to_write_to += MQTT_PROTOCOL_OUT_BLOCK_LENGTH;
    }

    if (xQueueSend(get_out_queue(), &mqtt_out_queue_obj, 0) == errQUEUE_FULL)
    {
        ESP_LOGE(TAG,"mqtt out queue is full");
    }

    if(send_telemetry_to_ble)
    {
        bsh_sys::ble::sent_telemetry_via_bt((const uint8_t *)mqtt_out_queue_obj.out_data, mqtt_out_queue_obj.data_len);
    }

    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "telemetry sent. blocks: %u, QOS:%u", NULL, 0, blocks_qty, qos_level);
}



int get_sys_actor_index (char *actor)
{
    if (actor == NULL)
    {
        ESP_LOGE (TAG,"null pointer param");
        return -1;
    }

    for (size_t i = 0; i < sizeof(system_actors_list)/sizeof(system_actors_list[0]); i++)
    {
        if (*(actor) == *(system_actors_list[i]) && 
            *(actor + 1) == *(system_actors_list[i]+1) &&
            *(actor + 2) == *(system_actors_list[i]+2))
        {
            return i;
        }    
    }

    return -1;
}



QueueHandle_t get_out_queue()
{
    return bsh_sys::mqtt::get_sending_queue();
}



QueueHandle_t get_incoming_queue()
{
    return incoming_queue;
}



QueueHandle_t get_incoming_sys_queue()
{    
    return incoming_sys_queue;
}



void send_err_message_to_broker(uint8_t err)
{
    mqtt_out_block_t block = {};
    memcpy (block.actor, "ERR", 3);
    block.index = 0;
    block.capability = 0;
    block.action_type = REPLY;
    block.value_type = INT;
    block.value.in_int = err + DEVICE_ERR_CODE_OFFSET_FOR_MQTT_PROTOCOL;

    send_blocks (&block, 1, 0);
}



void send_test_message()
{
    mqtt_out_block_t block = {};
    memcpy (block.actor, "MSG", 3);
    block.index = 0,
    block.capability = 0,
    block.action_type = REPLY,
    block.value_type = INT,
    block.value = {.in_int = 7},    // no meaning in this value. message meant to check the broker is properly connected and accepts msgs. qos=1

    send_blocks (&block, 1, 1);
}



void send_tlm_to_ble(bool on)
{
    send_telemetry_to_ble = on;
}



const char *pckt_err_to_string(pckt_integrity_err_events_t err)
{
    switch (err)
    {
    case PACKET_IS_OK: return "packet ok";
    case PROTOCOL_VERSION_ERROR: return "version err";
    case WRONG_ACTOR_TO_WRITE: return "write to read only actor";
    case WRONG_ACTOR_TO_READ: return "wrong actor to read";
    case WRONG_READ_WRITE_TYPE: return "wrong read/write type";
    case MNEMOCODE_NOT_RECOGNIZED: return "-";  // not used in this module
    case WRONG_INCREMENT_TYPE: return "wrong increment type";
    case WRONG_DATA_TYPE: return "wrong data type";
    case WRONG_MQTT_STRING_LENGTH: return "wrong string length";
    case WRONG_VALUE: return "wrong value";
    case WRONG_SIGNATURE: return "wrong signature";
    }

    return "?";
};




char *get_status_topic_name(const char *user_id, const uint8_t *device_id, uint8_t id_length)
{
    if (user_id == NULL || device_id == NULL || id_length == 0) return NULL;

    free_pointer_if_not_null(status_topic_name);

    const char * start = "/users/";
    const char * middle = "/devices/";
    const char * end = "/state";

    status_topic_name = (char*) malloc (strlen(start) + strlen(user_id) + strlen(middle) + id_length*2 + strlen(end) + 2 + 1); 

    // /users/
    memcpy(status_topic_name, start, strlen(start));

    // /users/user_id
    memcpy (status_topic_name + strlen(start), user_id, strlen(user_id));

    // /users/user_id/devices
    memcpy (status_topic_name + strlen(start) + strlen(user_id), middle, strlen(middle));

    // /users/user_id/devices/device_id
    for (size_t i = 0; i < id_length; i++)
    {
        snprintf (status_topic_name + strlen(start) + strlen(user_id) + strlen(middle) + i*2, 3 , "%02x",*(device_id+i));
    }

    // /users/user_id/devices/device_id/state
    memcpy (status_topic_name + strlen(start) + strlen(user_id) + strlen(middle) + id_length*2, end, strlen(end)+1); // +1 to copy '\0'

    ESP_LOGI(TAG, "status topic name:  %s", status_topic_name);
    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "status topic name: %s", NULL, 0, status_topic_name);
    return status_topic_name;
}



char *get_commands_topic_name(const char *user_id, const uint8_t *device_id, uint8_t id_length)
{
    if (user_id == NULL || device_id == NULL || id_length == 0) return NULL;

    free_pointer_if_not_null(commands_topic_name);

    const char * start = "/users/";
    const char * middle = "/devices/";
    const char * end = "/commands";

    commands_topic_name = (char*) malloc (strlen(start) + strlen(user_id) + strlen(middle) + id_length*2 + strlen(end) + 2 + 1); 

    // /users/
    memcpy(commands_topic_name, start, strlen(start));

    // /users/user_id
    memcpy (commands_topic_name + strlen(start), user_id, strlen(user_id));

    // /users/user_id/devices
    memcpy (commands_topic_name + strlen(start) + strlen(user_id), middle, strlen(middle));

    // /users/user_id/devices/device_id
    for (size_t i = 0; i < id_length; i++)
    {
        snprintf (commands_topic_name + strlen(start) + strlen(user_id) + strlen(middle) + i*2, 3 , "%02x",*(device_id+i));
    }

    // /users/user_id/devices/device_id/commands
    memcpy (commands_topic_name + strlen(start) + strlen(user_id) + strlen(middle) + id_length*2, end, strlen(end)+1);

    ESP_LOGI(TAG, "commands topic name:  %s", commands_topic_name);
    bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_INFO, TAG, "commands topic name: %s", NULL, 0, commands_topic_name);

    return commands_topic_name;
}



wifi_level_t wifi_db_to_bork_level(int8_t wifi_db)
{
    if (wifi_db < -70)
    {
        return WFL_WEAK;
    } else if (wifi_db < -60) {
        return WFL_MIDDLE;
    } else if (wifi_db < -45) {
        return WFL_STRONG;
    } else {
        return WFL_BEST;
    }
}



void treat_mqtt_packet(uint8_t *data, uint16_t length)
{
    // parse commands
    uint8_t commands_qty = *(data + 1);
    uint8_t *next_point_to_parse = data + 2; // 2 coz prot version byte and commands qty byte

    mqtt_in_block_t tmp_block = {};
    for (size_t i = 0; i < commands_qty; i++)
    {
        bool parse_result = parse_command(&next_point_to_parse, length, &tmp_block);
        if (parse_result != true)
        {
            esp_event_post_to(loop_handle, MQTT_SYS_PROTOCOL_EVENTS, WRONG_MQTT_STRING_LENGTH, NULL, 0, 0);
            bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, TAG, "cant parse command", data, length);
            return;
        }

        pckt_integrity_err_events_t err = check_block_integrity(&tmp_block);
        if (err != PACKET_IS_OK)
        {
            esp_event_post_to(loop_handle, MQTT_SYS_PROTOCOL_EVENTS, err, NULL, 0, 0);
            bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, TAG, "error in block ", (uint8_t *)&tmp_block, sizeof(tmp_block));
            if(send_telemetry_to_ble) 
            {
                uint8_t to_send[] = {0xF6, err};
                bsh_sys::ble::send_via_bt(to_send, 2);               
            }
            return;
        }
        if(send_telemetry_to_ble) 
        {
            uint8_t to_send[] = {0xF6, 0};
            bsh_sys::ble::send_via_bt(to_send, 2);
        }

        if (is_system_actor(&tmp_block))
        {
            xQueueSend(incoming_sys_queue, &tmp_block, 0);
        } else if (is_from_device_only_actor(&tmp_block)) {
            ESP_LOGE (TAG,"wrong actor to read");
            esp_event_post_to(loop_handle, MQTT_SYS_PROTOCOL_EVENTS, WRONG_ACTOR_TO_READ, NULL, 0, 0);
        } else {
            xQueueSend(incoming_queue, &tmp_block, 0);
        }

        log_mqtt_block(&tmp_block);
    }
}



const char * pckt_integrity_event_to_string(pckt_integrity_err_events_t event)
{
    switch (event)
    {
        case PACKET_IS_OK:              return "packet ok";
        case PROTOCOL_VERSION_ERROR:    return "version err";
        case WRONG_ACTOR_TO_WRITE:      return "write to r/o actor";
        case WRONG_ACTOR_TO_READ:       return "cant read from this actor";
        case WRONG_READ_WRITE_TYPE:     return "wrong read/write type";
        case MNEMOCODE_NOT_RECOGNIZED:  return "code not recognised";
        case WRONG_INCREMENT_TYPE:      return "wrong increment type";
        case WRONG_DATA_TYPE:           return "wrong data type";
        case WRONG_MQTT_STRING_LENGTH:  return "wrong string length";
        case WRONG_VALUE:               return "wrong value";
        case WRONG_SIGNATURE:           return "wrong signature";
    }
    return "";
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static pckt_integrity_err_events_t check_block_integrity (mqtt_in_block_t *block)
{
    if  (block->action_type != READ && block->action_type != WRITE)
    {
        ESP_LOGE (TAG,"wrong read/write type");
        return WRONG_READ_WRITE_TYPE;
    }

    if (block->increment != ABSOLUTE && block->increment != ICNREMENT)
    {
        ESP_LOGE (TAG,"wrong increment type");
        return WRONG_INCREMENT_TYPE;
    }

    if (block->value_type != INT && block->value_type != FLOAT && block->value_type != OTA_STRING)
    {
        ESP_LOGE (TAG,"wrong data type");
        return WRONG_DATA_TYPE;
    }

    if (get_sys_actor_index(block->actor) == RTT)
    {
        if (block->set_value.in_int == 0) return WRONG_VALUE;
    }

    ESP_LOGI (TAG,"packet is ok");
    return PACKET_IS_OK;
}



static pckt_integrity_err_events_t check_packet_integrity (const char* data, uint16_t length)
{
    //check protocol version
    if (*data != PROTOCOL_VERSION)
    {
        ESP_LOGW (TAG,"wrong protocol version");
        esp_event_post_to(loop_handle, MQTT_SYS_PROTOCOL_EVENTS, PROTOCOL_VERSION_ERROR, NULL, 0, 0);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, TAG, "wrong protocol version", NULL, 0);
        return PROTOCOL_VERSION_ERROR;
    }

    //check signature
#if CHECK_SIGNATURE == 1
    if (length < 260) 
    {
        ESP_LOGE (TAG,"to small packet. no signature?");
        esp_event_post_to(loop_handle, MQTT_SYS_PROTOCOL_EVENTS, WRONG_SIGNATURE, NULL, 0, 0);
        return WRONG_SIGNATURE;
    }
    if (!check_packet_signature((const char *)data, length))
    {
        ESP_LOGW (TAG, "wrong signature");
        esp_event_post_to(loop_handle, MQTT_SYS_PROTOCOL_EVENTS, WRONG_SIGNATURE, NULL, 0, 0);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, TAG, "wrong signature", NULL, 0);
        return WRONG_SIGNATURE;
    }
#else
    if (length < READ_MSG_LENGTH) 
    {
        ESP_LOGE (TAG, "wrong length of read string to parse");
        esp_event_post_to(loop_handle, MQTT_SYS_PROTOCOL_EVENTS, WRONG_MQTT_STRING_LENGTH, NULL, 0, 0);
        bsh_sys::mqtt_logs::post_log_msg(bsh_sys::mqtt_logs::MQTT_LOG_LVL_WARN, TAG, "wrong command length", NULL, 0);
        return WRONG_MQTT_STRING_LENGTH;
    }
    if ((*(next_cmd+5) == WRITE) && string_length < WRITE_MSG_LENGTH)
    {
        ESP_LOGW (TAG, "wrong length of write string to parse");
        esp_event_post_to(loop_handle, MQTT_SYS_PROTOCOL_EVENTS, WRONG_MQTT_STRING_LENGTH, NULL, 0, 0);
        return WRONG_MQTT_STRING_LENGTH;
    }
#endif

    return PACKET_IS_OK;
}



/**
 * Checks packet signature.
 * Signature is located at the end of package, after all actors.
 */ 
static bool check_packet_signature(const char* data, uint16_t length)
{
    int ret = bsh_sys::rsa::verify_signature((const unsigned char *)data, length - 256, (const unsigned char *)(data + length - 256));

    if (ret != 0) 
    {
        ESP_LOGE (TAG,"wrong signature");
        return false;
    }
    
    ESP_LOGI (TAG,"signature ok");
    return true;
}


// Shifts *data pointer to next block to parse
static bool parse_command(uint8_t **data, uint16_t data_len, mqtt_in_block_t *block)
{
    uint8_t *data_ptr = *data;
    memset (block, 0, sizeof(mqtt_in_block_t));  
    memcpy (block->actor, data_ptr, 3);
    block->index = *(data_ptr+3);
    block->capability = *(data_ptr+4);
    block->action_type = (action_type_t)*(data_ptr+5);

    if (block->action_type == READ) {
        *data += MQTT_PROTOCOL_READ_BLOCK_LENGTH;
        return true;
    }

    block->increment = (increment_type_t)*(data_ptr+6);
    block->value_type = (value_type_t)*(data_ptr+7);
    memcpy(&block->set_value, data_ptr+8, 4);

    if ((block->actor[0] == 'F' && block->actor[1] == 'U' && block->actor[2] == 'P') || 
        (block->actor[0] == 'O' && block->actor[1] == 'C' && block->actor[2] == 'T'))
    {
        if (block->set_value.in_int >= MAX_OTA_CERT_LENGTH)
        {
            block->firm_url = NULL;
            ESP_LOGE (TAG, "too long OTA link or OTA cert");
            return false;
        }

        if (block->actor[0] == 'F') {
            if(ota_link != NULL) free(ota_link);
            ota_link = (char *)calloc(1, block->set_value.in_int + 1);
            memcpy(ota_link, data_ptr +12, block->set_value.in_int);
            *(ota_link+block->set_value.in_int) = '\0';
            block->firm_url = ota_link;
        } else {
            if(ota_cert != NULL) free(ota_cert);
            ota_cert = (char *)calloc(1, block->set_value.in_int + OTA_CERT_EXTRA_SPACE_FOR_LINE_BREAKS);
            memcpy(ota_cert, data_ptr +12, block->set_value.in_int);
            *(ota_cert + block->set_value.in_int) = '\0';
            block->firm_url = ota_cert;
        }
        *data += MQTT_PROTOCOL_WRITE_BLOCK_LENGTH + block->set_value.in_int;
    } else {
        block->firm_url = NULL;
        *data += MQTT_PROTOCOL_WRITE_BLOCK_LENGTH;
    }

    return true;
}



static bool is_system_actor(mqtt_in_block_t *block)
{
    char actor_name[4] = {0};
    for (size_t i = 0; i < sizeof(system_actors_list)/sizeof(system_actors_list[0]); i++)
    {
        memcpy(actor_name, block->actor, 3);
        if (strcmp(actor_name, system_actors_list[i]) == 0)
        {
            return true;
        }
    }

    return false;
}



static bool is_from_device_only_actor(mqtt_in_block_t *block)
{
    char actor_name[4] = {0};
    for (size_t i = 0; i < sizeof(from_device_only_actors)/3; i++)
    {
        memcpy(actor_name, block->actor, 3);
        if (strcmp(actor_name, from_device_only_actors[i]) == 0)
        {
            return true;
        }
    }

    return false;  
}



static void log_mqtt_block(mqtt_in_block_t *block)
{
    ESP_LOGI (TAG, "    <=====  mqtt command:");
    ESP_LOGI (TAG,"actor: %c%c%c", block->actor[0], block->actor[1],  block->actor[2]);
    ESP_LOGI (TAG, "index: %u", block->index);
    ESP_LOGI (TAG, "capability: %u", block->capability);
    ESP_LOGI (TAG, "action_type: %u", block->action_type);
    if (block->action_type == 0) return; 
    ESP_LOGI (TAG, "increment: %u", block->increment);
    ESP_LOGI (TAG, "value type: %u", block->value_type);
    ESP_LOGI (TAG, "value:%u   raw: 0x%02x 0x%02x 0x%02x 0x%02x", block->set_value.in_int, 
                                                                  block->set_value.in_bytes[0],
                                                                  block->set_value.in_bytes[1],
                                                                  block->set_value.in_bytes[2],
                                                                  block->set_value.in_bytes[3]);
}



static bool compare_mnemocodes (const char *mc1, const char *mc2)
{
    // ESP_LOGW (TAG,"looking for mnemocode: %c%c%c", *mc1, *(mc1+1), *(mc1+2));
    for (size_t i = 0; i < 3; i++)
    {
        if (*(mc1+i) != *(mc2+i)) return false;
    }
    return true;
}



} // end of namepsace