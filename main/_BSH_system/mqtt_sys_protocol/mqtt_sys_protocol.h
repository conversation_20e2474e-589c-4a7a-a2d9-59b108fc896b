
/**
 * This module captures device-independent mqtt stuff:
 * 
 * === for incoming data ===
 * - accepts incoming mqtt string (raw data) via incoming_mqtt_data() function
 *    this function call and all other work supposed to be done in mqtt task
 * - checks incoming data signature
 * - checks incoming data compliance with protocol
 * - parses incoming data, splitting it into mqtt actor-blocks
 * - puts blocks into mqtt-in queue for other modules to act upon
 *        two queues exist: system commands and device commands
 * 
 * === for outgoing data === 
 * - takes array of mqtt_out_block_t objects via send_blocks() function,
 *      converts it into mqtt string and puts into mqt out queue
 * 
 * 
 * 
 * creates incoming mqtt queue and returns handles via get function.
 * outgoing mqtt queue is created inside mqtt module - it contains raw strings to be sent to broker
 * does not create its own task. data tresated in tasks calling 
 *     incoming_mqtt_data() and send_blocks() functions
 * 
 * 
 * 
 * === MQTT protocol packet structure ===
 * 
 * -byteN:   -data:
 * 1:       protocol version 0x01
 * 2:       qty of commands  
 * 3,4,5:   actor: FAN ect.
 * 6:       index: deened in case of multiple actors of same type
 * 7:       capability: if multiple capabilities of single actor. like cw/ccw rotation   
 * 8:       command type: write (1) or read(0).   read packet ends here
 * 
 * if its write command, more bytes:
 * 9:       CHANGE_TYPE: absolute (0) or increment (1)
 * 10:      DATA_TYPE: 0 - int32 , 1 - float   
 * 11,12,13,14:  data.   int - little endian: 3 = 0x03 0x00 0x00 0x00
 * 
 * 9 or 15:       next actor, if many of them in same packet. 9 if prev actor was read, 15 if it was write
 * 
 * for example: 01 01 46 56 52 00 00 00    read firmware version
 * 
 * 
 * 
 * Reply/status change packet structure:
 * 1:       protocol version 0x01
 * 2:       qty of segments
 * 3,4,5:   actor
 * 6:       index: if multiple actors of same type
 * 7:       capability 
 * 8:       action type - always 0x80 here
 * 9:       DATA_TYPE
 * 10,11,12,13:      data
 * 
 * 
 * 
 * Firmware update command (FUP) has one extra field: string with link to firmware.
 * 1:       protocol version 0x01
 * 2:       qty of commands
 * 3,4,5:   actor: FUP 
 * 6:       index: always 0 
 * 7:       capability: 0
 * 8:       command type: read(0) / write (1) 
 * since its write command
 * 9:       CHANGE_TYPE: absolute: 0
 * 10:      DATA_TYPE: 0     2
 * 11,12,13,14: int: url link length 
 * 15...    url link to firmware
 *
 * for example
 * 01 01 46 55 50 00 00 01 00 00 3E 00 00 00 68 74 74 70 3a 2f 2f 73 74 2e 62 6f 72 6b 2e 72 75 2f 61 70 70 6c 69 63 61 74 69 6f 6e 2f 66 69 72 6d 77 61 72 65 73 2f 77 69 66 69 2f 61 38 33 30 2f 66 69 72 6d 77 61 72 65 2e 62 69 6e
 *

 * OTA certificate set command (OCT) also has one extra field: string with certificate.
 * 1:       protocol version 0x01
 * 2:       qty of commands
 * 3,4,5:   actor: OCT 
 * 6:       index: always 0 
 * 7:       capability: 0
 * 8:       command type: read(0) / write (1) 
 * since its write command
 * 9:       CHANGE_TYPE: absolute: 0
 * 10:      DATA_TYPE: 2 (string)
 * 11,12,13,14: int: cert length 
 * 15...    certificate
 */



/*
 *    data bytes for FVR = firmware version packet:
 *       version 3.12 =>   0x00  0x03 0x0C   0x00
 *    if we'll need to extand versioning, like 3,12-33
 *    if will be   0x00  0x03 0x0C 0x21   
 */

#pragma once

#include <stdint.h>

#include "freertos/FreeRTOS.h"
#include "freertos/queue.h"
#include "esp_event.h"



#define CHECK_SIGNATURE 1

#define OTA_CERT_EXTRA_SPACE_FOR_LINE_BREAKS 100



namespace bsh_sys::mqtt_protocol {



#define PROTOCOL_VERSION 1
#define IN_QUEUE_SIZE 50
#define IN_SYS_QUEUE_SIZE 10
#define IN_OBJ_SIZE 16           // size of mqtt_in_block_t
        // out obj size is in mqtt.h

    typedef enum {   // !!!!!!  should match with system_actors_list
        FUP,     // firmware upgrade
        WFI,     // wifi signal level
        FVR,     // firmware version
        ORN,     // status change origin, see origin_code_t
        RPL,     // 0 if command execution failed, 1 if ok
        LOG,     // mqtt logs on/off/level
        OCT,     // ora certificate (for server verification) see decription above.
        OCO,     // its ota server verification off. send 1 to turn verif off.

        SYS_ACTORS_QTY,    
        // actors below are hidden, not included into every telemetry
        // BCT,     // broadcast decided to be device-dependent, coz might need to request latest data from main mcu
        RTL = 20,     // regular telemetry on/off
        RTT,     // regular telemetry update timer
        BLE,     // BLE on/off
    } system_command_actors_t;

    typedef enum {
        READ = 0,
        WRITE = 1,
        REPLY = 128,   // this is for messages from device
    } action_type_t;

    typedef enum {
        INT = 0,
        FLOAT,
        OTA_STRING,
    } value_type_t;

    typedef enum {
        WFL_WEAK = 1,
        WFL_MIDDLE,
        WFL_STRONG,
        WFL_BEST,
    } wifi_level_t;

    typedef union 
    {
        int in_int;
        float in_float;
        char in_bytes[4];
    } value_t;

    typedef enum
    {
        ABSOLUTE,
        ICNREMENT,
    } increment_type_t;

    typedef enum {
        ORN_TIME,
        ORN_BROKER_CMD,
        ORN_DEVICE,
        ORN_USER,
    } origin_code_t;

// status block. to be sent to broker
typedef struct
{
    char actor[3];                      // motor/sensor/etc type
    uint8_t index;                      // motor/sensor/etc N  (could have multiple)
    uint8_t capability;                 // capability - like which way to spin the motor
    action_type_t action_type;          // read/write.   always 0x80 for outgoing status packets
    value_type_t value_type;            // data type: int or float or string
    value_t value;
} mqtt_out_block_t;



// command block - to be sent by broker
typedef struct {
    char actor[3];                      // motor/sensor/etc type
    uint8_t index;                      // motor/sensor/etc N  (could have multiple)
    uint8_t capability;                 // capability - like which way to spin the motor
    action_type_t action_type;          // read/write.   always 0x80 for outgoing status packets
    increment_type_t increment;         // absolute value or increment
    value_type_t value_type;            // data type: int or float or string
    value_t set_value;
    char * firm_url;                    // only  for OTA = FUP write package or OCT(ota cert)package
} mqtt_in_block_t;



// !!! if change this, also change pckt_err_to_string() !!!
typedef enum {
    PACKET_IS_OK = 0,
    PROTOCOL_VERSION_ERROR,
    WRONG_ACTOR_TO_WRITE,
    WRONG_ACTOR_TO_READ,
    WRONG_READ_WRITE_TYPE,
    MNEMOCODE_NOT_RECOGNIZED,  // not used, here only for backwards compat
    WRONG_INCREMENT_TYPE,
    WRONG_DATA_TYPE,
    WRONG_MQTT_STRING_LENGTH,
    WRONG_VALUE,
    WRONG_SIGNATURE,
} pckt_integrity_err_events_t;
const char * pckt_integrity_event_to_string(pckt_integrity_err_events_t event);



typedef enum {
    OTA_ERR_CANT_GET_FIRMWARE = 21,
    OTA_ERR_DOWNLOAD,
    OTA_ERR_WRONG_FIRMWARE,
    OTA_ERR_CANT_ESTABLISH_TLS_CONN,
} ota_err_t;



ESP_EVENT_DECLARE_BASE(MQTT_SYS_PROTOCOL_EVENTS);



void init (esp_event_loop_handle_t handle);



/*************************************
 *          INCOMING DATA
**************************************/

/**
 * @brief Parses incoming data from broker and puts actor blocks into queue
 * 
 * @param data -incoming packet to parse
 * @param data_len - packet size
 */
void incoming_mqtt_data (uint8_t *data, uint16_t data_len);

// same as above, but must have no signature. For local device control feature.
void treat_mqtt_packet(uint8_t *data, uint16_t length);



/*************************************
 *          OUTGOING DATA
**************************************/
/**
 * @brief Puts array of mqtt blocks into mqtt out queue
 * 
 * @param blocks - array of blocks (will be converted to mqtt string)
 * @param blocks_qty - size of array
 */
void send_blocks (mqtt_out_block_t *blocks, uint8_t blocks_qty, uint8_t qos_level);

void send_err_message_to_broker(uint8_t err);
void send_test_message();
void send_tlm_to_ble(bool on);



/*************************************
 *             HELPERS
**************************************/
/**
 * @brief Returns system command-actor index
 *        Returns -1 if not found
 */
int get_sys_actor_index (char *actor);



/**
 * @brief returns handle to mqtt out queue. queue contains mqtt_out_block_t objects
 *        * just passes through queue from mqtt.h module - for convenience
 */
QueueHandle_t get_out_queue();



/**
 * @brief returns incoming queue
 */
QueueHandle_t get_incoming_queue();

/**
 * @brief returns incoming system commands queue
 */
QueueHandle_t get_incoming_sys_queue();



const char *pckt_err_to_string(pckt_integrity_err_events_t err);



/**
 * @brief Returns commands topic name, based on dev ID, user name, etc
 *          topic format is:
 *          /users/'user_id(cellphone_N)'/devices/'device_id'/commands
 * @param user_id - considered to be a string = cell phone number
 * @param device_id - id in form of bytes array. (in this app: 10 bytes array. 4 are random (generated once, and saved), 6 are device's mac)
 * 
 * @return pointer to topic name string
 */
char *get_commands_topic_name(const char *user_id, const uint8_t *device_id, uint8_t id_length);



/**
 * @brief Returns status topic name, based on dev ID, user name, etc
 *          topic format is: 
 *          /users/'user_id(cellphone_N)'/devices/'device_id'/state
 * 
 * @param user_id - considered to be a string = cell phone number
 * @param device_id - id in form of bytes array. (in this app: 10 bytes array. 4 are random (generated once, and saved), 6 are device's mac)
 * 
 * @return pointer to status name string
 */
char *get_status_topic_name(const char *user_id, const uint8_t *device_id, uint8_t id_length);



wifi_level_t wifi_db_to_bork_level(int8_t wifi_db);



} // end of bsh_sys::mqtt_protocol namespace