
#include <string.h>
#include <stdlib.h>

#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_http_client.h"
#include "esp_tls.h"
#include "esp_timer.h"

#include "http_request_to_balancer.h"
#include "mqtt_logs.h"
#include "commissioning.h"



#define LOG_LEVEL ESP_LOG_INFO



namespace bsh_sys::blsr {



ESP_EVENT_DEFINE_BASE(BLCR_REQ_EVENTS);

static const char *TAG = "-blcr_req-";
request_to_balancer_params_t *params;
static char *parsed_link = NULL;
static esp_http_client_handle_t client = NULL;
static uint8_t request_repeat_number = 0;
static esp_timer_handle_t repeat_timer = NULL; 
static bool connection_is_in_progress = false;
static bool reconnect = true;
static char *date[32] = {0};



/* ======= local function declarations ========= */
static esp_err_t _http_event_handler(esp_http_client_event_t *evt);
static bool parse_url (void *data, int data_length);
static void perform_request ();
static void reconnect_timer_cb (void *);
static void start_reconnect_timer();



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
void get_mqtt_addr_from_balancer (request_to_balancer_params_t *parameters)
{
    esp_log_level_set(TAG, LOG_LEVEL);

    if (connection_is_in_progress)
    {
        ESP_LOGE(TAG,"conn in progress");
        esp_http_client_close(client);
    }

    request_repeat_number = 0;
    params = parameters;
    reconnect = params->reconnect;

    if (client == NULL) 
    {
        esp_http_client_config_t config = {};
        config.url = parameters->balancer_link;
        // config.host = "*************";
        // config.port = 3000;
        // config.path = "/";
        config.username = parameters->user_name;
        config.password = parameters->passwd;
        config.auth_type = HTTP_AUTH_TYPE_BASIC;
        config.disable_auto_redirect = false;
        config.event_handler = _http_event_handler;
        config.transport_type = HTTP_TRANSPORT_OVER_TCP;
        config.is_async = false;

        client = esp_http_client_init(&config);

        if (client == NULL)
        {
            ESP_LOGE (TAG, "can't init http");
            return;
        }
    } else {
        esp_http_client_set_url(client, parameters->balancer_link);
        // esp_http_client_set_password(client, parameters->passwd);   // пока у нас без логина/пароля
        // esp_http_client_set_username(client, parameters->user_name);
    }

    esp_http_client_set_header(client, "DeviceId", bsh_sys::commiss::get_dev_id_as_string()); 
    esp_http_client_set_header(client, "Authorization", "Basic");  // без пароля и логина автоматически хедер не добавляется. так что вручную
    perform_request ();
}



void http_request_stop_reconnect()
{
    reconnect = false;
    if(client != NULL) esp_http_client_close(client);

    if (repeat_timer != NULL) 
    {
        ESP_LOGI (TAG, "reconnect timer stopped");
        esp_timer_stop(repeat_timer);
    }
}



const char * balancer_event_to_string(balancer_events_t event)
{
    switch (event)
    {
        case BLR_OK:                    return "ok";
        case BLR_CANT_REACH_BALANCER:   return "cant reach balancer";
        case BLR_CANT_PARCE_LINK:       return "cant parse link";
        case BLR_GOT_TIME:              return "got time";
        case BLR_QTY:                   return "";

    }
    return "";
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static esp_err_t _http_event_handler(esp_http_client_event_t *evt)
{
    static char *output_buffer;  // Buffer to store response of http request from event handler
    static int output_len;       // Stores number of bytes read
    switch(evt->event_id) {
        case HTTP_EVENT_ERROR:
            {
            ESP_LOGW(TAG, "HTTP_EVENT_ERROR");
            int status_code = esp_http_client_get_status_code(client);
            esp_event_post_to(params->loop_handle, BLCR_REQ_EVENTS, BLR_CANT_REACH_BALANCER, (void *)status_code, 0, 0);
            connection_is_in_progress = false;
            if (reconnect) start_reconnect_timer();
            }
            break;
        case HTTP_EVENT_ON_CONNECTED:
            ESP_LOGI(TAG, "http connected");
            break;
        case HTTP_EVENT_HEADER_SENT:
            ESP_LOGI(TAG, "HTTP_EVENT_HEADER_SENT");
            break;
        case HTTP_EVENT_ON_HEADER:
            ESP_LOGI(TAG, "HTTP_EVENT_ON_HEADER, key=%s, value=%s", evt->header_key, evt->header_value);
            if (strcmp(evt->header_key, "Date") == 0)  
            {
                memcpy (date, evt->header_value, 30);
                esp_event_post_to(params->loop_handle, BLCR_REQ_EVENTS, BLR_GOT_TIME, date, 32, 0);
            }
            break;
        case HTTP_EVENT_ON_DATA:
            ESP_LOGI(TAG, "http get data, len=%d", evt->data_len);
            /*
             *  Check for chunked encoding is added as the URL for chunked encoding used in this example returns binary data.
             *  However, event handler can also be used in case chunked encoding is used.
             */
            if (!esp_http_client_is_chunked_response(evt->client)) {
                // If user_data buffer is configured, copy the response into the buffer
                if (evt->user_data) {
                    memcpy((uint8_t*)evt->user_data + output_len, evt->data, evt->data_len);
                } else {
                    if (output_buffer == NULL) {
                        output_buffer = (char *) malloc(esp_http_client_get_content_length(evt->client));
                        output_len = 0;
                        if (output_buffer == NULL) {
                            ESP_LOGE(TAG, "Failed to allocate memory for output buffer");
                            return ESP_FAIL;
                        }
                    }
                    memcpy(output_buffer + output_len, evt->data, evt->data_len);
                }
                output_len += evt->data_len;
            }
            parse_url (evt->data, evt->data_len);
            break;
        case HTTP_EVENT_ON_FINISH:
            ESP_LOGI(TAG, "HTTP_EVENT_ON_FINISH");
            // parse_url (evt->data, evt->data_len);
            if (output_buffer != NULL) {
                // Response is accumulated in output_buffer. Uncomment the below line to print the accumulated response
                // ESP_LOG_BUFFER_HEX(TAG, output_buffer, output_len);
                free(output_buffer);
                output_buffer = NULL;
            }
            output_len = 0;
            connection_is_in_progress = false;
            esp_http_client_close(client);
            break;
        case HTTP_EVENT_DISCONNECTED:
            ESP_LOGI(TAG, "HTTP_EVENT_DISCONNECTED");
            if (output_buffer != NULL) {
                free(output_buffer);
                output_buffer = NULL;
            }
            output_len = 0;
            connection_is_in_progress = false;
            break;
        case HTTP_EVENT_REDIRECT:
            ESP_LOGI(TAG, "HTTP_EVENT_REDIRECT");
            break;
    }
    return ESP_OK;
}



static bool parse_url (void *data, int data_length)
{
    const char *url = NULL;
    const char *url_end = NULL;
    uint16_t link_length;

    url = strstr ((const char *)data,"url");
    if (url == NULL) {
        ESP_LOGE(TAG, "can't find link in server reply");
        parsed_link = NULL;
        esp_event_post_to(params->loop_handle, BLCR_REQ_EVENTS, BLR_CANT_PARCE_LINK, NULL, 0, 0);
        if (reconnect) start_reconnect_timer();
        return false;
    }

    url += 7; // this is coz --url": "-- text
    // ESP_LOGI (TAG, " found url start: %s", url);
    url_end = strstr (url, "\"");
    if (url_end == NULL)
    {
        ESP_LOGE (TAG,"can't find link end in server reply");
        parsed_link = NULL;
        esp_event_post_to(params->loop_handle, BLCR_REQ_EVENTS, BLR_CANT_PARCE_LINK, NULL, 0, 0);
        if (reconnect) start_reconnect_timer();
        return false;
    }
    // ESP_LOGI (TAG, " found url end %s", url_end);
    link_length = url_end - url;
    parsed_link = (char *) malloc ( link_length + 1); 
    memcpy (parsed_link ,url , link_length);
    *(parsed_link +link_length) = '\0';
    
    ESP_LOGW (TAG, "parsed broker url: %s", parsed_link);
    *(params->output_broker_link) = parsed_link;
    connection_is_in_progress = false;
    // esp_http_client_cleanup(client);
    esp_event_post_to(params->loop_handle, BLCR_REQ_EVENTS, BLR_OK, NULL, 0, 0);
    return true;
}



static void perform_request ()
{
    if (client == NULL) 
    {
        ESP_LOGE(TAG,"client is null");
        return;
    }

    ESP_LOGI (TAG, "performing http request");

    connection_is_in_progress = true;
    esp_http_client_set_timeout_ms(client, 10000);
    esp_http_client_perform(client);
}



static void reconnect_timer_cb (void *)
{ 
    if (!reconnect) return;
    ESP_LOGI (TAG, "reconnect timer elapsed");
    if (params->output_broker_link == NULL) 
    {
        ESP_LOGE (TAG,"null pointer: output");
        return;
    }
    perform_request ();
}



static void start_reconnect_timer()
{
    if (client == NULL) return;

    if (repeat_timer == NULL)
    {
        const esp_timer_create_args_t timerArgs = {reconnect_timer_cb, 0, {}, 0, 0};
        ESP_ERROR_CHECK(esp_timer_create(&timerArgs, &repeat_timer));
    }

    request_repeat_number ++;
    connection_is_in_progress = false;

    esp_timer_start_once(repeat_timer, (request_repeat_number > QUICK_TRY_QTY ? LONG_REPEAT_TIME : SHORT_REPEAT_TIME) * 1000000);
}



}  // bsh_sys::blsr namespace

