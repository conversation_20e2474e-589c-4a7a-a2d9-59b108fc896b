#pragma once

#include "esp_event.h"



#define QUICK_TRY_QTY     3    // s
#define SHORT_REPEAT_TIME 8    // s    these are settings for repeat request logic. fir module does QUCK_TRYes, with qty and time as pecified, then starts reconnections or LONG setting
#define LONG_REPEAT_TIME  60   // s



namespace bsh_sys::blsr {



ESP_EVENT_DECLARE_BASE(BLCR_REQ_EVENTS);

typedef enum {
    BLR_OK,
    BLR_CANT_REACH_BALANCER,
    BLR_CANT_PARCE_LINK,
    BLR_GOT_TIME,
    BLR_QTY,
} balancer_events_t;
const char * balancer_event_to_string(balancer_events_t event);

typedef struct {
    esp_event_loop_handle_t loop_handle;    // event loop to post events to
    const char *balancer_link;              // link to balancer
    const char *user_name;                  // user name for basic auth
    const char *passwd;                     // pasword for basic auth
    bool reconnect;                        // if true, will try to reconnect on fail
    char **output_broker_link;              // pointer to broker link will be put here
} request_to_balancer_params_t;



/**
 * @brief Sends request to balancer to obtain mqtt broker link. Then parses the link, and puts it into output_broker_link.
 * Non-blocking.
 * !! This function allocates memory for output_broker_link !! clean it up when nesessary.
 * @param parameters - struct with request parameters
 */
void get_mqtt_addr_from_balancer (request_to_balancer_params_t *parameters);



/** 
 * @brief when reconnect set to true, module automatically repeat requests, if failed to get proper reply from server.
 *        This function stops repeats.
 */
void http_request_stop_reconnect();



} // bsh_sys::blsr namespace