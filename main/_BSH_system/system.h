/**
 * This module provided device-INDEPENDENT services:
 * - uart, wifi, mqtt inits (injected with device-specific stuff), creds load/save
 * - comissioning and ble communication
 * - firmware version
 * - device ID
 * - RSA for signatures
 * - TLS certificates
 * - debug
 * - firmware upgrade
 * - bootstrap config
 */

#pragma once

#include "stdint.h"

#include "esp_event.h"

#include "mqtt_sys_protocol.h"    // contains mqtt incoming queue and its object type 
#include "telemetry_storage.h"    // accepts telem blocks for starage and pushing to broker
#include "uart.h"
#include "wifi.h"
#include "http_request_to_balancer.h"
#include "date_and_time.h"
#include "bluetooth_LE.h"
#include "ble_comm_logic.h"
#include "bsh_ota.h"



#define BLE_OUT_QUEUE_SIZE          3           // not so big, as BLE is not main function. so scimp memory on it
#define BLE_IN_QUEUE_SIZE           2           //small one, expecting main task to prosess incoming data very quick


#define USE_DEBUG_WIFI_CREDS        0
    #if USE_DEBUG_WIFI_CREDS == 1
        #define DEBUG_WIFI_SSID "MGuest"
        #define DEBUG_WIFI_PASSW "322223322"
        // #define DEBUG_WIFI_SSID "platinum"
        // #define DEBUG_WIFI_PASSW "-333666999-"
        // #define DEBUG_WIFI_SSID "devices"
        // #define DEBUG_WIFI_PASSW "369369369"
        // #define DEBUG_WIFI_SSID "q780upd"
        // #define DEBUG_WIFI_PASSW "nopassword"
    #endif
#define USE_DEBUG_MQTT_CONN         0
    #if USE_DEBUG_MQTT_CONN == 1
    // #define DEBUG_BALANCER_LINK  "http://mqtt-balance.stage.tbhub.ru/api/v1/brokers/simple-connectable"
    // #define DEBUG_BROKER_ADDR   "mqtt://mqtt-broker-instance-1-stage.tbhub.ru:1883"
    #define DEBUG_BROKER_ADDR   "mqtt://192.168.74.58:1883"
    // #define DEBUG_BROKER_URL "mqtt://mqtt.smart-home-mvp.bork.ru"  // from q780 debug. seems its still running
    #define DEBUG_BROKER_URL "mqtt-broker-instance-1-stage.tbhub.ru"
    #define DEBUG_OVERRIDE_CHANNELS 1
    #if DEBUG_OVERRIDE_CHANNELS == 1
        #define CHANNEL_COMMANDS "/users/76969696969/devices/69696969696969696969/commands"  // for stage server
        #define CHANNEL_STATUS "/users/76969696969/devices/69696969696969696969/state"       // for stage server
        // #define CHANNEL_COMMANDS "/users/79282177777/devices/string5/commands"               // for mvp server
        // #define CHANNEL_STATUS "/users/79282177777/devices/string5/state"                    // for mvp server
    #endif
    #endif
#define UDE_DEBUG_MQTT_CREDS    0
    #if UDE_DEBUG_MQTT_CREDS
    #define DEBUG_MQTT_UID     "CoreUser"
    #define DEBUG_MQTT_PSW     "CorePwd"
    #endif




namespace bsh_sys {
    


ESP_EVENT_DECLARE_BASE (SMART_HOME_CONN_EVENTS);
ESP_EVENT_DECLARE_BASE (BLE_COMMAND_EVENTS);   // for device-specific commands, not handled inside system module

typedef enum {
    BSH_NO_CONNECTION,
    BSH_PAIRING,
    BSH_WIFI_CONNECTED,
    BSH_SMART_HOME_CONNECTED,
    BSH_GOT_TIME,
} smart_home_conn_events_t;
const char * sh_event_to_string(smart_home_conn_events_t event);



typedef struct
{
    uint16_t firware_version_major;
    uint16_t firmware_version_minor;
    uint16_t firmware_version_patch;
    char commit_hash[8];    // 7 symbols + eos
    const char * ota_hardcoded_link; // for ota during development. not for production
    const char * ble_device_name;
    uint8_t ble_device_type[4];
    uint8_t ble_device_id_code;
    uint8_t actors_qty;
    const char * guid_prefix;  

    bool dont_need_uart;                // !!! for cases when BSH is part of core firmware and no need to send commands via uart
    bsh_sys::uart::uart_settings_t *uart_stt;

    char *default_wifi_login;          // could be null if not required
    char *default_wifi_passw;          // could be null if not required
} system_config_t;



/**
 * @brief initializes system services: uart, wifi, ble.
 *       Loads credentials and connects to wifi, then to mqtt
 * 
 * @param sys_config pointer to system configuration (mostly devoce-specific items)
 *             struct is NOT COPIED, so need to keep pointer alive 
 *
 * @return ESP_ERR_NOT_FOUND if no creds loaded
 */
esp_err_t init (system_config_t *sys_config);



/**
 * Returns pointer to system config, provided at init
 */
system_config_t *get_system_config();



/**
 * Returns incoming mqtt queue handle
 */
QueueHandle_t get_mqtt_incoming_queue();



bool is_first_start_after_ota();



/** 
 * Returns system event loop - can be used to subscribe for events
 * 
 */
esp_event_loop_handle_t get_event_loop_handle();



smart_home_conn_events_t get_network_status();



/**
 *  This function allows to change BLE status char values fron outside of system module.
 *  Data is writted to status array with offset, so system module data is not overwritten
 *  Offset is defined by BLE_STATUS_CHAR_SYSTEM_PARAMS_OFFSET
 *  Max length is BLE_SATUS_CHAR_ARR_SIZE - BLE_STATUS_CHAR_SYSTEM_PARAMS_OFFSET
 */
void update_ble_status_char(uint8_t *data, uint8_t data_len);



void print_app_info();
void no_autoconnect();
void conn_to_wifi();
void conn_to_balancer();



} // bsh_sys namespace

