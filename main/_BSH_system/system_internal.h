/**
 * This is system internal module.
 * Meant to store comissioning data, and return it upon request.
 */



#pragma once
#include "wifi.h"
#include "http_request_to_balancer.h"



/********************************************
 *            COMMISSIONING
 * 
 * implemented in commissioning.cpp
 ********************************************/
namespace bsh_sys {


void connect_to_smart_home();

typedef enum {
    WIFI_NO_PASSW_SET = bsh_sys::wifi::WIFI_STATUS_QTY,
    WIFI_NO_SSID_SET,
    WIFI_CONNECTING,
} wifi_conn_result_t;

/**
 * @brief Start connecting to wifi.
 *        To be called by ble module, after command from phone.
 * @return ok or creds err. other statuses are async and returned separately
 */
wifi_conn_result_t connect_to_wifi();



void no_autoconnect();



typedef enum {
    BLCR_NO_LINK_SET = bsh_sys::blsr::BLR_QTY,
    BLCR_CONNECTING,
    BLCR_NO_WIFI,
} blcr_conn_result_t;   // if changing , also change codes converter in ble_comm module 

/**
 * @brief Start connecting to balancer.
 *        To be called by ble module, after command from phone.
 * @return ok or creds err. other statuses are async and returned separately
 */
blcr_conn_result_t connect_to_balancer();



typedef enum {
    MQTT_NO_BROKER_LINK_SET,
    MQTT_NO_USER_ID_SET,
    MQTT_NO_USER_PWSD_SET,
    MQTT_NO_WIFI,
    MQTT_CONNECTING,
} broker_conn_result_t;

/**
 * @brief Start connecting to mqtt broker.
 *        To be called by ble module, after command from phone.
 * @return ok or creds arr. other statuses are async and returned separately
 */
broker_conn_result_t connect_to_broker();



bsh_sys::uart::uart_settings_t *get_uart_settings(); // returns pointer to uart settigns struct, for testing purposes



const char *get_ble_device_name();



} // bsh_sys namespace