/**
 * This module stores telemetry blocks, sends them to mqtt out queue upon command,
 * or regulary - by timer.
 * Regular updates can be turned off. 
 * Regular updates time can be changed.
 * Module receives changed blocks via update_block() command,
 * and can return any block upon request.
 * Module is device-independent.
 * Should be provided with blocks quantity at initialization.
 * Push telemetry commands require origin specifier.
 * 
 * push_telemetry_after_ms() can be used to accumulate telemetry for some period of time, without sending it,
 *      push_telemetry() is ignored during this ms period.
 *      multiple push_telemetry() also ignored during that peroid
 *      this is designed to accumulate data coming from device - as respond to mqtt command.
 * 
 * 
 * RPL block logic:
 * The block represents brocker command execution result: ok or not ok.
 * For devices where system is used to pass commands to main MCU,
 * sometimes its unknown what is result of execution.
 * In this case this module adds RPL = 0 block when it sees origin == ORN_BROKER_CMD
 * and !finds no RPL block!. This approach is requested by backend. 
 * <PERSON><PERSON><PERSON> clears this block right after sending. 
 * 
 * For devices where system used as part of device firmware, 
 * and result of brocker command execution is always obvious,
 * device operation code is expected to add RPL block itself.
 * This module looks for RPL block, and does nothing else when block is found in T.
 * 
 * TODO remove this feature from the module? make it responsibility 
 * of main device code to add or don't add RPL block?
 * 
 */

#pragma once

#include "mqtt_sys_protocol.h"



#define DEFAULT_QOS 0



namespace bsh_sys::telem_storage 
{


/**
 * Initializes module
 */ 
void init (uint8_t blocks_qty);



/**
 * To be called when new data arrives over uart.
 * Updates internally stored device data.
 * DOES NOT send mqtt packets
 */
void update_block (uint8_t block_index, bsh_sys::mqtt_protocol::mqtt_out_block_t *block);



/**
 * Wipes all block data from storage, including its name (like FAN)
 */
void clear_block (uint8_t block_index);



/**
 * @brief Sends telemetry instantly, ignoring delay conditions.
 *        Per protocol T. always should have origin code, and sometimes RPL code. 
 *        Hence the parameters below
 * 
 * @param origin   origin code: source of T change. like user, device, brocker command
 * @param RPL_code  if its reply to brocker cmd, this code indicates if cmd was executed, or err occured
 *                  Module will add RPL bloco per this parameter, and will delete it right after sending T.
 *                  Can be NULL if not needed
 */
void push_telemetry(bsh_sys::mqtt_protocol::origin_code_t origin, uint8_t *RPL_code);


/**
 * Sends single block to broker
 */
void push_single_block(uint8_t block_id);



/**
 * Sends telemetry after while.
 * If delay timer is already started by prev. call of this function, and incoming origin code is ORN_BROKER_CMD,
 * origin will be overriten. In case of any other code - current code stays as is. 
 */
void push_telemetry_after_ms(uint32_t delay_ms, bsh_sys::mqtt_protocol::origin_code_t origin);



/**
 * Stop regular telemetry send
 */
void stop_regular_telemetry();



/**
 * Starts regular telemetry send
 */
void start_regular_telemetry();



/**
 * Sets regular telemetry time in seconds
 */
void set_regular_telemetry_time (uint16_t seconds);



/**
 * Reset telem timer, doesn't send telemetry
 */
void reset_telem_timer();



bsh_sys::mqtt_protocol::mqtt_out_block_t *get_block(uint8_t block_n);



} // end of bsh_sys::telem_storage  namespace

