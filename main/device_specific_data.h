#pragma once

#include "driver/uart.h"
#include "driver/gpio.h"



/************************
 *        SYSTEM
 ***********************/
#define HARDCODED_OTA_LINK "http://st.bork.ru/application/firmwares/wifi/a630/firmware.bin"

#define FIRMWARE_VERSION_MAJOR 2
#define FIRMWARE_VERSION_MINOR 0
#define FIRMWARE_VERSION_PATCH 0

static uint8_t ble_dev_type[4] = {0,0,2,0};

#define DEFAULT_WIFI_LOGIN          "fungene\0"  // for wifi signal level test at manufacturing line
#define DEFAULT_WIFI_PASSW          "fungene123\0"




/************************
 *        MQTT
 ***********************/
#define MQTT_DEVICE_GUID_PREFIX     "bork_device_a630_"



/************************
 *        UART
 ***********************/
#define UART_OUT_QUEUE_SIZE         12



#define UART_NUM UART_NUM_2
#define UART_TX_PIN GPIO_NUM_17  //UART_PIN_NO_CHANGE
#define UART_RX_PIN GPIO_NUM_18  //UART_PIN_NO_CHANGE
#define UART_RTS_PIN UART_PIN_NO_CHANGE
#define UART_CTS_PIN UART_PIN_NO_CHANGE

static uart_config_t uart_cnf = {
    .baud_rate = 9600,
    .data_bits = UART_DATA_8_BITS,
    .parity = UART_PARITY_DISABLE,
    .stop_bits = UART_STOP_BITS_1,
    .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
    .rx_flow_ctrl_thresh = 0,
    .source_clk = UART_SCLK_APB,
};



/************************
 *        BLE
 ***********************/
#define BLE_DEVICE_NAME             "A630"
#define DEVICE_ID_CODE              0x0D // in advertising, for ios app
