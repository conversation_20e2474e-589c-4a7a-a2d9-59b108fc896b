#include <stddef.h>
#include <string.h>

#include "esp_log.h"

#include "queues_helpers.h"
#include "uart_protocol.h"
#include "mqtt_protocol.h"
#include "system.h"
#include "commissioning.h"
#include "system_internal.h"
#include "device_specific_data.h"



namespace a630::main_task::queue_helpers {



static const char *TAG = "-queues hlp-";

static QueueHandle_t uart_out_queue = NULL;
// uart in queue doesn exist coz uart incoming data is processed right from uart buffer



/* ======= local function declarations ========= */
static const char *command_id_to_string(uart_commands_t cmd);



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
void init()
{
    ESP_LOGI (TAG, "Initialising uart queue..");
    uart_out_queue = xQueueCreate(UART_OUT_QUEUE_SIZE, sizeof(bsh_sys::uart::uart_out_queue_obj_t));

    if (uart_out_queue == NULL)
    {
        ESP_LOGE(TAG, "Failed to init uart queue, restarting device...");
        esp_restart();
    }

    ESP_LOGI (TAG, "    Queue created");
}



QueueHandle_t get_uart_queue()
{
    return uart_out_queue;
}



void send_uart_command(uart_commands_t command)
{
    printf ("\n");
    ESP_LOGI(TAG, "sending uart cmd: == %s ==", command_id_to_string(command));
    if (uart_out_queue == NULL)
    {
        ESP_LOGE (TAG, "uart queue is null");
    }
    
    bsh_sys::uart::uart_out_queue_obj_t obj;
    
    switch (command)
    {
    case UART_CMD_QUERY_ALL:
        a630::uart_protocol::make_query_all_pkt((uint8_t *)obj.data, &obj.data_size);
        break;

    case UART_CMD_TIME:
        {
        a630::uart_protocol::tuya_time_t tuya_time;
        time_from_system_to_tuya(bsh_sys::date_time::get_date_time(), &tuya_time);
        a630::uart_protocol::make_time_pkt((uint8_t *)obj.data, &tuya_time, &obj.data_size);
        break;
        }

    case UART_CMD_NETWORK_STATUS:
        // a630::uart_protocol::make_network_status_pkt((uint8_t *)obj.data, sys_ntw_status_to_tuya_format(bsh_sys::get_network_status()), &obj.data_size);
        a630::uart_protocol::make_network_status_pkt((uint8_t *)obj.data, sys_ntw_status_to_tuya_format(bsh_sys::BSH_SMART_HOME_CONNECTED), &obj.data_size);
        break;

    case UART_CMD_START_FNG_OTA:
        a630::uart_protocol::make_fung_ota_set_wifi_ssid_packet((uint8_t *)obj.data, &obj.data_size, bsh_sys::commiss::get_wifi_ssid());
        xQueueSend(uart_out_queue, &obj, 0);

        a630::uart_protocol::make_fung_ota_set_wifi_psw_packet((uint8_t *)obj.data, &obj.data_size, bsh_sys::commiss::get_wifi_passw());
        xQueueSend(uart_out_queue, &obj, 0);

        a630::uart_protocol::make_fung_ota_start_packet((uint8_t *)obj.data, &obj.data_size);
        // will be sent to queue below- after switch-case
        break;

    case UART_CMD_REQUEST_PROD_INFO:
        a630::uart_protocol::make_product_info_pkt((uint8_t *)obj.data, &obj.data_size);
        break;

    case UART_CMD_SEND_RSSI:
        a630::uart_protocol::make_wifi_strength_packet((uint8_t *)obj.data, bsh_sys::wifi::get_rssi(), &obj.data_size);
        break;

    case UART_CMD_SEND_HEARTBEAT:
        a630::uart_protocol::make_heartbeat_pkt((uint8_t *)obj.data, &obj.data_size);
        break;
    }

    if (xQueueSend(uart_out_queue, &obj, 0) == errQUEUE_FULL)
    {
        ESP_LOGE(TAG,"mqtt out queue is full");
    }
}



void send_working_time_always_on ()
{
    printf ("\n");
    ESP_LOGI(TAG, "sending uart cmd: == work time = always on ==");

    if (uart_out_queue == NULL)
    {
        ESP_LOGE (TAG, "uart queue is null");
    }

    bsh_sys::uart::uart_out_queue_obj_t obj;

    // set start time = 0
    a630::uart_protocol::make_working_timing_start_time_pkt((uint8_t *)obj.data, &obj.data_size);
    if (xQueueSend(uart_out_queue, &obj, 0) == errQUEUE_FULL)
    {
        ESP_LOGE(TAG,"mqtt out queue is full");
    }
    // set end time = 0
    a630::uart_protocol::make_working_timing_end_time_pkt((uint8_t *)obj.data, &obj.data_size);
    if (xQueueSend(uart_out_queue, &obj, 0) == errQUEUE_FULL)
    {
        ESP_LOGE(TAG,"mqtt out queue is full");
    }
    // set all week days
    a630::uart_protocol::make_working_timing_all_week_days_pkt((uint8_t *)obj.data, &obj.data_size);
    if (xQueueSend(uart_out_queue, &obj, 0) == errQUEUE_FULL)
    {
        ESP_LOGE(TAG,"mqtt out queue is full");
    }
}



void send_DPs_to_uart(a630::uart_protocol::tuya_data_point_t *dps, uint8_t dps_qty)
{
    bsh_sys::uart::uart_out_queue_obj_t obj = {};
    a630::uart_protocol::tuya_out_uart_packet_t *tuya_data = (a630::uart_protocol::tuya_out_uart_packet_t *)obj.data ;
    a630::uart_protocol::out_pckt_init(tuya_data);

    for (size_t i = 0; i < dps_qty; i++)
    {
        a630::uart_protocol::out_pkt_add_dp(tuya_data, dps + i);
    }
    
    a630::uart_protocol::out_pkt_close(tuya_data);

    obj.data_size =  a630::uart_protocol::get_tuya_pkt_length(tuya_data);

    if (xQueueSend(uart_out_queue, &obj, 0) == errQUEUE_FULL)
    {
        ESP_LOGE(TAG,"mqtt out queue is full");
    }
}



void time_from_system_to_tuya(const bsh_sys::date_time::date_and_time_t *sys_time, a630::uart_protocol::tuya_time_t *out_tuya_time)
{
    if (sys_time->year == 0)
    {
        out_tuya_time->got_time_from_server = false;
        memset (out_tuya_time, 0, sizeof(a630::uart_protocol::tuya_time_t));
        return;
    }

    out_tuya_time->got_time_from_server = true;
    out_tuya_time->year_after_2000 = sys_time->year - 2000;
    out_tuya_time->month = sys_time->month;
    out_tuya_time->day = sys_time->day_n;
    out_tuya_time->hour = sys_time->hour;
    out_tuya_time->minute = sys_time->min;
    out_tuya_time->second = sys_time->sec;
    out_tuya_time->week = sys_time->week_day;
}



a630::uart_protocol::tuya_network_status_t sys_ntw_status_to_tuya_format(bsh_sys::smart_home_conn_events_t status)
{
    using namespace bsh_sys;

    switch (status)
    {
    case BSH_NO_CONNECTION: return a630::uart_protocol::NTW_ST_NOT_CONNECTED_TO_WIFI;
    case BSH_WIFI_CONNECTED: return a630::uart_protocol::NTW_ST_CONNECTED_TO_WIFI;
    case BSH_SMART_HOME_CONNECTED: return a630::uart_protocol::NTW_ST_CONNECTED_TO_CLOUD;
    case BSH_PAIRING: return a630::uart_protocol::NTW_ST_PAIRING;
    case BSH_GOT_TIME: break;
    }

    return a630::uart_protocol::NTW_ST_NOT_CONNECTED_TO_WIFI;
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static const char *command_id_to_string(uart_commands_t cmd)
{
    switch (cmd)
    {
    case UART_CMD_QUERY_ALL: return "query all";
    case UART_CMD_TIME: return "set time";
    case UART_CMD_NETWORK_STATUS: return "set network status";
    case UART_CMD_START_FNG_OTA: return "start ota";
    case UART_CMD_REQUEST_PROD_INFO: return "request prod info";
    case UART_CMD_SEND_RSSI: return "set rssi";
    case UART_CMD_SEND_HEARTBEAT: return "heartbeat";
    }
    return "??";
}


} // a630::main_task::queue_helpers