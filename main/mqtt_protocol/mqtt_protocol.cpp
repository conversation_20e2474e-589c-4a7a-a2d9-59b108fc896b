#include <stddef.h>
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>

#include "esp_log.h"
#include "esp_debug_helpers.h"

#include "mqtt_protocol.h"
#include "rsa_encryption.h"
#include "system.h"
#include "mqtt.h"



namespace a630::mqtt_protocol {



#define free_pointer_if_not_null(P) if(P != NULL) {free(P); P=NULL;}



/* ======= local defines for constants =========*/
static const char *TAG = "-device-";



/* ======= local function declarations ========= */
static bool compare_mnemocodes (const char *mc1, const char *mc2);



/*===============================================*\
 * Exported functions definitions
\*===============================================*/
bsh_sys::mqtt_protocol::pckt_integrity_err_events_t check_value (bsh_sys::mqtt_protocol::mqtt_in_block_t *cmd)
{
    // TODO ?  for now appoach is to dont check values, just pass them through
    return bsh_sys::mqtt_protocol::pckt_integrity_err_events_t::PACKET_IS_OK;
}



const char *actor_code_to_string(actor_t act)
{
    switch (act)
    {
        case MOD: return "MOD";
        case STA: return "STA";
        case ARA: return "ARA";
        case WLP: return "WLP";
        case ALP: return "ALP";
        case STS: return "STS";
        case STE: return "STE";
        case SCN: return "SCN";
        case FSC: return "FSC";
        case FSM: return "FSM";
        case FST: return "FST";
        case FSS: return "FSS";
        case FSR: return "FSR";
        case STC: return "STC";
        case WCN: return "WCN";
        case WTC: return "WTC";
        case CLR: return "CLR";
        case CLN: return "CLN";
        case SLP: return "SLP";
        case BCT: return "BCT";
        case FFV: return "FFV";
        case FFU: return "FFU";
        case FFS: return "FFS";

        case ACTORS_QTY: return "err";
    }
    return "err";
}



/**
 * @brief Device dependent
 *  Looks for actor in the list.
 * @param pointer to actor string (assumed 3 chars long)
 * @return Return actor N if found. ACTORS_QTY if not found.
 */
actor_t get_actor_id(const char *actor)
{
    // ESP_LOGI (TAG,"looking for mnemocode: %c%c%c @ %p", *actr, *(actr+1), *(actr+2), actr);
    for (size_t i = 0; i < ACTORS_QTY; i++)
    {
        if (compare_mnemocodes(actor, actor_code_to_string((actor_t)i)))
        {
            // ESP_LOGI (TAG,"found mnemocode: %s", actor_code_to_string((actor_t)i));
            return (actor_t)i;
            break;
        }
    }
    
    ESP_LOGE (TAG, "mnemocod not recognized: %c%c%c [%02x %02x %02x]", actor[0], actor[1], actor[2], actor[0], actor[1], actor[2]);
    return ACTORS_QTY;
}



void set_actor(bsh_sys::mqtt_protocol::mqtt_in_block_t *block, actor_t actor)
{
    memcpy (block->actor, actor_code_to_string(actor), 3);
}



/*===============================================*\
 * Local function definitions
\*===============================================*/
static bool compare_mnemocodes (const char *mc1, const char *mc2)
{
    // ESP_LOGW (TAG,"looking for mnemocode: %c%c%c", *mc1, *(mc1+1), *(mc1+2));
    for (size_t i = 0; i < 3; i++)
    {
        if (*(mc1+i) != *(mc2+i)) return false;
    }
    return true;
}



}  // a830::mqtt_protocol namespace