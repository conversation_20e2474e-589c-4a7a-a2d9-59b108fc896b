/**
 * This module defines device-specific MQTT protocol stuff:
 * - list of actors
 * - 
 */

 /**
 *  For testing:
 *  mosquitto_pub commands:
 *  mosquitto_pub -h 1.mqtt.prod.dpa.tbhub.ru -p 1881 -u BackendUser -P BackendPwd -t '/devices/qwe/commands' -i 'someid' 
 *  -m "$(printf \\x01)$(printf \\x01)MTR$(printf \\x00)$(printf \\x00)$(printf \\x01)"
 */

#pragma once

#include <stdbool.h>
#include <stdint.h>

#include "mqtt_sys_protocol.h"



namespace a630::mqtt_protocol {
    


/** 
* Actors described at https://youtrack.bork.ru/articles/SH-A-124
*/ 
typedef enum{
    MOD = 0,    // 0/1  = off/on
    STA,        // device state
                //      0 Not displayed 
                //      1 Normal 
                //      2 Sleeping 
                //      3 Leaving home 
                //      4 Instant fragrance 
                //      5 Lack of essential oil 
                //      6 Cleaning
    ARA,        // area	номер комнаты для сценариев (Usage Scenario)	N = 0,1,2,…,1000
    WLP,        // oil level
    ALP,        // 0,1,2...100 = aroma dissipation power
    STS,        // sleep_time_start	Время начала спящего режима (Sleep Mode	N = 0,1,2,...,1440 (HH:MM)
    STE,        // sleep_time_end	Время завершения спящего режима	 N = 0,1,2,...,1440 (HH:MM)
    SCN,	    // index	Выбор сценария (Usage Scenario)	 N = 0,1,2,…,100
    FSC,        // "Instant fragrance mode switch"  - used as module-internal storage for auto/manual mode setting. auto = 1, manual = 0
    FSM,        // fast smell mode
    FST,        // fast smell time - 0,1,2...1440  seconds to run
    FSS,        // fast smell start time - 0,1,2...1440
    FSR,        // fast smell repeat. in days of week.  0,1,2..6 = sunday, monday, tuesday etc..  one byte, bitmask. monday: [0]100 0000
    WTC,        // work_time_cd	Обратный отсчет до конца работы (до паузы)	N = 0,1,2,…,999999999
    STC,        // stay_time_cd	Обратный отсчет до начала работы  N = 0,1,2,…,99999999
    WCN,        // Текущая длительность работы
    CLR,        // clean_data	Удалить данные статистики	N = 0 - не активно  N = 1 - запуск удаления
    CLN,        // clean  0 off / 1 on
    SLP,        // energy_modal	Энергосберегающий режим (Conserve)	 
                //      N =0 = close
                //      N =1 - sleep mode
                //      N =2 - away from home mode
                //      N =3 - Fully open

    FFV,        // Fungene firmware version.
    FFU,        // fungene firmware update .  write 1 to start
    FFS,        // fungene firmware update status

    BCT,        // broadcast = need to update all data from device and send back to broker
    ACTORS_QTY,
} actor_t;



typedef enum {
    FFS_UNKNOWN,
    FFS_OK = 1,
    FFS_FAILED = 2,
} fungene_ota_status_t;



/**
 * @brief Returns actor id from actor_t enum.
 * @param actor - pointer to char[3] actor
 */
actor_t get_actor_id(const char *actor);



/**
 * @brief Returns string with actor name.
 * @param act - actor id from actor_t enum
 */
const char *actor_code_to_string(actor_t act);



void set_actor(bsh_sys::mqtt_protocol::mqtt_in_block_t *block, actor_t actor);



/**
 * @brief Check actor block value and its type for correctness
 *          Device - specific
 *          // TODO not implement, always returns ok
 */ 
bsh_sys::mqtt_protocol::pckt_integrity_err_events_t check_value (bsh_sys::mqtt_protocol::mqtt_in_block_t *cmd);



} // a830::mqtt_protocol namespace