/**
 * This module meant to receive A830 DPs updates,
 * then it converts DPs into mqtt actors blocks, and passes to
 * system telemetry storage.
 *   
 * Also it checks if critical data was changed,
 * and if it was - pushes telemetry sending
 * 
 * As additional functions: 
 * It saves and returns fungene product info:
 * firmware version, product id, ota status
 */

#pragma once

#include "uart_protocol.h"
#include "mqtt_protocol.h"
#include "telemetry_storage.h"



namespace a630::tlm_filter 
{



/**
 * To be called when new data arrives over uart.
 * Updates internally stored device data.
 * DOES NOT send mqtt packets
 */
void update_dp (a630::uart_protocol::tuya_data_point_t *dp);
void update_fung_firm_ver(uint8_t *fung_firm_ver);
void update_fung_ota_status(a630::mqtt_protocol::fungene_ota_status_t status);

void set_fung_product_id(char *fung_prod_id);
const char *get_fung_product_id();



} // end of a830::tlm_filter namespace