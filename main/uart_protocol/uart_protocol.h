/**
 *      This module provides functions to parse incoming UART packets, and to put together outgoing UART packets. 
 *      Parsed data is not copied while parsing. Parsing result structs contain only pointers to received values.
 *      The idea is to parse data right from UART buffer, and put events as nesessary to other queues (to mqtt out queue, to ble out, etc).
 *      Outgoing packets are stored in out-queue in full size, in spite of incoming packets.
 * 
 *      Packets structures/protocol is based on Tuya UART protocol
 *      https://developer.tuya.com/en/docs/iot/tuya-cloud-universal-serial-port-access-protocol?id=K9hhi0xxtn9cb#title-11-Send%20commands
 * 
 *  
 *      ==========================
 *         Packet (frame) format.  same for Module->MCU and MCU->Module communication
 *      ==========================
 * Header [2 bytes]         -   always 0x55aa
 * Version [1 byte]         -   It is used for updates and extensions.  see PROTOCOL_VERSION define
 * Command [1 byte]         -   Includes system commands, and control commands. see below
 * Data length [2 bytes]    -   Big-endian
 * Data [x bytes]           -   data
 * Checksum [1 byte]        -   Start from the header, add up all the bytes, and then divide the sum by 256 to get the remainder.
 * 
 *      TUYA Commands: (those with tab, like  "*      0x37"   are not implemented)
 * 0x00     - > heart beat
 *                  MCU returns  0 if right after restart. 1 otherwise
 * 0x01     - > query product information
 *                  contains MCU firmware version. we could use it for starting updates?
 *                  returns big string
 *      0x02     - query working mode. defines master/slave roles between MCU & module in network connection
 *                 no need in this function, as we already know the roles: module deals with network itself  
 * 0x03     - > network status. module reports connection statuses to MCU, for device to indicate them.  mcu also requests status at startup
 *              we should implement this, not clear if diffusor has indication meanings.
 *      0x37     - < notification of new feature setting. no need in this command
 *      0x04     - < reset wifi connection. no need in this
 *      0x05     - < select pairing mode
 * 0x06     - > SEND command - command from module to MCU. asynchronously
 * 0x07     - < mcu status change paket. change as result of module command 0x06, user action or reply to 0x08(Query DP status)
 * 0x22     - < report status (sync). MCU sends data.  module MUST RESPOND to recived data !!! othervise will be delays. see below
 *              "The MCU reports DP status and then waits for the result from the module."
 *              "The module must respond to each status reporting message. The MCU must not send a new reporting task until receiving a response from the module."
 * 0x23     - > module reply on 0x22
 * 0x34     - < report status from mcu : (sync) seems same as -x22.  The MCU reports DP status and then waits for the result from the module.
 * 0x34     - > reply on 0x34 from module to mcu
 * 0x08     - > Query ALL!!! DPs status. The module asynchronously queries the status of all object DPs.
 * 0x0a     - > update firmware (start) see more in the docs
        * 0x0c     - < get time (from MCU) in GMT
        * 0x1c     - < get local time
        * 0x0e     - < test wifi (Scanning)
        * 0x0f     - < Get module’s memory
        * 0x20     - < Enable weather services
        * 0x21     - < Send weather data
 * 0x24            - < Get Wi-Fi signal strength
        * 0x25     - < Disable heartbeats
        * 0x28     - < Map streaming for robot vacuum
        * 0x2A     - < Pairing via serial port
        * 0x2B     - < Get the current network status
        * 0x2C     - < Test Wi-Fi functionality
        * 0x2D     - < Get module’s MAC address
        * 0x2E     - >  IR status notification
        * 0x2F     - < IR functionality test
        * 0x30     - < Map data streaming for multiple maps
        * 0x33     - > RF functionality
        * 0x34     - < Get the map session ID   error?? duplicaed commands
        * 0x34     - < Proactively request weather data.     error?? duplicaed commands
        * 0x37     - > File transfer service
        * 
        * 0x60     - < Voice features
        * 0xXX     - <> several medial playing commands and alarm commands? bluetooth features, wifi creds
        * 
        * 
 *  0x1C     - get local time
        * 
 * -    a lot more commads (some optional) - see the docs
 * 
 * 
 * 
 * 
 *      ==========================
 *      Data points (units)
 *      ==========================
 * Single data point decribes a command to change device state
 * Consists of:
 * 
 * dpid [1 byte]       - id of device property being set
 * type [1 byte]       - data type
 * len  [2 bytes]      - qty of bytes with value.. in 4 bytes int = 34, its only one byte with value.
 *                       in big endian. even if there is no data to send, still send 1 byte with 0x00
 * value [ x bytes]    - data  (also in big endian)
 * 
 *      
 * 
 *      ==========================
 *      Data types:
 *      ==========================
 * https://developer.tuya.com/en/docs/iot/tuya-cloud-universal-serial-port-access-protocol?id=K9hhi0xxtn9cb#StatusDataType
 * 
 * 0x00 raw            Represents a DP of raw data type. The data is passed through the module to the cloud.
 * 0x01 boolean        Represents a DP of Boolean data type. Valid values include 0x00 and 0x01.  data length: 1 byte
 * 0x02 int            Represents a DP of integer type. The data is represented in big-endian format.
 * 0x03 string         Represents a DP of string type.
 * 0x04 enum           Represents a DP of enum type, ranging from 0 to 255.
 * 0x05 bitmap         Represents a DP of fault type. Data greater than one byte is represented in big-endian format.
 * 
 * 
 * 
 */
#pragma once


#include <stdint.h>



#define UART_PROTOCOL_VERSION 0
#define OUT_PACKET_DATA_SIZE 250
#define OUT_PACKET_SIZE (OUT_PACKET_DATA_SIZE + 7) // per tuya_out_uart_packet_t struct
#define DPS_QTY 27

#define HEADER_0 0x55
#define HEADER_1 0xAA



namespace a630::uart_protocol {



/***************************
 *  tuya data point struct
 ***************************/
typedef struct
{
    uint8_t dpid;
    uint8_t data_type;
    uint16_t data_len;  // already not in big endian in this struct!  converted at parsing
    uint8_t *data;      // still in big endian as its not changed at parsing and still points to uart buffer=raw data
} tuya_data_point_t;

// see define for DPs qty above. change it if qty changes
// if change DPs, also change is_used_dp function
    typedef enum
    {
        DP_MOD = 0x01,                  // bool
        DP_POWER_SAVE_MODE = 0x65,   // 0 off   1 sleep mode    2 Away from home mode     3 wide open
        DP_AREA = 0x66,                 // 0 - 1000     room number / screnario number
        DP_OIL_LEVEL = 0x6E,	        // oil level 0 - 100
        DP_CONCENTRATION = 0x6F,	    //Concentration reporting  0 - 100
        DP_SLEEP_TIME_START = 0x70,	    // in minutes
        DP_SLEEP_TIME_END = 0x71,	
        DP_SCENARIO = 0x78,	        // Usage Scenario   0 - 100  
        DP_FAST_SMELL = 0x7B,	
        DP_FAST_SMELL_TIME = 0x7C,	
        DP_FAST_SMELL_START_TIME = 0x7D, // start of working time period. in minutes from midnight
        DP_FAST_SMELL_CTRL = 0x7E,      // "Instant fragrance mode switch"   - ignored when comes from device
        DP_FAST_SMELL_REPEAT = 0x7F,	// string with week days. Instant aroma mode repeat time ("1,2,3,4,5,6,7")   no quotes no braces
        DP_DEVICE_STATE = 0x81,	
        DP_WORKING_TIME_END = 0x84,	  // end of working time period. in minutes from midnight
        DP_POSTPONE_START = 0xC3,	  // 0-999999999
        DP_WORK_COUNT = 0xC4,	
        DP_WORK_TIME_CD = 0xC5,	
        DP_CLEAR_DATA = 0xC6,	
        DP_CLEAN = 0xC7,	

        // OTA DPs
        DP_SET_WIFI_SSID = 0xC8,    // string
        DP_SET_WIFI_PSWD = 0xC9,    // string
        DP_OTA_STATE = 0xCA,        // int.    4 bytes: [1] - network connection status 0-not connected. 1-connected
                                    //                  [2] - new firmware avilability. 0-no new f. 1-have new.f
                                    //                  [3] - updating status. 0-not started. 1-done successfully. 2-failed. 3-standby 4-in progress
                                    //                  [4] - set to 1 to start update.
        DP_FIRM_VER = 0xCB,         // string

        // --- not used ---
        DP_SEND_TIME_STAMP = 0x69,
        DP_TOTAL_YEAR = 0x6A,	    // Liquid volume statistics
        DP_TOTAL_MONTH = 0x6B,	    // Liquid volume statistics
        DP_TOTAL_WEEK = 0x6C,	    // Liquid volume statistics
        DP_SCENE_0 = 0x72,	   
        DP_SCENE_1 = 0x73,	   
        DP_SCENE_2 = 0x74,	
        DP_SCENE_3 = 0x75,	
        DP_SCENE_4 = 0x76,	
        DP_SCENE_5 = 0x77,	
        DP_LOCATION = 0x67,        // something related to gps
        DP_BETTER = 0x6D,	       // ?? REPORT POWER ??? 
        DP_WEEK7 = 0x79,	    // oil consumption per week day
        DP_DAY_TOTAL = 0x7A,	// oil consumption per ?this? day
        DP_MONTH10 = 0x80,	
        DP_MONTH20 = 0x8A,	
        DP_MONTH31 = 0x94,	
        DP_YEAR1 = 0x9F,	
        DP_YEAR2 = 0xA0,	
        DP_YEAR3 = 0xA1,	
        // --- not used ---

        
    } data_point_id_t;


    typedef enum
    {
        TDT_RAW = 0,
        TDT_BOOL,
        TDT_INT,
        TDT_STRING,
        TDT_ENUM,
        TDT_BITMAP,
    } tuya_data_type_t;

    typedef enum 
    {
        NTW_ST_PAIRING = 0x00,           // in docs: "Pairing in EZ mode"
        NTW_ST_PAIRING_AS_AP = 0x01,         // in docs: "Pairing in AP mode"
        NTW_ST_NOT_CONNECTED_TO_WIFI = 0x02,
        NTW_ST_CONNECTED_TO_WIFI = 0x03,
        NTW_ST_CONNECTED_TO_CLOUD = 0x04,
        NTW_ST_BORK_MODULE_IS_IN_LOW_POWER = 0x05,
        NTW_ST_EZ_AND_AP = 0x06,   // in docs: "EZ mode and AP mode coexist"

    } tuya_network_status_t;

    typedef struct    // this struct arrange memory in order of uart packet. change it carefully!!!
    {
        uint8_t got_time_from_server;  // 0 - no , 1 - yes
        uint8_t year_after_2000;
        uint8_t month;
        uint8_t day;
        uint8_t hour;
        uint8_t minute;
        uint8_t second;
        uint8_t week;
    } tuya_time_t;





/***************************
 *  tuya uart packet struct
 ***************************/
static const uint8_t tuya_header[] = {0x55, 0xaa};

typedef enum
{
    TY_HEARTBEAT = 0x00,        // to & from mcu
    TY_GET_PRODUCT_INFO = 0x01, // to mcu, with reply
    TY_NETWORK_STATUS = 0x03,   // to & from mcu
    TY_SET = 0x06,              // to mcu
    TY_REPLY = 0x07,            // from mcu
    TY_QUERY_ALL = 0x08,        // to mcu
    TY_WIFI_POWER = 0x24,       // from mcu
    TY_LOCAL_TIME = 0x1C,       // from mcu
} tuya_command_t;

typedef struct 
{
    uint8_t header[2];
    uint8_t version;
    uint8_t command; 
    uint16_t data_len; 
    uint8_t *data;
    uint8_t checksum;
} tuya_uart_packet_t;    // small. contains only pointer to data - should point to uart buffer
#define TUYA_PACKET_SERVICE_BYTES_QTY (2 + 1 + 1 + 2 + 1)   // needed for packets length calcs everywhere


/** 
 * This struct forms memory to be sent to uart as raw data.
 * so no enums here.
 * data len is in big endia format
 * Checksum is not struct member, should be in data[], right after data bytes
 */
typedef struct 
{
    uint8_t header[2];
    uint8_t version;
    uint8_t command; 
    uint8_t data_len[2];   
    uint8_t data[OUT_PACKET_DATA_SIZE];
    // uint8_t checksum;      checksum should be in data[], right after data bytes
} tuya_out_uart_packet_t;  // BIG .contains data itself, not pointer



typedef enum 
{
    PCKT_NO_ERR = 0,
    PCKT_ERR_WRONG_HEADER,
    PCKT_ERR_WRONG_LENGTH,
    PCKT_ERR_WRONG_CHECKSUM,
    PCKT_ERR,
    PCKT_WARN_UNPARCED_DATA_LEFT,
    PCKT_WRONG_TYPE,
} parsing_errs_t;



/**********************************
 * 
 *     For incoming packets
 * 
 **********************************/
/**
 * @brief Parses incoming uart packet (only packet, not data units). Checks only for packet correctness. not for values correctness, etc.
 * After parsing packet, its supposed to call data points parser for evey data point in the packet.
 * @param data - pointer to uart raw packet
 * @param data_len - length of the packet
 * @param out - pointer to where to whrite parsed packet.
 *              data units are not copied, resulting struct only contains pointer to data units start
 * @return error type if packet is wrong.
 *     !!! If found two tuya packets in single uart data block - returns PCKT_WARN_UNPARCED_DATA_LEFT. Can be called one more time to parced what left.
 */
parsing_errs_t parse_packet(const uint8_t *data, uint16_t data_len, tuya_uart_packet_t *out);



/**
 * @brief Parsed data point(DP) per tuya protocol. Checks only for packet correctness. not for values correctness, etc.
 * @param data - pointer to DP start
 * @param len_limit - length of data PACKET (not of dp as its not transmitted). so its for simple chech that dp length is smaller than this.
 * @param out - pointer to where to write DP
 * @return returns packet err if any
 */
parsing_errs_t parse_data_point(const uint8_t *data, uint16_t len_limit, tuya_data_point_t *out);



// helpers
// finds header in raw data. return NULL  if not found
uint8_t * find_next_header(const uint8_t *data, uint16_t len);



/**********************************
 * 
 *     For outgoing packets
 * 
 **********************************/



/**
 * Functions to prepare uart out object for SET command.
 * Supposed to be called insequence: init, several adds, then close.
 */
void out_pckt_init(tuya_out_uart_packet_t *pkt);
void out_pkt_add_dp(tuya_out_uart_packet_t *pkt, const tuya_data_point_t *dp);
void out_pkt_close(tuya_out_uart_packet_t *pkt);

void make_single_dp_packet(tuya_out_uart_packet_t *pkt, const tuya_data_point_t *dp);

// strength is in db but without (-), like (-20db should be passed as 20)
void make_wifi_strength_packet(uint8_t *where, int8_t strength, uint16_t *out_size);
void make_network_status_pkt(uint8_t *where, tuya_network_status_t status, uint16_t *out_size); 
void make_time_pkt(uint8_t *where, tuya_time_t *time, uint16_t *out_size);
void make_query_all_pkt(uint8_t *where, uint16_t *out_size);
void make_product_info_pkt(uint8_t *where, uint16_t *out_size);
void make_heartbeat_pkt(uint8_t *where, uint16_t *out_size);
void make_working_timing_start_time_pkt (uint8_t *where, uint16_t *out_size);
void make_working_timing_end_time_pkt (uint8_t *where, uint16_t *out_size);
void make_working_timing_all_week_days_pkt (uint8_t *where, uint16_t *out_size);
void make_set_mode_dp(tuya_data_point_t *dp, bool mode);

// fungene MCU OTA packets
void make_fung_ota_set_wifi_psw_packet(uint8_t *where, uint16_t *out_size, char *wifi_psw);
void make_fung_ota_set_wifi_ssid_packet(uint8_t *where, uint16_t *out_size, char *wifi_ssid);
void make_fung_ota_start_packet(uint8_t *where, uint16_t *out_size);



/*****************************
 *      helpers
 *****************************/
bool is_used_dp(tuya_data_point_t *dp);
bool is_existing_dp(tuya_data_point_t *dp);



/**
 * @brief returns data point length (not of struct, but of raw data packet for such DP)
 */
uint16_t get_dp_length(const tuya_data_point_t *dp);
uint16_t get_tuya_pkt_length(const tuya_out_uart_packet_t *pkt);



// to strings
const char *dp_id_to_string(data_point_id_t dp_id);
const char *tuya_data_type_to_string (tuya_data_type_t type);
const char *pkt_error_to_string(parsing_errs_t err);

// logs
void log_uart_packet(tuya_uart_packet_t *pkt);
void log_dp(tuya_data_point_t *dp);



}  //  a830::uart_protocol namespace

