


v 1.3.0 Changes:
DHCP name changes to A630
MQTT logging
new reconnect logic
firmware version in BLE (F302) charachteristic, including piece of commit hash
local control via BLE
basic auth in balancer request, with device id

_OTA: diconnect error message added
_OTA: new messages: DOWNLOAD_CRASHED, WRONG_FIRMWARE

_BSH version 2.1.0      

v 1.3.1 Changed:
changed wifi reconnect logic:  nor run full scan and pick a hot spot with better signal
(before the change it was fast scan - till first hot spot with proper SSID is found)

change wifi power saving mode: now we disable BLE when its not needed (afet 2 min) and switch wifi to no-power-saving-mode

hope these changes will improve connection stability