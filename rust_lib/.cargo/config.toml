[build]
# Конфигурация для сборки под ESP32 с официальными образами Espressif

[target.xtensa-esp32-espidf]
linker = "ldproxy"

[target.xtensa-esp32s3-espidf]
linker = "ldproxy"

[target.riscv32imc-esp-espidf]
linker = "ldproxy"

[env]
# Переменные окружения для ESP32
ESP_IDF_VERSION = { value = "5.2.3" }

# Настройки для статической библиотеки
[target.'cfg(target_os = "espidf")']
rustflags = [
    # Оптимизации для размера (убираем конфликтные флаги)
    "-C", "opt-level=s",
]
