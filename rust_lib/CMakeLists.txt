cmake_minimum_required(VERSION 3.5)

# Определяем переменные для Rust
set(RUST_PROJECT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
set(RUST_TARGET_DIR ${RUST_PROJECT_DIR}/target)
set(RUST_LIB_NAME "liba630_rust_lib.a")

# Определяем целевую архитектуру для ESP32 (автоматически определяем из IDF_TARGET)
if(DEFINED ENV{IDF_TARGET})
    if($ENV{IDF_TARGET} STREQUAL "esp32s3")
        set(RUST_TARGET "xtensa-esp32s3-espidf")
    elseif($ENV{IDF_TARGET} STREQUAL "esp32")
        set(RUST_TARGET "xtensa-esp32-espidf")
    else()
        set(RUST_TARGET "xtensa-esp32s3-espidf")  # По умолчанию ESP32-S3
    endif()
else()
    set(RUST_TARGET "xtensa-esp32s3-espidf")  # По умолчанию ESP32-S3
endif()

message(STATUS "Rust target: ${RUST_TARGET}")

# Путь к статической библиотеке
set(RUST_LIB_PATH "${RUST_TARGET_DIR}/${RUST_TARGET}/release/${RUST_LIB_NAME}")

# Проверяем, что Rust библиотека уже собрана
if(NOT EXISTS ${RUST_LIB_PATH})
    message(FATAL_ERROR
        "Rust библиотека не найдена: ${RUST_LIB_PATH}\n"
        "Сначала соберите Rust библиотеку одним из способов:\n"
        "  ./.scripts/build_rust_no_std.sh\n"
        "  ./.scripts/build_rust_minimal.sh\n"
        "  ./.scripts/build_rust_in_docker.sh\n"
    )
endif()

message(STATUS "Найдена Rust библиотека: ${RUST_LIB_PATH}")

# Создаем фиктивную цель для совместимости
add_custom_target(rust_lib_build ALL
    COMMENT "Rust library already built"
)

# Регистрируем компонент
idf_component_register(
    INCLUDE_DIRS "include"
    REQUIRES ""
)

# Добавляем статическую библиотеку
add_library(rust_static_lib STATIC IMPORTED GLOBAL)
set_target_properties(rust_static_lib PROPERTIES
    IMPORTED_LOCATION ${RUST_LIB_PATH}
)

# Линкуем библиотеку к компоненту
target_link_libraries(${COMPONENT_LIB} INTERFACE rust_static_lib)

message(STATUS "Rust библиотека подключена к компоненту")
