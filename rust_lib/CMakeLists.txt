cmake_minimum_required(VERSION 3.5)

# Определяем переменные для Rust
set(RUST_PROJECT_DIR ${CMAKE_CURRENT_SOURCE_DIR})
set(RUST_TARGET_DIR ${RUST_PROJECT_DIR}/target)
set(RUST_LIB_NAME "liba630_rust_lib.a")

# Определяем целевую архитектуру для ESP32
set(RUST_TARGET "xtensa-esp32-espidf")

# Путь к статической библиотеке
set(RUST_LIB_PATH "${RUST_TARGET_DIR}/${RUST_TARGET}/release/${RUST_LIB_NAME}")

# Создаем кастомную команду для сборки Rust библиотеки
add_custom_command(
    OUTPUT ${RUST_LIB_PATH}
    COMMAND ${CMAKE_COMMAND} -E env 
        "IDF_PATH=$ENV{IDF_PATH}"
        "PATH=$ENV{PATH}"
        cargo build --release --target=${RUST_TARGET}
    WORKING_DIRECTORY ${RUST_PROJECT_DIR}
    COMMENT "Building Rust library"
    VERBATIM
)

# Создаем кастомную цель для Rust библиотеки
add_custom_target(rust_lib_build ALL
    DEPENDS ${RUST_LIB_PATH}
)

# Регистрируем компонент
idf_component_register(
    INCLUDE_DIRS "include"
    REQUIRES ""
)

# Добавляем статическую библиотеку
add_library(rust_static_lib STATIC IMPORTED GLOBAL)
set_target_properties(rust_static_lib PROPERTIES
    IMPORTED_LOCATION ${RUST_LIB_PATH}
)

# Устанавливаем зависимость
add_dependencies(rust_static_lib rust_lib_build)

# Линкуем библиотеку к компоненту
target_link_libraries(${COMPONENT_LIB} INTERFACE rust_static_lib)
