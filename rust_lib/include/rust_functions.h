#ifndef RUST_FUNCTIONS_H
#define RUST_FUNCTIONS_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

/**
 * @brief Складывает два целых числа
 * @param a Первое число
 * @param b Второе число
 * @return Сумма a и b
 */
int32_t rust_add_numbers(int32_t a, int32_t b);

/**
 * @brief Вычисляет длину C-строки
 * @param s Указатель на C-строку (null-terminated)
 * @return Длина строки в символах
 */
uint32_t rust_string_length(const char* s);

/**
 * @brief Вычисляет факториал числа
 * @param n Число для вычисления факториала
 * @return Факториал числа n
 */
uint32_t rust_factorial(uint32_t n);

/**
 * @brief Проверяет, является ли число четным
 * @param n Число для проверки
 * @return 1 если число четное, 0 если нечетное
 */
int32_t rust_is_even(int32_t n);

/**
 * @brief Находит максимальное значение в массиве
 * @param arr Указатель на массив целых чисел
 * @param len Длина массива
 * @return Максимальное значение в массиве
 */
int32_t rust_find_max(const int32_t* arr, uint32_t len);

#ifdef __cplusplus
}
#endif

#endif // RUST_FUNCTIONS_H
