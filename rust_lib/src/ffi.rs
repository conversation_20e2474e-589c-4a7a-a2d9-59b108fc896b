use std::os::raw::c_char;
use std::slice;
use crate::canonical_rust::{get_by_id, get_by_code};

#[repr(C)]
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)]
#[allow(non_camel_case_types)]
pub enum actor_t {
    MOD = 0, STA, ARA, WLP, ALP, STS, STE, SCN,
    FSC, FSM, FST, FSS, FSR, WTC, STC, WCN,
    CLR, CLN, SLP, FFV, FFU, FFS, BCT,
    #[allow(non_camel_case_types)]
    ACTORS_QTY,
}

#[no_mangle]
pub extern "C" fn actor_code_to_string(act: actor_t) -> *const c_char {
    let id = act as u32;
    match get_by_id(id) {
        Some(actor) => actor.mnemocode.as_ptr() as *const c_char,
        None => core::ptr::null(),
    }
}

#[no_mangle]
pub extern "C" fn get_actor_id(actor: *const c_char) -> actor_t {
    if actor.is_null() {
        return actor_t::ACTORS_QTY;
    }

    // Преобразуем указатель на char[3] в &[u8; 3]
    let slice = unsafe { slice::from_raw_parts(actor as *const u8, 3) };
    let code: &[u8; 3] = match slice.try_into() {
        Ok(arr) => arr,
        Err(_) => return actor_t::ACTORS_QTY,
    };

    match get_by_code(code) {
        Some(found) => unsafe { core::mem::transmute(found.id as u32) },
        None => actor_t::ACTORS_QTY,
    }
}
