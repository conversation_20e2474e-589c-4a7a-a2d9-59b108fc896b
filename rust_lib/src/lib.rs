// Убираем #![no_std] для совместимости с официальными образами Espressif
// Используем стандартные типы C для максимальной совместимости

use std::os::raw::{c_char, c_int, c_uint};

// Простая функция для демонстрации - сложение двух чисел
#[no_mangle]
pub extern "C" fn rust_add_numbers(a: c_int, b: c_int) -> c_int {
    a + b
}

// Функция для работы со строками - подсчет длины строки
#[no_mangle]
pub extern "C" fn rust_string_length(s: *const c_char) -> c_uint {
    if s.is_null() {
        return 0;
    }
    
    let mut len = 0;
    unsafe {
        let mut ptr = s;
        while *ptr != 0 {
            len += 1;
            ptr = ptr.add(1);
        }
    }
    len
}

// Функция для вычисления факториала
#[no_mangle]
pub extern "C" fn rust_factorial(n: c_uint) -> c_uint {
    if n <= 1 {
        1
    } else {
        n * rust_factorial(n - 1)
    }
}

// Функция для проверки четности числа
#[no_mangle]
pub extern "C" fn rust_is_even(n: c_int) -> c_int {
    if n % 2 == 0 { 1 } else { 0 }
}

// Функция для поиска максимума в массиве
#[no_mangle]
pub extern "C" fn rust_find_max(arr: *const c_int, len: c_uint) -> c_int {
    if arr.is_null() || len == 0 {
        return 0;
    }
    
    unsafe {
        let mut max = *arr;
        for i in 1..len {
            let val = *arr.add(i as usize);
            if val > max {
                max = val;
            }
        }
        max
    }
}
