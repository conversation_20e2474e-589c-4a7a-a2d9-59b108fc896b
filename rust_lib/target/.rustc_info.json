{"rustc_fingerprint": 13052414566692623844, "outputs": {"14745819392832847240": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.a\n/home/<USER>/.rustup/toolchains/esp\noff\n___\ndebug_assertions\nfmt_debug=\"full\"\noverflow_checks\npanic=\"abort\"\nproc_macro\nrelocation_model=\"static\"\ntarget_abi=\"\"\ntarget_arch=\"xtensa\"\ntarget_endian=\"little\"\ntarget_env=\"newlib\"\ntarget_family=\"unix\"\ntarget_feature=\"atomctl\"\ntarget_feature=\"bool\"\ntarget_feature=\"coprocessor\"\ntarget_feature=\"debug\"\ntarget_feature=\"dfpaccel\"\ntarget_feature=\"div32\"\ntarget_feature=\"exception\"\ntarget_feature=\"fp\"\ntarget_feature=\"highpriinterrupts\"\ntarget_feature=\"interrupt\"\ntarget_feature=\"loop\"\ntarget_feature=\"mac16\"\ntarget_feature=\"memctl\"\ntarget_feature=\"miscsr\"\ntarget_feature=\"mul32\"\ntarget_feature=\"mul32high\"\ntarget_feature=\"nsa\"\ntarget_feature=\"prid\"\ntarget_feature=\"regprotect\"\ntarget_feature=\"rvector\"\ntarget_feature=\"s32c1i\"\ntarget_feature=\"sext\"\ntarget_feature=\"threadptr\"\ntarget_feature=\"timerint\"\ntarget_feature=\"windowed\"\ntarget_has_atomic\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_has_reliable_f128\ntarget_has_reliable_f16\ntarget_has_reliable_f16_math\ntarget_os=\"espidf\"\ntarget_pointer_width=\"32\"\ntarget_vendor=\"espressif\"\nub_checks\nunix\n", "stderr": "warning: dropping unsupported crate type `dylib` for target `xtensa-esp32s3-espidf`\n\nwarning: dropping unsupported crate type `cdylib` for target `xtensa-esp32s3-espidf`\n\nwarning: dropping unsupported crate type `proc-macro` for target `xtensa-esp32s3-espidf`\n\nwarning: 3 warnings emitted\n\n"}, "679466108669735473": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.a\n/home/<USER>/.rustup/toolchains/esp\noff\n___\nfmt_debug=\"full\"\npanic=\"abort\"\nproc_macro\nrelocation_model=\"static\"\ntarget_abi=\"\"\ntarget_arch=\"xtensa\"\ntarget_endian=\"little\"\ntarget_env=\"newlib\"\ntarget_family=\"unix\"\ntarget_feature=\"atomctl\"\ntarget_feature=\"bool\"\ntarget_feature=\"coprocessor\"\ntarget_feature=\"debug\"\ntarget_feature=\"dfpaccel\"\ntarget_feature=\"div32\"\ntarget_feature=\"exception\"\ntarget_feature=\"fp\"\ntarget_feature=\"highpriinterrupts\"\ntarget_feature=\"interrupt\"\ntarget_feature=\"loop\"\ntarget_feature=\"mac16\"\ntarget_feature=\"memctl\"\ntarget_feature=\"miscsr\"\ntarget_feature=\"mul32\"\ntarget_feature=\"mul32high\"\ntarget_feature=\"nsa\"\ntarget_feature=\"prid\"\ntarget_feature=\"regprotect\"\ntarget_feature=\"rvector\"\ntarget_feature=\"s32c1i\"\ntarget_feature=\"sext\"\ntarget_feature=\"threadptr\"\ntarget_feature=\"timerint\"\ntarget_feature=\"windowed\"\ntarget_has_atomic\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_has_reliable_f128\ntarget_has_reliable_f16\ntarget_has_reliable_f16_math\ntarget_os=\"espidf\"\ntarget_pointer_width=\"32\"\ntarget_vendor=\"espressif\"\nunix\n", "stderr": "warning: dropping unsupported crate type `dylib` for target `xtensa-esp32s3-espidf`\n\nwarning: dropping unsupported crate type `cdylib` for target `xtensa-esp32s3-espidf`\n\nwarning: dropping unsupported crate type `proc-macro` for target `xtensa-esp32s3-espidf`\n\nwarning: 3 warnings emitted\n\n"}, "12525862909683003124": {"success": true, "status": "", "code": 0, "stdout": "___\nlib___.rlib\nlib___.so\nlib___.so\nlib___.a\nlib___.so\n/home/<USER>/.rustup/toolchains/esp\noff\npacked\nunpacked\n___\ndebug_assertions\nfmt_debug=\"full\"\noverflow_checks\npanic=\"unwind\"\nproc_macro\nrelocation_model=\"pic\"\ntarget_abi=\"\"\ntarget_arch=\"x86_64\"\ntarget_endian=\"little\"\ntarget_env=\"gnu\"\ntarget_family=\"unix\"\ntarget_feature=\"fxsr\"\ntarget_feature=\"sse\"\ntarget_feature=\"sse2\"\ntarget_feature=\"x87\"\ntarget_has_atomic\ntarget_has_atomic=\"16\"\ntarget_has_atomic=\"32\"\ntarget_has_atomic=\"64\"\ntarget_has_atomic=\"8\"\ntarget_has_atomic=\"ptr\"\ntarget_has_atomic_equal_alignment=\"16\"\ntarget_has_atomic_equal_alignment=\"32\"\ntarget_has_atomic_equal_alignment=\"64\"\ntarget_has_atomic_equal_alignment=\"8\"\ntarget_has_atomic_equal_alignment=\"ptr\"\ntarget_has_atomic_load_store\ntarget_has_atomic_load_store=\"16\"\ntarget_has_atomic_load_store=\"32\"\ntarget_has_atomic_load_store=\"64\"\ntarget_has_atomic_load_store=\"8\"\ntarget_has_atomic_load_store=\"ptr\"\ntarget_has_reliable_f128\ntarget_has_reliable_f16\ntarget_has_reliable_f16_math\ntarget_os=\"linux\"\ntarget_pointer_width=\"64\"\ntarget_thread_local\ntarget_vendor=\"unknown\"\nub_checks\nunix\n", "stderr": ""}, "15688847862728363398": {"success": true, "status": "", "code": 0, "stdout": "rustc 1.88.0-nightly (2ab28d2e7 2025-06-24) (********)\nbinary: rustc\ncommit-hash: 2ab28d2e728c222edd27f881fd18de24fd88332c\ncommit-date: 2025-06-24\nhost: x86_64-unknown-linux-gnu\nrelease: 1.88.0-nightly\nLLVM version: 19.1.2\n", "stderr": ""}}, "successes": {}}